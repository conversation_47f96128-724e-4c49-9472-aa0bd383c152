import { EventEmitter } from '../utils/EventEmitter';
import { ChainEvent, ChainEventType } from './chainTypes';

/**
 * Chain Listener Service
 *
 * This service listens for on-chain events and emits them as ChainEvent objects.
 * Currently, it only emits sample events for testing purposes.
 */
class ChainListenerService extends EventEmitter {
  private isListening: boolean = false;

  constructor() {
    super();
    console.log('Chain listener service initialized');
  }

  /**
   * Start listening to on-chain events
   *
   * This method emits sample events for testing purposes only.
   * No real blockchain events are fetched.
   */
  public startListening(): void {
    if (this.isListening) {
      console.log('Already listening to chain events');
      return;
    }

    this.isListening = true;
    console.log('Using sample chain events for testing');

    // Import the Helius service dynamically to avoid circular dependencies
    import('./heliusService').then(({ heliusService }) => {
      // Emit a DAO proposal event immediately
      const daoEvent = heliusService.createSampleDAOEvent();
      console.log('About to emit DAO event:', daoEvent);
      const emitted = this.emit('chainEvent', daoEvent);
      console.log('Emitted sample DAO event:', daoEvent.type, 'Success:', emitted, 'Listeners:', this.events['chainEvent']?.length || 0);

      // Emit a funding round event after a short delay
      setTimeout(() => {
        const fundingEvent = heliusService.createFundingRoundEvent();
        console.log('About to emit funding event:', fundingEvent);
        const emitted = this.emit('chainEvent', fundingEvent);
        console.log('Emitted sample funding event:', fundingEvent.type, 'Success:', emitted, 'Listeners:', this.events['chainEvent']?.length || 0);
      }, 5000);

      // Emit a security incident event after a longer delay
      setTimeout(() => {
        const securityEvent = heliusService.createSecurityIncidentEvent();
        console.log('About to emit security incident event:', securityEvent);
        const emitted = this.emit('chainEvent', securityEvent);
        console.log('Emitted sample security event:', securityEvent.type, 'Success:', emitted, 'Listeners:', this.events['chainEvent']?.length || 0);
      }, 10000);

      // Set up a recurring timer to emit random events every 15 seconds
      const eventInterval = setInterval(() => {
        // Choose a random event type
        const eventTypes = [
          () => heliusService.createSampleDAOEvent(),
          () => heliusService.createFundingRoundEvent(),
          () => heliusService.createSecurityIncidentEvent()
        ];

        const randomEventGenerator = eventTypes[Math.floor(Math.random() * eventTypes.length)];
        const randomEvent = randomEventGenerator();

        console.log('About to emit random event:', randomEvent);
        const emitted = this.emit('chainEvent', randomEvent);
        console.log('Emitted random event:', randomEvent.type, 'Success:', emitted, 'Listeners:', this.events['chainEvent']?.length || 0);
      }, 15000);

      // Store the interval ID so we can clear it if needed
      (this as any).eventInterval = eventInterval;
    }).catch(error => {
      console.error('Error importing Helius service:', error);
    });
  }

  /**
   * Stop listening to on-chain events
   */
  public stopListening(): void {
    this.isListening = false;

    // Clear the event interval if it exists
    if ((this as any).eventInterval) {
      clearInterval((this as any).eventInterval);
      (this as any).eventInterval = null;
    }

    console.log('Stopped listening to chain events');
  }
}

// Export a singleton instance
export const chainListener = new ChainListenerService();
