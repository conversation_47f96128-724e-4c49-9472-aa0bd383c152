# 📖 Manual Journal System Setup

The advanced journal system requires database changes. Here's how to set it up manually:

## Step 1: Add New Columns to journals Table

Go to your **Supabase Dashboard** → **Table Editor** → **journals** table and add these columns:

```sql
-- Add privacy level column
ALTER TABLE journals ADD COLUMN privacy_level TEXT DEFAULT 'public' 
  CHECK (privacy_level IN ('my_journal', 'public', 'locked_public', 'private', 'locked_private'));

-- Add summoned users array
ALTER TABLE journals ADD COLUMN summoned_users JSONB DEFAULT '[]'::JSONB;

-- Add tip to unlock fields
ALTER TABLE journals ADD COLUMN tip_to_unlock_amount DECIMAL(10,2);
ALTER TABLE journals ADD COLUMN tip_to_unlock_currency TEXT DEFAULT 'SOL';

-- Add social engagement counters
ALTER TABLE journals ADD COLUMN repost_count INTEGER DEFAULT 0;
ALTER TABLE journals ADD COLUMN reply_count INTEGER DEFAULT 0;
ALTER TABLE journals ADD COLUMN reaction_count INTEGER DEFAULT 0;
```

## Step 2: Create New Tables

### journal_access table
```sql
CREATE TABLE journal_access (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  journal_id UUID NOT NULL REFERENCES journals(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL,
  access_type TEXT NOT NULL CHECK (access_type IN ('summoned', 'tip_unlocked', 'owner')),
  granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  granted_by TEXT,
  tip_amount DECIMAL(10,2),
  tip_currency TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(journal_id, user_id, access_type)
);
```

### journal_reposts table
```sql
CREATE TABLE journal_reposts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  journal_id UUID NOT NULL REFERENCES journals(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(journal_id, user_id)
);
```

### journal_replies table
```sql
CREATE TABLE journal_replies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  journal_id UUID NOT NULL REFERENCES journals(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL,
  content TEXT,
  audio_url TEXT,
  audio_duration INTEGER,
  is_voice_reply BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### journal_reactions table
```sql
CREATE TABLE journal_reactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  journal_id UUID NOT NULL REFERENCES journals(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL,
  reaction_type TEXT NOT NULL CHECK (reaction_type IN ('like', 'love', 'laugh', 'wow', 'sad', 'angry')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(journal_id, user_id, reaction_type)
);
```

## Step 3: Create Indexes

```sql
CREATE INDEX idx_journals_privacy_level ON journals(privacy_level);
CREATE INDEX idx_journals_summoned_users ON journals USING GIN(summoned_users);
CREATE INDEX idx_journal_access_journal_id ON journal_access(journal_id);
CREATE INDEX idx_journal_access_user_id ON journal_access(user_id);
CREATE INDEX idx_journal_reposts_journal_id ON journal_reposts(journal_id);
CREATE INDEX idx_journal_reposts_user_id ON journal_reposts(user_id);
CREATE INDEX idx_journal_replies_journal_id ON journal_replies(journal_id);
CREATE INDEX idx_journal_reactions_journal_id ON journal_reactions(journal_id);
```

## Step 4: Enable RLS on New Tables

```sql
ALTER TABLE journal_access ENABLE ROW LEVEL SECURITY;
ALTER TABLE journal_reposts ENABLE ROW LEVEL SECURITY;
ALTER TABLE journal_replies ENABLE ROW LEVEL SECURITY;
ALTER TABLE journal_reactions ENABLE ROW LEVEL SECURITY;
```

## Step 5: Create RLS Policies

### For journal_access
```sql
CREATE POLICY "Users can view their own journal access" ON journal_access
  FOR SELECT USING (user_id = auth.uid()::text);

CREATE POLICY "Users can grant access to their own journals" ON journal_access
  FOR INSERT WITH CHECK (
    EXISTS (SELECT 1 FROM journals WHERE id = journal_id AND profile_id = auth.uid()::text)
  );
```

### For journal_reposts
```sql
CREATE POLICY "Users can view all reposts" ON journal_reposts FOR SELECT USING (true);
CREATE POLICY "Users can create reposts" ON journal_reposts FOR INSERT WITH CHECK (
  user_id = auth.uid()::text
);
CREATE POLICY "Users can delete their own reposts" ON journal_reposts FOR DELETE USING (
  user_id = auth.uid()::text
);
```

### For journal_replies
```sql
CREATE POLICY "Users can view all replies" ON journal_replies FOR SELECT USING (true);
CREATE POLICY "Users can create replies" ON journal_replies FOR INSERT WITH CHECK (
  user_id = auth.uid()::text
);
CREATE POLICY "Users can update their own replies" ON journal_replies FOR UPDATE USING (
  user_id = auth.uid()::text
);
CREATE POLICY "Users can delete their own replies" ON journal_replies FOR DELETE USING (
  user_id = auth.uid()::text
);
```

### For journal_reactions
```sql
CREATE POLICY "Users can view all reactions" ON journal_reactions FOR SELECT USING (true);
CREATE POLICY "Users can create reactions" ON journal_reactions FOR INSERT WITH CHECK (
  user_id = auth.uid()::text
);
CREATE POLICY "Users can update their own reactions" ON journal_reactions FOR UPDATE USING (
  user_id = auth.uid()::text
);
CREATE POLICY "Users can delete their own reactions" ON journal_reactions FOR DELETE USING (
  user_id = auth.uid()::text
);
```

## Step 6: Update Existing Data

```sql
-- Set privacy level for existing journals
UPDATE journals SET privacy_level = 'public' WHERE privacy_level IS NULL;
```

## Step 7: Test the Setup

After running all the SQL above:

1. **Refresh your app** (hard refresh with Ctrl+F5)
2. **Check the journals page** - your existing journals should still appear
3. **Try creating a new journal** - it should work with the new privacy system
4. **Check the browser console** - should see fewer errors

## 🎯 What This Enables

Once the database is set up, your journal system will support:

- **5 Privacy Levels**: My Journal, Public, Locked Public, Private, Locked Private
- **Social Features**: Reposts, Replies, Reactions
- **Access Control**: Summon users to private journals
- **Monetization**: Tip to unlock locked private journals
- **Engagement Metrics**: Track reposts, replies, and reactions

## 🔧 Troubleshooting

If you get errors:
1. **Check table names** - Make sure you're in the right database
2. **Run one statement at a time** - Don't run all SQL at once
3. **Check for typos** - SQL is case-sensitive
4. **Verify permissions** - Make sure you have admin access to the database

## ✅ Verification

After setup, you should see:
- New columns in the `journals` table
- 4 new tables: `journal_access`, `journal_reposts`, `journal_replies`, `journal_reactions`
- Your app should load journals without errors
- New privacy options should be available when creating journals

Your advanced journal system will be ready to use! 🎉
