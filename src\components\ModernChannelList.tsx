import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { useChannels } from '@/contexts/ChannelContext';
import {
  Hash,
  Plus,
  Search,
  Users,
  Crown,
  Lock,
  Globe,
  Mic,
  MessageCircle,
  TrendingUp,
  Star,
  Filter,
  RefreshCw
} from 'lucide-react';

interface ModernChannelListProps {
  userAddress: string;
  onCreateChannel: () => void;
  onJoinChannel: () => void;
}

const ModernChannelList: React.FC<ModernChannelListProps> = ({
  userAddress,
  onCreateChannel,
  onJoinChannel
}) => {
  const { userChannels, publicChannels, activeChannel, setActiveChannel, joinChannel, isUserMember, loading } = useChannels();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'joined' | 'popular'>('all');

  // Filter channels based on search and filter
  const filteredPublicChannels = publicChannels.filter(channel => {
    const matchesSearch = channel.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         channel.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         channel.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const isNotMember = !isUserMember(channel.id, userAddress);
    
    return matchesSearch && isNotMember;
  });

  const handleJoinChannel = async (channelId: string) => {
    console.log('🎯 JOIN BUTTON CLICKED:', channelId);

    // SIMPLE: Just join and enter
    joinChannel(channelId);
    setActiveChannel(channelId);
  };

  const handleChannelClick = (channel: any) => {
    console.log('🎯 CHANNEL CLICKED:', channel.name, channel.id);

    // SIMPLE: Just enter any channel, skip membership checks
    setActiveChannel(channel.id);
    console.log('✅ CHANNEL SET AS ACTIVE');
  };

  // Combine all channels for simple display
  const allChannels = [...userChannels, ...filteredPublicChannels];

  // Show loading state
  if (loading) {
    return (
      <div className="h-full flex flex-col bg-background">
        <div className="p-4 border-b border-border">
          <h2 className="text-lg font-bold text-foreground mb-3">Loading Channels...</h2>
        </div>
        <div className="flex-1 p-4">
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-20 bg-secondary rounded-lg animate-pulse"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Simple Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between mb-3">
          <div>
            <h2 className="text-lg font-bold text-foreground">All Channels</h2>
            <p className="text-xs text-muted-foreground">
              Current user: {userAddress?.slice(0, 6)}...{userAddress?.slice(-4)}
            </p>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => window.location.reload()}
            className="h-8 w-8 p-0"
            title="Refresh channels"
          >
            <RefreshCw size={16} />
          </Button>
        </div>

        {/* Simple Search */}
        <div className="relative">
          <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search channels..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Simple Channel List */}
      <div className="flex-1 overflow-y-auto p-4">
        {allChannels.length === 0 ? (
          <div className="text-center py-8">
            <Hash size={48} className="mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold mb-2">No Channels Found</h3>
            <p className="text-muted-foreground mb-4">
              {searchQuery ? 'No channels match your search' : 'No channels available or failed to load'}
            </p>
            <div className="space-y-2">
              <Button onClick={onCreateChannel} className="bg-voicechain-purple hover:bg-voicechain-accent">
                <Plus size={16} className="mr-2" />
                Create Channel
              </Button>
              <Button
                variant="outline"
                onClick={() => window.location.reload()}
                className="w-full"
              >
                <RefreshCw size={16} className="mr-2" />
                Refresh Page
              </Button>
              <Button
                variant="secondary"
                onClick={() => {
                  console.log('🔍 FULL DEBUG INFO:');
                  console.log('Wallet:', userAddress);
                  console.log('All Channels:', allChannels);
                  console.log('User Channels:', userChannels);
                  console.log('Public Channels:', publicChannels);
                  console.log('Active Channel:', activeChannel);
                  alert(`Debug Info:\nWallet: ${userAddress}\nChannels: ${allChannels.length}\nActive: ${activeChannel?.name || 'None'}\nCheck console for full details`);
                }}
                className="w-full text-xs"
              >
                🔧 Debug Info
              </Button>
              <Button
                variant="destructive"
                onClick={() => {
                  if (allChannels.length > 0) {
                    const firstChannel = allChannels[0];
                    console.log('🚨 EMERGENCY ENTER FIRST CHANNEL:', firstChannel);
                    setActiveChannel(firstChannel.id);
                  }
                }}
                className="w-full text-xs"
              >
                🚨 Emergency Enter First Channel
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            {/* Show ALL channels in a simple list */}
            {allChannels.map(channel => {
              const isJoined = isUserMember(channel.id, userAddress);
              const isActive = activeChannel === channel.id;

              return (
                <Card
                  key={channel.id}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    isActive
                      ? 'border-voicechain-purple bg-voicechain-purple/5 shadow-lg'
                      : 'border-border hover:border-border/80'
                  }`}
                  onClick={() => handleChannelClick(channel)}
                >
                  <CardContent className="p-4">
                    {/* Channel Banner/Cover Image */}
                    <div className="relative mb-3">
                      {channel.coverImageUrl ? (
                        <div className="h-20 rounded-lg bg-gradient-to-r from-voicechain-purple/20 to-voicechain-accent/20 bg-cover bg-center"
                             style={{ backgroundImage: `url(${channel.coverImageUrl})` }}>
                          <div className="absolute inset-0 bg-black/20 rounded-lg" />
                        </div>
                      ) : (
                        <div className="h-20 rounded-lg bg-gradient-to-r from-voicechain-purple/20 to-voicechain-accent/20 flex items-center justify-center">
                          <Hash size={24} className="text-voicechain-purple" />
                        </div>
                      )}

                      {/* Visibility Indicator */}
                      <div className="absolute top-2 right-2">
                        <Badge variant={channel.isPrivate ? "destructive" : "secondary"} className="text-xs">
                          {channel.isPrivate ? (
                            <>
                              <Lock size={10} className="mr-1" />
                              Private
                            </>
                          ) : (
                            <>
                              <Globe size={10} className="mr-1" />
                              Public
                            </>
                          )}
                        </Badge>
                      </div>
                    </div>

                    {/* Channel Info */}
                    <div className="space-y-3">
                      {/* Name and Join Status */}
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <h4 className="font-semibold text-lg truncate">{channel.name}</h4>
                          {isJoined && (
                            <Badge variant="outline" className="text-xs mt-1">
                              <Users size={10} className="mr-1" />
                              Joined
                            </Badge>
                          )}
                        </div>

                        <Button
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            console.log('🎯 BUTTON CLICKED - ENTERING CHANNEL:', channel.id);
                            setActiveChannel(channel.id);
                          }}
                          className="bg-voicechain-purple hover:bg-voicechain-accent text-white"
                        >
                          Enter
                        </Button>
                      </div>

                      {/* Description */}
                      {channel.description && (
                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {channel.description}
                        </p>
                      )}

                      {/* Tags/Categories */}
                      {channel.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {channel.tags.slice(0, 4).map(tag => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              #{tag}
                            </Badge>
                          ))}
                          {channel.tags.length > 4 && (
                            <Badge variant="secondary" className="text-xs">
                              +{channel.tags.length - 4}
                            </Badge>
                          )}
                        </div>
                      )}

                      {/* Creator & Stats */}
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <div className="flex items-center gap-2">
                          <Crown size={12} className="text-yellow-500" />
                          <span>
                            Creator: {channel.members.find(m => m.role === 'owner')?.address.slice(0, 6)}...
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Users size={12} />
                          <span>{channel.members.length} members</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </div>

      {/* Simple Action Buttons */}
      <div className="p-4 border-t border-border space-y-2">
        <Button
          onClick={onCreateChannel}
          className="w-full bg-voicechain-purple hover:bg-voicechain-accent"
        >
          <Plus size={16} className="mr-2" />
          Create Channel
        </Button>

        <Button
          variant="outline"
          onClick={onJoinChannel}
          className="w-full"
        >
          <Hash size={16} className="mr-2" />
          Join with Code
        </Button>
      </div>
    </div>
  );
};

export default ModernChannelList;
