import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { supabase } from '@/lib/supabase';
import { 
  Search, 
  TrendingUp, 
  Flame as Fire, 
  Star, 
  Play, 
  Users, 
  Hash,
  Clock,
  Eye,
  Heart,
  Share
} from 'lucide-react';

interface TrendingItem {
  id: string;
  content_type: string;
  content_id: string;
  trend_score: number;
  view_count: number;
  engagement_count: number;
  metadata: any;
  channel?: {
    name: string;
    description: string;
    members_count: number;
  };
  voice_message?: {
    transcript: string;
    duration: number;
    creator: {
      wallet_address: string;
      display_name?: string;
    };
  };
}

interface VoiceHighlight {
  id: string;
  title: string;
  description: string;
  duration: number;
  view_count: number;
  like_count: number;
  thumbnail_url?: string;
  creator: {
    wallet_address: string;
    display_name?: string;
  };
  channel?: {
    name: string;
  };
}

interface DiscoveryHubProps {
  userAddress: string;
}

const DiscoveryHub: React.FC<DiscoveryHubProps> = ({ userAddress }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [trendingContent, setTrendingContent] = useState<TrendingItem[]>([]);
  const [voiceHighlights, setVoiceHighlights] = useState<VoiceHighlight[]>([]);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchLoading, setSearchLoading] = useState(false);

  useEffect(() => {
    loadTrendingContent();
    loadVoiceHighlights();
  }, []);

  useEffect(() => {
    if (searchQuery.trim()) {
      performSearch();
    } else {
      setSearchResults([]);
    }
  }, [searchQuery]);

  const loadTrendingContent = async () => {
    try {
      const { data, error } = await supabase
        .from('trending_content')
        .select(`
          *,
          channel:channels(name, description),
          voice_message:voice_messages(
            transcript,
            duration,
            profile:profiles(wallet_address, display_name)
          )
        `)
        .eq('time_period', 'daily')
        .eq('trending_date', new Date().toISOString().split('T')[0])
        .order('trend_score', { ascending: false })
        .limit(10);

      if (error) {
        console.error('Error loading trending content:', error);
        return;
      }

      setTrendingContent(data || []);
    } catch (error) {
      console.error('Error loading trending content:', error);
    }
  };

  const loadVoiceHighlights = async () => {
    try {
      const { data, error } = await supabase
        .from('voice_highlights')
        .select(`
          *,
          creator:profiles(wallet_address, display_name),
          channel:channels(name)
        `)
        .eq('highlight_type', 'story')
        .gte('expires_at', new Date().toISOString())
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) {
        console.error('Error loading voice highlights:', error);
        return;
      }

      setVoiceHighlights(data || []);
    } catch (error) {
      console.error('Error loading voice highlights:', error);
    } finally {
      setLoading(false);
    }
  };

  const performSearch = async () => {
    if (!searchQuery.trim()) return;

    try {
      setSearchLoading(true);

      // Search voice transcripts
      const { data: transcriptResults, error: transcriptError } = await supabase
        .from('voice_transcripts')
        .select(`
          *,
          voice_message:voice_messages(
            id,
            duration,
            created_at,
            profile:profiles(wallet_address, display_name)
          ),
          channel:channels(name)
        `)
        .textSearch('transcript_text', searchQuery)
        .limit(10);

      // Search channels
      const { data: channelResults, error: channelError } = await supabase
        .from('channels')
        .select('*')
        .or(`name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`)
        .limit(5);

      if (transcriptError || channelError) {
        console.error('Search error:', transcriptError || channelError);
        return;
      }

      const combinedResults = [
        ...(channelResults || []).map(item => ({ ...item, type: 'channel' })),
        ...(transcriptResults || []).map(item => ({ ...item, type: 'voice_message' }))
      ];

      setSearchResults(combinedResults);
    } catch (error) {
      console.error('Error performing search:', error);
    } finally {
      setSearchLoading(false);
    }
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${Math.floor(diffHours / 24)}d ago`;
  };

  return (
    <div className="space-y-6">
      {/* Search Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Discover</h1>
          <Badge variant="outline" className="text-sm">
            Voice-First Social
          </Badge>
        </div>
        
        <div className="relative max-w-md">
          <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search voice content, channels, creators..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Search Results */}
      {searchQuery && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search size={20} />
              Search Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            {searchLoading ? (
              <div className="text-center py-4">Searching...</div>
            ) : searchResults.length === 0 ? (
              <div className="text-center py-4 text-muted-foreground">
                No results found for "{searchQuery}"
              </div>
            ) : (
              <div className="space-y-3">
                {searchResults.map((result, index) => (
                  <div key={index} className="p-3 rounded-lg border hover:bg-secondary/50 cursor-pointer">
                    {result.type === 'channel' ? (
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-voicechain-purple/10 rounded-lg flex items-center justify-center">
                          <Hash size={20} className="text-voicechain-purple" />
                        </div>
                        <div>
                          <h4 className="font-medium">{result.name}</h4>
                          <p className="text-sm text-muted-foreground">{result.description}</p>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-start gap-3">
                        <div className="w-10 h-10 bg-blue-500/10 rounded-lg flex items-center justify-center">
                          <Play size={20} className="text-blue-500" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium">
                              {result.voice_message?.profile?.display_name || 
                               `${result.voice_message?.profile?.wallet_address?.slice(0, 6)}...`}
                            </span>
                            <Badge variant="secondary" className="text-xs">
                              {formatDuration(result.voice_message?.duration || 0)}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground line-clamp-2">
                            {result.transcript_text}
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            in #{result.channel?.name} • {formatTimeAgo(result.voice_message?.created_at)}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Main Content */}
      <Tabs defaultValue="trending" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="trending">Trending</TabsTrigger>
          <TabsTrigger value="highlights">Stories</TabsTrigger>
          <TabsTrigger value="channels">Channels</TabsTrigger>
          <TabsTrigger value="creators">Creators</TabsTrigger>
        </TabsList>

        <TabsContent value="trending" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="text-orange-500" size={20} />
                Trending Today
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-3">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="h-16 bg-secondary rounded-lg animate-pulse"></div>
                  ))}
                </div>
              ) : trendingContent.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No trending content today
                </div>
              ) : (
                <div className="space-y-3">
                  {trendingContent.map((item, index) => (
                    <div key={item.id} className="flex items-center gap-3 p-3 rounded-lg hover:bg-secondary/50 cursor-pointer">
                      <div className="flex items-center justify-center w-8 h-8 bg-orange-500/10 rounded-full">
                        <span className="text-sm font-bold text-orange-500">#{index + 1}</span>
                      </div>
                      
                      {item.content_type === 'channel' ? (
                        <div className="w-10 h-10 bg-voicechain-purple/10 rounded-lg flex items-center justify-center">
                          <Hash size={20} className="text-voicechain-purple" />
                        </div>
                      ) : (
                        <div className="w-10 h-10 bg-blue-500/10 rounded-lg flex items-center justify-center">
                          <Play size={20} className="text-blue-500" />
                        </div>
                      )}
                      
                      <div className="flex-1">
                        <h4 className="font-medium">
                          {item.content_type === 'channel' ? item.channel?.name : 'Voice Message'}
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          {item.content_type === 'channel' 
                            ? item.channel?.description 
                            : item.voice_message?.transcript?.slice(0, 100) + '...'
                          }
                        </p>
                      </div>
                      
                      <div className="text-right">
                        <div className="flex items-center gap-3 text-sm text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <Eye size={12} />
                            {item.view_count}
                          </span>
                          <span className="flex items-center gap-1">
                            <Fire size={12} />
                            {Math.round(item.trend_score)}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="highlights" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="text-yellow-500" size={20} />
                Voice Stories
              </CardTitle>
            </CardHeader>
            <CardContent>
              {voiceHighlights.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No voice stories available
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {voiceHighlights.map(highlight => (
                    <Card key={highlight.id} className="cursor-pointer hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="aspect-square bg-gradient-to-br from-voicechain-purple/20 to-voicechain-accent/20 rounded-lg mb-3 flex items-center justify-center">
                          <Play size={32} className="text-voicechain-purple" />
                        </div>
                        <h4 className="font-medium mb-1">{highlight.title || 'Voice Story'}</h4>
                        <p className="text-sm text-muted-foreground mb-2">
                          by {highlight.creator.display_name || 
                              `${highlight.creator.wallet_address.slice(0, 6)}...`}
                        </p>
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <Clock size={12} />
                            {formatDuration(highlight.duration)}
                          </span>
                          <div className="flex items-center gap-2">
                            <span className="flex items-center gap-1">
                              <Eye size={12} />
                              {highlight.view_count}
                            </span>
                            <span className="flex items-center gap-1">
                              <Heart size={12} />
                              {highlight.like_count}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="channels" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Featured Channels</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Channel discovery coming soon...
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="creators" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Top Creators</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Creator leaderboard coming soon...
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DiscoveryHub;
