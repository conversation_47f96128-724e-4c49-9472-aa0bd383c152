import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { ChainVoicePost, chainVoicePostService } from '@/services/chainVoicePostService';
import { chainListener } from '@/services/chainListener';
import { voiceGenerator } from '@/services/voiceGenerator';

interface ChainVoiceContextType {
  posts: ChainVoicePost[];
  isLoading: boolean;
  isServiceRunning: boolean;
  startService: () => Promise<void>;
  stopService: () => void;
  getPostsByChannel: (channel: string) => ChainVoicePost[];
  getPostsByTag: (tag: string) => ChainVoicePost[];
  getPostsByChain: (chain: string) => ChainVoicePost[];
  refreshPosts: () => void;
  setApiKey: (key: string) => Promise<void>;
}

const ChainVoiceContext = createContext<ChainVoiceContextType | undefined>(undefined);

export const ChainVoiceProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [posts, setPosts] = useState<ChainVoicePost[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isServiceRunning, setIsServiceRunning] = useState<boolean>(false);

  // Define functions first using useCallback

  // Start the chain voice post service
  const startService = useCallback(async () => {
    try {
      // Import the services dynamically to avoid circular dependencies
      const { heliusService } = await import('@/services/heliusService');
      const { apiKeyManager, ApiKeyType } = await import('@/services/apiKeyManager');

      // Check if we have valid API keys
      const hasElevenLabsKey = apiKeyManager.hasApiKey(ApiKeyType.ELEVEN_LABS);

      if (!hasElevenLabsKey) {
        console.warn('ElevenLabs API key not set. Voice generation will not work properly.');
      }

      // Start the chain voice post service regardless of API key status
      // It will use mock data if needed
      chainVoicePostService.start();
      setIsServiceRunning(true);

      // Toast notification is handled in the Settings component
    } catch (error) {
      console.error('Error starting Chain Voice service:', error);
    }
  }, []);

  // Refresh posts
  const refreshPosts = useCallback(() => {
    setIsLoading(true);

    // Simulate loading delay
    setTimeout(() => {
      setPosts(chainVoicePostService.getAllPosts());
      setIsLoading(false);
    }, 1000);
  }, []);

  // Initialize the service
  useEffect(() => {
    // Subscribe to new posts
    const handleNewPost = (event: Event) => {
      const customEvent = event as CustomEvent<ChainVoicePost>;
      if (customEvent.detail) {
        setPosts(prevPosts => [customEvent.detail, ...prevPosts]);
      }
    };

    document.addEventListener('newChainVoicePost', handleNewPost);

    // Load initial posts
    refreshPosts();

    // Start the service automatically
    startService().catch(error => {
      console.error('Error starting Chain Voice service:', error);
    });

    return () => {
      document.removeEventListener('newChainVoicePost', handleNewPost);
    };
  }, [startService, refreshPosts]);

  // Stop the chain voice post service
  const stopService = useCallback(() => {
    chainVoicePostService.stop();
    setIsServiceRunning(false);
  }, []);

  // Get posts by channel
  const getPostsByChannel = useCallback((channel: string) => {
    return chainVoicePostService.getPostsByChannel(channel);
  }, []);

  // Get posts by tag
  const getPostsByTag = useCallback((tag: string) => {
    return chainVoicePostService.getPostsByTag(tag);
  }, []);

  // Get posts by chain
  const getPostsByChain = useCallback((chain: string) => {
    return chainVoicePostService.getPostsByChain(chain);
  }, []);

  // Set API key for voice generation
  const setApiKey = useCallback(async (key: string) => {
    try {
      await voiceGenerator.setApiKey(key);
    } catch (error) {
      console.error('Error setting API key:', error);
    }
  }, []);

  return (
    <ChainVoiceContext.Provider
      value={{
        posts,
        isLoading,
        isServiceRunning,
        startService,
        stopService,
        getPostsByChannel,
        getPostsByTag,
        getPostsByChain,
        refreshPosts,
        setApiKey
      }}
    >
      {children}
    </ChainVoiceContext.Provider>
  );
};

export const useChainVoice = (): ChainVoiceContextType => {
  const context = useContext(ChainVoiceContext);

  if (context === undefined) {
    throw new Error('useChainVoice must be used within a ChainVoiceProvider');
  }

  return context;
};
