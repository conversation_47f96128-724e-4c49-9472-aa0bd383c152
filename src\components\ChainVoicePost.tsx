import React, { useState, useRef, useEffect } from 'react';
import { ChainVoicePost } from '@/services/chainVoicePostService';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Heart, MessageSquare, Repeat, Share2, Play, Pause, Volume2 } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { Waveform } from '@/components/Waveform';

interface ChainVoicePostProps {
  post: ChainVoicePost;
  onReply?: (post: ChainVoicePost) => void;
  onLike?: (post: ChainVoicePost) => void;
  onRepost?: (post: ChainVoicePost) => void;
  onShare?: (post: ChainVoicePost) => void;
}

// Map of chain names to avatar images
const chainAvatars: Record<string, string> = {
  solana: '/images/chains/solana.png',
  ethereum: '/images/chains/ethereum.png',
  polygon: '/images/chains/polygon.png',
  arbitrum: '/images/chains/arbitrum.png',
  optimism: '/images/chains/optimism.png',
  base: '/images/chains/base.png',
};

// Map of event types to colors
const eventTypeColors: Record<string, string> = {
  dao_vote: 'bg-blue-500',
  dao_proposal_created: 'bg-blue-500',
  dao_proposal_executed: 'bg-blue-500',
  funding_round: 'bg-green-500',
  // NFT events removed
  token_transfer: 'bg-yellow-500',
  whale_movement: 'bg-yellow-500',
  protocol_update: 'bg-cyan-500',
  security_incident: 'bg-red-500',
  bridge_transaction: 'bg-orange-500',
};

export const ChainVoicePostComponent: React.FC<ChainVoicePostProps> = ({
  post,
  onReply,
  onLike,
  onRepost,
  onShare,
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(post.audioDuration);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [isLiked, setIsLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(Math.floor(Math.random() * 50));
  const [replyCount, setReplyCount] = useState(Math.floor(Math.random() * 20));
  const [repostCount, setRepostCount] = useState(Math.floor(Math.random() * 10));

  // Initialize audio element
  useEffect(() => {
    // Check if the audio URL is a speech synthesis URL
    if (post.audioUrl && post.audioUrl.startsWith('speech-synthesis://')) {
      console.log('Using speech synthesis for this post');
      // Set initial duration from post data
      setDuration(post.audioDuration || 10);

      // Load the voices
      if ('speechSynthesis' in window) {
        // Load voices if they're not already loaded
        if (window.speechSynthesis.getVoices().length === 0) {
          window.speechSynthesis.onvoiceschanged = () => {
            console.log('Voices loaded:', window.speechSynthesis.getVoices().length);
          };
        } else {
          console.log('Voices already loaded:', window.speechSynthesis.getVoices().length);
        }
      }

      return () => {
        // Cancel any ongoing speech when unmounting
        if ('speechSynthesis' in window) {
          window.speechSynthesis.cancel();
        }
        if (isPlaying) {
          setIsPlaying(false);
          setCurrentTime(0);
        }
      };
    }
    // Create a new audio element if we have a valid URL
    else if (post.audioUrl && post.audioUrl !== '') {
      console.log('Creating audio element with URL:', post.audioUrl);
      const audio = new Audio(post.audioUrl);
      audioRef.current = audio;

      audio.addEventListener('timeupdate', () => {
        setCurrentTime(audio.currentTime);
      });

      audio.addEventListener('loadedmetadata', () => {
        console.log('Audio metadata loaded, duration:', audio.duration);
        setDuration(audio.duration || post.audioDuration || 10);
      });

      audio.addEventListener('ended', () => {
        console.log('Audio playback ended');
        setIsPlaying(false);
        setCurrentTime(0);
      });

      audio.addEventListener('error', (e) => {
        console.error('Audio error:', e);
        console.log('Audio src was:', audio.src);
        // Use speech synthesis instead
        if (isPlaying) {
          simulatePlayback();
        }
      });

      // Set initial duration from post data
      setDuration(post.audioDuration || 10);
      console.log('Set initial duration:', post.audioDuration || 10);

      // Preload the audio
      audio.load();

      return () => {
        console.log('Cleaning up audio element');
        audio.pause();
        audio.src = '';
        audioRef.current = null;
      };
    } else {
      console.log('No audio URL provided, using speech synthesis');
      // If no audio URL, just use the duration from post data
      setDuration(post.audioDuration || 10);

      return () => {
        // Cancel any ongoing speech when unmounting
        if ('speechSynthesis' in window) {
          window.speechSynthesis.cancel();
        }
        if (isPlaying) {
          setIsPlaying(false);
          setCurrentTime(0);
        }
      };
    }
  }, [post.audioUrl, post.audioDuration, post.transcription]);

  // Handle play/pause
  const togglePlayPause = () => {
    console.log('Toggle play/pause, current state:', isPlaying);

    // Check if the audio URL is a speech synthesis URL
    if (post.audioUrl && post.audioUrl.startsWith('speech-synthesis://')) {
      console.log('Using speech synthesis for playback');

      if (isPlaying) {
        // If already playing, cancel the speech
        window.speechSynthesis.cancel();
        setIsPlaying(false);
      } else {
        // Start speech synthesis
        setIsPlaying(true);
        simulatePlayback();
      }
      return;
    }

    // Try to use regular audio playback
    if (!audioRef.current && post.audioUrl) {
      // Create a new audio element if it doesn't exist
      console.log('Creating new audio element with URL:', post.audioUrl);
      const audio = new Audio(post.audioUrl);
      audioRef.current = audio;

      audio.addEventListener('timeupdate', () => {
        setCurrentTime(audio.currentTime);
      });

      audio.addEventListener('loadedmetadata', () => {
        console.log('Audio metadata loaded, duration:', audio.duration);
        setDuration(audio.duration || post.audioDuration || 10);
      });

      audio.addEventListener('ended', () => {
        console.log('Audio playback ended');
        setIsPlaying(false);
        setCurrentTime(0);
      });

      audio.addEventListener('error', (e) => {
        console.error('Audio error during playback:', e);
        console.log('Audio src was:', audio.src);
        // Use speech synthesis instead
        setIsPlaying(true);
        simulatePlayback();
      });

      // Preload the audio
      audio.load();
    }

    if (audioRef.current) {
      // Control real audio playback
      if (isPlaying) {
        console.log('Pausing audio');
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        console.log('Playing audio');
        audioRef.current.play().catch(error => {
          console.error('Error playing audio:', error);
          // Use speech synthesis instead
          setIsPlaying(true);
          simulatePlayback();
        });
        setIsPlaying(true);
      }
    } else {
      console.log('No audio element, using speech synthesis');
      // Fallback to speech synthesis if no audio element
      setIsPlaying(!isPlaying);
      if (!isPlaying) {
        simulatePlayback();
      }
    }
  };

  // Use speech synthesis for playback
  const simulatePlayback = () => {
    console.log('Using speech synthesis for playback, isPlaying:', isPlaying);

    if (!isPlaying) {
      try {
        // Cancel any ongoing speech
        window.speechSynthesis.cancel();

        // Extract the narration ID from the audio URL
        let narrationText = post.transcription;
        let voiceOptions: {
          rate: number;
          pitch: number;
          volume: number;
          preferredVoice?: string; // Making preferredVoice optional
        } = {
          rate: 0.9,  // Slightly slower for better clarity
          pitch: 1.0,
          volume: 1.0
        };

        // Check if we have a speech synthesis URL with a narration ID
        if (post.audioUrl && post.audioUrl.startsWith('speech-synthesis://')) {
          const narrationId = post.audioUrl.replace('speech-synthesis://', '');

          // Get the narration text from the global variable
          if (window.narrationTexts && window.narrationTexts[narrationId]) {
            narrationText = window.narrationTexts[narrationId];
            console.log('Using stored narration text:', narrationText.substring(0, 50) + '...');
          }

          // Get the voice options from the global variable
          if (window.voiceOptions && window.voiceOptions[narrationId]) {
            voiceOptions = window.voiceOptions[narrationId];
            console.log('Using stored voice options:', voiceOptions);
          }
        }

        // Create a new utterance
        const utterance = new SpeechSynthesisUtterance(narrationText);

        // Set properties from voice options
        utterance.rate = voiceOptions.rate;
        utterance.pitch = voiceOptions.pitch;
        utterance.volume = voiceOptions.volume;

        // Try to find a good voice
        const voices = window.speechSynthesis.getVoices();
        if (voices.length > 0) {
          // First try to find a voice that matches the preferred voice
          if (voiceOptions.preferredVoice) {
            const preferredVoice = voices.find(v =>
              v.name.includes(voiceOptions.preferredVoice || '') ||
              v.lang.includes(voiceOptions.preferredVoice || '')
            );
            if (preferredVoice) {
              utterance.voice = preferredVoice;
              console.log('Using preferred voice:', preferredVoice.name);
            }
          }

          // If no preferred voice found, use a default one
          if (!utterance.voice) {
            // Try to find a good English voice
            const englishVoice = voices.find(v =>
              v.lang.startsWith('en') &&
              (v.name.includes('Daniel') || v.name.includes('Google') || v.name.includes('Microsoft'))
            );

            if (englishVoice) {
              utterance.voice = englishVoice;
              console.log('Using English voice:', englishVoice.name);
            } else {
              // Just use the first voice
              utterance.voice = voices[0];
              console.log('Using first available voice:', voices[0].name);
            }
          }
        }

        // Add event listeners
        utterance.onend = () => {
          console.log('Speech synthesis ended');
          setIsPlaying(false);
          setCurrentTime(0);
        };

        utterance.onerror = (e) => {
          console.error('Speech synthesis error:', e);
          setIsPlaying(false);
        };

        // Speak the text
        window.speechSynthesis.speak(utterance);

        console.log('Started speech synthesis for text of length:', narrationText.length);
      } catch (error) {
        console.error('Error using speech synthesis:', error);
      }

      // Animate the progress bar
      const interval = window.setInterval(() => {
        setCurrentTime(prev => {
          if (prev >= duration) {
            setIsPlaying(false);
            window.clearInterval(interval);
            console.log('Playback ended');
            return 0;
          }
          return prev + 0.1;
        });
      }, 100);

      return () => {
        window.clearInterval(interval);
        // Cancel any ongoing speech when cleaning up
        window.speechSynthesis.cancel();
      };
    } else {
      // If already playing, cancel the speech
      window.speechSynthesis.cancel();
    }
  };

  // Handle like
  const handleLike = () => {
    setIsLiked(!isLiked);
    setLikeCount(prev => isLiked ? prev - 1 : prev + 1);
    if (onLike) onLike(post);
  };

  // Get chain avatar
  const getChainAvatar = () => {
    return chainAvatars[post.sourceChain] || '/images/chains/default.png';
  };

  // Get chain name with proper capitalization
  const getChainName = () => {
    return post.sourceChain.charAt(0).toUpperCase() + post.sourceChain.slice(1);
  };

  // Get event type badge color
  const getEventTypeColor = () => {
    return eventTypeColors[post.eventType] || 'bg-gray-500';
  };

  // Format event type for display
  const formatEventType = () => {
    return post.eventType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <div className="p-3 sm:p-4 border-b border-border hover:bg-secondary/20 transition-colors">
      <div className="flex items-start space-x-2 sm:space-x-3">
        {/* Avatar */}
        <Avatar className="h-8 w-8 sm:h-10 sm:w-10 border border-border">
          <AvatarImage src={getChainAvatar()} alt={getChainName()} />
          <AvatarFallback>{getChainName().substring(0, 2)}</AvatarFallback>
        </Avatar>

        <div className="flex-1 min-w-0">
          {/* Header */}
          <div className="flex flex-wrap items-center gap-1 sm:gap-2">
            <span className="text-sm sm:text-base font-semibold">{getChainName()}</span>
            <Badge variant="outline" className="text-[10px] sm:text-xs h-5 px-1 sm:px-2">
              <Volume2 className="h-2.5 w-2.5 sm:h-3 sm:w-3 mr-0.5 sm:mr-1" />
              <span className="hidden xs:inline">Voice Moment</span>
            </Badge>
            <Badge className={`text-[10px] sm:text-xs h-5 px-1 sm:px-2 ${getEventTypeColor()} text-white`}>
              {formatEventType()}
            </Badge>
            <span className="text-[10px] sm:text-xs text-muted-foreground ml-auto">
              {formatDistanceToNow(post.timestamp, { addSuffix: true })}
            </span>
          </div>

          {/* Title */}
          <h3 className="font-semibold text-sm sm:text-lg mt-1">{post.title}</h3>

          {/* Audio Player */}
          <div className="mt-2 sm:mt-3 bg-secondary/30 rounded-lg p-2 sm:p-3">
            <div className="flex items-center space-x-2 sm:space-x-3">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 sm:h-10 sm:w-10 rounded-full bg-voicechain-purple text-white hover:bg-voicechain-accent"
                onClick={togglePlayPause}
              >
                {isPlaying ?
                  <Pause className="h-4 w-4 sm:h-5 sm:w-5" /> :
                  <Play className="h-4 w-4 sm:h-5 sm:w-5" />
                }
              </Button>

              <div className="flex-1">
                <Waveform
                  isPlaying={isPlaying}
                  duration={duration}
                  currentTime={currentTime}
                  waveformData={Array(40).fill(0).map(() => Math.random())}
                  onSeek={(position) => {
                    if (audioRef.current) {
                      audioRef.current.currentTime = position;
                      setCurrentTime(position);
                    } else {
                      setCurrentTime(position);
                    }
                  }}
                />
              </div>

              <span className="text-xs sm:text-sm text-muted-foreground">
                {Math.floor(duration)}s
              </span>
            </div>

            {/* Transcription */}
            <p className="mt-1.5 sm:mt-2 text-xs sm:text-sm text-muted-foreground">
              {post.transcription}
            </p>
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-1 mt-1.5 sm:mt-2">
            {post.tags.map((tag, index) => (
              <Badge key={index} variant="secondary" className="text-[10px] sm:text-xs h-5 px-1.5">
                #{tag}
              </Badge>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between mt-2 sm:mt-3">
            <Button
              variant="ghost"
              size="sm"
              className="h-7 sm:h-8 px-1.5 sm:px-2 text-xs sm:text-sm text-muted-foreground hover:text-foreground"
              onClick={() => onReply && onReply(post)}
            >
              <MessageSquare className="h-3.5 w-3.5 sm:h-4 sm:w-4 mr-0.5 sm:mr-1" />
              <span>{replyCount}</span>
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="h-7 sm:h-8 px-1.5 sm:px-2 text-xs sm:text-sm text-muted-foreground hover:text-foreground"
              onClick={() => onRepost && onRepost(post)}
            >
              <Repeat className="h-3.5 w-3.5 sm:h-4 sm:w-4 mr-0.5 sm:mr-1" />
              <span>{repostCount}</span>
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className={`h-7 sm:h-8 px-1.5 sm:px-2 text-xs sm:text-sm ${isLiked ? "text-red-500" : "text-muted-foreground hover:text-foreground"}`}
              onClick={handleLike}
            >
              <Heart className="h-3.5 w-3.5 sm:h-4 sm:w-4 mr-0.5 sm:mr-1" fill={isLiked ? "currentColor" : "none"} />
              <span>{likeCount}</span>
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="h-7 sm:h-8 px-1.5 sm:px-2 text-xs sm:text-sm text-muted-foreground hover:text-foreground"
              onClick={() => onShare && onShare(post)}
            >
              <Share2 className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
