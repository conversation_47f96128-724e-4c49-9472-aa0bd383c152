import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Play, Clock, Users, Calendar } from 'lucide-react';
import { spaceService } from '@/services/spaceService';

interface RecordedSpace {
  id: string;
  space_id: string;
  title: string;
  description?: string;
  host_profile_id: string;
  recording_url?: string;
  ended_at: string;
  duration_minutes?: number;
  participant_count?: number;
  is_public: boolean;
  host_profile?: {
    display_name?: string;
    username?: string;
  };
}

export const RecordedSpacesList: React.FC = () => {
  const [recordedSpaces, setRecordedSpaces] = useState<RecordedSpace[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadRecordedSpaces();
  }, []);

  const loadRecordedSpaces = async () => {
    try {
      setIsLoading(true);
      const spaces = await spaceService.getRecordedSpaces(20);
      setRecordedSpaces(spaces);
    } catch (error) {
      console.error('Error loading recorded spaces:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDuration = (minutes?: number) => {
    if (!minutes) return 'Unknown duration';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const playRecording = (space: RecordedSpace) => {
    if (space.recording_url) {
      // Open recording in new tab or play in modal
      window.open(space.recording_url, '_blank');
    } else {
      // TODO: Implement proper recording playback
      console.log('Playing recorded space:', space.id);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-muted rounded w-3/4"></div>
              <div className="h-3 bg-muted rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="h-3 bg-muted rounded w-full"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (recordedSpaces.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-10">
          <Play className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No Recorded Spaces</h3>
          <p className="text-muted-foreground">
            Recorded spaces will appear here after live spaces end.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Recorded Spaces</h2>
        <Badge variant="secondary">{recordedSpaces.length} recordings</Badge>
      </div>

      <div className="grid gap-4">
        {recordedSpaces.map((space) => (
          <Card key={space.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant="outline" className="text-xs">
                      <Play className="h-3 w-3 mr-1" />
                      RECORDED
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      <Calendar className="h-3 w-3 inline mr-1" />
                      {formatDate(space.ended_at)}
                    </span>
                  </div>
                  <CardTitle className="text-lg leading-tight">{space.title}</CardTitle>
                  {space.description && (
                    <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                      {space.description}
                    </p>
                  )}
                </div>
                <Button
                  onClick={() => playRecording(space)}
                  className="ml-4 flex-shrink-0"
                >
                  <Play className="h-4 w-4 mr-2" />
                  Play
                </Button>
              </div>
            </CardHeader>

            <CardContent className="pt-0">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Avatar className="w-5 h-5">
                      <AvatarFallback className="text-xs">
                        {space.host_profile?.display_name?.slice(0, 2).toUpperCase() || 
                         space.host_profile?.username?.slice(0, 2).toUpperCase() || 
                         'H'}
                      </AvatarFallback>
                    </Avatar>
                    <span>
                      {space.host_profile?.display_name || 
                       space.host_profile?.username || 
                       'Unknown Host'}
                    </span>
                  </div>
                  
                  {space.duration_minutes && (
                    <span className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {formatDuration(space.duration_minutes)}
                    </span>
                  )}
                  
                  {space.participant_count && (
                    <span className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      {space.participant_count} listened
                    </span>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default RecordedSpacesList;
