import React from 'react';
import { useChannels } from '@/contexts/ChannelContext';
import { Button } from '@/components/ui/button';
import { Hash, Lock, Plus, Settings } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';

interface ChannelListProps {
  userAddress: string;
  onCreateChannel: () => void;
  onJoinChannel: () => void;
  onChannelSettings: (channelId: string) => void;
}

const ChannelList: React.FC<ChannelListProps> = ({
  userAddress,
  onCreateChannel,
  onJoinChannel,
  onChannelSettings
}) => {
  const { userChannels, publicChannels, activeChannel, setActiveChannel, isUserModerator, joinChannel, isUserMember } = useChannels();

  // Group channels by first letter
  const groupedChannels = userChannels.reduce((acc, channel) => {
    const firstLetter = channel.name.charAt(0).toUpperCase();
    if (!acc[firstLetter]) {
      acc[firstLetter] = [];
    }
    acc[firstLetter].push(channel);
    return acc;
  }, {} as Record<string, typeof userChannels>);

  // Sort the keys
  const sortedKeys = Object.keys(groupedChannels).sort();

  // Get channels not joined by user for discovery
  const availableChannels = publicChannels.filter(channel =>
    !isUserMember(channel.id, userAddress)
  );

  return (
    <div className="space-y-6">
      {/* Your Channels Section */}
      <div>
        <div className="px-2 mb-3">
          <h2 className="text-sm font-semibold text-muted-foreground">Your Channels</h2>
        </div>

        {userChannels.length === 0 ? (
          <div className="px-2 py-6 text-center">
            <div className="flex flex-col items-center justify-center">
              <div className="w-12 h-12 bg-secondary/50 rounded-full flex items-center justify-center mb-3">
                <Hash size={24} className="text-muted-foreground" />
              </div>
              <p className="text-sm text-muted-foreground">
                No channels joined yet
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                Join or create a channel below
              </p>
            </div>
          </div>
        ) : (
        <div className="space-y-2">
          {sortedKeys.map(key => (
            <div key={key}>
              <h3 className="text-xs font-medium text-muted-foreground px-2 mb-1">{key}</h3>
              <div className="space-y-0.5">
                {groupedChannels[key].map(channel => {
                  const isActive = activeChannel?.id === channel.id;
                  const canModify = isUserModerator(channel.id, userAddress);

                  return (
                    <div
                      key={channel.id}
                      className={cn(
                        "flex items-center justify-between px-2 py-2 rounded-md cursor-pointer group",
                        isActive
                          ? "bg-voicechain-purple/20 border-l-2 border-voicechain-purple"
                          : "hover:bg-secondary border-l-2 border-transparent"
                      )}
                      onClick={() => setActiveChannel(channel.id)}
                    >
                      <div className="flex items-center gap-1.5 overflow-hidden">
                        <div className={cn(
                          "flex items-center justify-center w-5 h-5 rounded-md flex-shrink-0",
                          isActive ? "bg-voicechain-purple/20" : "bg-secondary/80"
                        )}>
                          {channel.isPrivate ? (
                            <Lock size={12} className={cn(
                              "flex-shrink-0",
                              isActive ? "text-voicechain-purple" : "text-muted-foreground"
                            )} />
                          ) : (
                            <Hash size={12} className={cn(
                              "flex-shrink-0",
                              isActive ? "text-voicechain-purple" : "text-muted-foreground"
                            )} />
                          )}
                        </div>
                        <span className={cn(
                          "truncate text-sm",
                          isActive ? "font-medium text-voicechain-purple" : "text-foreground"
                        )}>
                          {channel.name}
                        </span>
                        <Badge
                          variant={isActive ? "purple" : "secondary"}
                          className="text-[10px] py-0 px-1 ml-auto"
                        >
                          {channel.members.length}
                        </Badge>
                      </div>

                      {canModify && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className={cn(
                            "h-6 w-6 ml-1",
                            isActive ? "opacity-100" : "opacity-0 group-hover:opacity-100"
                          )}
                          onClick={(e) => {
                            e.stopPropagation();
                            onChannelSettings(channel.id);
                          }}
                        >
                          <Settings size={12} />
                        </Button>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      )}
      </div>

      {/* Public Channels Discovery Section */}
      {availableChannels.length > 0 && (
        <div>
          <div className="px-2 mb-3">
            <h2 className="text-sm font-semibold text-muted-foreground">Discover Channels</h2>
          </div>

          <div className="space-y-1">
            {availableChannels.slice(0, 5).map(channel => (
              <div
                key={channel.id}
                className="flex items-center justify-between px-2 py-2 rounded-md hover:bg-secondary/50 group"
              >
                <div className="flex items-center flex-1 min-w-0">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-voicechain-purple/20 mr-3 flex-shrink-0">
                    <Hash size={16} className="text-voicechain-purple" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <span className="text-sm font-medium text-foreground truncate block">
                      {channel.name}
                    </span>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>{channel.members.length} members</span>
                      {channel.tags.length > 0 && (
                        <span>• {channel.tags.slice(0, 2).join(', ')}</span>
                      )}
                    </div>
                  </div>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 px-2 text-xs opacity-0 group-hover:opacity-100 transition-opacity bg-voicechain-purple/10 hover:bg-voicechain-purple/20 text-voicechain-purple"
                  onClick={() => {
                    joinChannel(channel.id);
                    setActiveChannel(channel.id);
                  }}
                >
                  Join
                </Button>
              </div>
            ))}

            {availableChannels.length > 5 && (
              <div className="px-2 pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full text-xs"
                  onClick={onJoinChannel}
                >
                  View All Public Channels
                </Button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="px-2 space-y-2">
        <Button
          variant="default"
          size="sm"
          className="w-full text-xs bg-voicechain-purple hover:bg-voicechain-accent"
          onClick={onCreateChannel}
        >
          <Plus size={14} className="mr-2" />
          Create Channel
        </Button>

        <Button
          variant="outline"
          size="sm"
          className="w-full text-xs"
          onClick={onJoinChannel}
        >
          <Hash size={14} className="mr-2" />
          Join with Code
        </Button>
      </div>
    </div>
  );
};

export default ChannelList;
