import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Hash, MessageCircle, Users, Settings, Share, HelpCircle, X } from 'lucide-react';

interface ChannelGuideProps {
  isOpen: boolean;
  onClose: () => void;
}

const ChannelGuide: React.FC<ChannelGuideProps> = ({ isOpen, onClose }) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <HelpCircle size={24} className="text-voicechain-purple" />
            How Channels Work
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 text-sm">
          {/* What are Channels */}
          <div>
            <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
              <Hash size={20} className="text-voicechain-purple" />
              What are Channels?
            </h3>
            <p className="text-muted-foreground mb-3">
              Channels are dedicated spaces for voice conversations around specific topics. 
              Think of them like Discord servers or Telegram groups, but focused on voice messages.
            </p>
            <div className="bg-secondary/50 p-3 rounded-lg">
              <p className="text-sm">
                <strong>Example:</strong> Join the "Art Support System" channel to share voice messages 
                about art, get feedback, and connect with other artists.
              </p>
            </div>
          </div>

          {/* How to Use Channels */}
          <div>
            <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
              <MessageCircle size={20} className="text-voicechain-purple" />
              How to Use Channels
            </h3>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-voicechain-purple/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-xs font-bold text-voicechain-purple">1</span>
                </div>
                <div>
                  <p className="font-medium">Join a Channel</p>
                  <p className="text-muted-foreground text-xs">
                    Click "Join" on any public channel in the "Discover Channels" section
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-voicechain-purple/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-xs font-bold text-voicechain-purple">2</span>
                </div>
                <div>
                  <p className="font-medium">Select the Channel</p>
                  <p className="text-muted-foreground text-xs">
                    Click on the channel name in "Your Channels" to enter it
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-voicechain-purple/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-xs font-bold text-voicechain-purple">3</span>
                </div>
                <div>
                  <p className="font-medium">Start Talking</p>
                  <p className="text-muted-foreground text-xs">
                    Use the floating record button or channel record button to send voice messages
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-voicechain-purple/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-xs font-bold text-voicechain-purple">4</span>
                </div>
                <div>
                  <p className="font-medium">Interact</p>
                  <p className="text-muted-foreground text-xs">
                    Reply to messages, react with emojis, tip other users, and build community
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Channel Features */}
          <div>
            <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
              <Settings size={20} className="text-voicechain-purple" />
              Channel Features
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div className="bg-secondary/30 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <MessageCircle size={16} className="text-voicechain-purple" />
                  <span className="font-medium">Voice Messages</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Send and receive voice messages with automatic transcription
                </p>
              </div>
              
              <div className="bg-secondary/30 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Users size={16} className="text-voicechain-purple" />
                  <span className="font-medium">Community</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Connect with like-minded people around shared interests
                </p>
              </div>
              
              <div className="bg-secondary/30 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Share size={16} className="text-voicechain-purple" />
                  <span className="font-medium">Invites</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Share invite codes to bring friends to your channels
                </p>
              </div>
              
              <div className="bg-secondary/30 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Hash size={16} className="text-voicechain-purple" />
                  <span className="font-medium">Topics & Tags</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Organized by topics like Art, Web3, Gaming, etc.
                </p>
              </div>
            </div>
          </div>

          {/* Quick Tips */}
          <div>
            <h3 className="font-semibold text-lg mb-3">💡 Quick Tips</h3>
            <div className="space-y-2 text-xs text-muted-foreground">
              <p>• <strong>Public channels</strong> are open to everyone - great for discovery</p>
              <p>• <strong>Private channels</strong> require an invite code to join</p>
              <p>• <strong>Channel owners</strong> can manage settings and create invite codes</p>
              <p>• <strong>Voice messages</strong> are automatically transcribed for accessibility</p>
              <p>• <strong>Reactions and tips</strong> help build engagement in channels</p>
            </div>
          </div>
        </div>

        <div className="flex justify-end pt-4">
          <Button onClick={onClose} className="bg-voicechain-purple hover:bg-voicechain-accent">
            Got it!
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ChannelGuide;
