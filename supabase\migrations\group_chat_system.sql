-- Complete Group Chat System Migration
-- This creates all necessary tables for advanced group chat functionality

-- =============================================
-- VOICE CHATS TABLE (Enhanced)
-- =============================================
CREATE TABLE IF NOT EXISTS public.voice_chats (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
  name TEXT NOT NULL,
  description TEXT,
  type TEXT NOT NULL CHECK (type IN ('direct', 'group', 'wave', 'space')),
  creator_id TEXT NOT NULL,
  is_private BOOLEAN DEFAULT false,
  emotional_tone TEXT DEFAULT 'calm' CHECK (emotional_tone IN ('calm', 'excited', 'focused', 'creative', 'mysterious')),
  settings JSONB DEFAULT '{}',
  avatar_url TEXT,
  invite_code TEXT UNIQUE,
  max_participants INTEGER DEFAULT 100,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- VOICE CHAT PARTICIPANTS TABLE (Enhanced)
-- =============================================
CREATE TABLE IF NOT EXISTS public.voice_chat_participants (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
  chat_id TEXT NOT NULL REFERENCES public.voice_chats(id) ON DELETE CASCADE,
  profile_id TEXT NOT NULL,
  role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('admin', 'moderator', 'member')),
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  added_by TEXT, -- Who added this user
  last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_muted BOOLEAN DEFAULT false,
  is_banned BOOLEAN DEFAULT false,
  left_at TIMESTAMP WITH TIME ZONE,
  
  UNIQUE(chat_id, profile_id)
);

-- =============================================
-- VOICE CHAT MESSAGES TABLE (Enhanced)
-- =============================================
CREATE TABLE IF NOT EXISTS public.voice_chat_messages (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
  chat_id TEXT NOT NULL REFERENCES public.voice_chats(id) ON DELETE CASCADE,
  sender_id TEXT NOT NULL,
  content TEXT,
  message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'voice', 'image', 'system', 'emotion')),
  voice_url TEXT,
  voice_duration NUMERIC,
  voice_transcript TEXT,
  system_message_type TEXT CHECK (system_message_type IN ('user_added', 'user_removed', 'user_left', 'user_promoted', 'user_demoted', 'group_created', 'settings_changed')),
  metadata JSONB DEFAULT '{}',
  reply_to TEXT REFERENCES public.voice_chat_messages(id),
  is_deleted BOOLEAN DEFAULT false,
  deleted_for JSONB DEFAULT '[]', -- Array of user IDs who deleted this message
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- GROUP INVITES TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS public.group_invites (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
  chat_id TEXT NOT NULL REFERENCES public.voice_chats(id) ON DELETE CASCADE,
  invite_code TEXT NOT NULL UNIQUE,
  created_by TEXT NOT NULL,
  max_uses INTEGER DEFAULT NULL, -- NULL = unlimited
  current_uses INTEGER DEFAULT 0,
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- GROUP NOTIFICATIONS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS public.group_notifications (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
  chat_id TEXT NOT NULL REFERENCES public.voice_chats(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('added_to_group', 'removed_from_group', 'promoted', 'demoted', 'invite_received')),
  message TEXT NOT NULL,
  data JSONB DEFAULT '{}',
  is_read BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================
CREATE INDEX IF NOT EXISTS idx_voice_chats_creator ON public.voice_chats(creator_id);
CREATE INDEX IF NOT EXISTS idx_voice_chats_type ON public.voice_chats(type);
CREATE INDEX IF NOT EXISTS idx_voice_chats_invite_code ON public.voice_chats(invite_code);

CREATE INDEX IF NOT EXISTS idx_voice_chat_participants_chat ON public.voice_chat_participants(chat_id);
CREATE INDEX IF NOT EXISTS idx_voice_chat_participants_profile ON public.voice_chat_participants(profile_id);
CREATE INDEX IF NOT EXISTS idx_voice_chat_participants_role ON public.voice_chat_participants(role);

CREATE INDEX IF NOT EXISTS idx_voice_chat_messages_chat ON public.voice_chat_messages(chat_id);
CREATE INDEX IF NOT EXISTS idx_voice_chat_messages_sender ON public.voice_chat_messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_voice_chat_messages_created ON public.voice_chat_messages(created_at);

CREATE INDEX IF NOT EXISTS idx_group_invites_code ON public.group_invites(invite_code);
CREATE INDEX IF NOT EXISTS idx_group_invites_chat ON public.group_invites(chat_id);

CREATE INDEX IF NOT EXISTS idx_group_notifications_user ON public.group_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_group_notifications_chat ON public.group_notifications(chat_id);

-- =============================================
-- ENABLE ROW LEVEL SECURITY
-- =============================================
ALTER TABLE public.voice_chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.voice_chat_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.voice_chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.group_invites ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.group_notifications ENABLE ROW LEVEL SECURITY;

-- =============================================
-- RLS POLICIES
-- =============================================

-- Voice Chats Policies
CREATE POLICY "Users can view chats they participate in" ON public.voice_chats
  FOR SELECT USING (
    id IN (
      SELECT chat_id FROM public.voice_chat_participants 
      WHERE profile_id = auth.uid()::text AND left_at IS NULL
    )
  );

CREATE POLICY "Users can create chats" ON public.voice_chats
  FOR INSERT WITH CHECK (creator_id = auth.uid()::text);

CREATE POLICY "Admins can update their chats" ON public.voice_chats
  FOR UPDATE USING (
    id IN (
      SELECT chat_id FROM public.voice_chat_participants 
      WHERE profile_id = auth.uid()::text AND role = 'admin' AND left_at IS NULL
    )
  );

-- Voice Chat Participants Policies
CREATE POLICY "Users can view participants of their chats" ON public.voice_chat_participants
  FOR SELECT USING (
    chat_id IN (
      SELECT chat_id FROM public.voice_chat_participants 
      WHERE profile_id = auth.uid()::text AND left_at IS NULL
    )
  );

CREATE POLICY "Admins can manage participants" ON public.voice_chat_participants
  FOR ALL USING (
    chat_id IN (
      SELECT chat_id FROM public.voice_chat_participants 
      WHERE profile_id = auth.uid()::text AND role IN ('admin', 'moderator') AND left_at IS NULL
    )
  );

-- Voice Chat Messages Policies
CREATE POLICY "Users can view messages in their chats" ON public.voice_chat_messages
  FOR SELECT USING (
    chat_id IN (
      SELECT chat_id FROM public.voice_chat_participants 
      WHERE profile_id = auth.uid()::text AND left_at IS NULL
    )
    AND (
      is_deleted = false OR 
      sender_id = auth.uid()::text OR
      NOT (deleted_for ? auth.uid()::text)
    )
  );

CREATE POLICY "Users can send messages to their chats" ON public.voice_chat_messages
  FOR INSERT WITH CHECK (
    sender_id = auth.uid()::text AND
    chat_id IN (
      SELECT chat_id FROM public.voice_chat_participants 
      WHERE profile_id = auth.uid()::text AND left_at IS NULL
    )
  );

CREATE POLICY "Users can update their own messages" ON public.voice_chat_messages
  FOR UPDATE USING (sender_id = auth.uid()::text);

-- Group Invites Policies
CREATE POLICY "Users can view invites for their chats" ON public.group_invites
  FOR SELECT USING (
    chat_id IN (
      SELECT chat_id FROM public.voice_chat_participants 
      WHERE profile_id = auth.uid()::text AND left_at IS NULL
    )
  );

CREATE POLICY "Admins can manage invites" ON public.group_invites
  FOR ALL USING (
    chat_id IN (
      SELECT chat_id FROM public.voice_chat_participants 
      WHERE profile_id = auth.uid()::text AND role IN ('admin', 'moderator') AND left_at IS NULL
    )
  );

-- Group Notifications Policies
CREATE POLICY "Users can view their own notifications" ON public.group_notifications
  FOR SELECT USING (user_id = auth.uid()::text);

CREATE POLICY "System can create notifications" ON public.group_notifications
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update their own notifications" ON public.group_notifications
  FOR UPDATE USING (user_id = auth.uid()::text);
