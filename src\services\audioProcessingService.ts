import { VoiceFilter } from '@/components/VoiceFilterSelector';

// Audio Context singleton
let audioContext: AudioContext | null = null;

// Get or create AudioContext
export const getAudioContext = (): AudioContext => {
  if (!audioContext) {
    audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
  }
  return audioContext;
};

// Apply filters to audio buffer
export const applyFilters = async (
  audioBlob: Blob,
  filter: VoiceFilter
): Promise<Blob> => {
  console.log('Starting filter application process', filter);
  const context = getAudioContext();

  // If no filter parameters or normal filter, return original blob
  if (!filter.audioParams || filter.id === 'normal' || Object.keys(filter.audioParams).length === 0) {
    console.log('No filter parameters or normal filter selected, returning original audio');
    return audioBlob;
  }

  try {
    console.log('Converting blob to array buffer...');
    // Convert blob to array buffer
    const arrayBuffer = await audioBlob.arrayBuffer();

    console.log('Decoding audio data...');
    // Decode audio data
    const audioBuffer = await context.decodeAudioData(arrayBuffer);

    console.log('Creating offline context for processing...');
    // Create offline context for processing
    const offlineContext = new OfflineAudioContext(
      audioBuffer.numberOfChannels,
      audioBuffer.length,
      audioBuffer.sampleRate
    );

    // Create source node
    const source = offlineContext.createBufferSource();
    source.buffer = audioBuffer;

    // Create processing chain
    let currentNode: AudioNode = source;

    // Log the filter parameters being applied
    console.log('Applying filter with parameters:', filter.audioParams);

    // Apply pitch shift if needed
    if (filter.audioParams.pitch && filter.audioParams.pitch !== 0) {
      console.log(`Applying pitch shift: ${filter.audioParams.pitch}`);
      currentNode = applyPitchShift(offlineContext, currentNode, filter.audioParams.pitch);
    }

    // Apply echo if needed
    if (filter.audioParams.echo && filter.audioParams.echo > 0) {
      console.log(`Applying echo: ${filter.audioParams.echo}`);
      currentNode = applyEcho(offlineContext, currentNode, filter.audioParams.echo);
    }

    // Apply reverb if needed
    if (filter.audioParams.reverb && filter.audioParams.reverb > 0) {
      console.log(`Applying reverb: ${filter.audioParams.reverb}`);
      currentNode = applyReverb(offlineContext, currentNode, filter.audioParams.reverb);
    }

    // Apply distortion if needed
    if (filter.audioParams.distortion && filter.audioParams.distortion > 0) {
      console.log(`Applying distortion: ${filter.audioParams.distortion}`);
      currentNode = applyDistortion(offlineContext, currentNode, filter.audioParams.distortion);
    }

    // Apply bass boost if needed
    if (filter.audioParams.bassBoost && filter.audioParams.bassBoost > 0) {
      console.log(`Applying bass boost: ${filter.audioParams.bassBoost}`);
      currentNode = applyBassBoost(offlineContext, currentNode, filter.audioParams.bassBoost);
    }

    // Apply noise reduction if needed
    if (filter.audioParams.noiseReduction && filter.audioParams.noiseReduction > 0) {
      console.log(`Applying noise reduction: ${filter.audioParams.noiseReduction}`);
      currentNode = applyNoiseReduction(offlineContext, currentNode, filter.audioParams.noiseReduction);
    }

    // Connect to destination
    console.log('Connecting to destination...');
    currentNode.connect(offlineContext.destination);

    // Start source
    console.log('Starting source...');
    source.start(0);

    // Render audio
    console.log('Rendering audio...');
    const renderedBuffer = await offlineContext.startRendering();
    console.log('Audio rendering complete');

    // Convert buffer to wave file
    console.log('Converting buffer to WAV...');
    const wavBlob = await audioBufferToWaveBlob(renderedBuffer);
    console.log('Conversion to WAV complete');

    // Verify the output blob
    console.log(`Output blob size: ${wavBlob.size} bytes, type: ${wavBlob.type}`);

    return wavBlob;
  } catch (error) {
    console.error('Error applying audio filters:', error);
    console.error('Stack trace:', error.stack);
    return audioBlob; // Return original if processing fails
  }
};

// Helper function to apply pitch shift
const applyPitchShift = (
  context: OfflineAudioContext,
  sourceNode: AudioNode,
  pitchValue: number
): AudioNode => {
  // Create a more effective pitch shifter using multiple filters

  // Create a gain node for output
  const outputGain = context.createGain();

  // Create a biquad filter for low frequencies
  const lowFilter = context.createBiquadFilter();
  lowFilter.type = 'lowshelf';
  lowFilter.frequency.value = 220;
  lowFilter.gain.value = pitchValue * 3; // Amplify effect for low frequencies

  // Create a biquad filter for mid frequencies
  const midFilter = context.createBiquadFilter();
  midFilter.type = 'peaking';
  midFilter.Q.value = 1;
  midFilter.frequency.value = 880 * Math.pow(2, pitchValue / 12);
  midFilter.gain.value = pitchValue * 2;

  // Create a biquad filter for high frequencies
  const highFilter = context.createBiquadFilter();
  highFilter.type = 'highshelf';
  highFilter.frequency.value = 3000;
  highFilter.gain.value = pitchValue * 2;

  // Connect the nodes in series
  sourceNode.connect(lowFilter);
  lowFilter.connect(midFilter);
  midFilter.connect(highFilter);
  highFilter.connect(outputGain);

  // Add a compressor to prevent clipping
  const compressor = context.createDynamicsCompressor();
  compressor.threshold.value = -24;
  compressor.knee.value = 30;
  compressor.ratio.value = 12;
  compressor.attack.value = 0.003;
  compressor.release.value = 0.25;

  highFilter.connect(compressor);
  compressor.connect(outputGain);

  return outputGain;
};

// Helper function to apply echo
const applyEcho = (
  context: OfflineAudioContext,
  sourceNode: AudioNode,
  echoValue: number
): AudioNode => {
  // Create multiple delay nodes for a richer echo effect
  const delays: DelayNode[] = [];
  const feedbacks: GainNode[] = [];
  const delayTimes = [0.15, 0.3, 0.45]; // Multiple delay times for a more natural echo

  // Create output gain node
  const output = context.createGain();
  output.gain.value = 0.8; // Reduce overall volume to prevent clipping

  // Connect source directly to output for the dry signal
  sourceNode.connect(output);

  // Create multiple delay paths
  for (let i = 0; i < delayTimes.length; i++) {
    // Create delay node
    const delay = context.createDelay();
    delay.delayTime.value = delayTimes[i] * echoValue;
    delays.push(delay);

    // Create gain node for echo volume (decreasing for each subsequent echo)
    const feedback = context.createGain();
    feedback.gain.value = (0.5 - (i * 0.1)) * echoValue; // Decreasing feedback for each delay
    feedbacks.push(feedback);

    // Connect nodes: source -> delay -> feedback -> delay -> output
    sourceNode.connect(delay);
    delay.connect(feedback);
    feedback.connect(delay);
    delay.connect(output);
  }

  // Add a compressor to prevent clipping
  const compressor = context.createDynamicsCompressor();
  compressor.threshold.value = -18;
  compressor.knee.value = 30;
  compressor.ratio.value = 6;
  compressor.attack.value = 0.003;
  compressor.release.value = 0.25;

  output.connect(compressor);

  return compressor;
};

// Helper function to apply reverb
const applyReverb = (
  context: OfflineAudioContext,
  sourceNode: AudioNode,
  reverbValue: number
): AudioNode => {
  // Create convolver node for reverb
  const convolver = context.createConvolver();

  // Create a more realistic impulse response
  const impulseLength = context.sampleRate * (reverbValue + 0.5); // reverb length in samples (min 0.5s)
  const impulse = context.createBuffer(2, impulseLength, context.sampleRate);

  // Fill impulse buffer with a more realistic room reverb simulation
  for (let channel = 0; channel < impulse.numberOfChannels; channel++) {
    const impulseData = impulse.getChannelData(channel);

    // Early reflections (first 50ms)
    const earlyReflectionsEnd = Math.min(context.sampleRate * 0.05, impulseLength);
    for (let i = 0; i < earlyReflectionsEnd; i++) {
      // Create some distinct early reflections
      if (i % Math.floor(context.sampleRate * 0.008) === 0) { // Every ~8ms
        impulseData[i] = (Math.random() * 0.8 - 0.4) * Math.pow(1 - i / earlyReflectionsEnd, 0.5);
      } else {
        impulseData[i] = (Math.random() * 0.4 - 0.2) * Math.pow(1 - i / earlyReflectionsEnd, 1);
      }
    }

    // Late reverb (exponential decay)
    for (let i = earlyReflectionsEnd; i < impulseLength; i++) {
      // Exponential decay with some randomness
      const decay = Math.exp(-3 * (i - earlyReflectionsEnd) / (impulseLength - earlyReflectionsEnd));
      impulseData[i] = (Math.random() * 2 - 1) * decay * reverbValue;
    }
  }

  convolver.buffer = impulse;

  // Create gain nodes for dry/wet mix
  const dryGain = context.createGain();
  const wetGain = context.createGain();
  const output = context.createGain();

  // Adjust the dry/wet mix based on reverb value
  dryGain.gain.value = 1 - reverbValue * 0.6; // Dry signal (original)
  wetGain.gain.value = reverbValue * 0.7; // Wet signal (reverb)

  // Add some EQ to the reverb for a more natural sound
  const reverbLowCut = context.createBiquadFilter();
  reverbLowCut.type = 'highpass';
  reverbLowCut.frequency.value = 300; // Cut low frequencies in the reverb

  const reverbHighCut = context.createBiquadFilter();
  reverbHighCut.type = 'lowpass';
  reverbHighCut.frequency.value = 5000; // Cut high frequencies in the reverb

  // Connect nodes with EQ
  sourceNode.connect(dryGain);
  sourceNode.connect(convolver);
  convolver.connect(reverbLowCut);
  reverbLowCut.connect(reverbHighCut);
  reverbHighCut.connect(wetGain);
  dryGain.connect(output);
  wetGain.connect(output);

  // Add a compressor to prevent clipping
  const compressor = context.createDynamicsCompressor();
  compressor.threshold.value = -18;
  compressor.knee.value = 30;
  compressor.ratio.value = 6;
  compressor.attack.value = 0.003;
  compressor.release.value = 0.25;

  output.connect(compressor);

  return compressor;
};

// Helper function to apply distortion
const applyDistortion = (
  context: OfflineAudioContext,
  sourceNode: AudioNode,
  distortionValue: number
): AudioNode => {
  // Create a more complex distortion effect with pre and post EQ

  // Pre-distortion EQ to shape the sound before distortion
  const preHighPass = context.createBiquadFilter();
  preHighPass.type = 'highpass';
  preHighPass.frequency.value = 60;

  const preLowPass = context.createBiquadFilter();
  preLowPass.type = 'lowpass';
  preLowPass.frequency.value = 18000;

  // Create the distortion node
  const distortion = context.createWaveShaper();

  // Create a more musical distortion curve
  const samples = 44100;
  const curve = new Float32Array(samples);

  for (let i = 0; i < samples; ++i) {
    const x = i * 2 / samples - 1;

    // Combine multiple distortion algorithms for a richer sound
    if (distortionValue < 2) {
      // Mild distortion (tube-like)
      curve[i] = Math.tanh(x * distortionValue * 3);
    } else if (distortionValue < 4) {
      // Medium distortion (overdrive)
      curve[i] = Math.sign(x) * (1 - Math.exp(-Math.abs(x) * distortionValue));
    } else {
      // Heavy distortion (fuzz)
      curve[i] = x < 0 ?
        -Math.pow(Math.abs(x), 0.8) :
        Math.pow(x, 0.8);
    }
  }

  distortion.curve = curve;
  distortion.oversample = '4x';

  // Post-distortion EQ to shape the distorted sound
  const postLowShelf = context.createBiquadFilter();
  postLowShelf.type = 'lowshelf';
  postLowShelf.frequency.value = 400;
  postLowShelf.gain.value = -distortionValue * 2; // Cut some bass to prevent muddiness

  const postHighShelf = context.createBiquadFilter();
  postHighShelf.type = 'highshelf';
  postHighShelf.frequency.value = 3000;
  postHighShelf.gain.value = -distortionValue * 1.5; // Cut some highs to prevent harshness

  // Create a gain node to control the output level
  const outputGain = context.createGain();
  outputGain.gain.value = 0.7; // Reduce output to prevent clipping

  // Connect the nodes
  sourceNode.connect(preHighPass);
  preHighPass.connect(preLowPass);
  preLowPass.connect(distortion);
  distortion.connect(postLowShelf);
  postLowShelf.connect(postHighShelf);
  postHighShelf.connect(outputGain);

  // Add a compressor to prevent clipping
  const compressor = context.createDynamicsCompressor();
  compressor.threshold.value = -18;
  compressor.knee.value = 30;
  compressor.ratio.value = 12;
  compressor.attack.value = 0.003;
  compressor.release.value = 0.25;

  outputGain.connect(compressor);

  return compressor;
};

// Helper function to apply bass boost
const applyBassBoost = (
  context: OfflineAudioContext,
  sourceNode: AudioNode,
  boostValue: number
): AudioNode => {
  // Create a multi-band bass boost for a more natural sound

  // Create a gain node for output
  const outputGain = context.createGain();

  // Create a direct path for the original signal
  const directGain = context.createGain();
  directGain.gain.value = 1.0; // Keep original signal at full volume

  // Create a sub-bass boost (30-80Hz)
  const subBassFilter = context.createBiquadFilter();
  subBassFilter.type = 'lowshelf';
  subBassFilter.frequency.value = 60;
  subBassFilter.gain.value = boostValue * 4; // Strong boost for sub-bass

  // Create a low-bass boost (80-200Hz)
  const lowBassFilter = context.createBiquadFilter();
  lowBassFilter.type = 'peaking';
  lowBassFilter.frequency.value = 120;
  lowBassFilter.Q.value = 1.0;
  lowBassFilter.gain.value = boostValue * 3; // Medium boost for low-bass

  // Create a mid-bass boost (200-300Hz)
  const midBassFilter = context.createBiquadFilter();
  midBassFilter.type = 'peaking';
  midBassFilter.frequency.value = 250;
  midBassFilter.Q.value = 1.0;
  midBassFilter.gain.value = boostValue * 2; // Light boost for mid-bass

  // Create a high-cut filter to prevent muddiness
  const highCutFilter = context.createBiquadFilter();
  highCutFilter.type = 'highshelf';
  highCutFilter.frequency.value = 600;
  highCutFilter.gain.value = -boostValue; // Cut some highs to prevent muddiness

  // Connect the nodes in series for the bass-boosted path
  sourceNode.connect(directGain);
  sourceNode.connect(subBassFilter);
  subBassFilter.connect(lowBassFilter);
  lowBassFilter.connect(midBassFilter);
  midBassFilter.connect(highCutFilter);

  // Connect both paths to output
  directGain.connect(outputGain);
  highCutFilter.connect(outputGain);

  // Add a compressor to prevent clipping
  const compressor = context.createDynamicsCompressor();
  compressor.threshold.value = -12 - (boostValue * 2); // Lower threshold for higher boost
  compressor.knee.value = 30;
  compressor.ratio.value = 4;
  compressor.attack.value = 0.003;
  compressor.release.value = 0.25;

  outputGain.connect(compressor);

  return compressor;
};

// Helper function to apply noise reduction
const applyNoiseReduction = (
  context: OfflineAudioContext,
  sourceNode: AudioNode,
  reductionValue: number
): AudioNode => {
  // Create a more sophisticated noise reduction effect

  // Create a gain node for output
  const outputGain = context.createGain();

  // Create a multi-band approach for noise reduction

  // 1. Low frequency noise reduction (rumble, hum)
  const lowNoiseFilter = context.createBiquadFilter();
  lowNoiseFilter.type = 'highpass';
  lowNoiseFilter.frequency.value = 80 + (reductionValue * 40); // Cut frequencies below this
  lowNoiseFilter.Q.value = 0.7; // Gentle slope

  // 2. Mid frequency noise reduction (background noise)
  const midNoiseFilter1 = context.createBiquadFilter();
  midNoiseFilter1.type = 'notch'; // Cut a specific frequency band
  midNoiseFilter1.frequency.value = 300;
  midNoiseFilter1.Q.value = 2.0;
  midNoiseFilter1.gain.value = -reductionValue * 3;

  const midNoiseFilter2 = context.createBiquadFilter();
  midNoiseFilter2.type = 'notch'; // Cut another specific frequency band
  midNoiseFilter2.frequency.value = 1000;
  midNoiseFilter2.Q.value = 2.0;
  midNoiseFilter2.gain.value = -reductionValue * 2;

  // 3. High frequency noise reduction (hiss)
  const highNoiseFilter = context.createBiquadFilter();
  highNoiseFilter.type = 'lowpass';
  highNoiseFilter.frequency.value = 8000 - (reductionValue * 3000); // Cut frequencies above this
  highNoiseFilter.Q.value = 0.7; // Gentle slope

  // 4. Dynamic processing
  const compander = context.createDynamicsCompressor();
  compander.threshold.value = -50 + (reductionValue * 10); // Adjust threshold based on reduction value
  compander.knee.value = 40; // Very soft knee for natural sound
  compander.ratio.value = 3 + (reductionValue * 3); // Moderate compression ratio
  compander.attack.value = 0.005; // Fast attack to catch transients
  compander.release.value = 0.050; // Fast release

  // 5. Enhance voice frequencies (make speech clearer)
  const voiceEnhancer = context.createBiquadFilter();
  voiceEnhancer.type = 'peaking';
  voiceEnhancer.frequency.value = 2500; // Enhance speech intelligibility
  voiceEnhancer.Q.value = 1.0;
  voiceEnhancer.gain.value = reductionValue * 3; // Boost voice frequencies

  // Connect all nodes in series
  sourceNode.connect(lowNoiseFilter);
  lowNoiseFilter.connect(midNoiseFilter1);
  midNoiseFilter1.connect(midNoiseFilter2);
  midNoiseFilter2.connect(highNoiseFilter);
  highNoiseFilter.connect(compander);
  compander.connect(voiceEnhancer);
  voiceEnhancer.connect(outputGain);

  return outputGain;
};

// Helper function to convert AudioBuffer to WAV Blob
const audioBufferToWaveBlob = (buffer: AudioBuffer): Promise<Blob> => {
  return new Promise((resolve) => {
    const numberOfChannels = buffer.numberOfChannels;
    const length = buffer.length * numberOfChannels * 2;
    const outputBuffer = new ArrayBuffer(44 + length);
    const view = new DataView(outputBuffer);
    const sampleRate = buffer.sampleRate;
    const channels = buffer.numberOfChannels;

    /* RIFF identifier */
    writeString(view, 0, 'RIFF');
    /* RIFF chunk length */
    view.setUint32(4, 36 + length, true);
    /* RIFF type */
    writeString(view, 8, 'WAVE');
    /* format chunk identifier */
    writeString(view, 12, 'fmt ');
    /* format chunk length */
    view.setUint32(16, 16, true);
    /* sample format (raw) */
    view.setUint16(20, 1, true);
    /* channel count */
    view.setUint16(22, channels, true);
    /* sample rate */
    view.setUint32(24, sampleRate, true);
    /* byte rate (sample rate * block align) */
    view.setUint32(28, sampleRate * 4, true);
    /* block align (channel count * bytes per sample) */
    view.setUint16(32, channels * 2, true);
    /* bits per sample */
    view.setUint16(34, 16, true);
    /* data chunk identifier */
    writeString(view, 36, 'data');
    /* data chunk length */
    view.setUint32(40, length, true);

    // Write the PCM samples
    const offset = 44;
    let pos = 0;
    for (let i = 0; i < buffer.length; i++) {
      for (let channel = 0; channel < channels; channel++) {
        const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]));
        view.setInt16(offset + pos, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
        pos += 2;
      }
    }

    resolve(new Blob([outputBuffer], { type: 'audio/wav' }));
  });
};

// Helper function to write a string to a DataView
const writeString = (view: DataView, offset: number, string: string): void => {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i));
  }
};

export default {
  applyFilters,
  getAudioContext
};
