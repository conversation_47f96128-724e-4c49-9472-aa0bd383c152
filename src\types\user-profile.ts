
import { VerificationType } from '@/components/VerificationBadge';
import { OWNER_ADDRESS, VERIFIED_ADDRESSES } from '@/utils/verification-data';

export interface UserProfile {
  address: string;
  walletAddress?: string; // Added wallet_address from database
  username: string;
  displayName: string;
  bio: string;
  profileImageUrl: string;
  coverImageUrl: string;
  socialLinks: {
    twitter?: string;
    github?: string;
    website?: string;
  };
  stats: {
    posts: number;
    likes: number;
    tips: number;
    followers?: number;
    following?: number;
  };
  joinedDate: Date;
  verification?: {
    isVerified: boolean;
    type?: VerificationType;
    since?: Date;
  };
  ceramicDocId?: string; // Ceramic document ID (for backward compatibility)
  orbisDid?: string; // Orbis DID
}

export interface UserProfileUpdate extends Partial<Omit<UserProfile, 'address' | 'joinedDate' | 'stats'>> {
  stats?: Partial<UserProfile['stats']>;
}

export const createDefaultProfile = (address: string): UserProfile => {
  // Check if the address is the owner address
  // IMPORTANT: Only match exact owner address to prevent accidental verification
  const normalizedAddress = address.toLowerCase();
  const isOwner = normalizedAddress === OWNER_ADDRESS.toLowerCase();
  const verificationType = isOwner ? VERIFIED_ADDRESSES[normalizedAddress] : null;
  const isUserVerified = verificationType !== null;

  // Generate a random suffix to ensure username uniqueness
  const randomSuffix = Math.floor(Math.random() * 10000).toString().padStart(4, '0');

  return {
    address,
    walletAddress: normalizedAddress, // Include the normalized wallet address
    username: `user_${address.substring(2, 8).toLowerCase()}_${randomSuffix}`,
    displayName: `User ${address.substring(0, 6)}`,
    bio: '',
    profileImageUrl: '',
    coverImageUrl: '',
    socialLinks: {},
    stats: {
      posts: 0,
      likes: 0,
      tips: 0,
      followers: 0,
      following: 0
    },
    joinedDate: new Date(),
    verification: {
      isVerified: isUserVerified,
      type: verificationType || undefined,
      since: isUserVerified ? new Date() : undefined
    }
  };
};
