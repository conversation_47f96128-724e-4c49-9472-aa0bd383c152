import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { supabase } from '@/lib/supabase';
import { toast } from '@/components/ui/sonner';
import { 
  Zap, 
  Flame, 
  Trophy, 
  Gift, 
  Calendar,
  TrendingUp,
  Star,
  Coins,
  Clock,
  CheckCircle
} from 'lucide-react';

interface EngagementReward {
  id: string;
  reward_type: string;
  amount: number;
  amount_usd: number;
  token: {
    symbol: string;
    name: string;
  };
  is_claimed: boolean;
  created_at: string;
  expires_at?: string;
  streak_count?: number;
  quality_score?: number;
}

interface EngagementStreak {
  current_streak: number;
  longest_streak: number;
  last_activity_date: string;
  total_active_days: number;
  streak_multiplier: number;
}

interface EngagementRewardsProps {
  userAddress: string;
  channelId?: string;
}

const EngagementRewards: React.FC<EngagementRewardsProps> = ({
  userAddress,
  channelId
}) => {
  const [rewards, setRewards] = useState<EngagementReward[]>([]);
  const [streak, setStreak] = useState<EngagementStreak | null>(null);
  const [loading, setLoading] = useState(true);
  const [claimingReward, setClaimingReward] = useState<string | null>(null);
  const [totalEarnings, setTotalEarnings] = useState(0);
  const [todayProgress, setTodayProgress] = useState(0);

  const rewardConfig = {
    voice_post: {
      icon: Zap,
      color: 'text-blue-500',
      bgColor: 'bg-blue-500/10',
      label: 'Voice Post',
      description: 'Reward for creating voice content'
    },
    voice_reply: {
      icon: TrendingUp,
      color: 'text-green-500',
      bgColor: 'bg-green-500/10',
      label: 'Voice Reply',
      description: 'Reward for engaging with content'
    },
    daily_activity: {
      icon: Calendar,
      color: 'text-purple-500',
      bgColor: 'bg-purple-500/10',
      label: 'Daily Activity',
      description: 'Daily engagement bonus'
    },
    streak_bonus: {
      icon: Flame,
      color: 'text-orange-500',
      bgColor: 'bg-orange-500/10',
      label: 'Streak Bonus',
      description: 'Consecutive days bonus'
    },
    quality_bonus: {
      icon: Star,
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-500/10',
      label: 'Quality Bonus',
      description: 'High-quality content reward'
    },
    first_post: {
      icon: Trophy,
      color: 'text-pink-500',
      bgColor: 'bg-pink-500/10',
      label: 'First Post',
      description: 'Welcome bonus for new creators'
    }
  };

  useEffect(() => {
    loadRewards();
    loadStreak();
    calculateTodayProgress();
  }, [userAddress, channelId]);

  const loadRewards = async () => {
    try {
      setLoading(true);

      // Get user profile
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('wallet_address', userAddress)
        .single();

      if (profileError || !profileData) {
        console.error('Profile not found:', profileError);
        return;
      }

      // Get engagement rewards
      let query = supabase
        .from('engagement_rewards')
        .select(`
          *,
          token:tokens(symbol, name)
        `)
        .eq('profile_id', profileData.id)
        .order('created_at', { ascending: false })
        .limit(20);

      if (channelId) {
        query = query.eq('channel_id', channelId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error loading rewards:', error);
        return;
      }

      setRewards(data || []);

      // Calculate total earnings
      const total = (data || []).reduce((sum, reward) => sum + (reward.amount_usd || 0), 0);
      setTotalEarnings(total);

    } catch (error) {
      console.error('Error loading rewards:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadStreak = async () => {
    try {
      // Get user profile
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('wallet_address', userAddress)
        .single();

      if (profileError || !profileData) return;

      // Get engagement streak
      let query = supabase
        .from('engagement_streaks')
        .select('*')
        .eq('profile_id', profileData.id);

      if (channelId) {
        query = query.eq('channel_id', channelId);
      } else {
        query = query.is('channel_id', null);
      }

      const { data, error } = await query.single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error loading streak:', error);
        return;
      }

      setStreak(data);
    } catch (error) {
      console.error('Error loading streak:', error);
    }
  };

  const calculateTodayProgress = () => {
    const today = new Date().toDateString();
    const todayRewards = rewards.filter(r => 
      new Date(r.created_at).toDateString() === today
    );
    
    // Calculate progress based on daily goals (e.g., 3 activities = 100%)
    const dailyGoal = 3;
    const progress = Math.min((todayRewards.length / dailyGoal) * 100, 100);
    setTodayProgress(progress);
  };

  const claimReward = async (rewardId: string) => {
    try {
      setClaimingReward(rewardId);

      const { error } = await supabase
        .from('engagement_rewards')
        .update({ 
          is_claimed: true, 
          claimed_at: new Date().toISOString() 
        })
        .eq('id', rewardId);

      if (error) {
        console.error('Error claiming reward:', error);
        toast('Failed to claim reward');
        return;
      }

      toast('Reward claimed successfully! 🎉');
      loadRewards();

    } catch (error) {
      console.error('Error claiming reward:', error);
      toast('Failed to claim reward');
    } finally {
      setClaimingReward(null);
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffHours < 24) return `${diffHours}h ago`;
    return `${diffDays}d ago`;
  };

  const unclaimedRewards = rewards.filter(r => !r.is_claimed);
  const claimedRewards = rewards.filter(r => r.is_claimed);

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-4">
              <div className="h-16 bg-secondary rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-green-500/10 rounded-lg flex items-center justify-center">
                <Coins size={20} className="text-green-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Earned</p>
                <p className="text-lg font-bold">${totalEarnings.toFixed(2)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-orange-500/10 rounded-lg flex items-center justify-center">
                <Flame size={20} className="text-orange-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Current Streak</p>
                <p className="text-lg font-bold">{streak?.current_streak || 0} days</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-purple-500/10 rounded-lg flex items-center justify-center">
                <Calendar size={20} className="text-purple-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Today's Progress</p>
                <div className="flex items-center gap-2">
                  <Progress value={todayProgress} className="flex-1 h-2" />
                  <span className="text-sm font-medium">{Math.round(todayProgress)}%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Unclaimed Rewards */}
      {unclaimedRewards.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Gift className="text-green-500" size={20} />
              Unclaimed Rewards ({unclaimedRewards.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {unclaimedRewards.map((reward) => {
              const config = rewardConfig[reward.reward_type as keyof typeof rewardConfig];
              const Icon = config?.icon || Gift;
              const isExpired = reward.expires_at && new Date(reward.expires_at) < new Date();

              return (
                <div
                  key={reward.id}
                  className={`flex items-center justify-between p-3 rounded-lg border ${
                    isExpired ? 'opacity-50 bg-red-50 border-red-200' : 'bg-secondary/20'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${config?.bgColor}`}>
                      <Icon size={20} className={config?.color} />
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{config?.label}</span>
                        {reward.streak_count && (
                          <Badge variant="secondary" className="text-xs">
                            {reward.streak_count} day streak
                          </Badge>
                        )}
                        {reward.quality_score && reward.quality_score > 0.8 && (
                          <Badge variant="secondary" className="text-xs">
                            High Quality
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground">{config?.description}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatTimeAgo(reward.created_at)}
                        {reward.expires_at && (
                          <span className="ml-2">
                            • Expires {formatTimeAgo(reward.expires_at)}
                          </span>
                        )}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="text-right">
                      <p className="font-medium">
                        {reward.amount} {reward.token.symbol}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        ${reward.amount_usd?.toFixed(2)}
                      </p>
                    </div>
                    <Button
                      size="sm"
                      onClick={() => claimReward(reward.id)}
                      disabled={claimingReward === reward.id || isExpired}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      {claimingReward === reward.id ? (
                        <Clock size={14} className="animate-spin" />
                      ) : isExpired ? (
                        'Expired'
                      ) : (
                        'Claim'
                      )}
                    </Button>
                  </div>
                </div>
              );
            })}
          </CardContent>
        </Card>
      )}

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="text-blue-500" size={20} />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {claimedRewards.length === 0 ? (
            <div className="text-center py-8">
              <Zap size={48} className="mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">Start Earning Rewards!</h3>
              <p className="text-muted-foreground mb-4">
                Create voice posts, reply to others, and stay active to earn tokens
              </p>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="bg-secondary/20 p-3 rounded-lg">
                  <Zap size={16} className="text-blue-500 mb-1" />
                  <p className="font-medium">Voice Posts</p>
                  <p className="text-muted-foreground">Earn 1-5 tokens per post</p>
                </div>
                <div className="bg-secondary/20 p-3 rounded-lg">
                  <Flame size={16} className="text-orange-500 mb-1" />
                  <p className="font-medium">Daily Streaks</p>
                  <p className="text-muted-foreground">Bonus multipliers</p>
                </div>
              </div>
            </div>
          ) : (
            claimedRewards.map((reward) => {
              const config = rewardConfig[reward.reward_type as keyof typeof rewardConfig];
              const Icon = config?.icon || CheckCircle;

              return (
                <div key={reward.id} className="flex items-center gap-3 p-3 rounded-lg bg-secondary/10">
                  <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${config?.bgColor}`}>
                    <Icon size={16} className={config?.color} />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">{config?.label}</span>
                      <Badge variant="outline" className="text-xs">Claimed</Badge>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {formatTimeAgo(reward.created_at)}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">
                      +{reward.amount} {reward.token.symbol}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      ${reward.amount_usd?.toFixed(2)}
                    </p>
                  </div>
                </div>
              );
            })
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default EngagementRewards;
