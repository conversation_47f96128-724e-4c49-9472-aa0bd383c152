
import React, { createContext, useState, useEffect, useContext, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/sonner';
import { NotificationType } from '@/types/notification';
import { v4 as uuidv4 } from 'uuid';

export interface Notification {
  id: string;
  type: NotificationType;
  from_address: string;  // Using snake_case to match Supabase
  to_address: string;    // Using snake_case to match Supabase
  message_id?: string;
  data?: any;
  read: boolean;
  created_at: string;    // Using snake_case to match Supabase
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  loading: boolean;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  fetchNotifications: (userId: string) => Promise<void>;
  addNotification: (
    type: NotificationType,
    fromAddress: string,
    toAddress: string,
    messageId?: string,
    data?: any
  ) => Promise<void>;
}

export const NotificationContext = createContext<NotificationContextType>({
  notifications: [],
  unreadCount: 0,
  loading: false,
  markAsRead: async () => {},
  markAllAsRead: async () => {},
  fetchNotifications: async () => {},
  addNotification: async () => {},
});

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);

  const fetchNotifications = useCallback(async (userId: string) => {
    if (!userId) return;
    
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('to_address', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching notifications:', error);
        return;
      }

      // Map and filter the data to match our types
      const validNotifications = (data || [])
        .filter(item => item.type && ['like', 'reply', 'tip', 'follow', 'summon', 'repost', 'reaction'].includes(item.type))
        .map(item => ({
          id: item.id,
          type: item.type as NotificationType,
          from_address: item.from_address,
          to_address: item.to_address,
          message_id: item.message_id,
          data: item.data,
          read: item.read,
          created_at: item.created_at
        }));
      
      setNotifications(validNotifications);
      
      // Count unread
      const unread = validNotifications.filter(notif => !notif.read).length || 0;
      setUnreadCount(unread);
    } catch (error) {
      console.error('Error in fetchNotifications:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Get current user ID and fetch notifications
  useEffect(() => {
    const getCurrentUser = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          setCurrentUserId(user.id);
          await fetchNotifications(user.id);
        }
      } catch (error) {
        console.error('Error getting current user:', error);
      }
    };

    getCurrentUser();
  }, [fetchNotifications]);

  const markAsRead = async (id: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', id);

      if (error) {
        console.error('Error marking notification as read:', error);
        return;
      }

      // Update local state
      setNotifications(prev => 
        prev.map(notif => 
          notif.id === id ? { ...notif, read: true } : notif
        )
      );
      
      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Error in markAsRead:', error);
    }
  };

  const markAllAsRead = async () => {
    // Get IDs of unread notifications
    const unreadIds = notifications
      .filter(notif => !notif.read)
      .map(notif => notif.id);
    
    if (unreadIds.length === 0) return;

    try {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .in('id', unreadIds);

      if (error) {
        console.error('Error marking all notifications as read:', error);
        return;
      }

      // Update local state
      setNotifications(prev => 
        prev.map(notif => ({ ...notif, read: true }))
      );
      
      // Update unread count
      setUnreadCount(0);
    } catch (error) {
      console.error('Error in markAllAsRead:', error);
    }
  };

  const addNotification = async (
    type: NotificationType,
    fromAddress: string,
    toAddress: string,
    messageId?: string,
    data?: any
  ) => {
    if (!fromAddress || !toAddress) {
      console.error('Missing required parameters for notification');
      return;
    }

    try {
      const notificationId = uuidv4();
      
      const { error } = await supabase.from('notifications').insert({
        id: notificationId,
        type,
        from_address: fromAddress,
        to_address: toAddress,
        message_id: messageId,
        data,
        read: false,
        created_at: new Date().toISOString(),
      });

      if (error) {
        console.error('Error creating notification:', error);
        return;
      }

      console.log(`Notification created: ${type} from ${fromAddress} to ${toAddress}`);
    } catch (error) {
      console.error('Error in addNotification:', error);
    }
  };

  // Setup real-time notifications for the current user
  useEffect(() => {
    if (!currentUserId) return;
    
    const channel = supabase.channel('notification_changes')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'notifications',
        filter: `to_address=eq.${currentUserId}`
      }, (payload) => {
        console.log('New notification received:', payload);
        // Add to state and increment unread count
        const newNotification = payload.new as Notification;
        setNotifications(prev => [newNotification, ...prev]);
        setUnreadCount(prev => prev + 1);
        
        // Show toast notification
        toast.info('You have a new notification');
      })
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'notifications',
        filter: `to_address=eq.${currentUserId}`
      }, (payload) => {
        console.log('Notification updated:', payload);
        // Update in state
        const updatedNotification = payload.new as Notification;
        setNotifications(prev => 
          prev.map(notif => 
            notif.id === updatedNotification.id ? updatedNotification : notif
          )
        );
        
        // Update unread count if necessary
        if (payload.old.read === false && updatedNotification.read === true) {
          setUnreadCount(prev => Math.max(0, prev - 1));
        }
      })
      .subscribe();
      
    return () => {
      supabase.removeChannel(channel);
    };
  }, [currentUserId]);

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        loading,
        markAsRead,
        markAllAsRead,
        fetchNotifications,
        addNotification,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => useContext(NotificationContext);

export default NotificationProvider;
