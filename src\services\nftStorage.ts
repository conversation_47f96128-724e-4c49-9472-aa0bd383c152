/**
 * NFT.Storage service for IPFS uploads
 * This service handles uploading files to IPFS via NFT.Storage
 */

// Note: You'll need to install nft.storage with: npm install nft.storage
import { NFTStorage } from 'nft.storage';
import { NFT_STORAGE_TOKEN } from '@/utils/env';

/**
 * Get an NFT.Storage client instance
 */
export function getNFTStorageClient(): NFTStorage | null {
  if (!NFT_STORAGE_TOKEN) {
    console.warn('NFT.Storage token not found. Please set VITE_NFT_STORAGE_TOKEN in your environment variables.');
    return null;
  }

  // Validate the token format (NFT.Storage tokens are JWT tokens that start with 'eyJ')
  if (!NFT_STORAGE_TOKEN.startsWith('eyJ')) {
    console.warn('NFT.Storage token appears to be malformed. It should start with "eyJ".');
    console.warn('Current token format:', NFT_STORAGE_TOKEN.substring(0, 5) + '...');
    return null;
  }

  try {
    return new NFTStorage({ token: NFT_STORAGE_TOKEN });
  } catch (error) {
    console.error('Error creating NFT.Storage client:', error);
    return null;
  }
}

/**
 * Upload a file to IPFS via NFT.Storage
 * @param file The file to upload
 * @returns IPFS URI in the format ipfs://{CID}/{filename}
 */
export async function uploadToIPFS(file: File): Promise<string> {
  try {
    console.log(`Uploading file to IPFS: ${file.name} (${file.size} bytes, type: ${file.type})`);

    // Get NFT.Storage client
    const client = getNFTStorageClient();

    // If client is null, throw an error to trigger fallback
    if (!client) {
      throw new Error('NFT.Storage client could not be initialized');
    }

    // Upload the file to IPFS
    console.log('Starting upload to NFT.Storage...');
    const cid = await client.storeBlob(file);

    // Check if we got a valid CID
    if (!cid || typeof cid !== 'string' || cid.trim() === '') {
      throw new Error('Invalid CID returned from NFT.Storage');
    }

    console.log(`Successfully uploaded to IPFS with CID: ${cid}`);

    // Return the IPFS URI
    const ipfsUri = `ipfs://${cid}/${file.name}`;
    console.log(`IPFS URI: ${ipfsUri}`);
    return ipfsUri;
  } catch (error) {
    console.error('Error uploading to IPFS via NFT.Storage:', error);

    // Add more detailed error information
    if (error.message && error.message.includes('API Key is malformed')) {
      console.error('The NFT.Storage API key appears to be invalid or malformed.');
      console.error('Please check your API key or get a new one from https://nft.storage/');
    } else if (error.message && error.message.includes('401')) {
      console.error('Authentication failed with NFT.Storage. Your API key may be invalid or expired.');
    } else if (error.message && error.message.includes('429')) {
      console.error('Rate limit exceeded for NFT.Storage. Please try again later.');
    }

    // Create a fake IPFS URI with a timestamp to ensure uniqueness
    // This will allow the app to continue functioning even if IPFS upload fails
    const fakeIpfsUri = `indexeddb://${file.name}_${Date.now()}`;
    console.log(`Created fallback URI: ${fakeIpfsUri}`);

    // Throw the error to trigger IndexedDB fallback in the calling function
    throw error;
  }
}

/**
 * Upload a blob to IPFS via NFT.Storage
 * @param blob The blob to upload
 * @param fileName The name to give the file
 * @returns IPFS URI in the format ipfs://{CID}/{filename}
 */
export async function uploadBlobToIPFS(blob: Blob, fileName: string): Promise<string> {
  try {
    console.log(`Converting blob to File object: ${fileName} (${blob.size} bytes, type: ${blob.type})`);

    // Validate inputs
    if (!blob) {
      throw new Error('No blob provided for upload');
    }

    if (!fileName) {
      fileName = `file_${Date.now()}`;
      console.warn(`No filename provided, using generated name: ${fileName}`);
    }

    // Convert blob to File object
    const file = new File([blob], fileName, { type: blob.type });

    // Upload to IPFS
    console.log(`Uploading blob as file: ${fileName}`);

    // Store in IndexedDB as a backup
    try {
      await storeInIndexedDB(blob, fileName);
      console.log(`Stored ${fileName} in IndexedDB as backup`);
    } catch (indexedDBError) {
      console.warn('Failed to store in IndexedDB:', indexedDBError);
    }

    // Try to upload to IPFS
    return await uploadToIPFS(file);
  } catch (error) {
    console.error('Error uploading blob to IPFS via NFT.Storage:', error);

    // Try to get from IndexedDB
    try {
      const indexedDBUrl = await getIndexedDBUrl(fileName);
      if (indexedDBUrl) {
        console.log(`Retrieved ${fileName} from IndexedDB`);
        return indexedDBUrl;
      }
    } catch (indexedDBError) {
      console.warn('Failed to retrieve from IndexedDB:', indexedDBError);
    }

    // Create a blob URL as a last resort
    console.warn('Falling back to blob URL for storage');
    const blobUrl = URL.createObjectURL(blob);
    console.warn(`Created temporary blob URL: ${blobUrl}`);
    console.warn('Note: This URL will not persist across page refreshes');

    // Return the blob URL instead of throwing an error
    // This allows the app to continue functioning even if IPFS upload fails
    return blobUrl;
  }
}

/**
 * Store a blob in IndexedDB
 * @param blob The blob to store
 * @param key The key to store it under
 */
async function storeInIndexedDB(blob: Blob, key: string): Promise<void> {
  return new Promise((resolve, reject) => {
    try {
      const request = indexedDB.open('ImageStorage', 1);

      request.onupgradeneeded = function () {
        const db = request.result;
        if (!db.objectStoreNames.contains('images')) {
          db.createObjectStore('images', { keyPath: 'id' });
        }
      };

      request.onsuccess = function () {
        try {
          const db = request.result;
          const transaction = db.transaction(['images'], 'readwrite');
          const store = transaction.objectStore('images');

          const putRequest = store.put({
            id: key,
            blob: blob,
            timestamp: Date.now(),
            type: blob.type
          });

          putRequest.onsuccess = function () {
            console.log(`Image stored in IndexedDB with ID: ${key}`);
            resolve();
          };

          putRequest.onerror = function (event) {
            console.error('Error storing image in IndexedDB:', event);
            reject(new Error('Failed to store image in IndexedDB'));
          };
        } catch (error) {
          console.error('Error in IndexedDB transaction:', error);
          reject(error);
        }
      };

      request.onerror = function (event) {
        console.error('Error opening IndexedDB:', event);
        reject(new Error('Failed to open IndexedDB'));
      };
    } catch (error) {
      console.error('Error accessing IndexedDB:', error);
      reject(error);
    }
  });
}

/**
 * Get a URL for a blob stored in IndexedDB
 * @param key The key the blob was stored under
 * @returns A URL for the blob, or null if not found
 */
async function getIndexedDBUrl(key: string): Promise<string | null> {
  return new Promise((resolve) => {
    try {
      const request = indexedDB.open('ImageStorage', 1);

      request.onupgradeneeded = function () {
        const db = request.result;
        if (!db.objectStoreNames.contains('images')) {
          db.createObjectStore('images', { keyPath: 'id' });
        }
      };

      request.onsuccess = function () {
        try {
          const db = request.result;
          const transaction = db.transaction(['images'], 'readonly');
          const store = transaction.objectStore('images');
          const getRequest = store.get(key);

          getRequest.onsuccess = function () {
            if (getRequest.result) {
              console.log('Found image in IndexedDB:', key);
              const blobUrl = URL.createObjectURL(getRequest.result.blob);
              resolve(blobUrl);
            } else {
              console.log('Image not found in IndexedDB:', key);
              resolve(null);
            }
          };

          getRequest.onerror = function (event) {
            console.error('Error getting image from IndexedDB:', event);
            resolve(null);
          };
        } catch (error) {
          console.error('Error in IndexedDB transaction:', error);
          resolve(null);
        }
      };

      request.onerror = function (event) {
        console.error('Error opening IndexedDB:', event);
        resolve(null);
      };
    } catch (error) {
      console.error('Error accessing IndexedDB:', error);
      resolve(null);
    }
  });
}

/**
 * Get a gateway URL for an IPFS URI
 * @param ipfsUri IPFS URI in the format ipfs://{CID}/{filename}
 * @returns HTTP URL for accessing the content
 */
export function getIPFSGatewayUrl(ipfsUri: string): string {
  // Remove ipfs:// prefix
  const ipfsPath = ipfsUri.replace('ipfs://', '');

  // Split into CID and filename
  const [cid, ...pathParts] = ipfsPath.split('/');
  const path = pathParts.join('/');

  // Use NFT.Storage's IPFS gateway
  return `https://${cid}.ipfs.nftstorage.link/${path}`;
}

/**
 * Check if a string is an IPFS URI
 * @param uri The URI to check
 * @returns True if the URI is an IPFS URI
 */
export function isIPFSUri(uri: string): boolean {
  return uri.startsWith('ipfs://');
}

/**
 * Convert an IPFS URI to a gateway URL if needed
 * @param uri The URI to convert
 * @returns HTTP URL for accessing the content
 */
export function resolveIPFSUri(uri: string): string {
  if (isIPFSUri(uri)) {
    return getIPFSGatewayUrl(uri);
  }
  return uri;
}

/**
 * Check the status of an upload by its CID
 * @param cid The CID of the upload
 * @returns The status of the upload
 */
export async function checkStatus(cid: string): Promise<any> {
  try {
    const client = getNFTStorageClient();

    if (!client) {
      throw new Error('NFT.Storage client could not be initialized');
    }

    return await client.status(cid);
  } catch (error) {
    console.error('Error checking status:', error);
    // Return a default status object instead of throwing
    return {
      cid: cid,
      size: 0,
      created: new Date().toISOString(),
      pin: { status: 'error' },
      deals: []
    };
  }
}
