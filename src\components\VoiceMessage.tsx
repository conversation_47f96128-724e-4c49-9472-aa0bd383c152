
import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  MessageSquare,
  MoreHorizontal,
  PlayCircle,
  PauseCircle,
  Pin,
  Trash2,
  DollarSign
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { formatDistanceToNow } from 'date-fns';
import AudioWaveform from './AudioWaveform';
import MessageReactions from './MessageReactions';
import { useProfiles } from '@/contexts/SimpleProfileContext';

import CustomAvatarImage from './CustomAvatarImage';
import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/components/ui/sonner';
import TipModalWithoutWallet from './TipModalWithoutWallet';
import TipDisplay from './TipDisplay';

export interface VoiceMessageProps {
  id: string;
  originalId?: string; // For reposts, the original message ID
  userAddress: string;
  audioUrl: string;
  transcript: string;
  timestamp: Date;
  duration: number;
  isPinned?: boolean;
  isReply?: boolean;
  replies?: VoiceMessageProps[];
  parentId?: string;
  media?: { id: string; url: string; type: 'image' | 'video' }[];
  channelId?: string;
  connectedAccount?: string; // Add connected account prop
  isRepost?: boolean; // Whether this is a repost
  originalUserAddress?: string; // Original author for reposts
  repostTimestamp?: Date; // When it was reposted
  isTextOnly?: boolean; // Flag to indicate if this is a text-only reply
  onReply?: () => void;
  onDelete?: (id: string) => void;
  onPin?: (id: string, isPinned: boolean) => void;
}

const VoiceMessage: React.FC<VoiceMessageProps> = ({
  id,
  userAddress,
  audioUrl,
  transcript,
  timestamp,
  duration,
  isPinned = false,
  isReply = false,
  replies = [],
  media = [],
  connectedAccount,
  isTextOnly = false,
  onReply,
  onDelete,
  onPin,
}) => {
  const { getProfileByAddress } = useProfiles();
  const { user } = useAuth(); // Get authenticated user
  const profile = getProfileByAddress(userAddress);
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0);
  const [isTipModalOpen, setIsTipModalOpen] = useState(false);
  
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [isMenuOpen, setIsMenuOpen] = useState(false);



  const handlePlayPause = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play().catch((e) => {
          console.error('Error playing audio:', e);
        });
      }
    }
  };

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      const currentProgress = (audioRef.current.currentTime / audioRef.current.duration) * 100;
      setProgress(currentProgress);
    }
  };

  const handleEnded = () => {
    setIsPlaying(false);
    setProgress(0);
    if (audioRef.current) {
      audioRef.current.currentTime = 0;
    }
  };


  // Handle audio events
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const playHandler = () => setIsPlaying(true);
    const pauseHandler = () => setIsPlaying(false);
    const timeUpdateHandler = handleTimeUpdate;
    const endedHandler = handleEnded;

    audio.addEventListener('play', playHandler);
    audio.addEventListener('pause', pauseHandler);
    audio.addEventListener('timeupdate', timeUpdateHandler);
    audio.addEventListener('ended', endedHandler);

    return () => {
      audio.removeEventListener('play', playHandler);
      audio.removeEventListener('pause', pauseHandler);
      audio.removeEventListener('timeupdate', timeUpdateHandler);
      audio.removeEventListener('ended', endedHandler);
    };
  }, [audioRef.current]);

  // Format timestamp
  const formattedTime = formatDistanceToNow(new Date(timestamp), {
    addSuffix: true,
  });

  return (
    <div className={cn(
      "bg-background p-4 rounded-lg border", 
      isPinned ? "border-voicechain-purple" : "border-border",
      isReply ? "ml-6 mt-2" : ""
    )}>
      <div className="flex items-start gap-3">
        {/* User Avatar with Link */}
        <Link to={`/profile/${userAddress}`} className="flex-shrink-0">
          <Avatar className="h-10 w-10 rounded-full">
            <CustomAvatarImage src={profile.profileImageUrl} alt={profile.displayName} />
            <AvatarFallback className="bg-voicechain-accent text-white">
              {profile.displayName.charAt(0)}
            </AvatarFallback>
          </Avatar>
        </Link>

        <div className="flex-1 min-w-0">
          <div className="flex justify-between items-start">
            <div className="flex flex-col">
              <Link
                to={`/profile/${userAddress}`}
                className="text-sm font-medium hover:underline"
              >
                {profile.displayName}
              </Link>
              <span className="text-xs text-muted-foreground">
                {formattedTime} {isPinned && <Pin size={12} className="inline ml-1 text-voicechain-purple" />}
              </span>
            </div>
            
            {(onDelete || onPin) && (
              <DropdownMenu open={isMenuOpen} onOpenChange={setIsMenuOpen}>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <MoreHorizontal size={16} />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {onPin && (
                    <DropdownMenuItem onClick={() => {
                      onPin(id, isPinned);
                      setIsMenuOpen(false);
                    }}>
                      <Pin size={16} className="mr-2" />
                      {isPinned ? 'Unpin message' : 'Pin to profile'}
                    </DropdownMenuItem>
                  )}
                  {onDelete && (
                    <>
                      {onPin && <DropdownMenuSeparator />}
                      <DropdownMenuItem 
                        onClick={() => {
                          if (window.confirm('Are you sure you want to delete this message?')) {
                            onDelete(id);
                          }
                          setIsMenuOpen(false);
                        }}
                        className="text-destructive focus:text-destructive"
                      >
                        <Trash2 size={16} className="mr-2" />
                        Delete message
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>

          <div className="mt-3">
            {transcript && (
              <div className="mb-2 text-sm leading-snug">{transcript}</div>
            )}
            
            {/* Media Gallery */}
            {media && media.length > 0 && (
              <div className={`grid gap-2 mb-3 ${media.length === 1 ? 'grid-cols-1' : 'grid-cols-2'}`}>
                {media.map((item) => (
                  <div 
                    key={item.id} 
                    className="relative rounded-md overflow-hidden h-[200px]"
                  >
                    {item.type === 'image' ? (
                      <img 
                        src={item.url} 
                        alt="Attached media" 
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <video 
                        src={item.url} 
                        controls 
                        className="w-full h-full object-cover"
                      />
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* Audio Player - Only show for voice messages */}
            {!isTextOnly && audioUrl && (
              <>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="flex-shrink-0 h-10 w-10 rounded-full bg-secondary text-primary hover:text-primary-foreground hover:bg-primary"
                    onClick={handlePlayPause}
                  >
                    {isPlaying ? <PauseCircle size={24} /> : <PlayCircle size={24} />}
                  </Button>
                  <div className="flex-1">
                    <AudioWaveform
                      audioUrl={audioUrl}
                      audioRef={audioRef}
                      isPlaying={isPlaying}
                      progress={progress}
                      setIsPlaying={setIsPlaying}
                      duration={duration}
                    />
                  </div>
                </div>
                <audio ref={audioRef} src={audioUrl} preload="metadata" />
              </>
            )}

            {/* Text-only indicator */}
            {isTextOnly && (
              <div className="text-xs text-muted-foreground italic mt-2">
                Text reply
              </div>
            )}
          </div>

          <div className="mt-3 flex flex-wrap justify-between items-center gap-2">
            <MessageReactions
              messageId={id}
              userId={user?.id || ''}
            />
            
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-3 text-muted-foreground hover:text-foreground"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsTipModalOpen(true);
                }}
              >
                <DollarSign size={16} className="mr-2" />
                <span className="text-xs">Tip</span>
              </Button>

              {onReply && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 px-3 text-muted-foreground hover:text-foreground"
                  onClick={onReply}
                >
                  <MessageSquare size={16} className="mr-2" />
                  <span className="text-xs">Reply</span>
                </Button>
              )}

              {/* Delete button - only show for own posts */}
              {connectedAccount && userAddress === connectedAccount && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 px-3 text-red-500 hover:text-red-600 hover:bg-red-50"
                  onClick={async () => {
                    if (window.confirm('Are you sure you want to delete this post?')) {
                      try {
                        const { error } = await supabase
                          .from('voice_messages')
                          .delete()
                          .eq('id', id);

                        if (error) {
                          console.error('Error deleting post:', error);
                          toast('Failed to delete post');
                        } else {
                          toast('Post deleted successfully');
                          window.location.reload();
                        }
                      } catch (error) {
                        console.error('Error deleting post:', error);
                        toast('Failed to delete post');
                      }
                    }
                  }}
                  title="Delete post"
                >
                  <Trash2 size={16} className="mr-2" />
                  <span className="text-xs">Delete</span>
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Replies */}
      {/* Tips Display */}
      <div className="mt-3 pl-12">
        <TipDisplay messageId={id} limit={3} />
      </div>

      {replies && replies.length > 0 && (
        <div className="mt-3 pl-12">
          {replies.map((reply) => (
            <VoiceMessage
              key={reply.id}
              {...reply}
              isReply={true}
            />
          ))}
        </div>
      )}

      {/* Tip Modal */}
      <TipModalWithoutWallet
        isOpen={isTipModalOpen}
        onClose={() => setIsTipModalOpen(false)}
        recipientAddress={userAddress}
        messageId={id}
      />
    </div>
  );
};

export default VoiceMessage;
