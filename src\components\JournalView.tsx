import React, { useState, useRef } from 'react';
import { JournalEntry } from '@/types/journal';
import { Button } from '@/components/ui/button';
import {
  Play,
  Volume2,
  Lock,
  Clock,
  Users,
  FileText,
  Calendar,
  Trash2,
  Unlock,
  Image as ImageIcon,
  MessageCircle,
  Share,
  DollarSign,
  AtSign,
  ChevronUp,
  ChevronDown
} from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { MediaFile } from './MediaUploader';
import { formatDistanceToNow, format } from 'date-fns';
import { useJournals } from '@/contexts/JournalContext';
import { toast } from '@/components/ui/sonner';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

import RepostButton from '@/components/RepostButton';
import MessageReactions from '@/components/MessageReactions';
import JournalTipModal from '@/components/JournalTipModal';

interface JournalViewProps {
  journal: JournalEntry;
  currentUserAddress: string;
  onUnlock?: () => void;
  showOwnerControls?: boolean;
}

const JournalView: React.FC<JournalViewProps> = ({
  journal,
  currentUserAddress,
  onUnlock,
  showOwnerControls = false
}) => {
  const {
    unlockJournal,
    deleteJournal,
    canUserUnlockJournal,
    addJournalReply
  } = useJournals();
  const isMobile = useIsMobile();
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [showReplyInput, setShowReplyInput] = useState(false);
  const [isTipModalOpen, setIsTipModalOpen] = useState(false);
  const [isSummonModalOpen, setIsSummonModalOpen] = useState(false);
  const [isReplyModalOpen, setIsReplyModalOpen] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [replyText, setReplyText] = useState('');
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Check if the current user can unlock this journal
  const userCanUnlock = canUserUnlockJournal(journal.id, currentUserAddress);

  // Format time as MM:SS
  const formatTime = (seconds: number = 0) => {
    const mins = Math.floor(seconds / 60).toString().padStart(2, '0');
    const secs = Math.floor(seconds % 60).toString().padStart(2, '0');
    return `${mins}:${secs}`;
  };

  // Handle play/pause
  const togglePlay = () => {
    if (!audioRef.current) return;

    if (isPlaying) {
      audioRef.current.pause();
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }
    } else {
      audioRef.current.play();
      // Update progress every 100ms
      progressIntervalRef.current = setInterval(() => {
        if (audioRef.current) {
          setCurrentTime(audioRef.current.currentTime);
        }
      }, 100);
    }

    setIsPlaying(!isPlaying);
  };

  // Handle audio end
  const handleAudioEnd = () => {
    setIsPlaying(false);
    setCurrentTime(0);
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
  };

  // Handle unlock
  const handleUnlock = () => {
    // Check if the user can unlock this journal
    if (!userCanUnlock) {
      toast.error('You cannot unlock this journal yet');
      return;
    }

    const success = unlockJournal(journal.id, currentUserAddress);
    if (success) {
      toast.success('Journal unlocked successfully');
      if (onUnlock) onUnlock();
    } else {
      toast.error('Failed to unlock journal');
    }
  };

  // Handle delete
  const handleDelete = async () => {
    try {
      await deleteJournal(journal.id, currentUserAddress);
      toast.success('Journal deleted successfully');
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error('Error deleting journal:', error);
      toast.error('Failed to delete journal');
    }
  };

  // Handler functions like voice messages
  const handleTip = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsTipModalOpen(true);
  };

  const handleSummon = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsSummonModalOpen(true);
  };

  // Reply handler
  const handleReply = async () => {
    if (!replyText.trim()) return;

    try {
      const replyId = await addJournalReply(journal.id, currentUserAddress, replyText);
      if (replyId) {
        setReplyText('');
        setShowReplyInput(false);
        toast.success('Reply added!');
      }
    } catch (error) {
      console.error('Error adding reply:', error);
      toast.error('Failed to add reply');
    }
  };

  // Handle share button click
  const handleShare = (e: React.MouseEvent) => {
    e.stopPropagation();

    // Create a shareable URL
    const shareUrl = `${window.location.origin}/journal/${journal.id}`;

    // Try to use the Web Share API if available
    if (navigator.share) {
      navigator.share({
        title: 'Journal Entry',
        text: journal.transcript.substring(0, 100) + (journal.transcript.length > 100 ? '...' : ''),
        url: shareUrl
      }).catch(error => {
        console.error('Error sharing:', error);
        // Fallback to clipboard
        copyToClipboard(shareUrl);
      });
    } else {
      // Fallback to clipboard
      copyToClipboard(shareUrl);
    }
  };

  // Copy text to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast.success('Link copied to clipboard');
    }).catch(error => {
      console.error('Failed to copy to clipboard:', error);
      toast.error('Failed to copy link');
    });
  };

  // Calculate progress percentage
  const progressPercentage = journal.duration ? (currentTime / journal.duration) * 100 : 0;

  // Get icon based on unlock condition type
  const getUnlockIcon = () => {
    switch (journal.unlockCondition.type) {
      case 'time':
        return <Clock size={16} />;
      case 'token':
        return <Users size={16} />;
      case 'event':
        return <FileText size={16} />;
      default:
        return <Lock size={16} />;
    }
  };

  // Get unlock condition text
  const getUnlockText = () => {
    const { type, unlockDate, tokenAddress, eventId } = journal.unlockCondition;

    switch (type) {
      case 'time':
        return unlockDate
          ? `Unlocks on ${format(unlockDate, 'PPP')}`
          : 'Time-locked journal';
      case 'token':
        return `Token-gated: ${tokenAddress?.substring(0, 8)}...`;
      case 'event':
        return `Event-based: ${eventId}`;
      default:
        return 'Locked journal';
    }
  };

  return (
    <div className="bg-secondary rounded-xl p-3 sm:p-4 mb-3 sm:mb-4">
      <div className="flex justify-between items-start mb-2">
        <h3 className="text-sm sm:text-base font-medium">{journal.title}</h3>
        <div className="flex items-center gap-1 sm:gap-2 flex-shrink-0">
          {journal.isPrivate && (
            <span className="text-[10px] sm:text-xs bg-secondary-foreground/20 px-1.5 py-0.5 rounded-full flex items-center gap-0.5">
              <Lock size={10} className="sm:h-3 sm:w-3" />
              <span className="hidden xs:inline">Private</span>
            </span>
          )}
          <span className="text-[10px] sm:text-xs text-muted-foreground">
            {formatDistanceToNow(journal.createdAt, { addSuffix: true })}
          </span>
        </div>
      </div>

      {journal.description && (
        <p className="text-xs sm:text-sm text-muted-foreground mb-2 sm:mb-3">{journal.description}</p>
      )}

      {/* Unlock condition */}
      {!journal.isUnlocked && (
        <div className="bg-secondary-foreground/10 rounded-lg p-2 sm:p-3 mb-2 sm:mb-3">
          <div className="flex items-center justify-between mb-1">
            <div className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm">
              {getUnlockIcon()}
              <span className="line-clamp-1">{getUnlockText()}</span>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="text-[10px] sm:text-xs h-7 sm:h-8 px-2 flex items-center gap-0.5 sm:gap-1 flex-shrink-0"
              onClick={handleUnlock}
            >
              <Unlock size={12} className="sm:h-3.5 sm:w-3.5" />
              Unlock
            </Button>
          </div>

          {/* Show exact unlock date for time-based journals */}
          {journal.unlockCondition.type === 'time' && journal.unlockCondition.unlockDate && (
            <div className="flex items-center gap-1 text-[10px] sm:text-xs text-muted-foreground mt-1 bg-background/50 p-1.5 rounded">
              <Calendar size={12} className="sm:h-3.5 sm:w-3.5" />
              <span>
                This journal will unlock on <span className="font-medium">{format(journal.unlockCondition.unlockDate, 'PPP')}</span> at <span className="font-medium">{format(journal.unlockCondition.unlockDate, 'p')}</span>
              </span>
            </div>
          )}

          {/* Show locked media preview if journal has media */}
          {journal.media && journal.media.length > 0 && (
            <div className="flex items-center gap-1 text-[10px] sm:text-xs text-muted-foreground mt-1.5">
              <ImageIcon size={12} className="sm:h-3.5 sm:w-3.5" />
              <span>
                {journal.media.length} {journal.media.length === 1 ? 'media file' : 'media files'} will be available when unlocked
              </span>
            </div>
          )}
        </div>
      )}

      {/* Audio Player (only visible if unlocked) */}
      {journal.isUnlocked && (
        <div className="relative mb-2 sm:mb-3 bg-muted/50 rounded-lg p-2 sm:p-3 flex items-center">
          <Button
            size="icon"
            variant="ghost"
            className={`h-8 w-8 sm:h-10 sm:w-10 rounded-full flex-shrink-0 ${isPlaying ? 'bg-voicechain-purple' : 'bg-accent/30'}`}
            onClick={togglePlay}
          >
            {isPlaying ? <Volume2 size={16} className="sm:h-5 sm:w-5" /> : <Play size={16} className="sm:h-5 sm:w-5" />}
          </Button>

          <div className="ml-2 flex-1">
            <div className="h-1.5 bg-muted rounded-full overflow-hidden">
              <div
                className="h-full bg-voicechain-purple"
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
          </div>

          <span className="ml-2 text-[10px] sm:text-xs font-mono flex-shrink-0">
            {formatTime(currentTime)} / {formatTime(journal.duration)}
          </span>

          <audio
            ref={audioRef}
            src={journal.audioUrl}
            onEnded={handleAudioEnd}
          />
        </div>
      )}

      {/* Transcript (only visible if unlocked) */}
      {journal.isUnlocked && journal.transcript && (
        <div className="bg-background rounded-lg p-2 sm:p-3 mb-2 sm:mb-3">
          <p className="text-[10px] sm:text-xs font-medium mb-1">Transcript:</p>
          <p className="text-xs sm:text-sm">{journal.transcript}</p>
        </div>
      )}

      {/* Media Display (only visible if unlocked) */}
      {journal.isUnlocked && journal.media && journal.media.length > 0 && (
        <div className="mb-2 sm:mb-3">
          <div className="flex items-center gap-1 mb-1">
            <ImageIcon size={14} className="text-muted-foreground" />
            <p className="text-[10px] sm:text-xs font-medium text-muted-foreground">
              Media ({journal.media.length})
            </p>
          </div>
          <div className={`grid gap-2 ${journal.media.length === 1 ? 'grid-cols-1' :
            journal.media.length === 2 ? 'grid-cols-2' :
              journal.media.length >= 3 ? 'grid-cols-2' : ''}`}>
            {journal.media.map((mediaItem, index) => (
              <div
                key={mediaItem.id}
                className={`relative rounded-lg overflow-hidden border border-border
                           ${journal.media.length === 3 && index === 0 ? 'col-span-2 row-span-2' : ''}
                           ${journal.media.length === 4 && index < 2 ? 'aspect-[16/9]' : 'aspect-square'}`}
              >
                {mediaItem.type === 'image' ? (
                  <img
                    src={mediaItem.url}
                    alt={`Media ${index + 1}`}
                    className="w-full h-full object-cover cursor-pointer"
                    onClick={() => window.open(mediaItem.url, '_blank')}
                  />
                ) : (
                  <video
                    src={mediaItem.url}
                    controls
                    className="w-full h-full object-cover"
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Engagement Section - Same layout as voice messages */}
      {(journal.isUnlocked || journal.privacyLevel === 'public' || !journal.isPrivate) && (
        <div className="mt-4 border-t border-border pt-4">
          {/* Message Reactions */}
          <div className="mt-2 mb-1">
            <MessageReactions
              messageId={journal.id}
              userId={currentUserAddress}
            />
          </div>

          {/* Action buttons - mobile responsive layout */}
          <div className="flex items-center justify-between mt-2">
            <div className={`flex items-center ${isMobile ? 'gap-0' : 'gap-1'}`}>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-2 text-xs flex items-center"
                onClick={handleTip}
              >
                <DollarSign size={14} />
              </Button>

              <RepostButton
                postId={journal.id}
                currentUserId={currentUserAddress}
                originalUserId={journal.userAddress}
                postType="journal"
                size="sm"
                variant="ghost"
                className="h-8 px-2 text-xs"
                iconOnly={true}
              />

              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-2 text-xs flex items-center"
                onClick={handleSummon}
              >
                <AtSign size={14} />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-2 text-xs flex items-center"
                onClick={handleShare}
              >
                <Share size={14} />
              </Button>

              <Button
                variant={isExpanded ? "default" : "ghost"}
                size="sm"
                className={`h-8 px-2 text-xs flex items-center ${isExpanded ? "bg-voicechain-purple text-white" : ""}`}
                onClick={() => setIsExpanded(!isExpanded)}
              >
                <MessageCircle size={14} className="mr-1" />
                Reply
                {isExpanded && <ChevronUp size={14} className="ml-1" />}
                {!isExpanded && <ChevronDown size={14} className="ml-1" />}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Expanded Replies Section - Same as voice messages */}
      {isExpanded && (
        <div className="mt-4 border-t border-border pt-4">
          <div className="mb-4 space-y-2">
            <h4 className="text-sm font-medium">Reply to this journal</h4>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                className="flex-1 bg-secondary/50 hover:bg-secondary text-sm"
                onClick={() => setShowReplyInput(true)}
              >
                <MessageCircle size={14} className="mr-2" />
                Text Reply
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Reply Input */}
      {showReplyInput && (
        <div className="mt-3 p-3 bg-muted rounded-lg">
          <div className="flex gap-2">
            <input
              type="text"
              placeholder="Write a reply..."
              value={replyText}
              onChange={(e) => setReplyText(e.target.value)}
              className="flex-1 px-3 py-2 text-sm bg-background border border-border rounded-md"
              onKeyPress={(e) => e.key === 'Enter' && handleReply()}
            />
            <Button
              size="sm"
              onClick={handleReply}
              disabled={!replyText.trim()}
              className="text-xs"
            >
              Reply
            </Button>
          </div>
        </div>
      )}

      {/* Actions - Only show for journal owner */}
      {(showOwnerControls || journal.userAddress === currentUserAddress) && (
        <div className="flex justify-end">
          <Button
            variant="ghost"
            size="sm"
            className="text-[10px] sm:text-xs h-7 text-destructive hover:text-destructive flex items-center gap-0.5 sm:gap-1"
            onClick={() => setIsDeleteDialogOpen(true)}
          >
            <Trash2 size={12} className="sm:h-3.5 sm:w-3.5" />
            Delete
          </Button>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete your voice journal entry. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Journal Tip Modal */}
      <JournalTipModal
        isOpen={isTipModalOpen}
        onClose={() => setIsTipModalOpen(false)}
        journalId={journal.id}
        journalOwnerId={journal.userAddress}
        journalTitle={journal.title}
      />

      {isSummonModalOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-background rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Summon Creator</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Request the creator to respond to this journal
            </p>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setIsSummonModalOpen(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  setIsSummonModalOpen(false);
                  toast.success('Summon sent!');
                }}
                className="flex-1"
              >
                Send Summon
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default JournalView;
