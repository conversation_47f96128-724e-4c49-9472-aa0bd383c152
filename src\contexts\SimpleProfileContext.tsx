
import React, { createContext, useContext, useState, useEffect } from 'react';
import { UserProfile, UserProfileUpdate } from '@/types/user-profile';
import simpleProfileService from '@/services/simpleProfileService';
import { toast } from '@/components/ui/sonner';
import { supabase } from '@/integrations/supabase/client';

// Define the shape of the context
interface ProfileContextType {
  profiles: Record<string, UserProfile>;
  currentProfile: UserProfile | null;
  isLoading: boolean;
  getProfileByAddress: (address: string) => UserProfile | null;
  updateProfile: (address: string, update: UserProfileUpdate) => Promise<UserProfile | null>;
  refreshProfile: (address: string) => Promise<UserProfile | null>;
  incrementPostCount: (address: string, count?: number) => Promise<void>;
  getUserPosts: (address: string) => Promise<any[]>;
}

// Create the context with a default value
const ProfileContext = createContext<ProfileContextType>({
  profiles: {},
  currentProfile: null,
  isLoading: true,
  getProfileByAddress: () => null,
  updateProfile: async () => null,
  refreshProfile: async () => null,
  incrementPostCount: async () => {},
  getUserPosts: async () => []
});

// Custom hook to use the context
export const useProfiles = () => useContext(ProfileContext);

interface ProfileProviderProps {
  children: React.ReactNode;
  connectedAccount: string;
}

export const ProfileProvider: React.FC<ProfileProviderProps> = ({ children, connectedAccount }) => {
  const [profiles, setProfiles] = useState<Record<string, UserProfile>>({});
  const [currentProfile, setCurrentProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load the current user's profile when the component mounts or connectedAccount changes
  useEffect(() => {
    const loadCurrentProfile = async () => {
      if (!connectedAccount) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      try {
        // Ensure address is a string
        const addressStr = typeof connectedAccount === 'string' ? connectedAccount : String(connectedAccount);
        console.log(`Loading profile from Supabase for connected account: ${addressStr}`);
        
        let profile = await simpleProfileService.getProfileByAddress(addressStr);
        
        // If profile doesn't exist yet, create a basic one
        if (!profile) {
          console.log('No profile found for connected account, creating a basic one');
          
          // Create a default profile
          const defaultProfile: UserProfileUpdate = {
            displayName: `User ${addressStr.substring(0, 6)}`,
            username: `user_${addressStr.substring(0, 6)}`,
            bio: '',
            profileImageUrl: '',
            coverImageUrl: '',
            socialLinks: {},
          };
          
          // Try to create the profile
          profile = await simpleProfileService.createProfile(addressStr, defaultProfile);
        }
        
        if (profile) {
          console.log('Profile loaded from Supabase:', profile);
          setCurrentProfile(profile);
          
          // Add to profiles cache with normalized key
          const normalizedAddress = addressStr.toLowerCase();
          setProfiles(prev => ({
            ...prev,
            [normalizedAddress]: profile,
          }));
        } else {
          console.warn('Still no profile after attempted creation');
          setCurrentProfile(null);
        }
      } catch (error) {
        console.error('Error loading profile:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadCurrentProfile();
    
    // Subscribe to auth state changes
    const { data: authListener } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        console.log('Auth state changed: signed in');
        await loadCurrentProfile();
      } else if (event === 'SIGNED_OUT') {
        console.log('Auth state changed: signed out');
        setCurrentProfile(null);
        // Clear the profiles cache on sign out
        setProfiles({});
      }
    });
    
    return () => {
      // Clean up the subscription
      if (authListener && authListener.subscription) {
        authListener.subscription.unsubscribe();
      }
    };
  }, [connectedAccount]);

  const getProfileByAddress = (address: string): UserProfile | null => {
    if (!address) return null;
    
    // Ensure address is a string
    const addressStr = typeof address === 'string' ? address : String(address);
    const normalizedAddress = addressStr.toLowerCase();
    
    // Check if we already have this profile cached
    if (profiles[normalizedAddress]) {
      return profiles[normalizedAddress];
    }
    
    // If not cached, trigger an async load but return null for now
    refreshProfile(addressStr).catch(err => 
      console.error(`Error refreshing profile for ${addressStr}:`, err));
      
    return null;
  };

  const updateProfile = async (
    address: string,
    update: UserProfileUpdate
  ): Promise<UserProfile | null> => {
    if (!address) return null;

    try {
      // Ensure address is a string
      const addressStr = typeof address === 'string' ? address : String(address);
      console.log(`Updating profile for ${addressStr} with:`, update);
      
      // First, try to update profile in the service
      const updatedProfile = await simpleProfileService.updateProfile(addressStr, update);
      
      if (updatedProfile) {
        console.log('Profile updated successfully:', updatedProfile);
        
        // Update cache with normalized key
        const normalizedAddress = addressStr.toLowerCase();
        setProfiles(prev => ({
          ...prev,
          [normalizedAddress]: updatedProfile,
        }));
        
        // Update current profile if it's the same address
        if (currentProfile && currentProfile.address.toLowerCase() === normalizedAddress) {
          setCurrentProfile(updatedProfile);
        }
        
        return updatedProfile;
      } else {
        throw new Error("Failed to update profile");
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
      return null;
    }
  };

  const refreshProfile = async (address: string): Promise<UserProfile | null> => {
    if (!address) return null;

    try {
      // Ensure address is a string
      const addressStr = typeof address === 'string' ? address : String(address);
      console.log(`Refreshing profile for ${addressStr} from Supabase`);
      
      const profile = await simpleProfileService.getProfileByAddress(addressStr);
      
      if (profile) {
        console.log('Profile refreshed from Supabase:', profile);
        
        const normalizedAddress = addressStr.toLowerCase();
        
        // Update cache
        setProfiles(prev => ({
          ...prev,
          [normalizedAddress]: profile,
        }));
        
        // Update current profile if it's the same address
        if (currentProfile && currentProfile.address.toLowerCase() === normalizedAddress) {
          setCurrentProfile(profile);
        }
        
        return profile;
      } else {
        console.warn('No profile found when refreshing from Supabase');
        return null;
      }
    } catch (error) {
      console.error('Error refreshing profile from Supabase:', error);
      return null;
    }
  };

  const incrementPostCount = async (address: string, count?: number): Promise<void> => {
    if (!address) return;
    
    try {
      const normalizedAddress = typeof address === 'string' 
        ? address.toLowerCase() 
        : String(address).toLowerCase();
      
      console.log(`Incrementing post count for ${normalizedAddress}`);
      
      const profile = profiles[normalizedAddress];
      
      if (profile) {
        // First, get the actual count from Supabase
        let newCount = count;
        
        if (newCount === undefined) {
          // If count not provided, try to fetch from Supabase
          try {
            const { data: profileData } = await supabase
              .from('profiles')
              .select('wallet_address, id')
              .eq('wallet_address', normalizedAddress)
              .single();
              
            if (profileData && profileData.id) {
              const { count } = await supabase
                .from('voice_messages')
                .select('*', { count: 'exact', head: true })
                .eq('profile_id', profileData.id)
                .is('deleted_at', null);
                
              newCount = count || 0;
              console.log(`Fetched post count from Supabase: ${newCount}`);
            }
          } catch (error) {
            console.error('Error fetching post count from Supabase:', error);
            // Fallback to incrementing the current count
            newCount = (profile.stats?.posts || 0) + 1;
          }
        }
        
        if (newCount !== undefined) {
          console.log(`Updating post count from ${profile.stats?.posts || 0} to ${newCount}`);
          
          // Update the profile with the new count
          await updateProfile(normalizedAddress, {
            stats: {
              ...(profile.stats || {}),
              posts: newCount
            }
          });
        }
      }
    } catch (error) {
      console.error('Error incrementing post count:', error);
    }
  };

  const getUserPosts = async (address: string): Promise<any[]> => {
    if (!address) return [];
    
    try {
      console.log(`Getting posts for user ${address} from Supabase`);
      
      // First, get the profile UUID associated with the wallet address
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('wallet_address', address)
        .single();

      if (profileError) {
        console.error('Error fetching profile ID:', profileError);
        return [];
      }

      if (!profileData || !profileData.id) {
        console.log('No profile found for wallet address:', address);
        return [];
      }

      console.log(`Found profile ID ${profileData.id} for wallet address ${address}`);
      
      // Fetch the posts using the profile ID
      const { data, error } = await supabase
        .from('voice_messages')
        .select('*')
        .eq('profile_id', profileData.id)
        .is('deleted_at', null)
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error('Error fetching user posts from Supabase:', error);
        return [];
      }
      
      if (data && data.length > 0) {
        console.log(`Found ${data.length} posts for user ${address} in Supabase`);
        
        // Convert to VoiceMessageProps format
        const userPosts = data.map(post => ({
          id: post.id,
          userAddress: address,
          timestamp: new Date(post.created_at),
          transcript: post.transcript || '',
          audioUrl: post.audio_url || '',
          duration: post.audio_duration || 0,
          replies: [],
          isPinned: post.is_pinned || false
        }));
        
        return userPosts;
      }
      
      console.log('No posts found for user in Supabase');
      return [];
    } catch (error) {
      console.error('Error getting user posts from Supabase:', error);
      return [];
    }
  };

  const value = {
    profiles,
    currentProfile,
    isLoading,
    getProfileByAddress,
    updateProfile,
    refreshProfile,
    incrementPostCount,
    getUserPosts
  };

  return <ProfileContext.Provider value={value}>{children}</ProfileContext.Provider>;
};
