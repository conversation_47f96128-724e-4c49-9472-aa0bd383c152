/**
 * API Key Manager Service
 *
 * This service manages API keys securely by:
 * 1. Storing them in localStorage with encryption
 * 2. Providing methods to set, get, and validate API keys
 * 3. Offering a proxy endpoint approach for production use
 */

// API Key Types
export enum ApiKeyType {
  ELEVEN_LABS = 'elevenlabs',
  HELIUS = 'helius'
}

// Default API Keys (for development only - these should be removed in production)
const DEFAULT_API_KEYS: Record<ApiKeyType, string> = {
  [ApiKeyType.ELEVEN_LABS]: '***************************************************',
  [ApiKeyType.HELIUS]: '4418d794-039b-4530-a9c4-6f8e325faa18'
};

// Storage Keys
const STORAGE_PREFIX = 'voicechain_api_key_';

// Simple encryption/decryption functions
// Note: This is not secure for production, just adds a layer of obfuscation
const encrypt = (text: string): string => {
  return btoa(text);
};

const decrypt = (encryptedText: string): string => {
  try {
    return atob(encryptedText);
  } catch (error) {
    console.error('Error decrypting API key:', error);
    return '';
  }
};

class ApiKeyManagerService {
  private apiKeys: Record<ApiKeyType, string> = {
    [ApiKeyType.ELEVEN_LABS]: '',
    [ApiKeyType.HELIUS]: ''
  };

  private listeners: Record<ApiKeyType, Array<(apiKey: string) => void>> = {
    [ApiKeyType.ELEVEN_LABS]: [],
    [ApiKeyType.HELIUS]: []
  };

  constructor() {
    this.loadApiKeysFromStorage();
  }

  /**
   * Load API keys from storage
   */
  private loadApiKeysFromStorage(): void {
    Object.values(ApiKeyType).forEach(keyType => {
      const storedKey = localStorage.getItem(`${STORAGE_PREFIX}${keyType}`);

      if (storedKey) {
        try {
          const decryptedKey = decrypt(storedKey);
          this.apiKeys[keyType] = decryptedKey;
          console.log(`Loaded ${keyType} API key from storage`);
        } catch (error) {
          console.error(`Error loading ${keyType} API key from storage:`, error);
        }
      } else if (DEFAULT_API_KEYS[keyType]) {
        // Use default key if available and no stored key exists
        this.apiKeys[keyType] = DEFAULT_API_KEYS[keyType];
        console.log(`Using default ${keyType} API key`);
      }
    });
  }

  /**
   * Save API key to storage
   */
  private saveApiKeyToStorage(keyType: ApiKeyType, apiKey: string): void {
    try {
      const encryptedKey = encrypt(apiKey);
      localStorage.setItem(`${STORAGE_PREFIX}${keyType}`, encryptedKey);
      console.log(`Saved ${keyType} API key to storage`);
    } catch (error) {
      console.error(`Error saving ${keyType} API key to storage:`, error);
    }
  }

  /**
   * Set API key
   */
  public setApiKey(keyType: ApiKeyType, apiKey: string): void {
    this.apiKeys[keyType] = apiKey;
    this.saveApiKeyToStorage(keyType, apiKey);

    // Notify listeners
    this.listeners[keyType].forEach(listener => {
      listener(apiKey);
    });

    console.log(`Set ${keyType} API key`);
  }

  /**
   * Get API key
   */
  public getApiKey(keyType: ApiKeyType): string {
    return this.apiKeys[keyType] || '';
  }

  /**
   * Check if API key is set
   */
  public hasApiKey(keyType: ApiKeyType): boolean {
    return !!this.apiKeys[keyType];
  }

  /**
   * Add listener for API key changes
   */
  public addListener(keyType: ApiKeyType, listener: (apiKey: string) => void): void {
    this.listeners[keyType].push(listener);

    // Call listener immediately if API key is already set
    if (this.apiKeys[keyType]) {
      listener(this.apiKeys[keyType]);
    }
  }

  /**
   * Remove listener for API key changes
   */
  public removeListener(keyType: ApiKeyType, listener: (apiKey: string) => void): void {
    this.listeners[keyType] = this.listeners[keyType].filter(l => l !== listener);
  }

  /**
   * Clear all API keys
   */
  public clearApiKeys(): void {
    Object.values(ApiKeyType).forEach(keyType => {
      this.apiKeys[keyType] = '';
      localStorage.removeItem(`${STORAGE_PREFIX}${keyType}`);

      // Notify listeners
      this.listeners[keyType].forEach(listener => {
        listener('');
      });
    });

    console.log('Cleared all API keys');
  }

  /**
   * Validate API key
   */
  public async validateApiKey(keyType: ApiKeyType, apiKey: string): Promise<boolean> {
    try {
      switch (keyType) {
        case ApiKeyType.ELEVEN_LABS:
          return await this.validateElevenLabsApiKey(apiKey);
        case ApiKeyType.HELIUS:
          return await this.validateHeliusApiKey(apiKey);
        default:
          return false;
      }
    } catch (error) {
      console.error(`Error validating ${keyType} API key:`, error);
      return false;
    }
  }

  /**
   * Validate ElevenLabs API key
   */
  private async validateElevenLabsApiKey(apiKey: string): Promise<boolean> {
    try {
      const response = await fetch('https://api.elevenlabs.io/v1/voices', {
        method: 'GET',
        headers: {
          'xi-api-key': apiKey,
          'Content-Type': 'application/json'
        }
      });

      return response.ok;
    } catch (error) {
      console.error('Error validating ElevenLabs API key:', error);
      return false;
    }
  }

  /**
   * Validate Helius API key
   *
   * Note: Currently disabled as we're not fetching blockchain data
   */
  private async validateHeliusApiKey(apiKey: string): Promise<boolean> {
    console.log('Helius API key validation is currently disabled');
    // Always return true since we're not actually validating
    return true;
  }
}

// Export a singleton instance
export const apiKeyManager = new ApiKeyManagerService();
