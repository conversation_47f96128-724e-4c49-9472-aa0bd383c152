import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Radio, Users, Clock, Mic, MessageSquare, 
  Play, Volume2, Eye, Zap 
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { spaceService } from '@/services/spaceService';
import { supabase } from '@/integrations/supabase/client';

interface LiveSpace {
  id: string;
  title: string;
  description?: string;
  host_profile_id: string;
  participant_count: number;
  max_participants: number;
  chat_enabled: boolean;
  audience_can_request_mic: boolean;
  is_recorded: boolean;
  created_at: string;
  status: string;
  settings?: any;
}

interface LiveSpacesSectionProps {
  currentUserId?: string;
  onJoinSpace?: (spaceId: string) => void;
}

export const LiveSpacesSection: React.FC<LiveSpacesSectionProps> = ({
  currentUserId,
  onJoinSpace
}) => {
  const [liveSpaces, setLiveSpaces] = useState<LiveSpace[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hostnames, setHostnames] = useState<{[key: string]: string}>({});

  useEffect(() => {
    loadLiveSpaces();
    
    // Listen for space ended events to refresh the list
    const handleSpaceEnded = () => {
      loadLiveSpaces();
    };
    
    window.addEventListener('spaceEnded', handleSpaceEnded);
    
    return () => {
      window.removeEventListener('spaceEnded', handleSpaceEnded);
    };
  }, []);

  const loadLiveSpaces = async () => {
    try {
      setIsLoading(true);
      const spaces = await spaceService.getLiveStreams();
      
      // Filter only live spaces (remove 'active' comparison as it doesn't exist)
      const activeSpaces = spaces.filter(space => 
        space.status === 'live'
      );
      
      // Transform to match LiveSpace interface with all required fields
      const transformedSpaces: LiveSpace[] = activeSpaces.map(space => ({
        id: space.id,
        title: space.title,
        description: space.description,
        host_profile_id: space.host_profile_id,
        participant_count: space.participant_count,
        max_participants: space.max_participants || 1000,
        chat_enabled: space.chat_enabled || true,
        audience_can_request_mic: space.audience_can_request_mic || true,
        is_recorded: space.is_recorded || false,
        created_at: space.created_at,
        status: space.status,
        settings: space.settings
      }));
      
      setLiveSpaces(transformedSpaces);
      
      // Fetch hostnames
      if (transformedSpaces.length > 0) {
        await fetchHostnames(transformedSpaces);
      }
    } catch (error) {
      console.error('Error loading live spaces:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchHostnames = async (spaces: LiveSpace[]) => {
    const uniqueHostIds = [...new Set(spaces.map(space => space.host_profile_id))];
    const hostnameMap: {[key: string]: string} = {};
    
    for (const hostId of uniqueHostIds) {
      try {
        const { data: profile } = await supabase
          .from('profiles')
          .select('username, wallet_address')
          .eq('id', hostId)
          .single();
          
        if (profile?.username) {
          hostnameMap[hostId] = profile.username;
        } else {
          // Try by wallet address if UUID lookup fails
          const { data: profileByWallet } = await supabase
            .from('profiles')
            .select('username, wallet_address')
            .eq('wallet_address', hostId)
            .single();
            
          if (profileByWallet?.username) {
            hostnameMap[hostId] = profileByWallet.username;
          } else {
            hostnameMap[hostId] = hostId.startsWith('0x') 
              ? `${hostId.slice(0, 6)}...${hostId.slice(-4)}`
              : hostId;
          }
        }
      } catch (error) {
        hostnameMap[hostId] = hostId.startsWith('0x') 
          ? `${hostId.slice(0, 6)}...${hostId.slice(-4)}`
          : hostId;
      }
    }
    
    setHostnames(hostnameMap);
  };

  const getHostDisplayName = (hostId: string) => {
    return hostnames[hostId] || 'Loading...';
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'music': return '🎵';
      case 'tech': return '💻';
      case 'business': return '💼';
      case 'education': return '📚';
      case 'gaming': return '🎮';
      case 'wellness': return '💚';
      case 'entertainment': return '🎬';
      default: return '💬';
    }
  };

  const handleJoinSpace = (spaceId: string) => {
    if (onJoinSpace) {
      onJoinSpace(spaceId);
    }
  };

  // Don't show loading state - just load once and show results
  if (liveSpaces.length === 0 && !isLoading) {
    return null; // Don't show anything when no live spaces
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-4">
          <Radio className="h-5 w-5 text-red-500 animate-pulse" />
          <h2 className="text-lg font-semibold">Live Spaces</h2>
        </div>
        <div className="flex gap-4 overflow-x-auto pb-2">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse min-w-[300px] flex-shrink-0">
              <CardContent className="p-4">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Radio className="h-5 w-5 text-red-500 animate-pulse" />
          <h2 className="text-lg font-semibold">Live Spaces</h2>
          <Badge variant="secondary" className="bg-red-100 text-red-700">
            {liveSpaces.length} Live
          </Badge>
        </div>
      </div>

      {/* Horizontal scrollable layout */}
      <div className="flex gap-4 overflow-x-auto pb-2 scrollbar-hide">
        {liveSpaces.map((space) => (
          <Card key={space.id} className="hover:shadow-md transition-shadow cursor-pointer border-l-4 border-l-red-500 min-w-[320px] flex-shrink-0">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-lg">
                      {getCategoryIcon(space.settings?.category || 'general')}
                    </span>
                    <Badge variant="destructive" className="animate-pulse">
                      <Radio className="h-3 w-3 mr-1" />
                      LIVE
                    </Badge>
                  </div>
                  <CardTitle className="text-lg leading-tight">{space.title}</CardTitle>
                  {space.description && (
                    <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                      {space.description}
                    </p>
                  )}
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="pt-0">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 text-sm text-gray-500">
                  <div className="flex items-center gap-1">
                    <Avatar className="w-6 h-6">
                      <AvatarFallback className="text-xs">
                        {getHostDisplayName(space.host_profile_id).slice(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <span className="truncate max-w-[80px]">{getHostDisplayName(space.host_profile_id)}</span>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    <span>{space.participant_count || 0}</span>
                  </div>
                  
                  {space.chat_enabled && (
                    <div className="flex items-center gap-1">
                      <MessageSquare className="h-4 w-4" />
                    </div>
                  )}
                  
                  {space.is_recorded && (
                    <div className="flex items-center gap-1">
                      <Eye className="h-4 w-4" />
                    </div>
                  )}
                </div>
                
                <Button 
                  onClick={() => handleJoinSpace(space.id)}
                  size="sm"
                  className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                >
                  <Volume2 className="h-4 w-4 mr-1" />
                  Join
                </Button>
              </div>
              
              {/* Tags */}
              {space.settings?.tags && space.settings.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-3">
                  {space.settings.tags.slice(0, 2).map((tag: string, index: number) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      #{tag}
                    </Badge>
                  ))}
                  {space.settings.tags.length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{space.settings.tags.length - 2}
                    </Badge>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
