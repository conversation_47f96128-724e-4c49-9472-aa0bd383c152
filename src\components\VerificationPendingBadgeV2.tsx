import React from 'react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { Clock } from 'lucide-react';

interface VerificationPendingBadgeProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showTooltip?: boolean;
}

const VerificationPendingBadgeV2: React.FC<VerificationPendingBadgeProps> = ({
  size = 'md',
  className,
  showTooltip = true
}) => {
  // Size classes - smaller sizes to match VerificationBadgeV2
  const sizeClasses = {
    sm: 'w-3 h-3',
    md: 'w-3.5 h-3.5',
    lg: 'w-4 h-4'
  };

  // Badge element - designed to match VerificationBadgeV2
  const badge = (
    <div className={cn(
      'relative flex items-center justify-center rounded-full',
      'bg-amber-500', // Amber color for pending
      sizeClasses[size],
      'ring-[1px] ring-white/50', // Subtle white outline
      'shadow-sm', // Add subtle shadow
      className
    )}>
      <Clock className="text-white z-10 w-[60%] h-[60%] stroke-[3px]" />
    </div>
  );

  // Return with or without tooltip
  if (showTooltip) {
    return (
      <TooltipProvider>
        <Tooltip delayDuration={300}>
          <TooltipTrigger asChild>
            {badge}
          </TooltipTrigger>
          <TooltipContent className="px-3 py-1.5 text-xs">
            Verification Pending
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return badge;
};

export default VerificationPendingBadgeV2;
