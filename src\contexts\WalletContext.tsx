
import React, { createContext, useState, useEffect, useContext, useCallback } from 'react';
import { WalletData, TransactionData } from '@/services/walletService';
import { toast } from '@/components/ui/sonner';
import { useAuth } from './AuthContext';
import walletManager from '@/services/walletManager';
import { supabase } from '@/integrations/supabase/client';
import walletService from '@/services/walletService';
import * as web3 from '@solana/web3.js';

interface WalletContextProps {
  wallet: WalletData | null;
  isLoading: boolean;
  error: string | null;
  balance: string;
  refreshBalance: () => void;
  transactions: TransactionData[];
  addTransaction: (transaction: TransactionData) => void;
  clearTransactions: () => void;
  loadTransactions: () => Promise<void>;
  sendTip: (recipientAddress: string, amount: string, message?: string) => Promise<TransactionData | null>;
  sendToken: (recipientAddress: string, amount: string, tokenAddress?: string, message?: string) => Promise<TransactionData | null>;
  // New Solana methods
  sendSolanaTip: (recipientAddress: string, amount: string, message?: string) => Promise<TransactionData | null>;
  sendSolanaToken: (recipientAddress: string, amount: string, tokenAddress?: string, message?: string) => Promise<TransactionData | null>;
  refreshSolanaBalance: () => void;
  solanaBalance: string;
}

const WalletContext = createContext<WalletContextProps>({
  wallet: null,
  isLoading: true,
  error: null,
  balance: '0.0',
  refreshBalance: () => {},
  transactions: [],
  addTransaction: () => {},
  clearTransactions: () => {},
  loadTransactions: () => Promise.resolve(),
  sendTip: () => Promise.resolve(null),
  sendToken: () => Promise.resolve(null),
  // New Solana methods
  sendSolanaTip: () => Promise.resolve(null),
  sendSolanaToken: () => Promise.resolve(null),
  refreshSolanaBalance: () => {},
  solanaBalance: '0.0',
});

export const WalletProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const [wallet, setWallet] = useState<WalletData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [balance, setBalance] = useState<string>('0.0');
  const [solanaBalance, setSolanaBalance] = useState<string>('0.0');
  const [transactions, setTransactions] = useState<TransactionData[]>([]);

  // Create or get the wallet for the current user
  useEffect(() => {
    const initializeWallet = async () => {
      if (!isAuthenticated || !user) {
        setWallet(null);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Get or create wallet for this user ID
        const userWallet = walletManager.getWallet(user.id);
        console.log('Initialized wallet for user', user.id, ':', userWallet.address);
        console.log('Initialized Solana wallet:', userWallet.solana?.address);
        
        // Set the wallet and get its balance
        setWallet(userWallet);
        
        // Load balance from Base network
        await refreshWalletBalance(userWallet);
        
        // Load Solana balance
        if (userWallet.solana) {
          await refreshSolanaWalletBalance(userWallet);
        }
      } catch (e) {
        console.error('Error initializing wallet:', e);
        setError('Failed to initialize wallet');
      } finally {
        setIsLoading(false);
      }
    };

    initializeWallet();
  }, [isAuthenticated, user]);

  // Refresh wallet balance from Base network
  const refreshWalletBalance = async (walletData: WalletData) => {
    try {
      const ethers = await import('ethers');
      // Use Base network RPC URL
      const provider = new ethers.providers.JsonRpcProvider('https://mainnet.base.org');
      const balanceWei = await provider.getBalance(walletData.address);
      const balanceEth = ethers.utils.formatEther(balanceWei);
      console.log(`Real balance for ${walletData.address}: ${balanceEth} ETH`);
      setBalance(balanceEth);
      
      // Also check for Base USDC
      try {
        const usdcAddress = '******************************************'; // Base USDC
        const usdcAbi = [
          "function balanceOf(address owner) view returns (uint256)",
          "function decimals() view returns (uint8)"
        ];
        const usdcContract = new ethers.Contract(usdcAddress, usdcAbi, provider);
        const usdcDecimals = await usdcContract.decimals();
        const usdcBalanceRaw = await usdcContract.balanceOf(walletData.address);
        const usdcBalance = ethers.utils.formatUnits(usdcBalanceRaw, usdcDecimals);
        console.log(`Real USDC balance for ${walletData.address}: ${usdcBalance} USDC`);
        
        // Store USDC balance in wallet data
        const updatedWallet = {...walletData};
        updatedWallet.tokenBalances = {
          ...updatedWallet.tokenBalances,
          USDC: usdcBalance
        };
        setWallet(updatedWallet);
      } catch (tokenError) {
        console.warn('Error fetching USDC balance:', tokenError);
      }
    } catch (error) {
      console.error('Error getting real wallet balance:', error);
    }
  };

  // Refresh Solana wallet balance
  const refreshSolanaWalletBalance = async (walletData: WalletData) => {
    if (!walletData.solana || !walletData.solana.address) return;
    
    try {
      // Get SOL balance
      const solBalance = await walletService.getSolanaWalletBalance(walletData);
      console.log(`Real SOL balance for ${walletData.solana.address}: ${solBalance} SOL`);
      setSolanaBalance(solBalance);
      
      // Check for Solana USDC
      try {
        const usdcAddress = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'; // Solana Mainnet USDC
        const usdcBalance = await walletService.getSolanaTokenBalance(walletData, usdcAddress);
        console.log(`Real Solana USDC balance for ${walletData.solana.address}: ${usdcBalance} USDC`);
        
        // Store USDC balance in wallet data
        const updatedWallet = {...walletData};
        if (!updatedWallet.solana.tokenBalances) {
          updatedWallet.solana.tokenBalances = {};
        }
        
        updatedWallet.solana.tokenBalances.USDC = usdcBalance;
        updatedWallet.solana.balance = solBalance;
        
        setWallet(updatedWallet);
        
        // Update wallet in manager
        walletManager.updateSolanaWallet(user!.id, {
          balance: solBalance,
          tokenBalances: updatedWallet.solana.tokenBalances
        });
      } catch (tokenError) {
        console.warn('Error fetching Solana USDC balance:', tokenError);
      }
    } catch (error) {
      console.error('Error getting real Solana wallet balance:', error);
    }
  };

  // Load transactions for the current wallet
  const loadTransactions = useCallback(async () => {
    if (!isAuthenticated || !user || !wallet) return;

    try {
      setIsLoading(true);
      console.log('Loading transactions for wallet:', wallet.address);
      
      // Fetch transactions from Supabase
      const { data: txData, error: txError } = await supabase
        .from('wallet_transactions')
        .select('*')
        .eq('user_id', user.id)
        .order('timestamp', { ascending: false });

      if (txError) {
        console.error('Error loading transactions:', txError);
        return;
      }

      if (txData && txData.length > 0) {
        // Convert to TransactionData format
        const formattedTxs: TransactionData[] = txData.map(tx => ({
          id: tx.id,
          from: tx.from_address,
          to: tx.to_address,
          amount: tx.amount,
          timestamp: new Date(tx.timestamp),
          status: tx.status as 'pending' | 'completed' | 'failed',
          txHash: tx.tx_hash,
          type: tx.type as 'tip' | 'deposit' | 'withdrawal' | 'funding' | 'token_transfer',
          message: tx.message || undefined,
          token: tx.token_address ? {
            address: tx.token_address,
            symbol: tx.token_symbol || 'UNKNOWN',
            name: tx.token_symbol || 'Unknown Token',
            decimals: tx.token_decimals || 18
          } : undefined
        }));
        
        setTransactions(formattedTxs);
      } else {
        setTransactions([]);
      }

    } catch (e) {
      console.error('Error loading transactions:', e);
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user, wallet]);

  // Load transactions when wallet changes
  useEffect(() => {
    if (wallet) {
      loadTransactions();
    } else {
      setTransactions([]);
    }
  }, [wallet, loadTransactions]);

  // Refresh wallet balance
  const refreshBalance = useCallback(async () => {
    if (!wallet) return;

    try {
      await refreshWalletBalance(wallet);
    } catch (e) {
      console.error('Error refreshing balance:', e);
    }
  }, [wallet]);

  // Refresh Solana wallet balance
  const refreshSolanaBalance = useCallback(async () => {
    if (!wallet || !wallet.solana) return;

    try {
      await refreshSolanaWalletBalance(wallet);
    } catch (e) {
      console.error('Error refreshing Solana balance:', e);
    }
  }, [wallet, user]);

  // Add a new transaction
  const addTransaction = useCallback(async (transaction: TransactionData) => {
    if (!wallet || !user) return;

    try {
      // Add to local state
      setTransactions(prevTxs => [transaction, ...prevTxs]);
      
      // Save to Supabase
      const transformedTransaction = {
        user_id: user.id,
        tx_hash: transaction.txHash,
        from_address: transaction.from,
        to_address: transaction.to,
        amount: transaction.amount,
        token_address: transaction.token?.address,
        token_symbol: transaction.token?.symbol,
        token_decimals: transaction.token?.decimals,
        status: transaction.status,
        type: transaction.type,
        message: transaction.message,
        timestamp: transaction.timestamp.toISOString(),
        chain_id: 8453 // Base Mainnet
      };
      
      const { error } = await supabase
        .from('wallet_transactions')
        .insert(transformedTransaction);
        
      if (error) {
        console.error('Error saving transaction to database:', error);
      }
      
      // Refresh wallet balance
      refreshBalance();
    } catch (e) {
      console.error('Error adding transaction:', e);
    }
  }, [wallet, user, refreshBalance]);

  // Clear transactions
  const clearTransactions = useCallback(() => {
    setTransactions([]);
  }, []);

  // Send a tip
  const sendTip = useCallback(async (
    recipientAddress: string, 
    amount: string, 
    message?: string
  ): Promise<TransactionData | null> => {
    if (!wallet || !isAuthenticated) {
      toast.error('You need to be logged in with a wallet to send tips');
      return null;
    }

    try {
      setIsLoading(true);
      console.log(`Sending tip of ${amount} ETH to ${recipientAddress}`);

      if (parseFloat(amount) > parseFloat(balance)) {
        toast.error('Insufficient balance');
        return null;
      }

      const ethers = await import('ethers');
      const provider = new ethers.providers.JsonRpcProvider('https://mainnet.base.org');
      const walletWithSigner = new ethers.Wallet(wallet.privateKey, provider);
      
      // Create transaction object
      const tx = {
        to: recipientAddress,
        value: ethers.utils.parseEther(amount)
      };
      
      // Send transaction
      const txResponse = await walletWithSigner.sendTransaction(tx);
      console.log('Transaction sent:', txResponse.hash);
      
      // Wait for confirmation
      const receipt = await txResponse.wait(1);
      console.log('Transaction confirmed:', receipt);

      const txData: TransactionData = {
        id: `tx-${Date.now()}`,
        from: wallet.address,
        to: recipientAddress,
        amount: amount,
        timestamp: new Date(),
        status: 'completed',
        txHash: txResponse.hash,
        type: 'tip',
        message: message
      };

      // Add to transaction history
      addTransaction(txData);
      
      // Refresh balance after sending tip
      refreshBalance();

      toast.success(`Successfully sent ${amount} ETH tip!`);
      return txData;
    } catch (error: any) {
      console.error('Error sending tip:', error);
      toast.error(`Error sending tip: ${error.message || 'Transaction failed'}`);
      
      const failedTx: TransactionData = {
        id: `tx-${Date.now()}`,
        from: wallet.address,
        to: recipientAddress,
        amount: amount,
        timestamp: new Date(),
        status: 'failed',
        type: 'tip',
        message: message
      };
      
      addTransaction(failedTx);
      return failedTx;
    } finally {
      setIsLoading(false);
    }
  }, [wallet, isAuthenticated, balance, addTransaction, refreshBalance]);

  // Send tokens - new function to allow users to transfer tokens to external wallets
  const sendToken = useCallback(async (
    recipientAddress: string, 
    amount: string,
    tokenAddress?: string,
    message?: string
  ): Promise<TransactionData | null> => {
    if (!wallet || !isAuthenticated) {
      toast.error('You need to be logged in with a wallet to send tokens');
      return null;
    }

    try {
      setIsLoading(true);
      
      if (!tokenAddress) {
        // If no token address provided, send ETH
        console.log(`Sending ${amount} ETH to ${recipientAddress}`);
        
        if (parseFloat(amount) > parseFloat(balance)) {
          toast.error('Insufficient ETH balance');
          return null;
        }
        
        // Use the existing sendTip function for ETH transfers
        return await sendTip(recipientAddress, amount, message);
      } else {
        // Send ERC-20 token
        console.log(`Sending ${amount} tokens from ${wallet.address} to ${recipientAddress}`);
        
        // For token transfers, we use walletService's sendToken function
        const tokenBalances = wallet.tokenBalances || {};
        const tokenSymbol = Object.keys(tokenBalances).find(symbol => 
          walletService.commonTokens[symbol]?.address.toLowerCase() === tokenAddress.toLowerCase()
        );
        
        if (!tokenSymbol || !tokenBalances[tokenSymbol]) {
          toast.error('Token not found in your wallet');
          return null;
        }
        
        const tokenBalance = tokenBalances[tokenSymbol];
        if (parseFloat(amount) > parseFloat(tokenBalance)) {
          toast.error(`Insufficient ${tokenSymbol} balance`);
          return null;
        }
        
        const ethers = await import('ethers');
        const provider = new ethers.providers.JsonRpcProvider('https://mainnet.base.org');
        const walletWithSigner = new ethers.Wallet(wallet.privateKey, provider);
        
        const tokenData = walletService.commonTokens[tokenSymbol];
        
        // Standard ERC20 ABI for transfer function
        const tokenAbi = [
          "function transfer(address to, uint amount) returns (bool)",
          "function balanceOf(address owner) view returns (uint256)",
          "function decimals() view returns (uint8)"
        ];
        
        const tokenContract = new ethers.Contract(tokenAddress, tokenAbi, walletWithSigner);
        
        // Get decimals
        const decimals = await tokenContract.decimals();
        
        // Convert amount to token units
        const amountInTokenUnits = ethers.utils.parseUnits(amount, decimals);
        
        // Send transaction
        const txResponse = await tokenContract.transfer(recipientAddress, amountInTokenUnits);
        console.log('Token transaction sent:', txResponse.hash);
        
        // Wait for confirmation
        const receipt = await txResponse.wait(1);
        console.log('Token transaction confirmed:', receipt);
        
        const txData: TransactionData = {
          id: `tx-${Date.now()}`,
          from: wallet.address,
          to: recipientAddress,
          amount: amount,
          timestamp: new Date(),
          status: 'completed',
          txHash: txResponse.hash,
          type: 'token_transfer',
          message: message,
          token: {
            address: tokenAddress,
            symbol: tokenSymbol,
            name: tokenData.name,
            decimals: tokenData.decimals
          }
        };

        // Add to transaction history
        addTransaction(txData);
        
        // Refresh balance after sending token
        refreshBalance();

        toast.success(`Successfully sent ${amount} ${tokenSymbol}!`);
        return txData;
      }
    } catch (error: any) {
      console.error('Error sending token:', error);
      toast.error(`Error sending token: ${error.message || 'Transaction failed'}`);
      
      const failedTx: TransactionData = {
        id: `tx-${Date.now()}`,
        from: wallet.address,
        to: recipientAddress,
        amount: amount,
        timestamp: new Date(),
        status: 'failed',
        type: 'token_transfer',
        message: message,
        token: tokenAddress ? {
          address: tokenAddress,
          symbol: 'TOKEN',
          name: 'Unknown Token',
          decimals: 18
        } : undefined
      };
      
      addTransaction(failedTx);
      return failedTx;
    } finally {
      setIsLoading(false);
    }
  }, [wallet, isAuthenticated, balance, addTransaction, refreshBalance, sendTip]);

  // Send a Solana tip
  const sendSolanaTip = useCallback(async (
    recipientAddress: string, 
    amount: string, 
    message?: string
  ): Promise<TransactionData | null> => {
    if (!wallet || !wallet.solana || !isAuthenticated) {
      toast.error('You need to be logged in with a Solana wallet to send tips');
      return null;
    }

    try {
      setIsLoading(true);
      console.log(`Sending Solana tip of ${amount} SOL to ${recipientAddress}`);

      // Validate balance
      if (parseFloat(amount) > parseFloat(solanaBalance)) {
        toast.error('Insufficient SOL balance');
        return null;
      }

      // Send transaction using our sendSol function
      const txData = await walletService.sendSol(wallet, recipientAddress, amount, message);
      
      // Add to transaction history
      addTransaction(txData);
      
      // Refresh balance after sending tip
      refreshSolanaBalance();

      if (txData.status === 'completed') {
        toast.success(`Successfully sent ${amount} SOL tip!`);
      } else {
        toast.error(`Failed to send SOL tip: ${txData.status}`);
      }
      
      return txData;
    } catch (error: any) {
      console.error('Error sending SOL tip:', error);
      toast.error(`Error sending SOL tip: ${error.message || 'Transaction failed'}`);
      
      const failedTx: TransactionData = {
        id: `tx-sol-${Date.now()}`,
        from: wallet.solana!.address,
        to: recipientAddress,
        amount: amount,
        timestamp: new Date(),
        status: 'failed',
        type: 'tip',
        message: message,
        blockchain: 'solana'
      };
      
      addTransaction(failedTx);
      return failedTx;
    } finally {
      setIsLoading(false);
    }
  }, [wallet, isAuthenticated, solanaBalance, addTransaction, refreshSolanaBalance]);

  // Send Solana tokens (like USDC)
  const sendSolanaToken = useCallback(async (
    recipientAddress: string,
    amount: string,
    tokenAddress?: string,
    message?: string
  ): Promise<TransactionData | null> => {
    if (!wallet || !wallet.solana || !isAuthenticated) {
      toast.error('You need to be logged in with a Solana wallet to send tokens');
      return null;
    }

    try {
      setIsLoading(true);
      
      if (!tokenAddress) {
        // If no token address provided, send SOL
        return await sendSolanaTip(recipientAddress, amount, message);
      } else {
        // For Solana token transfers, we'd implement the SPL token transfer logic here
        // This is simplified for now and would need expansion
        toast.error('Solana token transfers not fully implemented yet');
        
        const failedTx: TransactionData = {
          id: `tx-sol-token-${Date.now()}`,
          from: wallet.solana!.address,
          to: recipientAddress,
          amount: amount,
          timestamp: new Date(),
          status: 'failed',
          type: 'token_transfer',
          message: message,
          blockchain: 'solana',
          token: {
            address: tokenAddress,
            symbol: 'TOKEN',
            name: 'Unknown Token',
            decimals: 6,
            blockchain: 'solana'
          }
        };
        
        addTransaction(failedTx);
        return failedTx;
      }
    } catch (error: any) {
      console.error('Error sending Solana token:', error);
      toast.error(`Error sending token: ${error.message || 'Transaction failed'}`);
      
      const failedTx: TransactionData = {
        id: `tx-sol-token-${Date.now()}`,
        from: wallet.solana!.address,
        to: recipientAddress,
        amount: amount,
        timestamp: new Date(),
        status: 'failed',
        type: 'token_transfer',
        message: message,
        blockchain: 'solana',
        token: tokenAddress ? {
          address: tokenAddress,
          symbol: 'TOKEN',
          name: 'Unknown Token',
          decimals: 6,
          blockchain: 'solana'
        } : undefined
      };
      
      addTransaction(failedTx);
      return failedTx;
    } finally {
      setIsLoading(false);
    }
  }, [wallet, isAuthenticated, solanaBalance, addTransaction, sendSolanaTip]);

  return (
    <WalletContext.Provider
      value={{
        wallet,
        isLoading,
        error,
        balance,
        refreshBalance,
        transactions,
        addTransaction,
        clearTransactions,
        loadTransactions,
        sendTip,
        sendToken,
        // New Solana methods
        sendSolanaTip,
        sendSolanaToken,
        refreshSolanaBalance,
        solanaBalance
      }}
    >
      {children}
    </WalletContext.Provider>
  );
};

export const useWallet = () => useContext(WalletContext);

export default WalletContext;
