import React, { useState, useRef, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Play, 
  Volume2, 
  Lock, 
  Calendar, 
  Clock, 
  FileText,
  Trash2,
  Unlock,
  Image as ImageIcon,
  DollarSign
} from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';
import { toast } from '@/components/ui/sonner';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import JournalTipModal from './JournalTipModal';
import { getJournalTipTotal } from '@/services/tipService';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  <PERSON><PERSON><PERSON><PERSON>ogHeader,
  AlertDialog<PERSON>itle,
} from '@/components/ui/alert-dialog';

// Define the JournalEntry type
export interface JournalEntry {
  id: string;
  title: string;
  description?: string;
  audioUrl: string;
  transcript: string;
  createdAt: Date;
  userAddress: string;
  isPrivate?: boolean;
  unlockCondition?: {
    type: 'time' | 'token' | 'event' | 'date' | 'password';
    value: string;
    unlockDate?: Date;
    tokenAddress?: string;
    eventId?: string;
  };
  isUnlocked?: boolean;
  isLocked?: boolean;
  duration: number;
  media?: Array<{
    id: string;
    url: string;
    type: 'image' | 'video';
  }>;
}

interface JournalCardProps {
  journal: JournalEntry;
  isOwner: boolean;
}

const JournalCard: React.FC<JournalCardProps> = ({ journal, isOwner }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isTipModalOpen, setIsTipModalOpen] = useState(false);
  const [tipTotal, setTipTotal] = useState<{ total: number; tipsByCurrency: Record<string, number> }>({ total: 0, tipsByCurrency: {} });
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const navigate = useNavigate();
  const { user } = useAuth();

  // Fetch tip totals on mount
  useEffect(() => {
    const fetchTipTotal = async () => {
      const tipData = await getJournalTipTotal(journal.id);
      setTipTotal(tipData);
    };
    fetchTipTotal();
  }, [journal.id]);

  // Format time as MM:SS
  const formatTime = (seconds: number = 0) => {
    const mins = Math.floor(seconds / 60).toString().padStart(2, '0');
    const secs = Math.floor(seconds % 60).toString().padStart(2, '0');
    return `${mins}:${secs}`;
  };

  // Handle play/pause
  const togglePlay = () => {
    if (isPlaying) {
      if (audioRef.current) {
        audioRef.current.pause();
      }
      setIsPlaying(false);
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }
    } else {
      if (audioRef.current) {
        audioRef.current.play();
        setIsPlaying(true);
        progressIntervalRef.current = setInterval(() => {
          if (audioRef.current) {
            setCurrentTime(audioRef.current.currentTime);
          }
        }, 100);
      }
    }
  };

  // Handle audio end
  const handleAudioEnd = () => {
    setIsPlaying(false);
    setCurrentTime(0);
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
  };

  // Handle delete
  const handleDelete = () => {
    // In a real implementation, this would call a service to delete the journal
    // For now, we'll just show a toast
    toast.success('Journal deleted');
    setIsDeleteDialogOpen(false);
  };

  // Calculate progress percentage
  const progressPercentage = journal.duration ? (currentTime / journal.duration) * 100 : 0;

  // Check if journal is locked
  const isLocked = journal.isLocked || (journal.isUnlocked === false);

  // Navigate to journal detail
  const goToJournalDetail = () => {
    navigate(`/journal/${journal.id}`);
  };

  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow cursor-pointer" onClick={goToJournalDetail}>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-base">{journal.title}</CardTitle>
          <div className="flex items-center gap-1">
            {isLocked && (
              <Badge variant="outline" className="bg-amber-500/10 text-amber-500 border-amber-500/20">
                <Lock size={12} className="mr-1" />
                <span className="text-[10px]">Locked</span>
              </Badge>
            )}
            {journal.isPrivate && (
              <Badge variant="outline" className="bg-secondary-foreground/20">
                <Lock size={12} className="mr-1" />
                <span className="text-[10px]">Private</span>
              </Badge>
            )}
          </div>
        </div>
        <CardDescription className="flex items-center text-xs">
          <Calendar size={12} className="mr-1" />
          {formatDistanceToNow(journal.createdAt, { addSuffix: true })}
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {/* Audio Player */}
        <div className="relative mb-3 bg-muted/50 rounded-lg p-2 flex items-center">
          <Button
            size="icon"
            variant="ghost"
            className={`h-8 w-8 rounded-full flex-shrink-0 ${isPlaying ? 'bg-voicechain-purple' : 'bg-accent/30'}`}
            onClick={(e) => {
              e.stopPropagation();
              togglePlay();
            }}
          >
            {isPlaying ? <Volume2 size={16} /> : <Play size={16} />}
          </Button>

          <div className="ml-2 flex-1">
            <div className="h-1.5 bg-muted rounded-full overflow-hidden">
              <div
                className="h-full bg-voicechain-purple"
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
          </div>

          <span className="ml-2 text-xs font-mono">
            {formatTime(currentTime)} / {formatTime(journal.duration)}
          </span>

          <audio
            ref={audioRef}
            src={journal.audioUrl}
            onEnded={handleAudioEnd}
          />
        </div>

        {/* Transcript Preview (truncated) */}
        {journal.transcript && !isLocked && (
          <div className="bg-background rounded-lg p-2 mb-2">
            <p className="text-xs line-clamp-2">{journal.transcript}</p>
          </div>
        )}

        {/* Media Preview */}
        {journal.media && journal.media.length > 0 && !isLocked && (
          <div className="mb-2">
            <div className="flex items-center gap-1 mb-1">
              <ImageIcon size={14} className="text-muted-foreground" />
              <p className="text-xs text-muted-foreground">
                Media ({journal.media.length})
              </p>
            </div>
            <div className="grid grid-cols-2 gap-1 h-20">
              {journal.media.slice(0, 2).map((mediaItem, index) => (
                <div
                  key={mediaItem.id}
                  className="relative rounded-lg overflow-hidden border border-border aspect-square"
                >
                  {mediaItem.type === 'image' ? (
                    <img
                      src={mediaItem.url}
                      alt={`Media ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-muted flex items-center justify-center">
                      <Play size={20} />
                    </div>
                  )}
                </div>
              ))}
              {journal.media.length > 2 && (
                <div className="absolute bottom-2 right-2 bg-background/80 rounded-full px-2 py-0.5 text-xs">
                  +{journal.media.length - 2} more
                </div>
              )}
            </div>
          </div>
        )}

        {/* Tip Display */}
        {tipTotal.total > 0 && (
          <div className="mb-2">
            <div className="flex items-center gap-2 text-xs">
              <DollarSign size={12} className="text-green-600" />
              <span className="text-muted-foreground">
                Received {Object.entries(tipTotal.tipsByCurrency).map(([currency, amount]) => 
                  `${amount} ${currency}`
                ).join(', ')} in tips
              </span>
            </div>
          </div>
        )}

        {/* Unlock Condition */}
        {isLocked && journal.unlockCondition && (
          <div className="bg-secondary/50 rounded-lg p-2 text-xs">
            {journal.unlockCondition.type === 'time' || journal.unlockCondition.type === 'date' ? (
              <div className="flex items-center gap-1">
                <Calendar size={12} />
                <span>
                  Unlocks on {journal.unlockCondition.unlockDate ? 
                    format(journal.unlockCondition.unlockDate, 'PPP') : 
                    format(new Date(journal.unlockCondition.value), 'PPP')}
                </span>
              </div>
            ) : journal.unlockCondition.type === 'token' ? (
              <div className="flex items-center gap-1">
                <Lock size={12} />
                <span>Requires token ownership to unlock</span>
              </div>
            ) : (
              <div className="flex items-center gap-1">
                <Lock size={12} />
                <span>Locked content</span>
              </div>
            )}
          </div>
        )}
      </CardContent>

      {/* Action Buttons Footer */}
      <CardFooter className="pt-0 pb-3 px-6">
        <div className="flex justify-between w-full">
          {/* Tip Button - only show if not owner and user is authenticated */}
          {!isOwner && user && (
            <Button
              variant="ghost"
              size="sm"
              className="text-xs text-muted-foreground hover:text-foreground"
              onClick={(e) => {
                e.stopPropagation();
                setIsTipModalOpen(true);
              }}
            >
              <DollarSign size={14} className="mr-1" />
              Tip Creator
            </Button>
          )}
          
          {/* Owner Actions */}
          {isOwner && (
            <div className="flex justify-end w-full">
              <Button
                variant="ghost"
                size="sm"
                className="text-xs text-destructive"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsDeleteDialogOpen(true);
                }}
              >
                <Trash2 size={14} className="mr-1" />
                Delete
              </Button>
            </div>
          )}
        </div>
      </CardFooter>

      {/* Journal Tip Modal */}
      <JournalTipModal
        isOpen={isTipModalOpen}
        onClose={() => setIsTipModalOpen(false)}
        journalId={journal.id}
        journalOwnerId={journal.userAddress}
        journalTitle={journal.title}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent onClick={(e) => e.stopPropagation()}>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete your voice journal entry. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
};

export default JournalCard;
