
import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import { useChannels } from '@/contexts/ChannelContext';
import { useNotifications } from '@/contexts/NotificationContext';
import AudioRecorder from './AudioRecorder';
import { toast } from '@/components/ui/sonner';
import { Play, Volume2, MessageSquareQuote, Square } from 'lucide-react';
import { Notification } from '@/contexts/NotificationContext';
import { MediaFile } from '@/types/media';

interface SummonResponseModalProps {
  isOpen: boolean;
  onClose: () => void;
  notification: Notification;
}

const SummonResponseModal: React.FC<SummonResponseModalProps> = ({
  isOpen,
  onClose,
  notification,
}) => {
  const { getProfileByAddress } = useProfiles();
  const { addMessageToChannel, getChannelById } = useChannels();
  const { markAsRead, addNotification } = useNotifications();
  const navigate = useNavigate();

  const [isRecording, setIsRecording] = useState(false);
  const [recordingComplete, setRecordingComplete] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [transcript, setTranscript] = useState('');
  const [audioDuration, setAudioDuration] = useState(0);

  // For playing the summon audio
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Get summoner profile
  const summonerProfile = getProfileByAddress(notification.from_address);

  // Get channel if available
  const channel = notification.data?.channelId
    ? getChannelById(notification.data.channelId)
    : null;

  // Mark notification as read when opened
  useEffect(() => {
    if (isOpen) {
      markAsRead(notification.id);
    }

    return () => {
      // Clean up audio playback
      if (audioRef.current) {
        audioRef.current.pause();
      }
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, [isOpen, notification.id, markAsRead]);

  const handleRecordingStart = () => {
    setIsRecording(true);
    setRecordingComplete(false);
  };

  const handleRecordingComplete = (blob: Blob, text: string, duration?: number, media?: MediaFile[]) => {
    setAudioBlob(blob);
    setTranscript(text);
    setAudioDuration(duration || 0);
    setIsRecording(false);
    setRecordingComplete(true);
  };

  const handleSendResponse = () => {
    if (!audioBlob || !transcript) {
      toast.error('Please record a voice response first');
      return;
    }

    // Create object URL for the audio blob
    const audioUrl = URL.createObjectURL(audioBlob);

    // Get the context from the notification
    const context = notification.data?.context || 'feed';
    const originalMessageId = notification.data?.messageId || notification.message_id;

    if (channel && notification.data?.channelId) {
      // Add the response as a message to the channel
      addMessageToChannel(channel.id, {
        id: Date.now().toString(),
        audioUrl,
        transcript: `@${summonerProfile.username} ${transcript}`,
        userAddress: localStorage.getItem('connectedAccount') || '',
        timestamp: new Date(),
        duration: audioDuration,
        replies: []
        // Use parent_id in the Supabase schema
      });

      toast.success(`Response sent to ${summonerProfile.displayName} in ${channel.name}`);
    } else {
      // If no channel, create a direct response
      // In a real implementation, this would add the response to the feed or profile
      // For now, we'll just send a notification back

      // Create a notification for the summoner
      const currentAccount = localStorage.getItem('connectedAccount');
      if (currentAccount) {
        addNotification(
          'reply',
          currentAccount,
          notification.from_address,
          originalMessageId,
          {
            text: transcript,
            replyId: `reply-${Date.now()}`,
            context: context
          }
        );
      }

      toast.success(`Response sent to ${summonerProfile.displayName}`);
    }

    // Close the modal and navigate back to the home page
    onClose();
    navigate('/');
  };

  // Toggle play/pause for the summon audio
  const togglePlay = () => {
    if (!audioRef.current) return;

    if (isPlaying) {
      audioRef.current.pause();
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }
    } else {
      audioRef.current.play().catch(error => {
        console.error("Error playing audio:", error);
        toast.error("Could not play audio. Please try again.");
      });
      // Update progress every 100ms
      progressIntervalRef.current = setInterval(() => {
        if (audioRef.current) {
          setCurrentTime(audioRef.current.currentTime);
        }
      }, 100);
    }

    setIsPlaying(!isPlaying);
  };

  // Handle audio end
  const handleAudioEnd = () => {
    setIsPlaying(false);
    setCurrentTime(0);
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
  };

  // Format time as MM:SS
  const formatTime = (seconds: number = 0) => {
    const mins = Math.floor(seconds / 60).toString().padStart(2, '0');
    const secs = Math.floor(seconds % 60).toString().padStart(2, '0');
    return `${mins}:${secs}`;
  };

  // Calculate progress percentage
  const progressPercentage = notification.data?.summonDuration
    ? (currentTime / notification.data.summonDuration) * 100
    : 0;

  if (!summonerProfile) {
    return null;
  }

  const closeAndNavigateToHome = () => {
    onClose();
    navigate('/');
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) {
        closeAndNavigateToHome();
      }
    }}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquareQuote size={18} className="text-voicechain-purple" />
            Voice Summon from {summonerProfile.displayName}
          </DialogTitle>
          <DialogDescription>
            {summonerProfile.displayName} has asked you a question. Listen and respond with your voice.
          </DialogDescription>
        </DialogHeader>

        <div className="flex items-center space-x-4 py-2">
          <Avatar className="h-10 w-10">
            <AvatarImage src={summonerProfile.profileImageUrl} alt={summonerProfile.displayName} />
            <AvatarFallback className="bg-voicechain-accent text-white">
              {summonerProfile.displayName.charAt(0)}
            </AvatarFallback>
          </Avatar>
          <div>
            <h4 className="font-medium">{summonerProfile.displayName}</h4>
            <p className="text-sm text-muted-foreground">@{summonerProfile.username}</p>
          </div>
        </div>

        {/* Summon Question */}
        <div className="bg-secondary/50 rounded-lg p-4 mb-4">
          <p className="text-sm font-medium mb-2">Their question:</p>
          <p className="text-sm mb-3">{notification.data?.summonQuestion || notification.data?.text}</p>

          {/* Audio Player for the summon */}
          {notification.data?.summonAudioUrl && (
            <div className="relative bg-muted/50 rounded-lg p-3 flex items-center">
              <Button
                size="icon"
                variant="ghost"
                className={`h-8 w-8 rounded-full flex-shrink-0 ${isPlaying ? 'bg-voicechain-purple' : 'bg-accent/30'}`}
                onClick={togglePlay}
              >
                {isPlaying ? <Square size={16} /> : <Play size={16} />}
              </Button>

              <div className="ml-2 flex-1">
                <div className="h-1.5 bg-muted rounded-full overflow-hidden">
                  <div
                    className="h-full bg-voicechain-purple"
                    style={{ width: `${progressPercentage}%` }}
                  />
                </div>
              </div>

              <span className="ml-2 text-xs font-mono">
                {formatTime(currentTime)} / {formatTime(notification.data?.summonDuration)}
              </span>

              <audio
                ref={audioRef}
                src={notification.data?.summonAudioUrl}
                onEnded={handleAudioEnd}
                preload="auto"
              />
            </div>
          )}
        </div>

        {/* Response Recorder */}
        <div className="bg-secondary/50 rounded-lg p-4">
          <p className="text-sm font-medium mb-2">Your response:</p>
          <AudioRecorder
            onRecordingStart={handleRecordingStart}
            onRecordingComplete={handleRecordingComplete}
            placeholder="Record your response..."
          />

          {recordingComplete && transcript && (
            <div className="mt-4 p-3 bg-background rounded-md">
              <p className="text-sm">{transcript}</p>
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-between sm:justify-between mt-4">
          <Button
            variant="outline"
            onClick={closeAndNavigateToHome}
          >
            Cancel
          </Button>
          <Button
            disabled={!recordingComplete || isRecording}
            onClick={handleSendResponse}
            className="bg-voicechain-purple hover:bg-voicechain-accent"
          >
            Send Response
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SummonResponseModal;
