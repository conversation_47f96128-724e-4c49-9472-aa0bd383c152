import React from 'react';
import { NotificationProvider } from '@/contexts/NotificationContext';
import { ChannelProvider } from '@/contexts/ChannelContext';
import { ChainVoiceProvider } from '@/contexts/ChainVoiceContext';
import { AudioProvider } from '@/contexts/AudioContext';

interface ContextProvidersProps {
  children: React.ReactNode;
  userAddress: string;
}

/**
 * ContextProviders component that wraps additional context providers
 */
export const ContextProviders: React.FC<ContextProvidersProps> = ({
  children,
  userAddress
}) => {
  return (
    <NotificationProvider>
      <ChannelProvider userAddress={userAddress}>
        <AudioProvider>
          <ChainVoiceProvider>
            {children}
          </ChainVoiceProvider>
        </AudioProvider>
      </ChannelProvider>
    </NotificationProvider>
  );
};
