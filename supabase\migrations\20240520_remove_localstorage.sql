-- Create user_settings table for app settings
CREATE TABLE IF NOT EXISTS user_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  key TEXT NOT NULL,
  user_id TEXT NOT NULL,
  value JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(key, user_id)
);

-- Add RLS policies for user_settings
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read their own settings"
  ON user_settings
  FOR SELECT
  USING (user_id = auth.uid() OR user_id = 'global');

CREATE POLICY "Users can insert their own settings"
  ON user_settings
  FOR INSERT
  WITH CHECK (user_id = auth.uid() OR user_id = 'global');

CREATE POLICY "Users can update their own settings"
  ON user_settings
  FOR UPDATE
  USING (user_id = auth.uid() OR user_id = 'global');

-- Create audio_mappings table for audio URL mappings
CREATE TABLE IF NOT EXISTS audio_mappings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  post_id TEXT NOT NULL UNIQUE,
  audio_url TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for audio_mappings
ALTER TABLE audio_mappings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read audio mappings"
  ON audio_mappings
  FOR SELECT
  USING (true);

CREATE POLICY "Authenticated users can insert audio mappings"
  ON audio_mappings
  FOR INSERT
  WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can update audio mappings"
  ON audio_mappings
  FOR UPDATE
  USING (auth.role() = 'authenticated');

-- Create wallet_data table for wallet information
CREATE TABLE IF NOT EXISTS wallet_data (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id TEXT NOT NULL UNIQUE,
  wallet_data JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for wallet_data
ALTER TABLE wallet_data ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read their own wallet data"
  ON wallet_data
  FOR SELECT
  USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own wallet data"
  ON wallet_data
  FOR INSERT
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own wallet data"
  ON wallet_data
  FOR UPDATE
  USING (user_id = auth.uid());

-- Create wallet_security table for wallet security settings
CREATE TABLE IF NOT EXISTS wallet_security (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id TEXT NOT NULL UNIQUE,
  is_locked BOOLEAN NOT NULL DEFAULT false,
  has_backup BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for wallet_security
ALTER TABLE wallet_security ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read their own wallet security"
  ON wallet_security
  FOR SELECT
  USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own wallet security"
  ON wallet_security
  FOR INSERT
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own wallet security"
  ON wallet_security
  FOR UPDATE
  USING (user_id = auth.uid());

-- Create transcripts table for voice transcripts
CREATE TABLE IF NOT EXISTS transcripts (
  id TEXT PRIMARY KEY,
  text TEXT NOT NULL,
  user_id TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for transcripts
ALTER TABLE transcripts ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read transcripts"
  ON transcripts
  FOR SELECT
  USING (true);

CREATE POLICY "Authenticated users can insert transcripts"
  ON transcripts
  FOR INSERT
  WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Users can update their own transcripts"
  ON transcripts
  FOR UPDATE
  USING (user_id = auth.uid());
