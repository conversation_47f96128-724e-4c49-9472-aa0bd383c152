import React, { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/sonner';

interface UniversalProfileWrapperProps {
  children: React.ReactNode;
}

export const UniversalProfileWrapper: React.FC<UniversalProfileWrapperProps> = ({ children }) => {
  const [isReady, setIsReady] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    ensureProfile();
  }, []);

  const ensureProfile = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        setIsLoading(false);
        return;
      }

      // Use the universal function to ensure profile exists
      const { data: profileData, error } = await supabase
        .rpc('get_or_create_user_profile', { p_user_id: user.id });

      if (error) {
        console.error('Error ensuring profile:', error);
        toast('Error setting up profile: ' + error.message);
      } else if (profileData && profileData.length > 0) {
        setIsReady(true);
      }
    } catch (error) {
      console.error('Error in ensureProfile:', error);
      toast('Failed to set up profile');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!isReady) {
    return (
      <div className="text-center p-4">
        <p className="text-muted-foreground">Setting up your profile...</p>
      </div>
    );
  }

  return <>{children}</>;
};