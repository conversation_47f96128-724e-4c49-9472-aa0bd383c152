import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with environment variables or hardcoded values
// For TypeScript to recognize import.meta.env, we need to add a declaration
// These values are hardcoded for deployment but can be overridden with environment variables
const SUPABASE_URL = "https://jcltjkaumevuycntdmds.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpjbHRqa2F1bWV2dXljbnRkbWRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTkwMDEsImV4cCI6MjA2MTg3NTAwMX0.LqQSk0NwB1mDB4Cznn0iQSrYvsAAWyUsaj8reo_etqM";

console.log('Initializing Supabase client with URL:', SUPABASE_URL);
console.log('API Key (first 10 chars):', SUPABASE_PUBLISHABLE_KEY.substring(0, 10) + '...');

export const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

// User management functions
export const getUsers = async (page = 1, limit = 10) => {
  const start = (page - 1) * limit;
  const end = start + limit - 1;

  const { data, error, count } = await supabase
    .from('profiles')
    .select('*', { count: 'exact' })
    .range(start, end);

  if (error) {
    console.error('Error fetching users:', error);
    throw error;
  }

  return { data, count };
};

export const getUserById = async (userId: string) => {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();

  if (error) {
    console.error('Error fetching user:', error);
    throw error;
  }

  return data;
};

export const suspendUser = async (userId: string, reason: string) => {
  const { error } = await supabase
    .from('profiles')
    .update({
      is_suspended: true,
      suspension_reason: reason,
      suspended_at: new Date().toISOString()
    })
    .eq('id', userId);

  if (error) {
    console.error('Error suspending user:', error);
    throw error;
  }

  return true;
};

export const unsuspendUser = async (userId: string) => {
  const { error } = await supabase
    .from('profiles')
    .update({
      is_suspended: false,
      suspension_reason: null,
      suspended_at: null
    })
    .eq('id', userId);

  if (error) {
    console.error('Error unsuspending user:', error);
    throw error;
  }

  return true;
};

// Verification management functions
export const getVerificationApplications = async (status = 'pending', page = 1, limit = 10) => {
  const start = (page - 1) * limit;
  const end = start + limit - 1;

  const query = supabase
    .from('verification_applications')
    .select('*, profiles!inner(*)', { count: 'exact' });

  if (status !== 'all') {
    query.eq('status', status);
  }

  const { data, error, count } = await query.range(start, end);

  if (error) {
    console.error('Error fetching verification applications:', error);
    throw error;
  }

  return { data, count };
};

export const updateVerificationStatus = async (applicationId: string, status: 'approved' | 'rejected', notes?: string) => {
  // Get the current user's ID
  const { data: { user } } = await supabase.auth.getUser();

  // First, get the application to get the user address and verification type
  const { data: application, error: fetchError } = await supabase
    .from('verification_applications')
    .select('*')
    .eq('id', applicationId)
    .single();

  if (fetchError) {
    console.error('Error fetching verification application:', fetchError);
    throw fetchError;
  }

  console.log('Updating verification application:', {
    id: applicationId,
    user_address: application.user_address,
    type: application.type,
    status,
    notes
  });

  // Update the application status - this will trigger the database trigger to update the profile
  const { error: updateError } = await supabase
    .from('verification_applications')
    .update({
      status,
      reviewed_at: new Date().toISOString(),
      admin_notes: notes || null,
      reviewed_by: user?.id || null
    })
    .eq('id', applicationId);

  if (updateError) {
    console.error('Error updating verification status:', updateError);
    throw updateError;
  }

  // For debugging purposes, check if the profile was updated after a short delay
  setTimeout(async () => {
    try {
      // Check if the user_address is a UUID
      let isUuid = false;
      try {
        // Try to parse as UUID
        const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        isUuid = uuidPattern.test(application.user_address);
      } catch (e) {
        isUuid = false;
      }

      let query = supabase
        .from('profiles')
        .select('is_verified, verification_type, verified_at');

      // Use different query approaches based on whether it's a UUID or not
      if (isUuid) {
        query = query.eq('id', application.user_address);
      } else {
        // For Ethereum addresses, use case-insensitive text comparison
        query = query.ilike('wallet_address', application.user_address);
      }

      const { data: profileData, error: profileError } = await query.maybeSingle();

      if (profileError) {
        console.error('Error checking profile after verification update:', profileError);
      } else if (profileData) {
        console.log('Profile after verification update:', profileData);
      } else {
        console.warn('Profile not found after verification update');
      }
    } catch (checkError) {
      console.error('Error checking profile after verification update:', checkError);
    }
  }, 1000); // Check after 1 second to allow the trigger to complete

  if (status === 'approved') {
    // Get the application to get user details
    const { data: application, error: fetchError } = await supabase
      .from('verification_applications')
      .select('*')
      .eq('id', applicationId)
      .single();

    if (fetchError) {
      console.error('Error fetching application:', fetchError);
      throw fetchError;
    }

    // Update the user's profile with verification status
    try {
      console.log(`Updating verification status for user ${application.user_address} to ${application.type}`);

      // Check if the user_address is a UUID
      let isUuid = false;
      try {
        // Try to parse as UUID
        const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        isUuid = uuidPattern.test(application.user_address);
      } catch (e) {
        isUuid = false;
      }

      // First check if the profile exists
      let profileQuery = supabase.from('profiles').select('id');

      if (isUuid) {
        profileQuery = profileQuery.eq('id', application.user_address);
      } else {
        profileQuery = profileQuery.ilike('wallet_address', application.user_address);
      }

      const { data: profileData, error: profileCheckError } = await profileQuery.single();

      if (profileCheckError) {
        console.error('Error checking profile existence:', profileCheckError);
        throw new Error(`Profile not found for user ${application.user_address}`);
      }

      // Try to update with all fields
      let updateQuery = supabase
        .from('profiles')
        .update({
          is_verified: true,
          verification_type: application.type,
          verified_at: new Date().toISOString()
        });

      if (isUuid) {
        updateQuery = updateQuery.eq('id', application.user_address);
      } else {
        updateQuery = updateQuery.ilike('wallet_address', application.user_address);
      }

      const { error: profileError } = await updateQuery;

      if (profileError) {
        console.error('Error updating profile with verification status:', profileError);
        throw profileError;
      }

      console.log(`Successfully updated verification status for user ${application.user_address}`);

      // Double-check that the update was successful
      let checkQuery = supabase
        .from('profiles')
        .select('is_verified, verification_type, verified_at');

      if (isUuid) {
        checkQuery = checkQuery.eq('id', application.user_address);
      } else {
        checkQuery = checkQuery.ilike('wallet_address', application.user_address);
      }

      const { data: updatedProfile, error: checkError } = await checkQuery.single();

      if (checkError) {
        console.error('Error checking updated profile:', checkError);
      } else {
        console.log('Updated profile verification status:', updatedProfile);
      }
    } catch (profileUpdateError: any) {
      // If the error is about is_verified or verified_at columns, try with just verification_type
      if (profileUpdateError?.message?.includes('is_verified') ||
        profileUpdateError?.message?.includes('verified_at')) {
        console.warn('Falling back to update with just verification_type');

        // Fallback to just updating verification_type
        let fallbackQuery = supabase
          .from('profiles')
          .update({
            verification_type: application.type
          });

        // Check if the user_address is a UUID
        const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        const isUuid = uuidPattern.test(application.user_address);

        if (isUuid) {
          fallbackQuery = fallbackQuery.eq('id', application.user_address);
        } else {
          fallbackQuery = fallbackQuery.ilike('wallet_address', application.user_address);
        }

        const { error: fallbackError } = await fallbackQuery;

        if (fallbackError) {
          console.error('Error updating user verification status (fallback):', fallbackError);
          throw fallbackError;
        }

        console.log(`Successfully updated verification_type for user ${application.user_address} (fallback method)`);
      } else {
        // If it's a different error, rethrow it
        console.error('Error updating user verification status:', profileUpdateError);
        throw profileUpdateError;
      }
    }
  }

  return true;
};

// Content moderation functions
export const getReportedContent = async (page = 1, limit = 10) => {
  const start = (page - 1) * limit;
  const end = start + limit - 1;

  const { data, error, count } = await supabase
    .from('content_reports')
    .select('*, profiles!inner(*)', { count: 'exact' })
    .range(start, end);

  if (error) {
    console.error('Error fetching reported content:', error);
    throw error;
  }

  return { data, count };
};

export const resolveReport = async (reportId: string, action: 'dismiss' | 'remove', notes?: string) => {
  const { error } = await supabase
    .from('content_reports')
    .update({
      status: action === 'dismiss' ? 'dismissed' : 'removed',
      resolved_at: new Date().toISOString(),
      admin_notes: notes
    })
    .eq('id', reportId);

  if (error) {
    console.error('Error resolving report:', error);
    throw error;
  }

  return true;
};
