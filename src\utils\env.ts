
/**
 * Environment variables utility
 * This file provides access to environment variables with proper typing
 */

// NFT.Storage API key
// NFT.Storage API keys should start with 'eyJ' and be quite long
export const NFT_STORAGE_TOKEN = import.meta.env.VITE_NFT_STORAGE_TOKEN || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.Oj6TS5SBGP-5vqmHkCUNCmcQJnLwMOMnCsLHHLqLXlE';

// Web3.Storage API key (legacy)
export const WEB3_STORAGE_TOKEN = import.meta.env.VITE_WEB3_STORAGE_TOKEN || '';

// Thirdweb client ID
// Get one from https://thirdweb.com/dashboard
export const THIRDWEB_CLIENT_ID = import.meta.env.VITE_THIRDWEB_CLIENT_ID || 'a3a3a3a3a3a3a3a3a3a3a3a3a3a3a3a3';

// Helius API key - Restored from .env file
export const HELIUS_API_KEY = import.meta.env.VITE_HELIUS_API_KEY ||
  '5422bf21-7f7b-43b6-8a10-3a72b3d7bb3c';

// ElevenLabs API key (from your existing configuration)
export const ELEVENLABS_API_KEY = import.meta.env.VITE_ELEVENLABS_API_KEY ||
  '***************************************************';

// Check if required environment variables are set
export function checkRequiredEnvVars(): void {
  const missingVars = [];
  const warningVars = [];

  // Critical variables (will show as warnings)
  if (!NFT_STORAGE_TOKEN && !THIRDWEB_CLIENT_ID) {
    warningVars.push('VITE_NFT_STORAGE_TOKEN or VITE_THIRDWEB_CLIENT_ID');
  }

  // Nice-to-have variables (will show as info)
  if (!THIRDWEB_CLIENT_ID) {
    missingVars.push('VITE_THIRDWEB_CLIENT_ID');
  }

  // Log warnings for critical missing variables
  if (warningVars.length > 0) {
    console.warn(`Missing critical environment variables: ${warningVars.join(', ')}`);
    console.warn('Some features may not work correctly without these variables.');
  }

  // Log info for nice-to-have missing variables
  if (missingVars.length > 0) {
    console.info(`Missing optional environment variables: ${missingVars.join(', ')}`);
    console.info('The application will still work, but some features may have limited functionality.');
  }

  if (warningVars.length === 0) {
    console.log('All required environment variables are set.');
  }
}
