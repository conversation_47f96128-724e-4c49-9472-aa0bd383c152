# Blockchain Storage Implementation for Audra

This document explains the blockchain storage implementation for the Audra voice-based Web3 social platform.

## Overview

The blockchain storage implementation uses:

- **NFT.Storage** for IPFS uploads (31GB free storage)
- **Ceramic Network** for metadata storage
- **Lit Protocol** for encryption

This provides permanent, decentralized storage for voice recordings and media files, solving the issue of blob URLs expiring after refresh.

## Installation

1. Install the required dependencies:

```bash
npm install nft.storage @ceramicnetwork/http-client dids key-did-resolver lit-protocol
```

2. The NFT.Storage API key is already configured in the code:

```
fd56bf09.020c8783b870438d93f29d2655391154
```

## How It Works

### Storage Service Factory

The `storageServiceFactory` chooses between Supabase and blockchain storage based on user settings:

```typescript
// Get the appropriate storage service
const storageService = getStorageService();

// Upload audio to storage
const audioUrl = await storageService.uploadAudio(audioBlob, userId);
```

### NFT.Storage for IPFS

The `nftStorage.ts` module handles uploading files to IPFS:

```typescript
// Upload a file to IPFS
const ipfsUri = await uploadToIPFS(file);
// Result: ipfs://{CID}/{filename}

// Get a gateway URL for an IPFS URI
const gatewayUrl = getIPFSGatewayUrl(ipfsUri);
// Result: https://{CID}.ipfs.nftstorage.link/{filename}
```

### Ceramic for Metadata

The `ceramicClient.ts` module handles storing and retrieving metadata:

```typescript
// Create a voice post document in Ceramic
const documentId = await publishVoicePost(metadata);

// Get a voice post document from Ceramic
const voicePost = await getVoicePost(documentId);
```

### Lit Protocol for Encryption

The `litProtocol.ts` module handles encrypting and decrypting files:

```typescript
// Encrypt a file
const { encryptedFile, encryptedSymmetricKey } = await encryptWithLit(fileBuffer, accessControlConditions);

// Decrypt a file
const decryptedFile = await decryptWithLit(encryptedFile, encryptedSymmetricKey, accessControlConditions);
```

## User Interface

Users can switch between Supabase and blockchain storage in the Settings page:

1. Go to Settings
2. Select the "Storage" tab
3. Toggle the switch to enable/disable blockchain storage

## Migration

A migration utility is provided to help users transition from Supabase to blockchain storage:

```typescript
// Migrate a single voice message
const documentId = await migrateVoiceMessage(messageId);

// Migrate all voice messages for a user
const results = await migrateUserVoiceMessages(profileId);
```

## Benefits

1. **Decentralized Storage**: Voice recordings and metadata are stored on decentralized networks (IPFS and Ceramic), making them resistant to censorship and server outages.

2. **Permanent Storage**: IPFS provides permanent storage for audio files, solving the issue of blob URLs expiring after refresh.

3. **User Control**: Users can choose between Supabase and blockchain storage based on their preferences.

4. **Encryption**: The Lit Protocol integration allows for encrypted storage with access control, enhancing privacy and security.

5. **Free Tier**: NFT.Storage provides 31GB of free storage, which is sufficient for development and testing.

## Technical Details

### IPFS URIs

IPFS URIs are in the format `ipfs://{CID}/{filename}`. These are converted to gateway URLs for immediate access:

```
https://{CID}.ipfs.nftstorage.link/{filename}
```

### Fallback Mechanism

If IPFS upload fails, the system falls back to blob URLs:

```typescript
try {
  // Try to upload to IPFS
  const ipfsUri = await uploadBlobToIPFS(audioBlob, fileName);
  return resolveIPFSUri(ipfsUri);
} catch (error) {
  // Fall back to blob URL
  console.warn('Falling back to blob URL for audio storage');
  return URL.createObjectURL(audioBlob);
}
```

### Caching

Uploaded audio is cached in memory for immediate access:

```typescript
// Store in global cache
if (typeof window !== 'undefined') {
  if (!(window as any).audioCache) {
    (window as any).audioCache = {};
  }
  (window as any).audioCache[gatewayUrl] = audioBlob;
}
```

## Future Improvements

1. Implement a more robust caching mechanism using IndexedDB
2. Add progress indicators for IPFS uploads
3. Implement batch uploads for better performance
4. Add support for more encryption options
5. Implement a more user-friendly migration tool
