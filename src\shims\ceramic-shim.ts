/**
 * Shim for Ceramic HTTP client
 * This file replaces the Ceramic client with a disabled version
 * to prevent errors when Ceramic is not available
 */

// Ensure process exists (for compatibility with other libraries)
if (typeof window !== 'undefined' && !window.process) {
  (window as any).process = {
    env: {
      NODE_ENV: import.meta.env.MODE || 'production',
      DEBUG: import.meta.env.VITE_DEBUG || '',
      CERAMIC_API_URL: 'disabled',
      DID_PRIVATE_KEY: '',
      BROWSER: 'true'
    },
    browser: true,
    version: '16.0.0',
    platform: 'browser'
  };
}

// Ensure Buffer exists (for compatibility with other libraries)
if (typeof window !== 'undefined' && !window.Buffer) {
  try {
    // Try to use the buffer module if available
    const bufferModule = { Buffer: { isBuffer: () => false } };
    (window as any).Buffer = bufferModule.Buffer;
  } catch (e) {
    // Fallback implementation
    (window as any).Buffer = {
      isBuffer: () => false,
      from: (data: any) => new Uint8Array(data),
    };
  }
}

// Replace the Ceramic client with our disabled version
// This is done by adding a module alias in the window object
// that will be used by the dynamic import() function
if (typeof window !== 'undefined') {
  // Create a moduleAlias object if it doesn't exist
  if (!(window as any).moduleAlias) {
    (window as any).moduleAlias = {};
  }

  // Add an alias for the Ceramic client
  (window as any).moduleAlias['@ceramicnetwork/http-client'] = '../services/disabledCeramicClient';
  (window as any).moduleAlias['@ceramicnetwork/stream-tile'] = '../services/disabledCeramicClient';

  // Monkey patch fetch to block Ceramic requests
  const originalFetch = window.fetch;
  window.fetch = function (input: RequestInfo | URL, init?: RequestInit): Promise<Response> {
    const url = input.toString();

    // Block requests to Ceramic endpoints
    if (url.includes('ceramic') ||
      url.includes('gateway-clay') ||
      url.includes('3boxlabs') ||
      url.includes('api/v0/node/healthcheck') ||
      url.includes('localhost:8085')) {
      console.warn(`Blocked request to Ceramic endpoint: ${url}`);

      // Return a mock successful response
      return Promise.resolve(new Response(JSON.stringify({ status: 'ok' }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }));
    }

    // Allow all other requests
    return originalFetch(input, init);
  };
}

// Export a dummy function to ensure this file is not tree-shaken
export function ensureCeramicShim() {
  console.log('Ceramic disabled - using stub implementation');
}
