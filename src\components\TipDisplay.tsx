import React, { useEffect, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { DollarSign, Clock, MessageCircle } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import walletService from '@/services/walletService';
// Simple time formatting function
const formatTimeAgo = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);
  
  if (minutes < 1) return 'just now';
  if (minutes < 60) return `${minutes}m ago`;
  if (hours < 24) return `${hours}h ago`;
  return `${days}d ago`;
};

interface Tip {
  id: string;
  amount: number;
  currency: string;
  created_at: string;
  sender_id: string;
  receiver_id: string;
  voice_message_id?: string;
  // Join data
  sender_profile?: {
    display_name?: string;
    username?: string;
    avatar_url?: string;
  };
  receiver_profile?: {
    display_name?: string;
    username?: string;
    avatar_url?: string;
  };
  voice_message?: {
    transcript?: string;
  };
}

interface TipDisplayProps {
  messageId?: string; // Show tips for specific message
  userId?: string; // Show tips for specific user
  limit?: number;
  showPublicTips?: boolean; // Show all public tips
}

const TipDisplay: React.FC<TipDisplayProps> = ({
  messageId,
  userId,
  limit = 10,
  showPublicTips = false
}) => {
  const [tips, setTips] = useState<Tip[]>([]);
  const [loading, setLoading] = useState(true);
  const { getProfileByAddress } = useProfiles();

  useEffect(() => {
    fetchTips();
  }, [messageId, userId, showPublicTips, limit]);

  const fetchTips = async () => {
    try {
      setLoading(true);
      let query = supabase
        .from('tips')
        .select(`
          *,
          sender_profile:profiles!tips_sender_id_fkey(display_name, username, avatar_url),
          receiver_profile:profiles!tips_receiver_id_fkey(display_name, username, avatar_url),
          voice_message:voice_messages(transcript)
        `)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (messageId) {
        query = query.eq('voice_message_id', messageId);
      } else if (userId) {
        query = query.or(`sender_id.eq.${userId},receiver_id.eq.${userId}`);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching tips:', error);
        return;
      }

      setTips(data || []);
    } catch (error) {
      console.error('Error fetching tips:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatSenderName = (tip: Tip) => {
    if (tip.sender_profile?.display_name) return tip.sender_profile.display_name;
    if (tip.sender_profile?.username) return `@${tip.sender_profile.username}`;
    return walletService.formatAddress(tip.sender_id);
  };

  const formatReceiverName = (tip: Tip) => {
    if (tip.receiver_profile?.display_name) return tip.receiver_profile.display_name;
    if (tip.receiver_profile?.username) return `@${tip.receiver_profile.username}`;
    return walletService.formatAddress(tip.receiver_id);
  };

  if (loading) {
    return (
      <div className="space-y-2">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-3">
              <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-muted rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (tips.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <DollarSign className="mx-auto h-12 w-12 mb-4 opacity-50" />
        <p>No tips yet</p>
        {messageId && <p className="text-sm">Be the first to tip this message!</p>}
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {tips.map((tip) => (
        <Card key={tip.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <DollarSign className="h-4 w-4 text-green-600" />
                  <span className="font-semibold text-green-600">
                    {tip.amount} {tip.currency}
                  </span>
                  <Badge variant="secondary" className="text-xs">
                    Tip
                  </Badge>
                </div>
                
                <div className="text-sm space-y-1">
                  <p>
                    <span className="font-medium">{formatSenderName(tip)}</span>
                    {' '} tipped {' '}
                    <span className="font-medium">{formatReceiverName(tip)}</span>
                  </p>
                  
                  {tip.voice_message?.transcript && (
                    <div className="bg-muted p-2 rounded-md mt-2">
                      <div className="flex items-center gap-1 mb-1">
                        <MessageCircle className="h-3 w-3" />
                        <span className="text-xs font-medium">Message:</span>
                      </div>
                      <p className="text-xs text-muted-foreground line-clamp-2">
                        {tip.voice_message.transcript}
                      </p>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-1 text-xs text-muted-foreground ml-4">
                <Clock className="h-3 w-3" />
                {formatTimeAgo(tip.created_at)}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default TipDisplay;