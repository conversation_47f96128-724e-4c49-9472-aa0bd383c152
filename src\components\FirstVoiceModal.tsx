import React from 'react';
import { Dialog, DialogContent, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Star, PartyPopper } from 'lucide-react';

interface FirstVoiceModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const FirstVoiceModal: React.FC<FirstVoiceModalProps> = ({ isOpen, onClose }) => {
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md text-center">
        <DialogTitle className="text-center text-2xl font-bold">
          <div className="flex items-center justify-center gap-2 mb-2">
            <PartyPopper className="h-6 w-6 text-yellow-400" />
            <span>Your First Voice on the Chain!</span>
            <PartyPopper className="h-6 w-6 text-yellow-400" />
          </div>
        </DialogTitle>

        {/* Celebration animation effect */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          <div className="animate-float-up absolute left-1/4 top-full w-2 h-2 rounded-full bg-yellow-400"></div>
          <div className="animate-float-up-slow absolute left-1/3 top-full w-3 h-3 rounded-full bg-voicechain-purple"></div>
          <div className="animate-float-up-slower absolute left-1/2 top-full w-2 h-2 rounded-full bg-blue-400"></div>
          <div className="animate-float-up absolute left-2/3 top-full w-4 h-4 rounded-full bg-voicechain-accent"></div>
          <div className="animate-float-up-slow absolute left-3/4 top-full w-2 h-2 rounded-full bg-green-400"></div>
        </div>

        <div className="flex justify-center my-4">
          <div className="relative">
            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-voicechain-purple to-voicechain-accent animate-pulse opacity-50"></div>
            <div className="relative bg-background rounded-full p-6">
              <Mic className="h-12 w-12 text-voicechain-purple" />
            </div>
          </div>
        </div>

        <DialogDescription className="text-center px-4">
          <div className="text-lg font-medium mb-2">
            Congratulations on your first voice recording!
          </div>
          <div className="text-sm text-muted-foreground mb-4">
            Your voice is now immortalized on the blockchain. Make it count and enjoy the journey of expressing yourself through voice.
          </div>

          <div className="grid grid-cols-3 gap-2 mb-4">
            <div className="flex flex-col items-center p-2 bg-secondary/50 rounded-lg">
              <Sparkles className="h-5 w-5 text-yellow-400 mb-1" />
              <span className="text-xs">Unique</span>
            </div>
            <div className="flex flex-col items-center p-2 bg-secondary/50 rounded-lg">
              <Award className="h-5 w-5 text-voicechain-purple mb-1" />
              <span className="text-xs">Authentic</span>
            </div>
            <div className="flex flex-col items-center p-2 bg-secondary/50 rounded-lg">
              <Star className="h-5 w-5 text-blue-400 mb-1" />
              <span className="text-xs">Yours</span>
            </div>
          </div>
        </DialogDescription>

        <DialogFooter className="flex justify-center sm:justify-center">
          <Button
            onClick={onClose}
            className="bg-gradient-to-r from-voicechain-purple to-voicechain-accent hover:opacity-90 text-white"
          >
            Start My Voice Journey
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default FirstVoiceModal;
