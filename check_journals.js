import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://jcltjkaumevuycntdmds.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpjbHRqa2F1bWV2dXljbnRkbWRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTkwMDEsImV4cCI6MjA2MTg3NTAwMX0.LqQSk0NwB1mDB4Cznn0iQSrYvsAAWyUsaj8reo_etqM'
);

async function checkJournals() {
  console.log('🔍 Checking journals in database...\n');
  
  try {
    // Check journals table
    const { data: journals, error } = await supabase
      .from('journals')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);
      
    if (error) {
      console.log('❌ Error accessing journals table:', error.message);
      
      // Try voice_journals table as fallback
      console.log('🔄 Trying voice_journals table...');
      const { data: voiceJournals, error: voiceError } = await supabase
        .from('voice_journals')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(10);
        
      if (voiceError) {
        console.log('❌ Error accessing voice_journals table:', voiceError.message);
        return;
      } else {
        console.log('📖 Found', voiceJournals.length, 'voice journals:');
        voiceJournals.forEach((journal, i) => {
          console.log(`${i+1}. ${journal.title || 'Untitled'} (ID: ${journal.id})`);
          console.log(`   User: ${journal.profile_id}`);
          console.log(`   Public: ${journal.is_public}`);
          console.log(`   Soulbound: ${journal.is_soulbound}`);
          console.log(`   Created: ${journal.created_at}`);
          console.log('');
        });
      }
    } else {
      console.log('📖 Found', journals.length, 'journals in journals table:');
      journals.forEach((journal, i) => {
        console.log(`${i+1}. ${journal.title} (ID: ${journal.id})`);
        console.log(`   User: ${journal.profile_id}`);
        console.log(`   Published: ${journal.is_published}`);
        console.log(`   Locked: ${journal.is_locked}`);
        console.log(`   Audio URL: ${journal.audio_url}`);
        console.log(`   Created: ${journal.created_at}`);
        console.log('');
      });
    }
    
    // Also check journal_media table
    console.log('🖼️ Checking journal media...');
    const { data: media, error: mediaError } = await supabase
      .from('journal_media')
      .select('*')
      .limit(5);
      
    if (mediaError) {
      console.log('❌ Error accessing journal_media:', mediaError.message);
    } else {
      console.log(`📷 Found ${media.length} media items`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkJournals().catch(console.error);
