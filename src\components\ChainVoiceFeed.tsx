import React, { useState, useEffect } from 'react';
import { ChainVoicePost, chainVoicePostService } from '@/services/chainVoicePostService';
import { ChainVoicePostComponent } from '@/components/ChainVoicePost';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Filter, RefreshCw, Search } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';

interface ChainVoiceFeedProps {
  channel?: string;
  tag?: string;
  chain?: string;
}

export const ChainVoiceFeed: React.FC<ChainVoiceFeedProps> = ({
  channel,
  tag,
  chain,
}) => {
  const [posts, setPosts] = useState<ChainVoicePost[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<ChainVoicePost[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Load posts on mount
  useEffect(() => {
    loadPosts();

    // Subscribe to new posts
    const handleNewPost = (event: Event) => {
      const customEvent = event as CustomEvent<ChainVoicePost>;
      if (customEvent.detail) {
        setPosts(prevPosts => [customEvent.detail, ...prevPosts]);
      }
    };

    document.addEventListener('newChainVoicePost', handleNewPost);

    return () => {
      document.removeEventListener('newChainVoicePost', handleNewPost);
    };
  }, []);

  // Apply filters when posts, channel, tag, chain, or search query changes
  useEffect(() => {
    filterPosts();
  }, [posts, channel, tag, chain, searchQuery, activeTab]);

  // Load posts from service
  const loadPosts = () => {
    setIsLoading(true);

    // Simulate loading delay
    setTimeout(() => {
      let loadedPosts: ChainVoicePost[];

      if (channel) {
        loadedPosts = chainVoicePostService.getPostsByChannel(channel);
      } else if (tag) {
        loadedPosts = chainVoicePostService.getPostsByTag(tag);
      } else if (chain) {
        loadedPosts = chainVoicePostService.getPostsByChain(chain);
      } else {
        loadedPosts = chainVoicePostService.getAllPosts();
      }

      setPosts(loadedPosts);
      setIsLoading(false);
    }, 1000);
  };

  // Filter posts based on active tab and search query
  const filterPosts = () => {
    let filtered = [...posts];

    // Apply tab filter
    if (activeTab !== 'all') {
      filtered = filtered.filter(post => post.eventType.includes(activeTab));
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        post =>
          post.title.toLowerCase().includes(query) ||
          post.transcription.toLowerCase().includes(query) ||
          post.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    setFilteredPosts(filtered);
  };

  // Handle refresh
  const handleRefresh = () => {
    setIsRefreshing(true);

    // Simulate refresh delay
    setTimeout(() => {
      loadPosts();
      setIsRefreshing(false);
    }, 1000);
  };

  // Handle post interactions
  const handleReply = (post: ChainVoicePost) => {
    console.log('Reply to post:', post.id);
    // Implement reply functionality
  };

  const handleLike = (post: ChainVoicePost) => {
    console.log('Like post:', post.id);
    // Implement like functionality
  };

  const handleRepost = (post: ChainVoicePost) => {
    console.log('Repost:', post.id);
    // Implement repost functionality
  };

  const handleShare = (post: ChainVoicePost) => {
    console.log('Share post:', post.id);
    // Implement share functionality
  };

  // Render loading skeletons
  const renderSkeletons = () => {
    return Array(5)
      .fill(0)
      .map((_, index) => (
        <div key={index} className="p-4 border-b border-border">
          <div className="flex items-start space-x-3">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-16" />
              </div>
              <Skeleton className="h-6 w-3/4 mt-2" />
              <Skeleton className="h-20 w-full mt-3 rounded-lg" />
              <div className="flex justify-between mt-3">
                <Skeleton className="h-8 w-16" />
                <Skeleton className="h-8 w-16" />
                <Skeleton className="h-8 w-16" />
                <Skeleton className="h-8 w-16" />
              </div>
            </div>
          </div>
        </div>
      ));
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="sticky top-0 bg-background/95 backdrop-blur-md z-10 px-2 py-3 sm:p-4 border-b border-border">
        <div className="flex items-center justify-between mb-3 sm:mb-4">
          <h2 className="text-lg sm:text-xl font-bold">Voice Moments</h2>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 sm:h-9 sm:w-9"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 sm:h-5 sm:w-5 ${isRefreshing ? 'animate-spin' : ''}`} />
          </Button>
        </div>

        {/* Search and Filter */}
        <div className="flex items-center space-x-1 sm:space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 sm:h-4 sm:w-4 text-muted-foreground" />
            <Input
              placeholder="Search voice moments..."
              className="pl-7 sm:pl-8 h-8 sm:h-10 text-xs sm:text-sm"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button variant="outline" size="icon" className="h-8 w-8 sm:h-10 sm:w-10">
            <Filter className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
          </Button>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-3 sm:mt-4">
          <TabsList className="w-full overflow-x-auto flex-nowrap h-8 sm:h-10">
            <TabsTrigger value="all" className="text-xs sm:text-sm px-2 sm:px-3">All</TabsTrigger>
            <TabsTrigger value="dao" className="text-xs sm:text-sm px-2 sm:px-3">DAOs</TabsTrigger>
            <TabsTrigger value="funding" className="text-xs sm:text-sm px-2 sm:px-3">Funding</TabsTrigger>
            <TabsTrigger value="whale" className="text-xs sm:text-sm px-2 sm:px-3">Whales</TabsTrigger>
            <TabsTrigger value="security" className="text-xs sm:text-sm px-2 sm:px-3">Security</TabsTrigger>
            <TabsTrigger value="bridge" className="text-xs sm:text-sm px-2 sm:px-3">Bridges</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Feed Content */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          renderSkeletons()
        ) : filteredPosts.length > 0 ? (
          filteredPosts.map((post) => (
            <ChainVoicePostComponent
              key={post.id}
              post={post}
              onReply={handleReply}
              onLike={handleLike}
              onRepost={handleRepost}
              onShare={handleShare}
            />
          ))
        ) : (
          <div className="flex flex-col items-center justify-center h-48 sm:h-64 text-center p-4">
            <p className="text-base sm:text-lg font-medium mb-2">No voice moments found</p>
            <p className="text-xs sm:text-sm text-muted-foreground">
              {searchQuery
                ? "Try adjusting your search or filters"
                : "Voice moments from the chain will appear here"}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
