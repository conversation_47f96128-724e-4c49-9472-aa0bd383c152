import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { migrateUserVoiceMessages, BatchMigrationCallbacks } from '@/utils/thirdwebMigration';
import { toast } from '@/components/ui/sonner';
import { HardDrive, CloudCog, CheckCircle2, AlertCircle, RefreshCw, Database } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';

interface StorageMigrationToolProps {
  userId: string;
}

interface MigrationStatus {
  messageId: string;
  progress: number;
  status: 'pending' | 'migrating' | 'success' | 'error';
  documentId?: string;
  error?: string;
}

const StorageMigrationTool: React.FC<StorageMigrationToolProps> = ({ userId }) => {
  const [isMigrating, setIsMigrating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [totalMessages, setTotalMessages] = useState(0);
  const [completedCount, setCompletedCount] = useState(0);
  const [successCount, setSuccessCount] = useState(0);
  const [errorCount, setErrorCount] = useState(0);
  const [migrationComplete, setMigrationComplete] = useState(false);
  const [migrationStatuses, setMigrationStatuses] = useState<Record<string, MigrationStatus>>({});
  const [showDetails, setShowDetails] = useState(false);
  const [batchSize, setBatchSize] = useState(5);

  // Reset migration status when userId changes
  useEffect(() => {
    setMigrationComplete(false);
    setProgress(0);
    setTotalMessages(0);
    setCompletedCount(0);
    setSuccessCount(0);
    setErrorCount(0);
    setMigrationStatuses({});
  }, [userId]);

  const handleMigration = async () => {
    try {
      // Reset state
      setIsMigrating(true);
      setProgress(0);
      setTotalMessages(0);
      setCompletedCount(0);
      setSuccessCount(0);
      setErrorCount(0);
      setMigrationComplete(false);
      setMigrationStatuses({});

      // Create callbacks for tracking progress
      const callbacks: BatchMigrationCallbacks = {
        onBatchStart: (totalCount) => {
          setTotalMessages(totalCount);
          // Initialize migration statuses
          const initialStatuses: Record<string, MigrationStatus> = {};
          setMigrationStatuses(initialStatuses);
        },
        onMessageStart: (messageId, index, total) => {
          setMigrationStatuses(prev => ({
            ...prev,
            [messageId]: {
              messageId,
              progress: 0,
              status: 'migrating'
            }
          }));
        },
        onMessageProgress: (messageId, progress, index, total) => {
          setMigrationStatuses(prev => ({
            ...prev,
            [messageId]: {
              ...prev[messageId],
              progress
            }
          }));
        },
        onMessageSuccess: (messageId, documentId, index, total) => {
          setSuccessCount(prev => prev + 1);
          setMigrationStatuses(prev => ({
            ...prev,
            [messageId]: {
              ...prev[messageId],
              status: 'success',
              documentId
            }
          }));
        },
        onMessageError: (messageId, error, index, total) => {
          setErrorCount(prev => prev + 1);
          setMigrationStatuses(prev => ({
            ...prev,
            [messageId]: {
              ...prev[messageId],
              status: 'error',
              error: typeof error === 'string' ? error : 'Unknown error'
            }
          }));
        },
        onBatchProgress: (completed, total, success, error) => {
          setCompletedCount(completed);
          setSuccessCount(success);
          setErrorCount(error);
          setProgress(Math.round((completed / total) * 100));
        },
        onBatchComplete: (results) => {
          setMigrationComplete(true);
        }
      };

      // Start migration
      await migrateUserVoiceMessages(userId, callbacks, batchSize);
    } catch (error) {
      console.error('Error during migration:', error);
      toast.error('Error during migration. Please try again.');
    } finally {
      setIsMigrating(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CloudCog className="h-5 w-5" />
          <span>Storage Migration Tool</span>
        </CardTitle>
        <CardDescription>
          Migrate your voice messages from Supabase to decentralized blockchain storage
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="flex items-center justify-center gap-8 py-4">
          <div className="flex flex-col items-center">
            <Database className="h-12 w-12 text-primary/70" />
            <span className="mt-2 text-sm">Supabase</span>
          </div>

          <div className="flex-1 border-t-2 border-dashed border-primary/30"></div>

          <div className="flex flex-col items-center">
            <HardDrive className="h-12 w-12 text-primary/70" />
            <span className="mt-2 text-sm">IPFS via Thirdweb</span>
          </div>
        </div>

        {isMigrating && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Migration in progress...</span>
              <div className="flex items-center gap-2">
                <span>{completedCount} of {totalMessages} complete</span>
                {errorCount > 0 && (
                  <Badge variant="destructive" className="text-xs">
                    {errorCount} failed
                  </Badge>
                )}
              </div>
            </div>
            <Progress value={progress} className="h-2" />

            {totalMessages > 0 && (
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{successCount} successful</span>
                <span>{Math.round(progress)}%</span>
              </div>
            )}
          </div>
        )}

        {migrationComplete && (
          <div className={`rounded-lg ${errorCount > 0 ? 'bg-amber-500/10' : 'bg-green-500/10'} p-4 flex items-center gap-3`}>
            {errorCount > 0 ? (
              <>
                <AlertCircle className="h-5 w-5 text-amber-500" />
                <div>
                  <p>Migration completed with some issues.</p>
                  <p className="text-sm text-muted-foreground">
                    {successCount} messages migrated successfully, {errorCount} failed.
                  </p>
                </div>
              </>
            ) : (
              <>
                <CheckCircle2 className="h-5 w-5 text-green-500" />
                <div>
                  <p>Migration complete!</p>
                  <p className="text-sm text-muted-foreground">
                    All {successCount} voice messages are now stored on the blockchain.
                  </p>
                </div>
              </>
            )}
          </div>
        )}

        {showDetails && Object.keys(migrationStatuses).length > 0 && (
          <div className="mt-4 border rounded-lg">
            <div className="p-3 border-b bg-secondary/20 font-medium">Migration Details</div>
            <ScrollArea className="h-[200px]">
              <div className="p-2 space-y-2">
                {Object.values(migrationStatuses).map((status) => (
                  <div key={status.messageId} className="border rounded-md p-2 text-sm">
                    <div className="flex justify-between items-center">
                      <div className="font-mono text-xs truncate max-w-[150px]">{status.messageId}</div>
                      <Badge
                        variant={
                          status.status === 'success' ? 'default' :
                            status.status === 'error' ? 'destructive' :
                              'secondary'
                        }
                        className="text-xs"
                      >
                        {status.status === 'migrating' ? (
                          <div className="flex items-center gap-1">
                            <RefreshCw className="h-3 w-3 animate-spin" />
                            <span>{Math.round(status.progress)}%</span>
                          </div>
                        ) : status.status}
                      </Badge>
                    </div>
                    {status.status === 'error' && status.error && (
                      <p className="text-xs text-destructive mt-1">{status.error}</p>
                    )}
                    {status.status === 'success' && status.documentId && (
                      <p className="text-xs text-muted-foreground mt-1 truncate">
                        IPFS URI: {status.documentId.substring(0, 20)}...
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}
      </CardContent>

      <CardFooter className="flex flex-col gap-3 sm:flex-row">
        <Button
          onClick={handleMigration}
          disabled={isMigrating}
          className="w-full sm:w-auto bg-voicechain-purple hover:bg-voicechain-accent"
        >
          {isMigrating ? 'Migrating...' : migrationComplete ? 'Migrate Again' : 'Start Migration'}
        </Button>

        {(isMigrating || Object.keys(migrationStatuses).length > 0) && (
          <Button
            variant="outline"
            onClick={() => setShowDetails(!showDetails)}
            className="w-full sm:w-auto"
          >
            {showDetails ? 'Hide Details' : 'Show Details'}
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default StorageMigrationTool;
