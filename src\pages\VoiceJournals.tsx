import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { FileText, Plus, Lock, Unlock, Globe } from 'lucide-react';
import JournalCreationModal from '@/components/JournalCreationModal';
import JournalView from '@/components/JournalView';
import { useJournals } from '@/contexts/JournalContext';

interface VoiceJournalsProps {
  userAddress: string;
}

const VoiceJournals: React.FC<VoiceJournalsProps> = ({ userAddress }) => {
  const { addJournal, getUnlockedJournals, getLockedJournals, getAllPublicJournals } = useJournals();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('unlocked');
  const [refreshKey, setRefreshKey] = useState(0);

  // Get journals based on active tab
  const unlockedJournals = getUnlockedJournals(userAddress);
  const lockedJournals = getLockedJournals(userAddress);
  const publicJournals = getAllPublicJournals();

  // Handle journal creation
  const handleJournalCreated = async (journal: any) => {
    try {
      await addJournal(journal);
      setRefreshKey(prev => prev + 1);
    } catch (error) {
      console.error('Error creating journal:', error);
    }
  };

  // Handle journal unlock
  const handleJournalUnlocked = () => {
    setRefreshKey(prev => prev + 1);
  };

  return (
    <div className="w-full flex flex-col h-full md:container md:mx-auto md:max-w-3xl">
      {/* Header */}
      <div className="sticky top-[60px] bg-background/95 backdrop-blur-md z-10 px-3 py-3 sm:p-4 border-b border-border">
        <div className="flex items-center justify-between">
          <h1 className="text-xl sm:text-2xl font-bold flex items-center gap-1 sm:gap-2">
            <FileText className="text-voicechain-purple h-5 w-5 sm:h-6 sm:w-6" />
            Voice Journals
          </h1>
          <Button
            onClick={() => setIsCreateModalOpen(true)}
            className="bg-voicechain-purple hover:bg-voicechain-accent text-xs sm:text-sm px-2 sm:px-3"
          >
            <Plus size={14} className="mr-1 sm:mr-2" />
            Create Journal
          </Button>
        </div>
        <p className="text-xs sm:text-sm text-muted-foreground mt-1">
          Create time-locked voice diaries stored on decentralized storage
        </p>
      </div>

      {/* Tabs */}
      <div className="px-2 py-3 sm:p-4">
        <Tabs defaultValue="unlocked" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3 mb-4">
            <TabsTrigger value="unlocked" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm">
              <Unlock size={14} className="sm:h-4 sm:w-4" />
              <span>My Journals</span>
              {unlockedJournals.length > 0 && (
                <span className="ml-1 text-xs bg-secondary px-1.5 rounded-full">
                  {unlockedJournals.length}
                </span>
              )}
            </TabsTrigger>
            <TabsTrigger value="locked" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm">
              <Lock size={14} className="sm:h-4 sm:w-4" />
              <span>Locked</span>
              {lockedJournals.length > 0 && (
                <span className="ml-1 text-xs bg-secondary px-1.5 rounded-full">
                  {lockedJournals.length}
                </span>
              )}
            </TabsTrigger>
            <TabsTrigger value="public" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm">
              <Globe size={14} className="sm:h-4 sm:w-4" />
              <span>Public</span>
              {publicJournals.length > 0 && (
                <span className="ml-1 text-xs bg-secondary px-1.5 rounded-full">
                  {publicJournals.length}
                </span>
              )}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="unlocked" className="space-y-4">
            {unlockedJournals.length === 0 ? (
              <div className="text-center py-8 sm:py-12">
                <Unlock size={36} className="mx-auto mb-3 sm:mb-4 text-muted-foreground" />
                <h3 className="text-base sm:text-lg font-medium mb-2">No unlocked journals</h3>
                <p className="text-xs sm:text-sm text-muted-foreground max-w-md mx-auto mb-4 sm:mb-6 px-4">
                  You don't have any unlocked voice journals yet. Create a new journal or wait for your locked journals to unlock.
                </p>
                <Button
                  onClick={() => setIsCreateModalOpen(true)}
                  className="bg-voicechain-purple hover:bg-voicechain-accent text-xs sm:text-sm"
                >
                  <Plus size={14} className="mr-1 sm:mr-2" />
                  Create Journal
                </Button>
              </div>
            ) : (
              <div className="space-y-3 sm:space-y-4">
                {unlockedJournals.map(journal => (
                  <JournalView
                    key={`${journal.id}-${refreshKey}`}
                    journal={journal}
                    currentUserAddress={userAddress}
                    onUnlock={handleJournalUnlocked}
                    showOwnerControls={true}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="locked" className="space-y-4">
            {lockedJournals.length === 0 ? (
              <div className="text-center py-8 sm:py-12">
                <Lock size={36} className="mx-auto mb-3 sm:mb-4 text-muted-foreground" />
                <h3 className="text-base sm:text-lg font-medium mb-2">No locked journals</h3>
                <p className="text-xs sm:text-sm text-muted-foreground max-w-md mx-auto mb-4 sm:mb-6 px-4">
                  You don't have any locked voice journals waiting to be unlocked. Create a new time-locked journal to get started.
                </p>
                <Button
                  onClick={() => setIsCreateModalOpen(true)}
                  className="bg-voicechain-purple hover:bg-voicechain-accent text-xs sm:text-sm"
                >
                  <Plus size={14} className="mr-1 sm:mr-2" />
                  Create Journal
                </Button>
              </div>
            ) : (
              <div className="space-y-3 sm:space-y-4">
                {lockedJournals.map(journal => (
                  <JournalView
                    key={`${journal.id}-${refreshKey}`}
                    journal={journal}
                    currentUserAddress={userAddress}
                    onUnlock={handleJournalUnlocked}
                    showOwnerControls={true}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="public" className="space-y-4">
            {publicJournals.length === 0 ? (
              <div className="text-center py-8 sm:py-12">
                <Globe size={36} className="mx-auto mb-3 sm:mb-4 text-muted-foreground" />
                <h3 className="text-base sm:text-lg font-medium mb-2">No public journals</h3>
                <p className="text-xs sm:text-sm text-muted-foreground max-w-md mx-auto mb-4 sm:mb-6 px-4">
                  There are no public voice journals available yet. Create a new journal and make it public to share with others.
                </p>
                <Button
                  onClick={() => setIsCreateModalOpen(true)}
                  className="bg-voicechain-purple hover:bg-voicechain-accent text-xs sm:text-sm"
                >
                  <Plus size={14} className="mr-1 sm:mr-2" />
                  Create Journal
                </Button>
              </div>
            ) : (
              <div className="space-y-3 sm:space-y-4">
                {publicJournals.map(journal => (
                  <JournalView
                    key={`${journal.id}-${refreshKey}`}
                    journal={journal}
                    currentUserAddress={userAddress}
                    onUnlock={handleJournalUnlocked}
                    showOwnerControls={journal.userAddress === userAddress}
                  />
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Journal Creation Modal */}
      <JournalCreationModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSave={handleJournalCreated}
        userAddress={userAddress}
      />
    </div>
  );
};

export default VoiceJournals;
