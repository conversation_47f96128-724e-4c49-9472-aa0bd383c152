
/**
 * Account utility functions
 */

import { supabase } from '@/integrations/supabase/client';

/**
 * Get the current connected account from Supabase session
 * @returns The connected account address or empty string if not found
 */
export const getCurrentUserAccount = async (): Promise<string> => {
  try {
    // Get current session from Supabase
    const { data: { session } } = await supabase.auth.getSession();
    if (session?.user?.id) {
      return session.user.id;
    }
    return '';
  } catch (error) {
    console.error('Error getting current user account:', error);
    return '';
  }
};

/**
 * Set the current connected account (no longer uses localStorage)
 * @param address The wallet address to set
 */
export const setCurrentUserAccount = async (address: string): Promise<void> => {
  // This function is kept for backwards compatibility
  // but doesn't need to do anything as we now rely on Supabase auth
  console.log('Setting current account is now handled by Supabase auth');
};

/**
 * Check if the current user is the owner of a profile
 * @param profileAddress The profile address to check
 * @returns True if the current user is the owner
 */
export const isCurrentUser = async (profileAddress: string): Promise<boolean> => {
  const currentAccount = await getCurrentUserAccount();
  
  if (!currentAccount || !profileAddress) {
    return false;
  }
  
  // Ensure both addresses are strings before comparing
  const profileAddressStr = typeof profileAddress === 'string' ? profileAddress : String(profileAddress);
  return currentAccount.toLowerCase() === profileAddressStr.toLowerCase();
};

/**
 * Format an address for display (0x1234...5678)
 * @param address The full address
 * @param prefixLength Number of characters to show at start
 * @param suffixLength Number of characters to show at end
 * @returns Formatted address string
 */
export const formatAddress = (
  address: string | null | undefined,
  prefixLength: number = 6,
  suffixLength: number = 4
): string => {
  if (!address) return 'Unknown Address';
  
  // Ensure address is a string
  const addressStr = typeof address === 'string' ? address : String(address);
  
  if (addressStr.length <= prefixLength + suffixLength) {
    return addressStr;
  }
  
  return `${addressStr.substring(0, prefixLength)}...${addressStr.substring(
    addressStr.length - suffixLength
  )}`;
};

export default {
  getCurrentUserAccount,
  setCurrentUserAccount,
  isCurrentUser,
  formatAddress
};
