-- Create live audio spaces tables and functionality
-- This implements Twitter Spaces-like live audio rooms with enhanced features

-- Live Streams/Spaces table
CREATE TABLE IF NOT EXISTS public.live_streams (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  host_profile_id UUID NOT NULL,
  channel_id UUID NOT NULL REFERENCES public.channels(id) ON DELETE CASCADE,
  section_id UUID REFERENCES public.channel_sections(id) ON DELETE SET NULL,
  status TEXT NOT NULL DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'live', 'ended', 'cancelled')),
  scheduled_start TIMESTAMP WITH TIME ZONE,
  actual_start TIMESTAMP WITH TIME ZONE,
  ended_at TIMESTAMP WITH TIME ZONE,
  max_participants INTEGER DEFAULT 1000,
  chat_enabled BOOLEAN DEFAULT true,
  audience_can_request_mic BOOLEAN DEFAULT true,
  is_recorded BOOLEAN DEFAULT false,
  recording_url TEXT,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Stream Participants table
CREATE TABLE IF NOT EXISTS public.stream_participants (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  stream_id UUID NOT NULL REFERENCES public.live_streams(id) ON DELETE CASCADE,
  profile_id UUID NOT NULL,
  role TEXT NOT NULL DEFAULT 'listener' CHECK (role IN ('host', 'co_host', 'speaker', 'listener')),
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  left_at TIMESTAMP WITH TIME ZONE,
  mic_requested_at TIMESTAMP WITH TIME ZONE,
  mic_granted_at TIMESTAMP WITH TIME ZONE,
  is_muted BOOLEAN DEFAULT false
);

-- Stream Chat table
CREATE TABLE IF NOT EXISTS public.stream_chat (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  stream_id UUID NOT NULL REFERENCES public.live_streams(id) ON DELETE CASCADE,
  profile_id UUID NOT NULL,
  message TEXT NOT NULL,
  message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'emoji', 'system')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on all tables
ALTER TABLE public.live_streams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stream_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stream_chat ENABLE ROW LEVEL SECURITY;

-- RLS Policies for live_streams
CREATE POLICY "Anyone can view live streams" ON public.live_streams
  FOR SELECT USING (true);

CREATE POLICY "Hosts can create streams" ON public.live_streams
  FOR INSERT WITH CHECK (auth.uid() = host_profile_id);

CREATE POLICY "Hosts can update their streams" ON public.live_streams
  FOR UPDATE USING (auth.uid() = host_profile_id);

CREATE POLICY "Hosts can delete their streams" ON public.live_streams
  FOR DELETE USING (auth.uid() = host_profile_id);

-- RLS Policies for stream_participants
CREATE POLICY "Anyone can view stream participants" ON public.stream_participants
  FOR SELECT USING (true);

CREATE POLICY "Users can join streams" ON public.stream_participants
  FOR INSERT WITH CHECK (auth.uid() = profile_id);

CREATE POLICY "Users can update their participation" ON public.stream_participants
  FOR UPDATE USING (auth.uid() = profile_id);

CREATE POLICY "Users can leave streams" ON public.stream_participants
  FOR DELETE USING (auth.uid() = profile_id);

-- RLS Policies for stream_chat
CREATE POLICY "Anyone can view stream chat" ON public.stream_chat
  FOR SELECT USING (true);

CREATE POLICY "Participants can send chat messages" ON public.stream_chat
  FOR INSERT WITH CHECK (
    auth.uid() = profile_id AND 
    EXISTS (
      SELECT 1 FROM public.stream_participants 
      WHERE stream_id = stream_chat.stream_id 
      AND profile_id = auth.uid() 
      AND left_at IS NULL
    )
  );

-- Functions for stream management
CREATE OR REPLACE FUNCTION public.join_live_stream(
  p_stream_id UUID,
  p_profile_id UUID,
  p_role TEXT DEFAULT 'listener'
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if stream exists and is live
  IF NOT EXISTS (
    SELECT 1 FROM public.live_streams 
    WHERE id = p_stream_id AND status = 'live'
  ) THEN
    RETURN FALSE;
  END IF;
  
  -- Check if user is already in the stream
  IF EXISTS (
    SELECT 1 FROM public.stream_participants 
    WHERE stream_id = p_stream_id 
    AND profile_id = p_profile_id 
    AND left_at IS NULL
  ) THEN
    RETURN TRUE; -- Already joined
  END IF;
  
  -- Add participant
  INSERT INTO public.stream_participants (
    stream_id, profile_id, role, joined_at
  ) VALUES (
    p_stream_id, p_profile_id, p_role, NOW()
  )
  ON CONFLICT (stream_id, profile_id) 
  DO UPDATE SET 
    left_at = NULL,
    joined_at = NOW(),
    role = p_role;
  
  RETURN TRUE;
END;
$$;

CREATE OR REPLACE FUNCTION public.leave_live_stream(
  p_stream_id UUID,
  p_profile_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE public.stream_participants
  SET left_at = NOW()
  WHERE stream_id = p_stream_id 
  AND profile_id = p_profile_id 
  AND left_at IS NULL;
  
  RETURN FOUND;
END;
$$;

CREATE OR REPLACE FUNCTION public.request_mic_access(
  p_stream_id UUID,
  p_profile_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE public.stream_participants
  SET mic_requested_at = NOW()
  WHERE stream_id = p_stream_id 
  AND profile_id = p_profile_id 
  AND left_at IS NULL;
  
  RETURN FOUND;
END;
$$;

CREATE OR REPLACE FUNCTION public.grant_mic_access(
  p_stream_id UUID,
  p_profile_id UUID,
  p_host_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if requester is host
  IF NOT EXISTS (
    SELECT 1 FROM public.live_streams 
    WHERE id = p_stream_id AND host_profile_id = p_host_id
  ) THEN
    RETURN FALSE;
  END IF;
  
  UPDATE public.stream_participants
  SET 
    mic_granted_at = NOW(),
    role = 'speaker'
  WHERE stream_id = p_stream_id 
  AND profile_id = p_profile_id 
  AND left_at IS NULL;
  
  RETURN FOUND;
END;
$$;

CREATE OR REPLACE FUNCTION public.get_live_streams(p_channel_id UUID DEFAULT NULL)
RETURNS TABLE(
  id UUID,
  title TEXT,
  description TEXT,
  host_profile_id UUID,
  channel_id UUID,
  status TEXT,
  scheduled_start TIMESTAMP WITH TIME ZONE,
  actual_start TIMESTAMP WITH TIME ZONE,
  ended_at TIMESTAMP WITH TIME ZONE,
  participant_count BIGINT,
  speaker_count BIGINT,
  created_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ls.id,
    ls.title,
    ls.description,
    ls.host_profile_id,
    ls.channel_id,
    ls.status,
    ls.scheduled_start,
    ls.actual_start,
    ls.ended_at,
    COALESCE(participant_counts.participant_count, 0) as participant_count,
    COALESCE(speaker_counts.speaker_count, 0) as speaker_count,
    ls.created_at
  FROM public.live_streams ls
  LEFT JOIN (
    SELECT 
      stream_id, 
      COUNT(*) as participant_count
    FROM public.stream_participants 
    WHERE left_at IS NULL 
    GROUP BY stream_id
  ) participant_counts ON ls.id = participant_counts.stream_id
  LEFT JOIN (
    SELECT 
      stream_id, 
      COUNT(*) as speaker_count
    FROM public.stream_participants 
    WHERE left_at IS NULL 
    AND role IN ('host', 'co_host', 'speaker')
    GROUP BY stream_id
  ) speaker_counts ON ls.id = speaker_counts.stream_id
  WHERE (p_channel_id IS NULL OR ls.channel_id = p_channel_id)
  AND ls.status IN ('scheduled', 'live')
  ORDER BY 
    CASE WHEN ls.status = 'live' THEN 0 ELSE 1 END,
    ls.actual_start DESC,
    ls.scheduled_start ASC;
END;
$$;

-- Unique constraint to prevent duplicate active participants
ALTER TABLE public.stream_participants 
ADD CONSTRAINT unique_active_participant 
UNIQUE (stream_id, profile_id) 
DEFERRABLE INITIALLY DEFERRED;

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_live_streams_status ON public.live_streams(status);
CREATE INDEX IF NOT EXISTS idx_live_streams_channel ON public.live_streams(channel_id);
CREATE INDEX IF NOT EXISTS idx_stream_participants_stream ON public.stream_participants(stream_id);
CREATE INDEX IF NOT EXISTS idx_stream_participants_active ON public.stream_participants(stream_id, profile_id) WHERE left_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_stream_chat_stream ON public.stream_chat(stream_id, created_at);

-- Triggers for updated_at
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_live_streams_updated_at
  BEFORE UPDATE ON public.live_streams
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();