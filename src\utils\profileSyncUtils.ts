// Add the missing imports at the top of the file
import { supabase } from '@/integrations/supabase/client';
import { UserProfile, UserProfileUpdate } from '@/types/user-profile';
import { toast } from '@/components/ui/sonner';

// Only update the forceSyncProfileToSupabase function to make it more robust

export async function forceSyncProfileToSupabase(profile: UserProfile): Promise<boolean> {
  if (!profile || !profile.address) {
    console.error('Invalid profile for syncing to Supabase');
    return false;
  }

  try {
    console.log(`Force syncing profile to Supabase for ${profile.address}`);

    // Normalize the address
    const normalizedAddress = profile.address.toLowerCase();

    // First check if this wallet address already has a profile in Supabase
    const { data: existingProfile, error: queryError } = await supabase
      .from('profiles')
      .select('id, username')
      .eq('wallet_address', normalizedAddress)
      .maybeSingle();

    if (queryError) {
      console.error('Error checking for existing profile:', queryError);
      throw queryError;
    }

    // Check if we have a user with this wallet address
    // We need to use the auth.users table, which requires admin privileges
    // Instead, we'll use the existing profile ID if available
    let userId: string | undefined = existingProfile?.id;

    if (!userId) {
      // If we don't have an existing profile, we need to get the user ID from auth
      try {
        // Get the current user's ID from auth
        const { data: { user } } = await supabase.auth.getUser();

        if (user) {
          userId = user.id;
          console.log(`Using current authenticated user ID: ${userId}`);
        } else {
          // For users without authentication, generate a UUID based on the address
          userId = crypto.randomUUID();
          console.log(`Generated new UUID for profile: ${userId}`);
        }
      } catch (authError) {
        console.error('Error getting current user:', authError);
        // Generate a UUID as fallback
        userId = crypto.randomUUID();
        console.log(`Generated fallback UUID for profile: ${userId}`);
      }
    } else {
      console.log(`Using existing profile ID: ${userId}`);
    }

    // Use the user ID as the profile ID
    const profileId = existingProfile?.id || userId;

    // Check if the username already exists (if it's not the current user's username)
    let username = profile.username;
    if (username && (!existingProfile || existingProfile.username !== username)) {
      const { data: usernameCheck, error: usernameError } = await supabase
        .from('profiles')
        .select('username')
        .eq('username', username)
        .neq('id', profileId) // Exclude the current profile
        .maybeSingle();

      if (usernameError) {
        console.error('Error checking username uniqueness:', usernameError);
      } else if (usernameCheck) {
        // Username already exists, generate a unique one
        console.log(`Username ${username} already exists, generating a unique one`);
        const randomSuffix = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        username = `${username}_${randomSuffix}`;
        console.log(`Generated unique username: ${username}`);
      }
    }

    // Convert the profile to Supabase format
    const supabaseProfile = {
      id: profileId,
      wallet_address: normalizedAddress,
      username: username,
      display_name: profile.displayName,
      bio: profile.bio || '',
      avatar_url: profile.profileImageUrl || '',
      cover_image_url: profile.coverImageUrl || '',
      social_links: profile.socialLinks || {},
      post_count: profile.stats?.posts || 0,
      like_count: profile.stats?.likes || 0,
      tip_count: profile.stats?.tips || 0,
      is_verified: profile.verification?.isVerified || false,
      verification_type: profile.verification?.type || null,
      verified_at: profile.verification?.since?.toISOString() || null,
      updated_at: new Date().toISOString()
    };

    // First try to use the RPC function if available
    try {
      const { data: rpcResult, error: rpcError } = await supabase.rpc(
        'update_profile_by_address',
        {
          p_wallet_address: normalizedAddress,
          p_username: username,
          p_display_name: profile.displayName,
          p_bio: profile.bio || '',
          p_avatar_url: profile.profileImageUrl || '',
          p_cover_image_url: profile.coverImageUrl || '',
          p_social_links: profile.socialLinks || {}
        }
      );

      if (!rpcError) {
        console.log('Successfully used RPC to update profile:', rpcResult);
        return true;
      } else {
        console.warn('RPC error, falling back to direct upsert:', rpcError);
      }
    } catch (rpcError) {
      console.warn('RPC function not available, falling back to direct upsert:', rpcError);
    }

    // Use direct upsert to save the profile with explicit conflict handling
    const { error: upsertError } = await supabase
      .from('profiles')
      .upsert(supabaseProfile, {
        onConflict: 'id',
        ignoreDuplicates: false
      });

    if (upsertError) {
      // If we still get a unique constraint error, try one more approach
      if (upsertError.code === '23505' && upsertError.message.includes('profiles_username_key')) {
        console.log('Still got username conflict, trying with timestamp suffix');

        // Add timestamp to make it truly unique
        const timestampSuffix = Date.now().toString().slice(-6);
        supabaseProfile.username = `${username}_${timestampSuffix}`;

        // Try one more time with a different approach - update if exists, insert if not
        try {
          // First check if profile exists by ID
          const { data: existingProfileById } = await supabase
            .from('profiles')
            .select('id')
            .eq('id', profileId)
            .maybeSingle();

          if (existingProfileById) {
            // Update existing profile
            const { error: updateError } = await supabase
              .from('profiles')
              .update({
                username: supabaseProfile.username,
                display_name: supabaseProfile.display_name,
                bio: supabaseProfile.bio,
                avatar_url: supabaseProfile.avatar_url,
                cover_image_url: supabaseProfile.cover_image_url,
                social_links: supabaseProfile.social_links,
                updated_at: supabaseProfile.updated_at
              })
              .eq('id', profileId);

            if (updateError) {
              console.error('Error updating profile:', updateError);
              throw updateError;
            }
          } else {
            // Insert new profile
            const { error: insertError } = await supabase
              .from('profiles')
              .insert(supabaseProfile);

            if (insertError) {
              console.error('Error inserting profile:', insertError);
              throw insertError;
            }
          }
        } catch (finalError) {
          console.error('Error in final attempt to save profile:', finalError);
          throw finalError;
        }
      } else {
        console.error('Error upserting profile to Supabase:', upsertError);
        throw upsertError;
      }
    }

    console.log(`Successfully force synced profile to Supabase for ${normalizedAddress}`);
    return true;
  } catch (error) {
    console.error('Error force syncing profile to Supabase:', error);
    toast.error('Failed to sync profile to Supabase');
    return false;
  }
}

/**
 * Check and fix duplicate usernames in the database
 * This function finds profiles with duplicate usernames and updates them to be unique
 * @returns True if the operation was successful
 */
export async function checkAndFixDuplicateUsernames(): Promise<boolean> {
  try {
    console.log('Checking for duplicate usernames in the database');

    // Find duplicate usernames using direct SQL query
    const { data, error: queryError } = await supabase.rpc('execute_sql', {
      sql_query: `
        SELECT username, COUNT(*) as count
        FROM profiles
        WHERE username IS NOT NULL
        GROUP BY username
        HAVING COUNT(*) > 1;
      `
    });

    if (queryError) {
      console.error('Error finding duplicate usernames:', queryError);
      return false;
    }

    // Process the results
    const duplicateUsernames: string[] = [];
    if (data && Array.isArray(data)) {
      for (const row of data) {
        if (row && row.username) {
          duplicateUsernames.push(row.username);
        }
      }
    }

    if (duplicateUsernames.length > 0) {
      console.log(`Found ${duplicateUsernames.length} duplicate usernames`);

      // Fix each duplicate username
      for (const username of duplicateUsernames) {
        await fixDuplicateUsername(username);
      }
    } else {
      console.log('No duplicate usernames found');
    }

    return true;
  } catch (error) {
    console.error('Error checking and fixing duplicate usernames:', error);
    return false;
  }
}

/**
 * Fix duplicate username by adding a unique suffix to all but one profile
 * @param username The duplicate username
 * @returns True if the operation was successful
 */
async function fixDuplicateUsername(username: string): Promise<boolean> {
  try {
    console.log(`Fixing duplicate username: ${username}`);

    // Get all profiles with this username
    const { data: profiles, error: queryError } = await supabase
      .from('profiles')
      .select('id, username, wallet_address')
      .eq('username', username);

    if (queryError) {
      console.error('Error getting profiles with duplicate username:', queryError);
      return false;
    }

    if (!profiles || profiles.length <= 1) {
      console.log('No duplicates found for username:', username);
      return true;
    }

    console.log(`Found ${profiles.length} profiles with username ${username}`);

    // Keep the first profile as is, update the rest
    for (let i = 1; i < profiles.length; i++) {
      const profile = profiles[i];
      const newUsername = `${username}_${i}_${Date.now().toString().slice(-6)}`;

      console.log(`Updating profile ${profile.id} username from ${profile.username} to ${newUsername}`);

      const { error: updateError } = await supabase
        .from('profiles')
        .update({ username: newUsername })
        .eq('id', profile.id);

      if (updateError) {
        console.error(`Error updating profile ${profile.id} username:`, updateError);
      }
    }

    return true;
  } catch (error) {
    console.error('Error fixing duplicate username:', error);
    return false;
  }
}

/**
 * Force sync a profile from Supabase
 * This function ensures that the profile data is properly loaded from Supabase
 * @param address The wallet address
 * @returns The synced profile or null if not found
 */
export async function forceSyncProfileFromSupabase(address: string): Promise<UserProfile | null> {
  try {
    console.log(`Force syncing profile from Supabase for ${address}`);

    // Normalize the address
    const normalizedAddress = address.toLowerCase();

    // Get profile from Supabase
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('wallet_address', normalizedAddress)
      .maybeSingle();

    if (error) {
      console.error('Error getting profile from Supabase:', error);
      throw error;
    }

    if (!data) {
      console.log(`No profile found in Supabase for ${normalizedAddress}`);
      return null;
    }

    console.log(`Found profile in Supabase for ${normalizedAddress}:`, data);

    // Convert to UserProfile format
    const profile: UserProfile = {
      address: normalizedAddress,
      walletAddress: data.wallet_address,
      username: data.username || `user_${normalizedAddress.substring(2, 8)}`,
      displayName: data.display_name || `User ${normalizedAddress.substring(0, 6)}`,
      bio: data.bio || '',
      profileImageUrl: data.avatar_url || '',
      coverImageUrl: data.cover_image_url || '',
      socialLinks: (data.social_links as UserProfile['socialLinks']) || {},
      stats: {
        posts: data.post_count || 0,
        likes: data.like_count || 0,
        tips: data.tip_count || 0
      },
      joinedDate: new Date(data.created_at),
      verification: {
        isVerified: !!data.is_verified,
        type: data.verification_type as any,
        since: data.verified_at ? new Date(data.verified_at) : undefined
      }
    };

    return profile;
  } catch (error) {
    console.error('Error force syncing profile from Supabase:', error);
    toast.error('Failed to sync profile from Supabase');
    return null;
  }
}
