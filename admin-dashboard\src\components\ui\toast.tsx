interface ToastProps {
  title: string;
  description: string;
  duration?: number;
}

// Simple toast implementation
export const toast = ({ title, description, duration = 3000 }: ToastProps) => {
  // Create toast element
  const toastEl = document.createElement('div');
  toastEl.className = 'fixed top-4 right-4 z-50 bg-white dark:bg-gray-800 shadow-lg rounded-lg p-4 max-w-md transform transition-all duration-300 ease-in-out translate-y-0 opacity-0';
  toastEl.style.minWidth = '300px';
  
  // Create toast content
  toastEl.innerHTML = `
    <div class="flex items-start">
      <div class="flex-1">
        <h3 class="font-medium text-gray-900 dark:text-white">${title}</h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">${description}</p>
      </div>
      <button class="ml-4 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
      </button>
    </div>
  `;
  
  // Add to DOM
  const toaster = document.getElementById('toaster');
  if (toaster) {
    toaster.appendChild(toastEl);
    
    // Animate in
    setTimeout(() => {
      toastEl.classList.remove('translate-y-0', 'opacity-0');
      toastEl.classList.add('translate-y-2', 'opacity-100');
    }, 10);
    
    // Add close button functionality
    const closeBtn = toastEl.querySelector('button');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        removeToast();
      });
    }
    
    // Auto remove after duration
    const timeout = setTimeout(() => {
      removeToast();
    }, duration);
    
    // Remove function
    function removeToast() {
      toastEl.classList.remove('translate-y-2', 'opacity-100');
      toastEl.classList.add('translate-y-0', 'opacity-0');
      
      setTimeout(() => {
        toastEl.remove();
      }, 300);
      
      clearTimeout(timeout);
    }
  }
};
