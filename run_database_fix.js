#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to run the database fix for invite_code column
 */

import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://jcltjkaumevuycntdmds.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpjbHRqa2F1bWV2dXljbnRkbWRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTkwMDEsImV4cCI6MjA2MTg3NTAwMX0.LqQSk0NwB1mDB4Cznn0iQSrYvsAAWyUsaj8reo_etqM";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function runDatabaseFix() {
  console.log('🔧 Running database fix for invite_code column...\n');

  try {
    // Step 1: Add invite_code column if it doesn't exist
    console.log('1️⃣ Adding invite_code column...');
    const { error: columnError } = await supabase.rpc('exec_sql', {
      sql: `
        DO $$
        BEGIN
          IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'voice_chats' 
            AND column_name = 'invite_code'
          ) THEN
            ALTER TABLE public.voice_chats ADD COLUMN invite_code TEXT UNIQUE;
            CREATE INDEX IF NOT EXISTS idx_voice_chats_invite_code ON public.voice_chats(invite_code);
          END IF;
        END $$;
      `
    });

    if (columnError) {
      console.log('❌ Error adding invite_code column:', columnError.message);
    } else {
      console.log('✅ invite_code column added successfully');
    }

    // Step 2: Create group_invites table
    console.log('\n2️⃣ Creating group_invites table...');
    const { error: tableError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS public.group_invites (
          id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
          chat_id TEXT NOT NULL REFERENCES public.voice_chats(id) ON DELETE CASCADE,
          invite_code TEXT NOT NULL UNIQUE,
          created_by TEXT NOT NULL,
          max_uses INTEGER DEFAULT NULL,
          current_uses INTEGER DEFAULT 0,
          expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        CREATE INDEX IF NOT EXISTS idx_group_invites_code ON public.group_invites(invite_code);
        CREATE INDEX IF NOT EXISTS idx_group_invites_chat ON public.group_invites(chat_id);
      `
    });

    if (tableError) {
      console.log('❌ Error creating group_invites table:', tableError.message);
    } else {
      console.log('✅ group_invites table created successfully');
    }

    // Step 3: Enable RLS and create policies
    console.log('\n3️⃣ Setting up RLS policies...');
    const { error: rlsError } = await supabase.rpc('exec_sql', {
      sql: `
        ALTER TABLE public.group_invites ENABLE ROW LEVEL SECURITY;

        CREATE POLICY "Users can view invites for their chats" ON public.group_invites
          FOR SELECT USING (
            chat_id IN (
              SELECT chat_id FROM public.voice_chat_participants 
              WHERE profile_id = auth.uid()::text AND left_at IS NULL
            )
          );

        CREATE POLICY "Admins can create invites" ON public.group_invites
          FOR INSERT WITH CHECK (
            chat_id IN (
              SELECT chat_id FROM public.voice_chat_participants 
              WHERE profile_id = auth.uid()::text AND role IN ('admin', 'moderator') AND left_at IS NULL
            )
          );
      `
    });

    if (rlsError) {
      console.log('❌ Error setting up RLS policies:', rlsError.message);
    } else {
      console.log('✅ RLS policies created successfully');
    }

    console.log('\n🎉 Database fix completed successfully!');
    console.log('You can now create and delete chats properly.');

  } catch (error) {
    console.error('❌ Error running database fix:', error);
  }
}

runDatabaseFix();
