
/// <reference types="vite/client" />

interface Window {
  ethereum?: {
    isMetaMask?: boolean;
    request: (args: { method: string; params?: any[] }) => Promise<any>;
  };
  process?: {
    env: {
      NODE_ENV: string;
      DEBUG?: string;
      CERAMIC_API_URL?: string;
      DID_PRIVATE_KEY?: string;
      BROWSER?: string;
      [key: string]: string | undefined;
    };
    browser?: boolean;
    version?: string;
    platform?: string;
  };
  userContext?: {
    walletAddress?: string;
    userId?: string;
    [key: string]: any;
  };
}
