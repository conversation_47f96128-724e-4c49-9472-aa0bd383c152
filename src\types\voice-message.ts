
export interface VoiceMessageProps {
  id: string;
  userAddress: string;
  audioUrl: string;
  transcript: string;
  timestamp: Date;
  duration: number;
  isPinned?: boolean;
  media?: {
    url: string;
    type: 'image' | 'video';
    id: string;
  }[];
  replies: VoiceMessageProps[];
  channelId?: string;
  isDeleted?: boolean;
  parentId?: string;
  isReply?: boolean;
  isTextOnly?: boolean; // Flag to indicate if this is a text-only reply
}

export interface VoiceMessageReaction {
  id: string;
  emoji: string;
  userAddress: string;
  timestamp: Date;
}

export interface VoiceMessageMedia {
  url: string;
  type: string;
  id: string;
}

export interface MediaFile {
  id: string;
  url: string;
  type: 'image' | 'video';
  file?: File;
  previewUrl?: string;
  name?: string;
  size?: number;
}
