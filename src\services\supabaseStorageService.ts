
import { supabase } from '@/integrations/supabase/client';
import { v4 as uuidv4 } from 'uuid';
import { toast } from '@/components/ui/sonner';
/**
 * Ensure required storage buckets exist
 */
export async function ensureStorageBuckets(): Promise<void> {
  try {
    // Get list of buckets
    const { data: buckets, error } = await supabase.storage.listBuckets();

    if (error) {
      console.error('Error listing buckets:', error);
      // Continue execution even if we can't list buckets
      // The app should still function with existing buckets
      console.log('Continuing without creating buckets...');
      return;
    }

    const requiredBuckets = [
      {
        name: 'audio',
        public: true,
        fileSizeLimit: 50000000, // 50MB
        allowedMimeTypes: ['audio/webm', 'audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/ogg']
      },
      {
        name: 'profiles',
        public: true,
        fileSizeLimit: 5000000, // 5MB
        allowedMimeTypes: ['image/png', 'image/jpeg', 'image/gif', 'image/webp']
      },
      {
        name: 'media',
        public: true,
        fileSizeLimit: 20000000, // 20MB
        allowedMimeTypes: ['image/png', 'image/jpeg', 'image/gif', 'image/webp', 'video/mp4', 'video/webm', 'video/quicktime']
      }
    ];

    // Create missing buckets
    for (const bucket of requiredBuckets) {
      // Check if bucket already exists
      if (!buckets?.find(b => b.name === bucket.name)) {
        console.log(`Checking bucket: ${bucket.name}...`);

        try {
          // Try to create the bucket, but don't fail if we can't
          const { error } = await supabase.storage.createBucket(bucket.name, {
            public: bucket.public,
            fileSizeLimit: bucket.fileSizeLimit,
            allowedMimeTypes: bucket.allowedMimeTypes
          });

          if (error) {
            // Log the error but continue execution
            console.warn(`Note: Could not create ${bucket.name} bucket. This is expected if you don't have admin privileges.`);
            console.warn(`The app will attempt to use existing buckets.`);
          } else {
            console.log(`${bucket.name} bucket created successfully`);
          }
        } catch (bucketError) {
          // Catch any exceptions during bucket creation
          console.warn(`Note: Could not create ${bucket.name} bucket. This is expected if you don't have admin privileges.`);
          console.warn(`The app will attempt to use existing buckets.`);
        }
      } else {
        console.log(`${bucket.name} bucket already exists`);
      }
    }
  } catch (error) {
    // Catch any exceptions but allow the app to continue
    console.error('Error ensuring storage buckets:', error);
    console.log('Continuing without creating buckets...');
  }
}

/**
 * Run this on app initialization
 */
export function initializeSupabaseStorage(): void {
  // Try to ensure buckets exist, but don't block app initialization if it fails
  ensureStorageBuckets().catch(error => {
    console.error('Error during storage initialization:', error);
    console.log('App will continue without initializing storage buckets.');
  });
}

/**
 * Upload file to Supabase
 * @param file The file to upload
 * @param bucket The bucket to upload to
 * @param path The path to upload to
 * @returns URL of the uploaded file
 */
export async function uploadFile(file: File, bucket: string, path: string): Promise<string | null> {
  try {
    console.log(`Uploading file to ${bucket}/${path}`, {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type
    });

    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, file, {
        cacheControl: '3600',
        upsert: true,
        contentType: file.type
      });

    if (error) {
      console.error(`Error uploading file to ${bucket}:`, error);

      // Provide specific error messages
      if (error.message.includes('row-level security')) {
        console.error('❌ RLS Policy Error: Please run the fix_media_rls_policies.sql script in Supabase');
        toast.error('Media upload blocked by security policy. Please contact support.');
      } else if (error.message.includes('bucket')) {
        console.error('❌ Bucket Error: Bucket may not exist or have proper policies');
        toast.error('Storage bucket not configured properly.');
      } else {
        toast.error('Failed to upload file');
      }

      return null;
    }

    const { data: urlData } = supabase.storage
      .from(bucket)
      .getPublicUrl(data.path);

    console.log(`✅ File uploaded successfully: ${urlData.publicUrl}`);
    return urlData.publicUrl;
  } catch (error) {
    console.error(`Error uploading file to ${bucket}:`, error);
    toast.error('Failed to upload file');
    return null;
  }
}

/**
 * Upload audio to Supabase storage
 * @param audioBlob The audio blob
 * @param userId The user ID
 * @returns URL of the uploaded audio
 */
export async function uploadAudio(audioBlob: Blob, userId: string): Promise<string> {
  try {
    console.log('Uploading audio to Supabase storage...');
    
    // Generate a unique file name
    const fileExtension = getExtensionFromBlob(audioBlob);
    const fileName = `${userId}/${uuidv4()}.${fileExtension}`;
    
    console.log(`Uploading file: ${fileName} (${audioBlob.size} bytes, type: ${audioBlob.type})`);
    
    // Retry logic for more reliable uploads
    let retries = 3;
    let uploadedUrl = null;
    
    while (retries > 0 && !uploadedUrl) {
      try {
        // Upload to Supabase with optimized settings for larger files
        const { data, error } = await supabase.storage
          .from('audio')
          .upload(fileName, audioBlob, {
            contentType: audioBlob.type,
            cacheControl: '3600',
            upsert: true
          });

        if (error) {
          console.error('Error uploading audio to Supabase:', error);
          retries--;
          if (retries > 0) {
            console.log(`Retrying upload... (${retries} attempts left)`);
            // Wait before retrying
            await new Promise(resolve => setTimeout(resolve, 1000));
          } else {
            throw error;
          }
        } else {
          // Get the public URL
          const { data: publicUrlData } = supabase.storage
            .from('audio')
            .getPublicUrl(fileName);

          uploadedUrl = publicUrlData.publicUrl;
          console.log('Audio successfully uploaded to Supabase:', uploadedUrl);
          break;
        }
      } catch (retryError) {
        console.error(`Upload attempt failed (${retries} left):`, retryError);
        retries--;
        if (retries === 0) {
          throw retryError;
        }
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    if (!uploadedUrl) {
      throw new Error('Failed to upload audio after multiple attempts');
    }
    
    return uploadedUrl;
  } catch (error) {
    console.error('Error uploading audio:', error);
    toast.error('Failed to upload audio. Please try again.');
    throw new Error('Failed to upload audio to Supabase storage');
  }
}

/**
 * Get the correct file extension from a blob
 * @param blob The audio blob
 * @returns The file extension
 */
function getExtensionFromBlob(blob: Blob): string {
  switch (blob.type) {
    case 'audio/webm':
      return 'webm';
    case 'audio/mp4':
      return 'm4a';
    case 'audio/mpeg':
      return 'mp3';
    case 'audio/wav':
      return 'wav';
    case 'audio/ogg':
      return 'ogg';
    default:
      return 'webm';
  }
}

/**
 * Delete file from Supabase
 * @param bucket The bucket to delete from
 * @param path The path to delete
 * @returns Success status
 */
export async function deleteFile(bucket: string, path: string): Promise<boolean> {
  try {
    const { error } = await supabase.storage
      .from(bucket)
      .remove([path]);

    if (error) {
      console.error(`Error deleting file from ${bucket}:`, error);
      toast.error('Failed to delete file');
      return false;
    }

    return true;
  } catch (error) {
    console.error(`Error deleting file from ${bucket}:`, error);
    toast.error('Failed to delete file');
    return false;
  }
}

/**
 * Upload profile image to Supabase
 * @param file The image file
 * @param userId The user ID
 * @returns URL of the uploaded image
 */
export async function uploadProfileImage(file: File, userId: string): Promise<string | null> {
  const path = `profiles/${userId}/avatar-${Date.now()}.${file.name.split('.').pop()}`;
  return uploadFile(file, 'profiles', path);
}

/**
 * Upload cover image to Supabase
 * @param file The image file
 * @param userId The user ID
 * @returns URL of the uploaded image
 */
export async function uploadCoverImage(file: File, userId: string): Promise<string | null> {
  const path = `profiles/${userId}/cover-${Date.now()}.${file.name.split('.').pop()}`;
  return uploadFile(file, 'profiles', path);
}

export default {
  ensureStorageBuckets,
  initializeSupabaseStorage,
  uploadFile,
  uploadAudio,
  deleteFile,
  uploadProfileImage,
  uploadCoverImage
};
