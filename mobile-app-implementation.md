# Mobile App Implementation Guide

## Adapting Your Web App to Mobile

### 1. Shared Business Logic

Identify and extract shared business logic from your web app:

- Authentication services
- Wallet management
- API calls to Supabase
- Voice recording/playback logic

### 2. Mobile-Specific UI Components

Create mobile-optimized versions of your UI components:

- Replace web modals with full-screen mobile dialogs
- Adapt to touch interactions instead of mouse events
- Use native mobile UI patterns (bottom tabs, swipe gestures)
- Optimize for different screen sizes

### 3. Navigation Structure

```typescript
// src/navigation/AppNavigator.tsx
import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/Feather';

import HomeScreen from '../screens/main/HomeScreen';
import ProfileScreen from '../screens/main/ProfileScreen';
import NotificationsScreen from '../screens/main/NotificationsScreen';
import SettingsScreen from '../screens/main/SettingsScreen';
import PostDetailScreen from '../screens/main/PostDetailScreen';
import RecordScreen from '../screens/main/RecordScreen';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

const HomeStack = () => (
  <Stack.Navigator>
    <Stack.Screen name="Feed" component={HomeScreen} />
    <Stack.Screen name="PostDetail" component={PostDetailScreen} />
  </Stack.Navigator>
);

const ProfileStack = () => (
  <Stack.Navigator>
    <Stack.Screen name="Profile" component={ProfileScreen} />
    <Stack.Screen name="Settings" component={SettingsScreen} />
  </Stack.Navigator>
);

const AppNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Home') {
            iconName = 'home';
          } else if (route.name === 'Profile') {
            iconName = 'user';
          } else if (route.name === 'Record') {
            iconName = 'mic';
          } else if (route.name === 'Notifications') {
            iconName = 'bell';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
      })}
    >
      <Tab.Screen name="Home" component={HomeStack} />
      <Tab.Screen name="Record" component={RecordScreen} />
      <Tab.Screen name="Notifications" component={NotificationsScreen} />
      <Tab.Screen name="Profile" component={ProfileStack} />
    </Tab.Navigator>
  );
};

export default AppNavigator;
```

### 4. Handling Audio Recording

Mobile devices have different permissions and APIs for audio recording. Use the `AudioRecorder` component from the previous file.

### 5. Wallet Integration

Adapt your wallet management for mobile:

```typescript
// src/services/walletManager.ts
import { ethers } from 'ethers';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { WalletData } from './walletService';

// Constants
const WALLET_STORAGE_PREFIX = 'audra_wallet_';
const DEFAULT_NETWORK = 'base-goerli';

class WalletManager {
  private static instance: WalletManager;
  private walletCache: Map<string, WalletData> = new Map();

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  public static getInstance(): WalletManager {
    if (!WalletManager.instance) {
      WalletManager.instance = new WalletManager();
    }
    return WalletManager.instance;
  }

  public async getWallet(userId: string): Promise<WalletData> {
    // Special case for the owner
    const OWNER_WALLET_ADDRESS = '******************************************';

    // Check if this is the owner's account by user ID
    if (userId === '2cd24c86-d3c5-4406-a92c-1f0892495e0a') {
      console.log('Owner account detected by user ID');
      
      // Create a wallet with the owner's address
      const ownerWallet: WalletData = {
        address: OWNER_WALLET_ADDRESS,
        privateKey: '', // We don't have the private key for security reasons
        balance: '0.0',
        network: DEFAULT_NETWORK as 'base' | 'base-goerli',
        hasBackup: true
      };

      // Store it for future use
      await this.storeWallet(userId, ownerWallet);
      this.walletCache.set(userId, ownerWallet);
      
      // Mark as owner in AsyncStorage for future reference
      await AsyncStorage.setItem('is_owner', 'true');
      
      return ownerWallet;
    }

    // Check if this is the owner's account by stored wallet address
    const storedWallet = await this.getStoredWallet(userId);
    if (storedWallet && storedWallet.address.toLowerCase() === OWNER_WALLET_ADDRESS.toLowerCase()) {
      console.log('Owner account detected by wallet address');
      this.walletCache.set(userId, storedWallet);
      return storedWallet;
    }

    // Check if this user is marked as the owner in AsyncStorage
    const isOwner = await AsyncStorage.getItem('is_owner') === 'true';
    if (isOwner) {
      console.log('Owner account detected by AsyncStorage flag');
      
      // Create a wallet with the owner's address
      const ownerWallet: WalletData = {
        address: OWNER_WALLET_ADDRESS,
        privateKey: '', // We don't have the private key for security reasons
        balance: '0.0',
        network: DEFAULT_NETWORK as 'base' | 'base-goerli',
        hasBackup: true
      };

      // Store it for future use
      await this.storeWallet(userId, ownerWallet);
      this.walletCache.set(userId, ownerWallet);

      return ownerWallet;
    }

    // For non-owner users, always create a deterministic wallet based on user ID
    // This ensures the same wallet address across all devices
    const wallet = this.createDeterministicWallet(userId);
    
    // Store it for future use
    await this.storeWallet(userId, wallet);
    this.walletCache.set(userId, wallet);
    
    console.log(`Deterministic wallet created for user ${userId}: ${wallet.address}`);
    
    return wallet;
  }

  private createDeterministicWallet(userId: string): WalletData {
    try {
      // Create a deterministic private key from the user ID
      const privateKey = this.generateDeterministicPrivateKey(userId);
      const wallet = new ethers.Wallet(privateKey);

      return {
        address: wallet.address,
        privateKey: wallet.privateKey,
        balance: '0.0',
        network: DEFAULT_NETWORK as 'base' | 'base-goerli',
        hasBackup: false
      };
    } catch (error) {
      console.error('Error creating deterministic wallet:', error);
      throw new Error('Failed to create wallet');
    }
  }

  private generateDeterministicPrivateKey(userId: string): string {
    // We need to ensure the private key is valid for Ethereum
    // It must be 32 bytes (64 hex characters) without 0x prefix

    // First, create a hash of the user ID
    const hash = ethers.utils.id(userId); // keccak256 hash

    // The hash is already 32 bytes, so we can use it directly
    return hash;
  }

  private async storeWallet(userId: string, wallet: WalletData): Promise<void> {
    try {
      await AsyncStorage.setItem(
        `${WALLET_STORAGE_PREFIX}${userId}`,
        JSON.stringify(wallet)
      );
    } catch (error) {
      console.error('Error storing wallet:', error);
    }
  }

  private async getStoredWallet(userId: string): Promise<WalletData | null> {
    try {
      const storedWallet = await AsyncStorage.getItem(`${WALLET_STORAGE_PREFIX}${userId}`);
      if (storedWallet) {
        return JSON.parse(storedWallet);
      }
      return null;
    } catch (error) {
      console.error('Error getting stored wallet:', error);
      return null;
    }
  }
}

// Export the singleton instance
export const walletManager = WalletManager.getInstance();

export default walletManager;
```
