import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from '@/components/ui/use-toast';
import {
  Users,
  UserPlus,
  Link,
  Crown,
  Shield,
  UserMinus,
  Copy,
  Settings,
  ExternalLink,
  Calendar,
  Hash,
  Edit3,
  Trash2,
  MoreVertical,
  Image,
  Lock,
  Globe,
  UserCheck,
  MessageSquare
} from 'lucide-react';
import { voiceChatService, VoiceChat, VoiceChatParticipant, GroupInvite } from '@/services/voiceChatService';
import { useAuth } from '@/contexts/AuthContext';

interface GroupManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  chat: VoiceChat;
  onChatUpdate: (updatedChat: VoiceChat) => void;
}

const GroupManagementModal: React.FC<GroupManagementModalProps> = ({
  isOpen,
  onClose,
  chat,
  onChatUpdate
}) => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [participants, setParticipants] = useState<VoiceChatParticipant[]>([]);
  const [invites, setInvites] = useState<GroupInvite[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Group settings state
  const [groupName, setGroupName] = useState(chat.name);
  const [groupDescription, setGroupDescription] = useState(chat.description || '');
  const [isPrivate, setIsPrivate] = useState(chat.is_private);
  const [allowMemberInvites, setAllowMemberInvites] = useState(true);
  const [requireApproval, setRequireApproval] = useState(false);
  const [muteNonAdmins, setMuteNonAdmins] = useState(false);

  // Invite settings
  const [newUserEmail, setNewUserEmail] = useState('');
  const [inviteExpireDays, setInviteExpireDays] = useState(7);
  const [inviteMaxUses, setInviteMaxUses] = useState<number | undefined>(undefined);

  // Check if current user is admin
  const isCurrentUserAdmin = participants.find(p => p.profile_id === user?.id)?.role === 'admin';

  useEffect(() => {
    if (isOpen) {
      loadGroupDetails();
      loadGroupInvites();
    }
  }, [isOpen, chat.id]);

  const loadGroupDetails = async () => {
    try {
      const groupDetails = await voiceChatService.getGroupDetails(chat.id);
      if (groupDetails?.participants) {
        setParticipants(groupDetails.participants);
      }
    } catch (error) {
      console.error('Error loading group details:', error);
    }
  };

  const loadGroupInvites = async () => {
    try {
      const groupInvites = await voiceChatService.getGroupInvites(chat.id);
      setInvites(groupInvites);
    } catch (error) {
      console.error('Error loading group invites:', error);
    }
  };

  const handleAddUser = async () => {
    if (!newUserEmail.trim() || !user?.id) return;

    setIsLoading(true);
    try {
      // In a real app, you'd look up user by email
      // For now, we'll assume the email is the user ID
      const success = await voiceChatService.addUserToGroup(
        chat.id,
        newUserEmail.trim(),
        user.id
      );

      if (success) {
        toast({
          title: "User Added",
          description: "User has been successfully added to the group",
        });
        setNewUserEmail('');
        loadGroupDetails();
      } else {
        toast({
          title: "Error",
          description: "Failed to add user to group",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add user to group",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveUser = async (userId: string) => {
    if (!user?.id) return;

    try {
      const success = await voiceChatService.removeUserFromGroup(chat.id, userId, user.id);
      if (success) {
        toast({
          title: "User Removed",
          description: "User has been removed from the group",
        });
        loadGroupDetails();
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to remove user",
        variant: "destructive",
      });
    }
  };

  const handlePromoteUser = async (userId: string, newRole: 'admin' | 'moderator') => {
    if (!user?.id) return;

    try {
      const success = await voiceChatService.promoteUser(chat.id, userId, newRole, user.id);
      if (success) {
        toast({
          title: "User Promoted",
          description: `User has been promoted to ${newRole}`,
        });
        loadGroupDetails();
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to promote user",
        variant: "destructive",
      });
    }
  };

  const handleCreateInvite = async () => {
    if (!user?.id) return;

    setIsLoading(true);
    try {
      const invite = await voiceChatService.createInviteLink(
        chat.id,
        user.id,
        inviteMaxUses,
        inviteExpireDays
      );

      if (invite) {
        toast({
          title: "Invite Created",
          description: "Invite link has been created successfully",
        });
        loadGroupInvites();
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create invite link",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const copyInviteLink = (inviteCode: string) => {
    const inviteUrl = `${window.location.origin}/join/${inviteCode}`;
    navigator.clipboard.writeText(inviteUrl);
    toast({
      title: "Copied!",
      description: "Invite link copied to clipboard",
    });
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Crown className="h-4 w-4 text-yellow-500" />;
      case 'moderator':
        return <Shield className="h-4 w-4 text-blue-500" />;
      default:
        return <Users className="h-4 w-4 text-gray-500" />;
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case 'moderator':
        return 'bg-blue-100 text-blue-800 border-blue-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Manage Group: {chat.name}
          </DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="members" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Members ({participants.length})
            </TabsTrigger>
            <TabsTrigger value="invites" className="flex items-center gap-2">
              <Link className="h-4 w-4" />
              Invites ({invites.length})
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Settings
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="flex-1 mt-4">
            <div className="space-y-6">
              {/* Group Info */}
              <div className="flex items-start gap-4 p-4 bg-muted/30 rounded-lg">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={chat.avatar_url} />
                  <AvatarFallback className="text-lg">
                    {chat.name.slice(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold">{chat.name}</h3>
                  <p className="text-muted-foreground mt-1">
                    {chat.description || 'No description'}
                  </p>
                  <div className="flex items-center gap-4 mt-3 text-sm text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      {participants.length} members
                    </span>
                    <span className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      Created {new Date(chat.created_at).toLocaleDateString()}
                    </span>
                    <Badge variant={chat.is_private ? "secondary" : "outline"}>
                      {chat.is_private ? <Lock className="h-3 w-3 mr-1" /> : <Globe className="h-3 w-3 mr-1" />}
                      {chat.is_private ? 'Private' : 'Public'}
                    </Badge>
                  </div>
                </div>
                {isCurrentUserAdmin && (
                  <Button variant="outline" size="sm" onClick={() => setActiveTab('settings')}>
                    <Edit3 className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                )}
              </div>

              {/* Quick Actions */}
              <div className="grid grid-cols-2 gap-4">
                <Button variant="outline" onClick={() => setActiveTab('members')} className="h-20 flex-col gap-2">
                  <Users className="h-6 w-6" />
                  <span>Manage Members</span>
                  <span className="text-xs text-muted-foreground">{participants.length} members</span>
                </Button>
                <Button variant="outline" onClick={() => setActiveTab('invites')} className="h-20 flex-col gap-2">
                  <Link className="h-6 w-6" />
                  <span>Invite Links</span>
                  <span className="text-xs text-muted-foreground">{invites.length} active</span>
                </Button>
              </div>

              {/* Recent Activity */}
              <div>
                <h4 className="font-medium mb-3">Recent Activity</h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-3 p-3 bg-muted/20 rounded-lg">
                    <UserPlus className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Group created</span>
                    <span className="text-xs text-muted-foreground ml-auto">
                      {new Date(chat.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="members" className="flex-1 mt-4">
            <div className="space-y-4">
              {/* Add User Section */}
              {isCurrentUserAdmin && (
                <div className="p-4 border rounded-lg bg-muted/50">
                  <Label htmlFor="newUser" className="text-sm font-medium">
                    Add New Member
                  </Label>
                  <div className="flex gap-2 mt-2">
                    <Input
                      id="newUser"
                      placeholder="Enter user email or ID"
                      value={newUserEmail}
                      onChange={(e) => setNewUserEmail(e.target.value)}
                      className="flex-1"
                    />
                    <Button 
                      onClick={handleAddUser} 
                      disabled={!newUserEmail.trim() || isLoading}
                      size="sm"
                    >
                      <UserPlus className="h-4 w-4 mr-1" />
                      Add
                    </Button>
                  </div>
                </div>
              )}

              {/* Members List */}
              <ScrollArea className="h-[300px]">
                <div className="space-y-2">
                  {participants.map((participant) => (
                    <div key={participant.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                          {participant.profile?.display_name?.[0] || participant.profile?.username?.[0] || 'U'}
                        </div>
                        <div>
                          <div className="font-medium">
                            {participant.profile?.display_name || participant.profile?.username || 'Unknown User'}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            Joined {new Date(participant.joined_at).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Badge className={getRoleBadgeColor(participant.role)}>
                          <span className="flex items-center gap-1">
                            {getRoleIcon(participant.role)}
                            {participant.role}
                          </span>
                        </Badge>
                        
                        {isCurrentUserAdmin && participant.profile_id !== user?.id && (
                          <div className="flex gap-1">
                            {participant.role !== 'admin' && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handlePromoteUser(participant.profile_id, 'admin')}
                              >
                                <Crown className="h-3 w-3" />
                              </Button>
                            )}
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleRemoveUser(participant.profile_id)}
                            >
                              <UserMinus className="h-3 w-3" />
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          </TabsContent>

          <TabsContent value="invites" className="flex-1 mt-4">
            <div className="space-y-4">
              {/* Create Invite Section */}
              {isCurrentUserAdmin && (
                <div className="p-4 border rounded-lg bg-muted/50">
                  <h4 className="font-medium mb-3">Create New Invite</h4>
                  <div className="grid grid-cols-2 gap-3 mb-3">
                    <div>
                      <Label htmlFor="expireDays" className="text-sm">Expires in (days)</Label>
                      <Input
                        id="expireDays"
                        type="number"
                        value={inviteExpireDays}
                        onChange={(e) => setInviteExpireDays(Number(e.target.value))}
                        min="1"
                        max="30"
                      />
                    </div>
                    <div>
                      <Label htmlFor="maxUses" className="text-sm">Max uses (optional)</Label>
                      <Input
                        id="maxUses"
                        type="number"
                        value={inviteMaxUses || ''}
                        onChange={(e) => setInviteMaxUses(e.target.value ? Number(e.target.value) : undefined)}
                        placeholder="Unlimited"
                        min="1"
                      />
                    </div>
                  </div>
                  <Button onClick={handleCreateInvite} disabled={isLoading} className="w-full">
                    <Link className="h-4 w-4 mr-2" />
                    Create Invite Link
                  </Button>
                </div>
              )}

              {/* Existing Invites */}
              <ScrollArea className="h-[300px]">
                <div className="space-y-2">
                  {invites.map((invite) => (
                    <div key={invite.id} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <Hash className="h-4 w-4 text-muted-foreground" />
                          <code className="text-sm bg-muted px-2 py-1 rounded">
                            {invite.invite_code}
                          </code>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => copyInviteLink(invite.invite_code)}
                        >
                          <Copy className="h-3 w-3 mr-1" />
                          Copy
                        </Button>
                      </div>
                      <div className="text-sm text-muted-foreground space-y-1">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-3 w-3" />
                          Expires: {new Date(invite.expires_at).toLocaleDateString()}
                        </div>
                        <div>
                          Uses: {invite.current_uses} / {invite.max_uses || '∞'}
                        </div>
                      </div>
                    </div>
                  ))}
                  {invites.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      No active invites
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>
          </TabsContent>

          <TabsContent value="settings" className="flex-1 mt-4">
            <ScrollArea className="h-[400px]">
              <div className="space-y-6 pr-4">
                {/* Basic Group Settings */}
                <div className="space-y-4">
                  <h4 className="font-medium flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    Group Settings
                  </h4>

                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="groupName">Group Name</Label>
                      <Input
                        id="groupName"
                        value={groupName}
                        onChange={(e) => setGroupName(e.target.value)}
                        disabled={!isCurrentUserAdmin}
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label htmlFor="groupDescription">Description</Label>
                      <Textarea
                        id="groupDescription"
                        value={groupDescription}
                        onChange={(e) => setGroupDescription(e.target.value)}
                        disabled={!isCurrentUserAdmin}
                        className="mt-1"
                        rows={3}
                        placeholder="Describe what this group is about..."
                      />
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Privacy Settings */}
                <div className="space-y-4">
                  <h4 className="font-medium flex items-center gap-2">
                    <Lock className="h-4 w-4" />
                    Privacy & Permissions
                  </h4>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Private Group</Label>
                        <p className="text-sm text-muted-foreground">Only invited members can join</p>
                      </div>
                      <Switch
                        checked={isPrivate}
                        onCheckedChange={setIsPrivate}
                        disabled={!isCurrentUserAdmin}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Members Can Invite</Label>
                        <p className="text-sm text-muted-foreground">Allow members to invite others</p>
                      </div>
                      <Switch
                        checked={allowMemberInvites}
                        onCheckedChange={setAllowMemberInvites}
                        disabled={!isCurrentUserAdmin}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Require Admin Approval</Label>
                        <p className="text-sm text-muted-foreground">New members need admin approval</p>
                      </div>
                      <Switch
                        checked={requireApproval}
                        onCheckedChange={setRequireApproval}
                        disabled={!isCurrentUserAdmin}
                      />
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Message Settings */}
                <div className="space-y-4">
                  <h4 className="font-medium flex items-center gap-2">
                    <MessageSquare className="h-4 w-4" />
                    Message Settings
                  </h4>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Mute Non-Admins</Label>
                        <p className="text-sm text-muted-foreground">Only admins can send messages</p>
                      </div>
                      <Switch
                        checked={muteNonAdmins}
                        onCheckedChange={setMuteNonAdmins}
                        disabled={!isCurrentUserAdmin}
                      />
                    </div>

                    <div className="p-3 bg-muted/30 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Edit3 className="h-4 w-4" />
                        <span className="font-medium text-sm">Message Editing</span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Members can edit their text messages (voice messages cannot be edited)
                      </p>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Danger Zone */}
                {isCurrentUserAdmin && (
                  <div className="space-y-4">
                    <h4 className="font-medium flex items-center gap-2 text-red-600">
                      <Trash2 className="h-4 w-4" />
                      Danger Zone
                    </h4>

                    <div className="p-4 border border-red-200 rounded-lg bg-red-50/50">
                      <div className="space-y-3">
                        <div>
                          <h5 className="font-medium text-red-800">Delete Group</h5>
                          <p className="text-sm text-red-600">
                            Permanently delete this group and all its messages. This cannot be undone.
                          </p>
                        </div>
                        <Button variant="destructive" size="sm">
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete Group
                        </Button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Save Button */}
                {isCurrentUserAdmin && (
                  <div className="flex justify-end gap-2 pt-4">
                    <Button variant="outline" onClick={onClose}>
                      Cancel
                    </Button>
                    <Button onClick={() => {/* TODO: Save settings */}}>
                      Save Changes
                    </Button>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default GroupManagementModal;
