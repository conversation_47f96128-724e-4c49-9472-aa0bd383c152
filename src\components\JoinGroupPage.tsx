import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from '@/components/ui/use-toast';
import { 
  Users, 
  Lock, 
  Globe, 
  MessageCircle, 
  CheckCircle, 
  AlertCircle,
  Loader2
} from 'lucide-react';
import { voiceChatService } from '@/services/voiceChatService';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

interface JoinGroupPageProps {
  userAddress: string;
}

const JoinGroupPage: React.FC<JoinGroupPageProps> = ({ userAddress }) => {
  const { inviteCode } = useParams<{ inviteCode: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isJoining, setIsJoining] = useState(false);
  const [groupInfo, setGroupInfo] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [alreadyMember, setAlreadyMember] = useState(false);

  useEffect(() => {
    const checkInviteCode = async () => {
      if (!inviteCode || !user?.id) return;

      try {
        setIsLoading(true);
        
        // Get group info from invite code
        const { data: chat, error: chatError } = await supabase
          .from('voice_chats')
          .select(`
            id,
            name,
            description,
            type,
            is_private,
            avatar_url,
            created_at,
            voice_chat_participants(profile_id)
          `)
          .eq('invite_code', inviteCode)
          .single();

        if (chatError || !chat) {
          setError('Invalid or expired invite link');
          return;
        }

        setGroupInfo(chat);

        // Check if user is already a member
        const isMember = chat.voice_chat_participants?.some(
          (p: any) => p.profile_id === user.id
        );
        setAlreadyMember(isMember);

      } catch (error) {
        console.error('Error checking invite code:', error);
        setError('Failed to load group information');
      } finally {
        setIsLoading(false);
      }
    };

    checkInviteCode();
  }, [inviteCode, user?.id]);

  const handleJoinGroup = async () => {
    if (!inviteCode || !user?.id) return;

    try {
      setIsJoining(true);
      
      const result = await voiceChatService.joinGroupViaInvite(inviteCode, user.id);
      
      if (result.success) {
        toast({
          title: "Successfully joined group!",
          description: `Welcome to ${groupInfo.name}`,
        });
        
        // Navigate to messages page
        navigate('/messages');
      } else {
        toast({
          title: "Failed to join group",
          description: result.error || "An error occurred",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error joining group:', error);
      toast({
        title: "Error",
        description: "Failed to join group",
        variant: "destructive",
      });
    } finally {
      setIsJoining(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md">
          <CardContent className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading group information...</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <CardTitle>Invalid Invite Link</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => navigate('/messages')} 
              className="w-full"
            >
              Go to Messages
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Avatar className="h-20 w-20">
              <AvatarImage src={groupInfo.avatar_url} />
              <AvatarFallback className="text-2xl">
                {groupInfo.name.slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
          </div>
          
          <CardTitle className="text-xl">{groupInfo.name}</CardTitle>
          <CardDescription>
            {groupInfo.description || 'Join this group to start chatting'}
          </CardDescription>
          
          <div className="flex items-center justify-center gap-4 mt-4">
            <Badge variant={groupInfo.is_private ? "secondary" : "outline"}>
              {groupInfo.is_private ? (
                <>
                  <Lock className="h-3 w-3 mr-1" />
                  Private
                </>
              ) : (
                <>
                  <Globe className="h-3 w-3 mr-1" />
                  Public
                </>
              )}
            </Badge>
            
            <Badge variant="outline">
              <Users className="h-3 w-3 mr-1" />
              {groupInfo.voice_chat_participants?.length || 0} members
            </Badge>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {alreadyMember ? (
            <div className="text-center space-y-4">
              <div className="flex items-center justify-center gap-2 text-green-600">
                <CheckCircle className="h-5 w-5" />
                <span>You're already a member of this group</span>
              </div>
              
              <Button 
                onClick={() => navigate('/messages')} 
                className="w-full"
              >
                <MessageCircle className="h-4 w-4 mr-2" />
                Go to Messages
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="text-center text-sm text-muted-foreground">
                You've been invited to join this group
              </div>
              
              <Button 
                onClick={handleJoinGroup}
                disabled={isJoining}
                className="w-full"
              >
                {isJoining ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Joining...
                  </>
                ) : (
                  <>
                    <Users className="h-4 w-4 mr-2" />
                    Join Group
                  </>
                )}
              </Button>
              
              <Button 
                variant="outline" 
                onClick={() => navigate('/messages')} 
                className="w-full"
              >
                Cancel
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default JoinGroupPage;
