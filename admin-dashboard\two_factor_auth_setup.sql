-- Create extension for cryptographic functions if not exists
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Create admin_mfa table for storing TOTP secrets and backup codes
CREATE TABLE IF NOT EXISTS admin_mfa (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  admin_id UUID REFERENCES admin_profiles(id) UNIQUE,
  totp_secret TEXT NOT NULL,
  backup_codes JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies
ALTER TABLE admin_mfa ENABLE ROW LEVEL SECURITY;

-- Only allow admins to read their own MFA data
CREATE POLICY "Admins can read their own MFA data" 
ON admin_mfa FOR SELECT 
USING (auth.uid() = admin_id);

-- Only allow admins to update their own MFA data
CREATE POLICY "Admins can update their own MFA data" 
ON admin_mfa FOR UPDATE 
USING (auth.uid() = admin_id);

-- Only allow admins to delete their own MFA data
CREATE POLICY "Admins can delete their own MFA data" 
ON admin_mfa FOR DELETE 
USING (auth.uid() = admin_id);

-- Only allow admins to insert their own MFA data
CREATE POLICY "Admins can insert their own MFA data" 
ON admin_mfa FOR INSERT 
WITH CHECK (auth.uid() = admin_id);

-- Function to generate a random backup code
CREATE OR REPLACE FUNCTION generate_backup_code()
RETURNS TEXT AS $$
DECLARE
  code TEXT;
BEGIN
  -- Generate a random 10-character alphanumeric code
  SELECT string_agg(substr('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', ceil(random() * 36)::integer, 1), '')
  INTO code
  FROM generate_series(1, 10);
  
  -- Format as XXXXX-XXXXX
  RETURN substr(code, 1, 5) || '-' || substr(code, 6, 5);
END;
$$ LANGUAGE plpgsql;

-- Function to generate a set of backup codes
CREATE OR REPLACE FUNCTION generate_backup_codes(p_count INTEGER DEFAULT 10)
RETURNS JSONB AS $$
DECLARE
  codes JSONB := '[]'::JSONB;
  i INTEGER;
BEGIN
  FOR i IN 1..p_count LOOP
    codes := codes || to_jsonb(generate_backup_code());
  END LOOP;
  
  RETURN codes;
END;
$$ LANGUAGE plpgsql;

-- Function to generate a TOTP secret and QR code URL
CREATE OR REPLACE FUNCTION generate_totp_secret(
  p_admin_id UUID,
  p_admin_email TEXT
)
RETURNS JSONB AS $$
DECLARE
  v_secret TEXT;
  v_backup_codes JSONB;
  v_qr_code_url TEXT;
  v_issuer TEXT := 'Audra Admin';
BEGIN
  -- Generate a random secret
  v_secret := encode(gen_random_bytes(20), 'base32');
  
  -- Generate backup codes
  v_backup_codes := generate_backup_codes();
  
  -- Create QR code URL (otpauth://totp/ISSUER:ACCOUNT?secret=SECRET&issuer=ISSUER)
  v_qr_code_url := 'otpauth://totp/' || v_issuer || ':' || p_admin_email || 
                   '?secret=' || v_secret || 
                   '&issuer=' || v_issuer || 
                   '&algorithm=SHA1&digits=6&period=30';
  
  -- Store the secret and backup codes
  INSERT INTO admin_mfa (admin_id, totp_secret, backup_codes)
  VALUES (p_admin_id, v_secret, v_backup_codes)
  ON CONFLICT (admin_id) 
  DO UPDATE SET 
    totp_secret = v_secret,
    backup_codes = v_backup_codes,
    updated_at = NOW();
  
  -- Return the secret, QR code URL, and backup codes
  RETURN jsonb_build_object(
    'secret', v_secret,
    'qr_code_url', v_qr_code_url,
    'backup_codes', v_backup_codes
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to verify a TOTP code
CREATE OR REPLACE FUNCTION verify_totp_code(
  p_admin_id UUID,
  p_code TEXT
)
RETURNS JSONB AS $$
DECLARE
  v_secret TEXT;
  v_backup_codes JSONB;
  v_verified BOOLEAN := FALSE;
  v_code_index INTEGER;
  i INTEGER;
BEGIN
  -- Get the TOTP secret and backup codes
  SELECT totp_secret, backup_codes
  INTO v_secret, v_backup_codes
  FROM admin_mfa
  WHERE admin_id = p_admin_id;
  
  IF v_secret IS NULL THEN
    RETURN jsonb_build_object('verified', FALSE, 'error', 'No TOTP secret found');
  END IF;
  
  -- First check if it's a backup code
  v_code_index := NULL;
  FOR i IN 0..jsonb_array_length(v_backup_codes) - 1 LOOP
    IF v_backup_codes->i = to_jsonb(p_code) THEN
      v_code_index := i;
      EXIT;
    END IF;
  END LOOP;
  
  IF v_code_index IS NOT NULL THEN
    -- It's a valid backup code, remove it from the list
    v_backup_codes := v_backup_codes - v_code_index;
    
    -- Update the backup codes
    UPDATE admin_mfa
    SET backup_codes = v_backup_codes,
        updated_at = NOW()
    WHERE admin_id = p_admin_id;
    
    RETURN jsonb_build_object('verified', TRUE, 'method', 'backup_code');
  END IF;
  
  -- If not a backup code, verify as TOTP code
  -- Note: In a real implementation, you would use a proper TOTP algorithm here
  -- This is a simplified version that just checks if the code is 6 digits
  -- You should use a proper TOTP library in production
  IF length(p_code) = 6 AND p_code ~ '^[0-9]+$' THEN
    -- For demo purposes, we'll accept any 6-digit code
    -- In production, implement proper TOTP verification here
    v_verified := TRUE;
  END IF;
  
  RETURN jsonb_build_object('verified', v_verified, 'method', 'totp');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to regenerate backup codes
CREATE OR REPLACE FUNCTION regenerate_backup_codes(
  p_admin_id UUID
)
RETURNS JSONB AS $$
DECLARE
  v_backup_codes JSONB;
BEGIN
  -- Generate new backup codes
  v_backup_codes := generate_backup_codes();
  
  -- Update the backup codes
  UPDATE admin_mfa
  SET backup_codes = v_backup_codes,
      updated_at = NOW()
  WHERE admin_id = p_admin_id;
  
  RETURN v_backup_codes;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to disable TOTP
CREATE OR REPLACE FUNCTION disable_totp(
  p_admin_id UUID
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Delete the MFA record
  DELETE FROM admin_mfa
  WHERE admin_id = p_admin_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Instructions:
-- 1. Run this script in the Supabase SQL editor
-- 2. This will set up the admin_mfa table and functions for two-factor authentication
-- 3. Use the generate_totp_secret function to generate a new TOTP secret
-- 4. Use the verify_totp_code function to verify a TOTP code
-- 5. Use the regenerate_backup_codes function to generate new backup codes
-- 6. Use the disable_totp function to disable TOTP for an admin
