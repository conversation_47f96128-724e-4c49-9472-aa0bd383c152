
import { useState, useEffect } from 'react'
import { Routes, Route, Navigate, BrowserRouter } from 'react-router-dom'
import { Toaster } from './components/ui/toaster'
import Login from './pages/Login'
import Dashboard from './pages/Dashboard'
import Users from './pages/Users'
import Verifications from './pages/Verifications'
import ContentModeration from './pages/ContentModeration'
import ApiKeys from './pages/ApiKeys'
import Analytics from './pages/Analytics'
import Settings from './pages/Settings'
import AdminManagement from './pages/AdminManagement'
import AuditLogs from './pages/AuditLogs'
import TwoFactorAuth from './pages/TwoFactorAuth'
import Channels from './pages/Channels'
import Layout from './components/Layout'
import { AuthProvider, useAuth } from './contexts/AuthContext'

// Protected route component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated, isLoading } = useAuth()

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p className="text-lg">Loading...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    console.log('Not authenticated, redirecting to login')
    return <Navigate to="/login" replace />
  }

  return <>{children}</>
}

function AppRoutes() {
  const { isAuthenticated } = useAuth()

  return (
    <Routes>
      <Route path="/login" element={
        isAuthenticated ? (
          <Navigate to="/" replace />
        ) : (
          <Login />
        )
      } />

      <Route path="/" element={
        <ProtectedRoute>
          <Layout>
            <Dashboard />
          </Layout>
        </ProtectedRoute>
      } />

      <Route path="/users" element={
        <ProtectedRoute>
          <Layout>
            <Users />
          </Layout>
        </ProtectedRoute>
      } />

      <Route path="/verifications" element={
        <ProtectedRoute>
          <Layout>
            <Verifications />
          </Layout>
        </ProtectedRoute>
      } />

      <Route path="/content-moderation" element={
        <ProtectedRoute>
          <Layout>
            <ContentModeration />
          </Layout>
        </ProtectedRoute>
      } />

      <Route path="/api-keys" element={
        <ProtectedRoute>
          <Layout>
            <ApiKeys />
          </Layout>
        </ProtectedRoute>
      } />

      <Route path="/analytics" element={
        <ProtectedRoute>
          <Layout>
            <Analytics />
          </Layout>
        </ProtectedRoute>
      } />

      <Route path="/settings" element={
        <ProtectedRoute>
          <Layout>
            <Settings />
          </Layout>
        </ProtectedRoute>
      } />

      <Route path="/admin-management" element={
        <ProtectedRoute>
          <Layout>
            <AdminManagement />
          </Layout>
        </ProtectedRoute>
      } />

      <Route path="/audit-logs" element={
        <ProtectedRoute>
          <Layout>
            <AuditLogs />
          </Layout>
        </ProtectedRoute>
      } />

      <Route path="/two-factor-auth" element={
        <ProtectedRoute>
          <Layout>
            <TwoFactorAuth />
          </Layout>
        </ProtectedRoute>
      } />

      <Route path="/channels" element={
        <ProtectedRoute>
          <Layout>
            <Channels />
          </Layout>
        </ProtectedRoute>
      } />

      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  )
}

function App() {
  return (
    <AuthProvider>
      <AppRoutes />
      <Toaster />
    </AuthProvider>
  )
}

export default App
