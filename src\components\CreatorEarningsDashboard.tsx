import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { supabase } from '@/lib/supabase';
import { 
  DollarSign, 
  TrendingUp, 
  Users, 
  Heart,
  Calendar,
  Download,
  Eye,
  Zap,
  Gift,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';

interface EarningsData {
  period_start: string;
  period_end: string;
  tips_received_count: number;
  tips_received_amount: number;
  tips_received_usd: number;
  rewards_earned_count: number;
  rewards_earned_amount: number;
  rewards_earned_usd: number;
  voice_posts_count: number;
  voice_replies_count: number;
  engagement_score: number;
  top_tippers: any[];
}

interface CreatorStats {
  totalEarnings: number;
  totalTips: number;
  totalRewards: number;
  totalContent: number;
  avgTipAmount: number;
  topChannel: string;
  growthRate: number;
}

interface CreatorEarningsDashboardProps {
  userAddress: string;
}

const CreatorEarningsDashboard: React.FC<CreatorEarningsDashboardProps> = ({
  userAddress
}) => {
  const [earnings, setEarnings] = useState<EarningsData[]>([]);
  const [stats, setStats] = useState<CreatorStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [selectedChannel, setSelectedChannel] = useState('all');
  const [channels, setChannels] = useState<any[]>([]);

  useEffect(() => {
    loadEarningsData();
    loadChannels();
  }, [userAddress, selectedPeriod, selectedChannel]);

  const loadEarningsData = async () => {
    try {
      setLoading(true);

      // Get user profile
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('wallet_address', userAddress)
        .single();

      if (profileError || !profileData) {
        console.error('Profile not found:', profileError);
        return;
      }

      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      
      switch (selectedPeriod) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(endDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(endDate.getDate() - 90);
          break;
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
      }

      // Get earnings data
      let query = supabase
        .from('creator_earnings')
        .select('*')
        .eq('profile_id', profileData.id)
        .gte('period_start', startDate.toISOString().split('T')[0])
        .lte('period_end', endDate.toISOString().split('T')[0])
        .order('period_start', { ascending: false });

      if (selectedChannel !== 'all') {
        query = query.eq('channel_id', selectedChannel);
      }

      const { data: earningsData, error: earningsError } = await query;

      if (earningsError) {
        console.error('Error loading earnings:', earningsError);
        return;
      }

      setEarnings(earningsData || []);

      // Calculate stats
      if (earningsData && earningsData.length > 0) {
        const totalEarnings = earningsData.reduce((sum, e) => sum + (e.tips_received_usd + e.rewards_earned_usd), 0);
        const totalTips = earningsData.reduce((sum, e) => sum + e.tips_received_count, 0);
        const totalRewards = earningsData.reduce((sum, e) => sum + e.rewards_earned_count, 0);
        const totalContent = earningsData.reduce((sum, e) => sum + e.voice_posts_count + e.voice_replies_count, 0);
        const avgTipAmount = totalTips > 0 ? earningsData.reduce((sum, e) => sum + e.tips_received_usd, 0) / totalTips : 0;

        // Calculate growth rate (comparing first and last periods)
        const firstPeriod = earningsData[earningsData.length - 1];
        const lastPeriod = earningsData[0];
        const growthRate = firstPeriod ? 
          ((lastPeriod.tips_received_usd + lastPeriod.rewards_earned_usd) - 
           (firstPeriod.tips_received_usd + firstPeriod.rewards_earned_usd)) / 
           (firstPeriod.tips_received_usd + firstPeriod.rewards_earned_usd) * 100 : 0;

        setStats({
          totalEarnings,
          totalTips,
          totalRewards,
          totalContent,
          avgTipAmount,
          topChannel: 'General', // This would be calculated from data
          growthRate
        });
      }

    } catch (error) {
      console.error('Error loading earnings data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadChannels = async () => {
    try {
      // Get user profile
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('wallet_address', userAddress)
        .single();

      if (profileError || !profileData) return;

      // Get channels where user is a member
      const { data: channelData, error: channelError } = await supabase
        .from('channel_members')
        .select(`
          channel:channels(id, name)
        `)
        .eq('profile_id', profileData.id);

      if (channelError) {
        console.error('Error loading channels:', channelError);
        return;
      }

      const channelList = channelData?.map(cm => cm.channel).filter(Boolean) || [];
      setChannels(channelList);

    } catch (error) {
      console.error('Error loading channels:', error);
    }
  };

  const exportData = () => {
    // Create CSV data
    const csvData = earnings.map(e => ({
      Period: `${e.period_start} to ${e.period_end}`,
      'Tips Received': e.tips_received_count,
      'Tips Amount (USD)': e.tips_received_usd.toFixed(2),
      'Rewards Earned': e.rewards_earned_count,
      'Rewards Amount (USD)': e.rewards_earned_usd.toFixed(2),
      'Voice Posts': e.voice_posts_count,
      'Voice Replies': e.voice_replies_count,
      'Engagement Score': e.engagement_score.toFixed(2)
    }));

    // Convert to CSV string
    const headers = Object.keys(csvData[0] || {});
    const csvString = [
      headers.join(','),
      ...csvData.map(row => headers.map(header => row[header as keyof typeof row]).join(','))
    ].join('\n');

    // Download file
    const blob = new Blob([csvString], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `creator-earnings-${selectedPeriod}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-4">
                <div className="h-16 bg-secondary rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <div>
          <h2 className="text-2xl font-bold">Creator Earnings</h2>
          <p className="text-muted-foreground">Track your voice content monetization</p>
        </div>
        
        <div className="flex gap-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedChannel} onValueChange={setSelectedChannel}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Channels</SelectItem>
              {channels.map((channel) => (
                <SelectItem key={channel.id} value={channel.id}>
                  {channel.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button variant="outline" size="sm" onClick={exportData}>
            <Download size={16} className="mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-green-500/10 rounded-lg flex items-center justify-center">
                <DollarSign size={20} className="text-green-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Earnings</p>
                <p className="text-xl font-bold">${stats?.totalEarnings.toFixed(2) || '0.00'}</p>
                {stats?.growthRate !== undefined && (
                  <div className="flex items-center gap-1 text-xs">
                    {stats.growthRate >= 0 ? (
                      <ArrowUpRight size={12} className="text-green-500" />
                    ) : (
                      <ArrowDownRight size={12} className="text-red-500" />
                    )}
                    <span className={stats.growthRate >= 0 ? 'text-green-500' : 'text-red-500'}>
                      {Math.abs(stats.growthRate).toFixed(1)}%
                    </span>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-500/10 rounded-lg flex items-center justify-center">
                <Heart size={20} className="text-blue-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Tips Received</p>
                <p className="text-xl font-bold">{stats?.totalTips || 0}</p>
                <p className="text-xs text-muted-foreground">
                  Avg: ${stats?.avgTipAmount.toFixed(2) || '0.00'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-purple-500/10 rounded-lg flex items-center justify-center">
                <Zap size={20} className="text-purple-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Rewards Earned</p>
                <p className="text-xl font-bold">{stats?.totalRewards || 0}</p>
                <p className="text-xs text-muted-foreground">Engagement mining</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-orange-500/10 rounded-lg flex items-center justify-center">
                <BarChart3 size={20} className="text-orange-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Content Created</p>
                <p className="text-xl font-bold">{stats?.totalContent || 0}</p>
                <p className="text-xs text-muted-foreground">Voice posts & replies</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="tips">Tips</TabsTrigger>
          <TabsTrigger value="rewards">Rewards</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp size={20} />
                  Earnings Breakdown
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Heart size={16} className="text-blue-500" />
                      <span>Voice Tips</span>
                    </div>
                    <span className="font-medium">
                      ${earnings.reduce((sum, e) => sum + e.tips_received_usd, 0).toFixed(2)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Zap size={16} className="text-purple-500" />
                      <span>Engagement Rewards</span>
                    </div>
                    <span className="font-medium">
                      ${earnings.reduce((sum, e) => sum + e.rewards_earned_usd, 0).toFixed(2)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users size={20} />
                  Top Supporters
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {earnings.length > 0 && earnings[0].top_tippers?.length > 0 ? (
                    earnings[0].top_tippers.slice(0, 3).map((tipper: any, index: number) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center">
                            {index + 1}
                          </Badge>
                          <span className="text-sm">
                            {tipper.address?.slice(0, 6)}...{tipper.address?.slice(-4)}
                          </span>
                        </div>
                        <span className="text-sm font-medium">${tipper.amount?.toFixed(2)}</span>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-muted-foreground text-center py-4">
                      No tips received yet
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="tips" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Tip History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {earnings.map((period, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-secondary/20">
                    <div>
                      <p className="font-medium">{period.tips_received_count} tips received</p>
                      <p className="text-sm text-muted-foreground">
                        {period.period_start} - {period.period_end}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">${period.tips_received_usd.toFixed(2)}</p>
                      <p className="text-sm text-muted-foreground">
                        Avg: ${period.tips_received_count > 0 ? (period.tips_received_usd / period.tips_received_count).toFixed(2) : '0.00'}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rewards" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Engagement Rewards</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {earnings.map((period, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-secondary/20">
                    <div>
                      <p className="font-medium">{period.rewards_earned_count} rewards earned</p>
                      <p className="text-sm text-muted-foreground">
                        Engagement score: {period.engagement_score.toFixed(1)}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">${period.rewards_earned_usd.toFixed(2)}</p>
                      <p className="text-sm text-muted-foreground">Mining rewards</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Content Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {earnings.map((period, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-secondary/20">
                    <div>
                      <p className="font-medium">
                        {period.voice_posts_count} posts, {period.voice_replies_count} replies
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {period.period_start} - {period.period_end}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        ${(period.tips_received_usd + period.rewards_earned_usd).toFixed(2)}
                      </p>
                      <p className="text-sm text-muted-foreground">Total earned</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CreatorEarningsDashboard;
