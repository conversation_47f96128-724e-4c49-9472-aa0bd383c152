import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Edit, MessageCircle, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import VoiceMessageWithoutChannel from '@/components/VoiceMessageWithoutChannel';
import { VoiceMessageProps } from '@/types/voice-message';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import CustomAvatarImage from '@/components/CustomAvatarImage';
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from '@/components/ui/badge';
import { Link } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';

interface UserProfileProps {
  messages: VoiceMessageProps[];
  onReply: (messageId: string) => void;
  connectedAccount: string;
}

const UserProfile: React.FC<UserProfileProps> = ({ messages, onReply, connectedAccount }) => {
  const { address } = useParams<{ address: string }>();
  const navigate = useNavigate();
  const { profiles, getProfileByAddress, updateProfile } = useProfiles();
  const [userProfile, setUserProfile] = useState(address ? getProfileByAddress(address) : null);
  const [isLoading, setIsLoading] = useState(true);
  const [userPosts, setUserPosts] = useState<VoiceMessageProps[]>([]);

  // Load user profile
  useEffect(() => {
    if (address) {
      const profile = getProfileByAddress(address);
      setUserProfile(profile);
      setIsLoading(false);
    }
  }, [address, getProfileByAddress]);

  // Load user posts directly from Supabase
  useEffect(() => {
    const fetchUserPosts = async () => {
      if (!address) return;
      
      try {
        setIsLoading(true);
        console.log('Loading posts for profile address:', address);
        
        // First get the profile UUID associated with the wallet address
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('id')
          .eq('wallet_address', address)
          .single();
        
        if (profileError) {
          console.error('Error fetching profile ID:', profileError);
          setIsLoading(false);
          return;
        }
        
        if (!profileData || !profileData.id) {
          console.log('No profile found for wallet address:', address);
          setIsLoading(false);
          return;
        }
        
        console.log(`Found profile ID ${profileData.id} for wallet address ${address}`);
        
        // Then fetch posts using the profile ID
        const { data: postsData, error: postsError } = await supabase
          .from('voice_messages')
          .select('*')
          .eq('profile_id', profileData.id)
          .is('deleted_at', null)
          .order('created_at', { ascending: false });
        
        if (postsError) {
          console.error('Error fetching user posts:', postsError);
          setIsLoading(false);
          return;
        }
        
        if (postsData && postsData.length > 0) {
          console.log(`Found ${postsData.length} posts for user ${address}`);
          
          // Format posts for the component
          const formattedPosts = postsData.map(post => ({
            id: post.id,
            userAddress: address,
            timestamp: new Date(post.created_at),
            transcript: post.transcript || '',
            audioUrl: post.audio_url || '',
            duration: post.audio_duration || 0,
            replies: [],
            isPinned: post.is_pinned || false
          }));
          
          setUserPosts(formattedPosts);
          
          // Update post count if needed
          if (userProfile && (userProfile.stats?.posts || 0) !== formattedPosts.length) {
            updateProfile(address, {
              stats: {
                ...(userProfile.stats || {}),
                posts: formattedPosts.length
              }
            });
          }
        } else {
          console.log('No posts found for user:', address);
          setUserPosts([]);
        }
        
        setIsLoading(false);
      } catch (error) {
        console.error('Error loading user posts:', error);
        setIsLoading(false);
      }
    };
    
    fetchUserPosts();
  }, [address, userProfile, updateProfile]);

  if (isLoading) {
    return (
      <div className="container max-w-3xl mx-auto p-4">
        <div className="flex items-center mb-4">
          <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-xl font-bold ml-2">Profile</h1>
        </div>

        <div className="space-y-4">
          <Skeleton className="h-32 w-full rounded-xl" />
          <div className="flex items-center space-x-4">
            <Skeleton className="h-16 w-16 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-48" />
              <Skeleton className="h-4 w-32" />
            </div>
          </div>
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-5/6" />
          <Skeleton className="h-4 w-2/3" />
        </div>
      </div>
    );
  }

  if (!userProfile) {
    return (
      <div className="container max-w-3xl mx-auto p-4">
        <div className="flex items-center mb-4">
          <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-xl font-bold ml-2">Profile Not Found</h1>
        </div>

        <div className="text-center py-12">
          <p className="text-muted-foreground mb-4">The profile you're looking for doesn't exist.</p>
          <Button onClick={() => navigate('/')}>Return to Home</Button>
        </div>
      </div>
    );
  }

  // Log the current state to help debugging
  console.log('Current profile:', userProfile);
  console.log('Posts in user profile:', userPosts.length);

  const handleReply = (messageId: string) => {
    onReply(messageId);
  };

  return (
    <div className="w-full md:container md:mx-auto md:max-w-3xl flex flex-col min-h-0">
      {/* Header */}
      <div className="sticky top-0 bg-background/95 backdrop-blur-md z-10 px-3 py-3 sm:p-4 border-b border-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={(e) => {
                e.stopPropagation();
                navigate('/');
              }}
              className="hover:bg-secondary/80 active:bg-secondary"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-lg sm:text-xl font-bold ml-2">Profile</h1>
          </div>

          {/* Edit Profile Button - Conditionally render if it's the connected user's profile */}
          {connectedAccount === address && (
            <Link to="/profile/edit">
              <Button
                variant="ghost"
                size="sm"
                className="text-xs flex items-center gap-1 px-2 md:px-3"
              >
                <Edit size={14} />
                <span className="hidden sm:inline">Edit Profile</span>
              </Button>
            </Link>
          )}
        </div>
      </div>

      {/* Profile Content */}
      <div className="flex-1 overflow-y-auto">
        {/* Profile Header */}
        <div className="relative">
          <img
            src={userProfile.coverImageUrl || '/placeholder-image.jpg'}
            alt="Cover"
            className="w-full h-32 sm:h-48 object-cover rounded-b-lg"
          />
          <Avatar className="absolute left-4 -bottom-8 sm:-bottom-12 h-16 w-16 sm:h-24 sm:w-24 border-2 border-background">
            <CustomAvatarImage src={userProfile.profileImageUrl} alt={userProfile.displayName || 'Avatar'} />
            <AvatarFallback className="text-lg sm:text-3xl">
              {userProfile.displayName?.[0] || '?'}
            </AvatarFallback>
          </Avatar>
        </div>

        {/* Profile Info */}
        <div className="px-4 pt-8 pb-2">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold">{userProfile.displayName}</h2>
              <p className="text-sm text-muted-foreground">@{userProfile.username}</p>
            </div>
            {userProfile.verification?.isVerified && (
              <Badge variant="outline">Verified</Badge>
            )}
          </div>
          <p className="text-sm mt-2">{userProfile.bio || 'No bio yet.'}</p>

          {/* Social Links */}
          {userProfile.socialLinks && (
            <div className="flex items-center gap-2 mt-2">
              {userProfile.socialLinks.twitter && (
                <a href={userProfile.socialLinks.twitter} target="_blank" rel="noopener noreferrer">
                  Twitter
                </a>
              )}
              {userProfile.socialLinks.github && (
                <a href={userProfile.socialLinks.github} target="_blank" rel="noopener noreferrer">
                  GitHub
                </a>
              )}
              {userProfile.socialLinks.website && (
                <a href={userProfile.socialLinks.website} target="_blank" rel="noopener noreferrer">
                  Website
                </a>
              )}
            </div>
          )}
        </div>

        <Separator />

        {/* Tabs */}
        <Tabs defaultValue="posts" className="flex-1 flex flex-col">
          <TabsList className="px-4">
            <TabsTrigger value="posts" className="text-sm">Posts</TabsTrigger>
            <TabsTrigger value="about" className="text-sm">About</TabsTrigger>
          </TabsList>

          {/* Posts Tab */}
          <TabsContent value="posts" className="space-y-4">
            {userPosts.length > 0 ? (
              userPosts.map((post) => (
                <VoiceMessageWithoutChannel
                  key={post.id}
                  {...post}
                  onReply={handleReply}
                />
              ))
            ) : (
              <div className="text-center py-10">
                <p className="text-muted-foreground">No posts yet</p>
              </div>
            )}
          </TabsContent>

          {/* About Tab */}
          <TabsContent value="about" className="space-y-4 p-4">
            <div className="rounded-md border p-4">
              <div className="flex items-center space-x-4 pb-2">
                <User className="h-4 w-4" />
                <h4 className="text-sm font-medium leading-none">Profile Information</h4>
              </div>
              <Separator />
              <div className="grid gap-6 py-4">
                <div className="space-y-2">
                  <p className="text-sm font-medium leading-none">Display Name</p>
                  <p className="text-sm text-muted-foreground">{userProfile.displayName}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium leading-none">Username</p>
                  <p className="text-sm text-muted-foreground">@{userProfile.username}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium leading-none">Wallet Address</p>
                  <p className="text-sm text-muted-foreground">{userProfile.address}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium leading-none">Joined Date</p>
                  <p className="text-sm text-muted-foreground">
                    {userProfile.joinedDate ? new Date(userProfile.joinedDate).toLocaleDateString() : 'N/A'}
                  </p>
                </div>
              </div>
            </div>

            <div className="rounded-md border p-4">
              <div className="flex items-center space-x-4 pb-2">
                <MessageCircle className="h-4 w-4" />
                <h4 className="text-sm font-medium leading-none">Stats</h4>
              </div>
              <Separator />
              <div className="grid gap-6 py-4">
                <div className="space-y-2">
                  <p className="text-sm font-medium leading-none">Posts</p>
                  <p className="text-sm text-muted-foreground">{userProfile.stats?.posts || userPosts.length || 0}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium leading-none">Reactions</p>
                  <p className="text-sm text-muted-foreground">{userProfile.stats?.likes || 0}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium leading-none">Tips</p>
                  <p className="text-sm text-muted-foreground">{userProfile.stats?.tips || 0}</p>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default UserProfile;
