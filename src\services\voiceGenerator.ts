import { ContextualizedEvent } from './eventContextualizer';

// Interface for voice generation options
export interface VoiceGenerationOptions {
  voice: string;
  speed?: number;
  pitch?: number;
  usePrerecorded?: boolean;
}

// Interface for voice generation result
export interface VoiceGenerationResult {
  audioUrl: string;
  audioDuration: number;
  transcription: string;
}

// Interface for registered voice narrators
export interface VoiceNarrator {
  id: string;
  name: string;
  projectOrDao: string;
  voiceId: string;
  isAI: boolean;
  prerecordedClips?: Record<string, string>; // Map of phrase to audio URL
}

class VoiceGeneratorService {
  private apiKey: string = '';
  private apiEndpoint: string = 'https://api.elevenlabs.io/v1/text-to-speech';
  private defaultVoice: string = 'EXAVITQu4vr4xnSDxMaL'; // Antoni voice

  // Available voice IDs from ElevenLabs
  private availableVoices: string[] = [
    'EXAVITQu4vr4xnSDxMaL', // Antoni
    '21m00Tcm4TlvDq8ikWAM', // Josh
    'AZnzlk1XvdvUeBnXmlld', // Domi
    'MF3mGyEYCl7XYWbV9V6O', // Elli
    'TxGEqnHWrfWFTfGW9XjX', // Adam
    'VR6AewLTigWG4xSOukaG', // Sam
    'pNInz6obpgDQGcFmaJgB', // Bella
    'yoZ06aMxZJJ28mfd3POQ', // Harry
    'jBpfuIE2acCO8z3wKNLl', // Liam
    'jsCqWAovK2LkecY7zXl4'  // Dorothy
  ];
  private registeredNarrators: VoiceNarrator[] = [];

  constructor() {
    // Load registered narrators from storage or API
    this.loadRegisteredNarrators();
  }

  /**
   * Set the API key for the voice generation service
   */
  public async setApiKey(apiKey: string): Promise<void> {
    this.apiKey = apiKey;

    // Import the ElevenLabs service dynamically to avoid circular dependencies
    const { elevenLabsService } = await import('./elevenLabsService');

    // Set the API key for ElevenLabs
    elevenLabsService.setApiKey(apiKey);

    console.log('Voice generation API key set');
  }

  /**
   * Generate voice audio from a contextualized event
   */
  public async generateVoiceAudio(
    event: ContextualizedEvent,
    options: Partial<VoiceGenerationOptions> = {}
  ): Promise<VoiceGenerationResult> {
    // Determine which voice to use
    const voiceOptions = this.determineVoiceOptions(event, options);

    // Check if we should use pre-recorded clips
    if (voiceOptions.usePrerecorded) {
      const prerecordedResult = await this.tryUsePrerecordedClips(event, voiceOptions);
      if (prerecordedResult) {
        return prerecordedResult;
      }
    }

    // Generate voice using AI
    return this.generateAIVoice(event.narrationText, voiceOptions);
  }

  /**
   * Register a new voice narrator
   */
  public registerNarrator(narrator: VoiceNarrator): void {
    // Check if narrator already exists
    const existingIndex = this.registeredNarrators.findIndex(n => n.id === narrator.id);

    if (existingIndex >= 0) {
      // Update existing narrator
      this.registeredNarrators[existingIndex] = {
        ...this.registeredNarrators[existingIndex],
        ...narrator
      };
    } else {
      // Add new narrator
      this.registeredNarrators.push(narrator);
    }

    // Save updated narrators
    this.saveRegisteredNarrators();
  }

  /**
   * Get a list of all registered narrators
   */
  public getRegisteredNarrators(): VoiceNarrator[] {
    return [...this.registeredNarrators];
  }

  /**
   * Find a narrator for a specific project or DAO
   */
  public findNarratorForProject(projectOrDao: string): VoiceNarrator | undefined {
    return this.registeredNarrators.find(
      narrator => narrator.projectOrDao.toLowerCase() === projectOrDao.toLowerCase()
    );
  }

  /**
   * Determine voice options based on the event and user preferences
   */
  private determineVoiceOptions(
    event: ContextualizedEvent,
    userOptions: Partial<VoiceGenerationOptions>
  ): VoiceGenerationOptions {
    // Check if there's a registered narrator for this project/DAO
    let narrator: VoiceNarrator | undefined;

    if (event.event.source.project) {
      narrator = this.findNarratorForProject(event.event.source.project);
    } else if (event.event.source.dao) {
      narrator = this.findNarratorForProject(event.event.source.dao);
    }

    // Default options
    const defaultOptions: VoiceGenerationOptions = {
      voice: this.defaultVoice,
      speed: 1.0,
      pitch: 1.0,
      usePrerecorded: false
    };

    // If we found a narrator, use their voice
    if (narrator) {
      defaultOptions.voice = narrator.voiceId;
      defaultOptions.usePrerecorded = !narrator.isAI;
    }

    // Override with user options
    return {
      ...defaultOptions,
      ...userOptions
    };
  }

  /**
   * Try to use pre-recorded clips for the narration
   */
  private async tryUsePrerecordedClips(
    event: ContextualizedEvent,
    options: VoiceGenerationOptions
  ): Promise<VoiceGenerationResult | null> {
    // Find the narrator
    const narrator = this.registeredNarrators.find(
      n => n.voiceId === options.voice && !n.isAI
    );

    if (!narrator || !narrator.prerecordedClips) {
      return null;
    }

    // This is a simplified implementation
    // In a real system, you would:
    // 1. Break down the narration text into phrases
    // 2. Find matching pre-recorded clips
    // 3. Stitch them together

    // For now, we'll just check if we have an exact match
    if (narrator.prerecordedClips[event.narrationText]) {
      return {
        audioUrl: narrator.prerecordedClips[event.narrationText],
        audioDuration: 5, // This would be calculated based on the actual audio
        transcription: event.narrationText
      };
    }

    return null;
  }

  /**
   * Generate voice using AI service (ElevenLabs)
   */
  private async generateAIVoice(
    text: string,
    options: VoiceGenerationOptions
  ): Promise<VoiceGenerationResult> {
    console.log(`Generating AI voice for text: "${text}" using voice: ${options.voice}`);

    try {
      // In a real implementation, we would call the ElevenLabs API here
      // For now, we'll use the browser's built-in speech synthesis API

      // We'll create a unique identifier for this narration
      const narrationId = `narration-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

      // Store the text in a global variable so we can access it later
      if (!(window as any).narrationTexts) {
        (window as any).narrationTexts = {};
      }
      (window as any).narrationTexts[narrationId] = text;

      // Store voice options in a global variable
      if (!(window as any).voiceOptions) {
        (window as any).voiceOptions = {};
      }

      // Set voice options based on the event type
      let voiceOptions = {
        rate: 0.9,  // Slightly slower for better clarity
        pitch: 1.0,
        volume: 1.0,
        preferredVoice: options.voice || 'en-US'
      };

      // Store the voice options
      (window as any).voiceOptions[narrationId] = voiceOptions;

      // Estimate the duration based on word count (about 150 words per minute)
      const wordCount = text.split(/\s+/).length;
      const estimatedDuration = Math.max(5, (wordCount / 150) * 60);

      console.log('Generated voice narration for:', text.substring(0, 100) + '...', 'with estimated duration:', estimatedDuration);

      // Return a special URL that includes the narration ID
      const audioUrl = `speech-synthesis://${narrationId}`;

      return {
        audioUrl,
        audioDuration: estimatedDuration,
        transcription: text
      };
    } catch (error) {
      console.error('Error generating AI voice:', error);

      // Fallback to a placeholder in case of error
      return {
        audioUrl: '',
        audioDuration: 5,
        transcription: text
      };
    }
  }

  /**
   * Load registered narrators from storage
   */
  private loadRegisteredNarrators(): void {
    // In a real implementation, this would load from localStorage, IndexedDB, or an API
    // For now, we'll initialize with some example narrators

    this.registeredNarrators = [
      {
        id: 'solana-foundation',
        name: 'Solana Foundation',
        projectOrDao: 'Solana Foundation',
        voiceId: 'Antoni',
        isAI: true
      },
      {
        id: 'developer-dao',
        name: 'Developer DAO',
        projectOrDao: 'DeveloperDAO',
        voiceId: 'Rachel',
        isAI: true
      }
    ];
  }

  /**
   * Save registered narrators to storage
   */
  private saveRegisteredNarrators(): void {
    // In a real implementation, this would save to localStorage, IndexedDB, or an API
    console.log('Saving registered narrators:', this.registeredNarrators);
  }
}

// Export a singleton instance
export const voiceGenerator = new VoiceGeneratorService();
