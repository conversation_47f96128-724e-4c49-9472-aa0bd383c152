/**
 * Blockchain Storage Service
 * This service integrates NFT.Storage (IPFS) and Lit Protocol
 * to provide decentralized storage for audio files and metadata
 *
 * Note: This service now uses Supabase for metadata storage instead of Ceramic
 */

import { uploadBlobToIPFS, resolveIPFSUri } from './nftStorage';
import { encryptWithLit, createWalletAccessControl } from './litProtocol';
import { publishVoicePost, getVoicePost, VoicePostMetadata, MediaItem } from './disabledCeramicClient';
import { MediaFile } from '@/components/MediaUploader';

/**
 * Service for handling decentralized storage of audio and metadata
 */
class BlockchainStorageService {
  /**
   * Upload audio to IPFS
   * @param audioBlob The audio blob to upload
   * @param userId The user ID (wallet address)
   * @param encrypt Whether to encrypt the audio (default: false)
   * @returns The IPFS URI or gateway URL
   */
  public async uploadAudio(
    audioBlob: Blob,
    userId: string,
    encrypt: boolean = false
  ): Promise<string> {
    try {
      // Generate a unique file name
      const timestamp = Date.now();
      const fileName = `${userId.substring(0, 10)}_${timestamp}.webm`;

      let finalBlob = audioBlob;

      // Encrypt the audio if requested
      if (encrypt) {
        // Convert blob to ArrayBuffer for encryption
        const arrayBuffer = await audioBlob.arrayBuffer();

        // Create access control conditions for the user
        const accessControlConditions = createWalletAccessControl(userId);

        // Encrypt the file
        const { encryptedFile } = await encryptWithLit(
          arrayBuffer,
          accessControlConditions
        );

        finalBlob = encryptedFile;

        // Note: We're not using the encryptedSymmetricKey here, but it would be needed
        // for decryption. In a full implementation, we would store this key securely.
      }

      try {
        // Try to upload to IPFS
        console.log('Attempting to upload audio to IPFS...');
        const ipfsUri = await uploadBlobToIPFS(finalBlob, fileName);

        // Store in global cache for immediate access
        if (typeof window !== 'undefined') {
          if (!(window as any).audioCache) {
            (window as any).audioCache = {};
          }

          // Use the resolved gateway URL as the key
          const gatewayUrl = resolveIPFSUri(ipfsUri);
          (window as any).audioCache[gatewayUrl] = audioBlob;

          // Also store the original blob for backup
          (window as any).audioCache[`original_${gatewayUrl}`] = audioBlob;
        }

        console.log('Successfully uploaded audio to IPFS:', ipfsUri);

        // Return the gateway URL for immediate playback
        return resolveIPFSUri(ipfsUri);
      } catch (ipfsError) {
        console.error('Error uploading audio to IPFS:', ipfsError);

        // Check if it's an API key issue
        if (ipfsError.message && ipfsError.message.includes('API Key is malformed')) {
          console.error('NFT.Storage API key is invalid or malformed. Please check your API key.');
          console.error('Falling back to blob URL storage.');
        } else if (ipfsError.message && ipfsError.message.includes('401')) {
          console.error('Authentication failed with NFT.Storage. Your API key may be invalid or expired.');
          console.error('Falling back to blob URL storage.');
        }

        // Fall back to blob URL if IPFS upload fails
        console.warn('Falling back to blob URL for audio storage');
        const blobUrl = URL.createObjectURL(audioBlob);
        console.log('Created blob URL:', blobUrl);

        // Store in global cache
        if (typeof window !== 'undefined') {
          if (!(window as any).audioCache) {
            (window as any).audioCache = {};
          }

          (window as any).audioCache[blobUrl] = audioBlob;

          // Also store in localStorage for persistence across page refreshes
          try {
            // Store a reference to the blob URL
            const audioUrls = JSON.parse(localStorage.getItem('audioUrls') || '{}');
            audioUrls[blobUrl] = {
              timestamp: Date.now(),
              userId: userId,
              fileName: fileName
            };
            localStorage.setItem('audioUrls', JSON.stringify(audioUrls));

            console.log('Stored blob URL reference in localStorage');
          } catch (localStorageError) {
            console.warn('Failed to store blob URL reference in localStorage:', localStorageError);
          }
        }

        return blobUrl;
      }
    } catch (error) {
      console.error('Error in uploadAudio:', error);

      // Final fallback to blob URL
      console.warn('Final fallback to blob URL for audio storage');
      const blobUrl = URL.createObjectURL(audioBlob);

      // Store in global cache
      if (typeof window !== 'undefined') {
        if (!(window as any).audioCache) {
          (window as any).audioCache = {};
        }

        (window as any).audioCache[blobUrl] = audioBlob;
      }

      return blobUrl;
    }
  }

  /**
   * Upload audio for transcription
   * @param audioBlob The audio blob to upload
   * @param userId The user ID (wallet address)
   * @returns The IPFS URI or gateway URL
   */
  public async uploadForTranscription(audioBlob: Blob, userId: string): Promise<string> {
    try {
      // Generate a unique file name
      const timestamp = Date.now();
      const fileName = `transcription_${userId.substring(0, 10)}_${timestamp}.webm`;

      // Upload to IPFS
      const ipfsUri = await uploadBlobToIPFS(audioBlob, fileName);

      console.log('Successfully uploaded audio for transcription to IPFS');

      // Return the gateway URL for immediate access
      return resolveIPFSUri(ipfsUri);
    } catch (error) {
      console.error('Error uploading audio for transcription to IPFS:', error);

      // Fall back to blob URL if IPFS upload fails
      console.warn('Falling back to blob URL for audio storage');
      const blobUrl = URL.createObjectURL(audioBlob);
      return blobUrl;
    }
  }

  /**
   * Save a voice message with metadata to Supabase
   * @param userId The user ID (wallet address)
   * @param audioUrl The audio URL (IPFS URI or gateway URL)
   * @param transcript The transcript text
   * @param audioDuration The audio duration in seconds
   * @param media Optional media files
   * @param options Additional options
   * @returns The document ID
   */
  public async saveVoiceMessage(
    userId: string,
    audioUrl: string,
    transcript: string,
    audioDuration: number,
    media: MediaFile[] = [],
    options: {
      parentId?: string;
      channelId?: string;
      isPinned?: boolean;
      encrypted?: boolean;
    } = {}
  ): Promise<string> {
    try {
      // Process media files - upload to IPFS if they're not already there
      const processedMedia: MediaItem[] = await Promise.all(
        media.map(async (item) => {
          // If the URL is already an IPFS URI, use it
          if (item.url.startsWith('ipfs://')) {
            return {
              id: item.id,
              url: item.url,
              type: item.type
            };
          }

          // If it's a blob URL and we have the file, upload to IPFS
          if (item.file) {
            const ipfsUri = await uploadBlobToIPFS(
              item.file,
              `${item.id}_${Date.now()}.${item.file.name.split('.').pop() || 'jpg'}`
            );
            return {
              id: item.id,
              url: ipfsUri,
              type: item.type
            };
          }

          // Otherwise, just use the existing URL
          return {
            id: item.id,
            url: item.url,
            type: item.type
          };
        })
      );

      // Create metadata for Supabase
      const metadata = {
        audioUrl: audioUrl,
        transcript,
        userAddress: userId,
        timestamp: new Date().toISOString(),
        duration: audioDuration,
        media: processedMedia,
        parentId: options.parentId,
        channelId: options.channelId,
        isPinned: options.isPinned || false,
        encrypted: options.encrypted || false
      } as VoicePostMetadata;

      // Publish to Supabase
      const documentId = await publishVoicePost(metadata);

      return documentId;
    } catch (error) {
      console.error('Error saving voice message to Supabase:', error);
      throw error;
    }
  }

  /**
   * Get a voice message from Supabase
   * @param documentId The document ID
   * @returns The voice post metadata
   */
  public async getVoiceMessage(documentId: string): Promise<VoicePostMetadata> {
    try {
      return await getVoicePost(documentId);
    } catch (error) {
      console.error('Error getting voice message from Supabase:', error);
      throw error;
    }
  }
}

// Export a singleton instance
const blockchainStorageService = new BlockchainStorageService();
export default blockchainStorageService;
