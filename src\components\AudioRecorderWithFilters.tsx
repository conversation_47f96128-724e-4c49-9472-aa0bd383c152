import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Mic, Square, Pause, Play, Trash2, Send, Loader2 } from 'lucide-react';
import { toast } from '@/components/ui/sonner';
import audioStorageService from '@/services/audioStorageService';
import * as ipfsAudioService from '@/services/ipfsAudioService';
import VoiceFilterSelector, { VoiceFilter, voiceFilters } from './VoiceFilterSelector';
import audioProcessingService from '@/services/audioProcessingService';
import MediaUploader, { MediaFile } from './MediaUploader';

interface AudioRecorderWithFiltersProps {
  onRecordingComplete?: (audioBlob: Blob, transcript: string, duration: number, media?: MediaFile[]) => void;
  onRecordingStart?: () => void;
  onSubmit?: (audioUrl: string, transcript: string, audioDuration: number) => void;
  isSubmitting?: boolean;
  onCancel?: () => void;
  placeholder?: string;
  maxMediaFiles?: number;
}

const AudioRecorderWithFilters: React.FC<AudioRecorderWithFiltersProps> = ({
  onRecordingComplete,
  onRecordingStart,
  onSubmit,
  isSubmitting = false,
  onCancel,
  placeholder = "Recording...",
  maxMediaFiles = 0
}) => {
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [isPaused, setIsPaused] = useState<boolean>(false);
  const [recordingTime, setRecordingTime] = useState<number>(0);
  const [audioURL, setAudioURL] = useState<string | null>(null);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [transcript, setTranscript] = useState<string>("");
  const [isTranscribing, setIsTranscribing] = useState<boolean>(false);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [selectedFilter, setSelectedFilter] = useState<string>('normal');
  const [customFilterParams, setCustomFilterParams] = useState<VoiceFilter['audioParams']>({});
  const [media, setMedia] = useState<MediaFile[]>([]);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const timerIdRef = useRef<NodeJS.Timeout | null>(null);
  const audioPlayerRef = useRef<HTMLAudioElement | null>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60).toString().padStart(2, '0');
    const secs = Math.floor(seconds % 60).toString().padStart(2, '0');
    return `${mins}:${secs}`;
  };

  const startRecording = async () => {
    try {
      setIsProcessing(true);

      // Reset states
      setRecordingTime(0);
      setAudioURL(null);
      setAudioBlob(null);
      setTranscript("");
      audioChunksRef.current = [];

      // Call onRecordingStart if provided
      if (onRecordingStart) {
        onRecordingStart();
      }

      // Get user media with high-quality audio settings
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 48000,
          sampleSize: 16,
          channelCount: 2
        }
      });

      streamRef.current = stream;

      // Create media recorder with high-quality settings
      const options = {
        mimeType: getSupportedMimeType(),
        audioBitsPerSecond: 256000
      };

      mediaRecorderRef.current = new MediaRecorder(stream, options);

      // Set up event handlers
      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorderRef.current.onstop = async () => {
        // Create a blob from the audio chunks
        const rawAudioBlob = new Blob(audioChunksRef.current, { type: getSupportedMimeType() });

        // Apply selected filter
        let processedBlob = rawAudioBlob;
        try {
          console.log(`Applying audio filter: ${selectedFilter}`);

          if (selectedFilter !== 'normal') {
            const filter = voiceFilters.find(f => f.id === selectedFilter);
            if (filter) {
              console.log(`Found filter: ${filter.name}`, filter.audioParams);
              toast.info(`Applying ${filter.name} filter...`);

              // Apply the filter
              processedBlob = await audioProcessingService.applyFilters(rawAudioBlob, filter);
              console.log('Filter applied successfully');
            }
          } else if (Object.keys(customFilterParams).length > 0) {
            // Apply custom filter
            console.log('Applying custom filter with params:', customFilterParams);
            toast.info('Applying custom filter...');

            const customFilter: VoiceFilter = {
              id: 'custom',
              name: 'Custom',
              icon: <></>,
              description: 'Custom filter',
              audioParams: customFilterParams
            };
            processedBlob = await audioProcessingService.applyFilters(rawAudioBlob, customFilter);
            console.log('Custom filter applied successfully');
          } else {
            console.log('No filter selected, using original audio');
          }
        } catch (filterError) {
          console.error('Error applying audio filter:', filterError);
          toast.error('Error applying audio filter. Using original audio.');
          processedBlob = rawAudioBlob;
        }

        // Save the processed blob
        setAudioBlob(processedBlob);

        // Generate a URL for the audio blob for local playback
        const url = URL.createObjectURL(processedBlob);
        setAudioURL(url);

        // Stop the microphone access
        if (streamRef.current) {
          const tracks = streamRef.current.getTracks();
          tracks.forEach(track => track.stop());
          streamRef.current = null;
        }

        // Create an audio element to get the duration
        const audioElement = new Audio(url);

        audioElement.onloadedmetadata = () => {
          const duration = audioElement.duration;
          console.log('Audio duration:', duration);

          // Attempt to transcribe the audio
          transcribeAudio(processedBlob, url, duration);

          // Don't automatically call onRecordingComplete here
          // Let the user play back and confirm first
          setIsProcessing(false);
        };

        audioElement.onerror = () => {
          console.error('Error loading audio metadata');

          // Use recording time as fallback duration
          transcribeAudio(processedBlob, url, recordingTime);

          setIsProcessing(false);
        };
      };

      // Start recording
      mediaRecorderRef.current.start(1000); // Collect data every second
      setIsRecording(true);
      setIsPaused(false);

      // Start the timer
      timerIdRef.current = setInterval(() => {
        setRecordingTime((prevTime) => prevTime + 1);
      }, 1000);

      setIsProcessing(false);
    } catch (error) {
      console.error('Error accessing microphone:', error);
      toast.error('Unable to access microphone. Please check your browser permissions.');
      setIsProcessing(false);
    }
  };

  const pauseRecording = () => {
    if (mediaRecorderRef.current && isRecording && !isPaused) {
      mediaRecorderRef.current.pause();
      setIsPaused(true);

      // Pause the timer
      if (timerIdRef.current) {
        clearInterval(timerIdRef.current);
        timerIdRef.current = null;
      }
    }
  };

  const resumeRecording = () => {
    if (mediaRecorderRef.current && isRecording && isPaused) {
      mediaRecorderRef.current.resume();
      setIsPaused(false);

      // Resume the timer
      timerIdRef.current = setInterval(() => {
        setRecordingTime((prevTime) => prevTime + 1);
      }, 1000);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      setIsPaused(false);

      // Stop the timer
      if (timerIdRef.current) {
        clearInterval(timerIdRef.current);
        timerIdRef.current = null;
      }
    }
  };

  const transcribeAudio = async (audioBlob: Blob, audioUrl: string, duration: number) => {
    setIsTranscribing(true);

    try {
      // Try to use the Web Speech API for transcription
      const transcript = await transcribeWithWebSpeech(audioBlob);
      setTranscript(transcript);
    } catch (error) {
      console.error('Error transcribing audio:', error);
      toast.error('Failed to transcribe audio. You can still send the message.');
      setTranscript(placeholder || "Voice message recorded successfully");
    } finally {
      setIsTranscribing(false);
    }
  };

  // Function to transcribe audio using Web Speech API
  const transcribeWithWebSpeech = async (audioBlob: Blob): Promise<string> => {
    return new Promise((resolve) => {
      // Check if browser supports SpeechRecognition
      if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        console.error('Speech recognition not supported in this browser');
        resolve("Voice message recorded successfully");
        return;
      }

      try {
        // Create a direct URL for the audio blob
        const audioURL = URL.createObjectURL(audioBlob);

        // Create an audio element to analyze the audio
        const audioElement = new Audio(audioURL);

        // Set a default transcript in case recognition fails
        let defaultTranscript = "Voice message recorded successfully";

        // Use a simpler approach - just set a placeholder transcript
        // This ensures users always have something to work with
        setTranscript(defaultTranscript);

        // Resolve with the default transcript
        // Users can manually edit the transcript if needed
        resolve(defaultTranscript);

        // In the background, try to get a better transcript using the Web Speech API
        // This is a best-effort approach that won't block the UI
        try {
          // Convert audio to a format that works better with speech recognition
          const audioContext = new AudioContext();
          const audioSource = audioContext.createMediaElementSource(audioElement);
          const analyser = audioContext.createAnalyser();
          audioSource.connect(analyser);
          analyser.connect(audioContext.destination);

          // Set up speech recognition
          const SpeechRecognition = window.webkitSpeechRecognition || window.SpeechRecognition;
          const recognition = new SpeechRecognition();

          recognition.lang = 'en-US';
          recognition.continuous = true;
          recognition.interimResults = true;

          let finalTranscript = '';

          // Update transcript in real-time as recognition happens
          recognition.onresult = (event) => {
            let interimTranscript = '';

            for (let i = event.resultIndex; i < event.results.length; i++) {
              const transcript = event.results[i][0].transcript;
              if (event.results[i].isFinal) {
                finalTranscript += transcript + ' ';
                // Update the transcript state in real-time
                if (finalTranscript.trim()) {
                  setTranscript(finalTranscript.trim());
                }
              } else {
                interimTranscript += transcript;
              }
            }

            // Show interim results too
            if (interimTranscript && finalTranscript) {
              setTranscript((finalTranscript + interimTranscript).trim());
            }
          };

          recognition.onend = () => {
            if (finalTranscript.trim()) {
              setTranscript(finalTranscript.trim());
            }
            audioElement.pause();
            // Only close the AudioContext if it's not already closed
            if (audioContext.state !== 'closed') {
              try {
                audioContext.close();
              } catch (error) {
                console.warn('Error closing AudioContext:', error);
              }
            }
          };

          // Start recognition
          recognition.start();

          // Play the audio to analyze
          audioElement.play().catch(error => {
            console.error('Error playing audio for transcription:', error);
            recognition.stop();
          });

          // Stop recognition after a reasonable time (increased for PWA)
          setTimeout(() => {
            recognition.stop();
            audioElement.pause();
            // Only close the AudioContext if it's not already closed
            if (audioContext.state !== 'closed') {
              try {
                audioContext.close();
              } catch (error) {
                console.warn('Error closing AudioContext:', error);
              }
            }
          }, 20000); // 20 seconds for PWA compatibility (was 10 seconds)
        } catch (recognitionError) {
          console.error('Error with Web Speech API:', recognitionError);
          // We already resolved with the default transcript, so this won't block the UI
        }
      } catch (error) {
        console.error('Error with audio transcription:', error);
        resolve("Voice message recorded successfully");
      }
    });
  };

  const handlePlayPause = async () => {
    if (!audioURL) return;

    try {
      // Use the local blob URL directly for playback
      const playableUrl = audioURL;

      if (!audioPlayerRef.current) {
        audioPlayerRef.current = new Audio(playableUrl);

        audioPlayerRef.current.onended = () => {
          setIsPlaying(false);
        };
      } else {
        // Update the source if it's different
        if (audioPlayerRef.current.src !== playableUrl) {
          audioPlayerRef.current.src = playableUrl;
          audioPlayerRef.current.load();
        }
      }

      if (isPlaying) {
        audioPlayerRef.current.pause();
      } else {
        try {
          await audioPlayerRef.current.play();
        } catch (error) {
          console.error('Error playing audio:', error);
          toast.error('Failed to play audio');
        }
      }

      setIsPlaying(!isPlaying);
    } catch (error) {
      console.error('Error preparing audio for playback:', error);
      toast.error('Failed to prepare audio for playback');
    }
  };

  const handleSubmit = async () => {
    if (!audioURL || !audioBlob) {
      toast.error('No recording available');
      return;
    }

    setIsProcessing(true);

    try {
      // Get the current wallet address or a fallback
      const userAddress = localStorage.getItem('connectedAccount') || 'anonymous_user';

      // Now upload to IPFS for permanent storage
      const ipfsUrl = await ipfsAudioService.uploadAudioToIPFS(audioBlob, userAddress);
      console.log('Audio uploaded to IPFS successfully:', ipfsUrl);

      // Also upload to Supabase as a backup
      try {
        const supabaseUrl = await audioStorageService.uploadAudio(audioBlob, userAddress);
        console.log('Audio also uploaded to Supabase as backup:', supabaseUrl);
      } catch (supabaseError) {
        console.warn('Failed to upload to Supabase (using IPFS only):', supabaseError);
      }

      // Now call onRecordingComplete with the processed blob
      if (onRecordingComplete) {
        const duration = recordingTime; // Use the recorded time as duration
        console.log('Calling onRecordingComplete with media:', media);
        onRecordingComplete(audioBlob, transcript, duration, media.length > 0 ? media : undefined);
      }

      // Also call onSubmit if provided
      if (onSubmit) {
        onSubmit(ipfsUrl, transcript, recordingTime);
      }
    } catch (error) {
      console.error('Error during submission:', error);
      toast.error('Failed to upload audio. Please try again.');

      // Fall back to local URL if IPFS upload fails
      if (onRecordingComplete && audioBlob) {
        onRecordingComplete(audioBlob, transcript, recordingTime, media.length > 0 ? media : undefined);
      }
    } finally {
      setIsProcessing(false);
    }
  };

  const cancelRecording = () => {
    // Stop recording if active
    if (isRecording) {
      if (mediaRecorderRef.current) {
        mediaRecorderRef.current.stop();
      }

      // Stop the timer
      if (timerIdRef.current) {
        clearInterval(timerIdRef.current);
        timerIdRef.current = null;
      }

      // Stop microphone access
      if (streamRef.current) {
        const tracks = streamRef.current.getTracks();
        tracks.forEach(track => track.stop());
        streamRef.current = null;
      }
    }

    // Reset all states
    setIsRecording(false);
    setIsPaused(false);
    setRecordingTime(0);
    setAudioURL(null);
    setAudioBlob(null);
    setTranscript("");
    setIsTranscribing(false);
    setIsPlaying(false);
    setIsProcessing(false);
    setMedia([]); // Clear media files on cancel
    console.log('Recording cancelled, media cleared');

    // Call onCancel if provided
    if (onCancel) {
      onCancel();
    }
  };

  // Get supported mime type based on browser
  const getSupportedMimeType = (): string => {
    const possibleTypes = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/ogg;codecs=opus',
      'audio/ogg'
    ];

    for (const type of possibleTypes) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type;
      }
    }

    // Fallback to general audio/webm
    return 'audio/webm';
  };

  // Handle filter change
  const handleFilterChange = (filterId: string) => {
    setSelectedFilter(filterId);
  };

  // Handle custom filter change
  const handleCustomFilterChange = (params: VoiceFilter['audioParams']) => {
    setCustomFilterParams(params);
  };

  // Handle media files
  const handleMediaChange = (files: MediaFile[]) => {
    console.log('Media files changed:', files);
    setMedia(files);
  };

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (timerIdRef.current) {
        clearInterval(timerIdRef.current);
      }

      if (audioPlayerRef.current) {
        audioPlayerRef.current.pause();
      }

      if (streamRef.current) {
        const tracks = streamRef.current.getTracks();
        tracks.forEach(track => track.stop());
      }
    };
  }, []);

  return (
    <div className="space-y-4 w-full">
      {/* Recording time and progress */}
      <div className="flex items-center justify-between mb-2">
        <div className="text-sm font-medium">
          {isRecording ?
            (isPaused ? 'Paused' : 'Recording...') :
            audioURL ? 'Recording complete' : 'Ready to record'}
        </div>
        <div className="text-sm font-mono">{formatTime(recordingTime)}</div>
      </div>

      <Progress value={isRecording ? (recordingTime % 60) / 60 * 100 : 0}
        className={`transition-all ${isRecording && !isPaused ? 'bg-red-200' : ''}`} />

      {/* Voice filters - only show when not recording */}
      {!isRecording && !audioURL && (
        <div className="mb-4">
          <VoiceFilterSelector
            selectedFilter={selectedFilter}
            onFilterChange={handleFilterChange}
            onCustomFilterChange={handleCustomFilterChange}
          />
        </div>
      )}

      {/* Recording controls */}
      <div className="flex justify-center space-x-4">
        {!isRecording && !audioURL && (
          <Button
            onClick={startRecording}
            variant="default"
            size="sm"
            className="bg-voicechain-purple hover:bg-voicechain-accent"
            disabled={isProcessing}
          >
            {isProcessing ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : (
              <Mic className="h-5 w-5" />
            )}
            <span className="ml-2">Start Recording</span>
          </Button>
        )}

        {isRecording && !isPaused && (
          <>
            <Button onClick={pauseRecording} variant="outline" size="sm">
              <Pause className="h-5 w-5 mr-2" />
              Pause
            </Button>
            <Button onClick={stopRecording} variant="default" size="sm">
              <Square className="h-5 w-5 mr-2" />
              Stop
            </Button>
          </>
        )}

        {isRecording && isPaused && (
          <>
            <Button
              onClick={resumeRecording}
              variant="default"
              size="sm"
              className="bg-voicechain-purple hover:bg-voicechain-accent"
            >
              <Mic className="h-5 w-5 mr-2" />
              Resume
            </Button>
            <Button onClick={stopRecording} variant="default" size="sm">
              <Square className="h-5 w-5 mr-2" />
              Stop
            </Button>
          </>
        )}

        {audioURL && !isRecording && (
          <>
            <Button
              onClick={handlePlayPause}
              variant="outline"
              size="sm"
              disabled={isProcessing || isTranscribing}
            >
              {isPlaying ? (
                <>
                  <Pause className="h-5 w-5 mr-2" />
                  Pause
                </>
              ) : (
                <>
                  <Play className="h-5 w-5 mr-2" />
                  Play
                </>
              )}
            </Button>
            <Button
              onClick={cancelRecording}
              variant="outline"
              size="sm"
              disabled={isProcessing || isTranscribing || isSubmitting}
            >
              <Trash2 className="h-5 w-5 mr-2" />
              Discard
            </Button>
            <Button
              onClick={handleSubmit}
              variant="default"
              size="sm"
              className="bg-voicechain-purple hover:bg-voicechain-accent"
              disabled={isProcessing || isTranscribing || isSubmitting}
            >
              {isSubmitting || isProcessing || isTranscribing ? (
                <Loader2 className="h-5 w-5 animate-spin mr-2" />
              ) : (
                <Send className="h-5 w-5 mr-2" />
              )}
              {isSubmitting ? 'Sending...' : 'Send'}
            </Button>
          </>
        )}
      </div>

      {/* Media uploader - only show when recording is complete or not started */}
      {maxMediaFiles > 0 && !isRecording && (
        <div className="mt-4">
          <h4 className="text-sm font-medium mb-2">Attach Media (Optional)</h4>
          <MediaUploader
            maxFiles={maxMediaFiles}
            onFilesSelected={handleMediaChange}
            selectedFiles={media}
          />
        </div>
      )}

      {/* Transcript */}
      {isTranscribing && (
        <div className="text-sm text-center text-muted-foreground flex items-center justify-center">
          <Loader2 className="h-3 w-3 mr-2 animate-spin" />
          Transcribing...
        </div>
      )}

      {transcript && !isTranscribing && (
        <div className="text-sm">
          <h4 className="font-medium text-xs text-muted-foreground mb-1">Transcript:</h4>
          <p className="bg-secondary/30 p-2 rounded text-sm">{transcript}</p>
        </div>
      )}
    </div>
  );
};

export default AudioRecorderWithFilters;
