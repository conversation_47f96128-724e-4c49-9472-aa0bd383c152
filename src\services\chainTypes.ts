
export interface ChainEvent {
  id: string;
  type: string; // Adding the missing type property
  eventType?: string; // Keep backward compatibility
  sourceChain?: string; // Keep backward compatibility
  title?: string;
  description?: string;
  timestamp: number | Date;
  data: Record<string, any>;
  tags?: string[];
  source: {
    chain: string;
    project?: string;
    dao?: string;
  }
}

// Add the ChainEventType enum that's being referenced in multiple files
export enum ChainEventType {
  DAO_PROPOSAL_CREATED = 'dao_proposal_created',
  DAO_PROPOSAL_EXECUTED = 'dao_proposal_executed',
  DAO_VOTE = 'dao_vote',
  FUNDING_ROUND = 'funding_round',
  SECURITY_INCIDENT = 'security_incident',
  NFT_LAUNCH = 'nft_launch',
  TOKEN_LISTING = 'token_listing',
  WHALE_MOVEMENT = 'whale_movement',
  BRIDGE_TRANSACTION = 'bridge_transaction'
}

export interface ChainVoicePost {
  id: string;
  eventType: string;
  sourceChain: string;
  title: string;
  transcription: string;
  audioUrl: string;
  audioDuration: number;
  timestamp: Date;
  tags: string[];
}
