-- Create journal_tips table for tipping journal owners
CREATE TABLE public.journal_tips (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  from_user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  to_user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  journal_id uuid NOT NULL REFERENCES public.journals(id) ON DELETE CASCADE,
  amount numeric NOT NULL,
  currency text NOT NULL DEFAULT 'SOL',
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.journal_tips ENABLE ROW LEVEL SECURITY;

-- Create policies for journal tips
CREATE POLICY "Anyone can view journal tips" 
ON public.journal_tips 
FOR SELECT 
USING (true);

CREATE POLICY "Authenticated users can send journal tips" 
ON public.journal_tips 
FOR INSERT 
WITH CHECK (auth.uid() = from_user_id);

-- Create indexes for better performance
CREATE INDEX idx_journal_tips_journal_id ON public.journal_tips(journal_id);
CREATE INDEX idx_journal_tips_to_user_id ON public.journal_tips(to_user_id);
CREATE INDEX idx_journal_tips_from_user_id ON public.journal_tips(from_user_id);

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_journal_tips_updated_at
BEFORE UPDATE ON public.journal_tips
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();