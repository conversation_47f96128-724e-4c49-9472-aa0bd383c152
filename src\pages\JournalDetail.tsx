import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/sonner';
import {
  ArrowLeft,
  Calendar,
  Clock,
  Lock,
  Unlock,
  Play,
  Volume2,
  Heart,
  Share,
  MessageCircle,
  MoreVertical
} from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import { supabase } from '@/integrations/supabase/client';
import PostOptionsMenu from '@/components/PostOptionsMenu';
import PostReactions from '@/components/PostReactions';

// Journal types
export interface JournalEntry {
  id: string;
  title: string;
  audioUrl: string;
  transcript: string;
  userAddress: string;
  createdAt: Date;
  scheduledFor?: Date;
  isPublished: boolean;
  isLocked: boolean;
  unlockCondition?: {
    type: 'date' | 'password' | 'token';
    value: string;
  };
  duration: number;
  media?: {
    id: string;
    url: string;
    type: 'image' | 'video';
  }[];
}

const JournalDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { getProfileByAddress } = useProfiles();
  const [journal, setJournal] = useState<JournalEntry | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [isUnlocking, setIsUnlocking] = useState(false);
  const [unlockValue, setUnlockValue] = useState('');
  const [audioRef, setAudioRef] = useState<HTMLAudioElement | null>(null);

  // Get current user
  const getCurrentUserAccount = () => {
    return localStorage.getItem('connectedAccount') || '';
  };

  // Load journal data
  useEffect(() => {
    const loadJournal = async () => {
      if (!id) return;

      try {
        // Fetch journal from Supabase
        const { data, error } = await supabase
          .from('journals')
          .select('*, media:journal_media(*)')
          .eq('id', id)
          .single();

        if (error) {
          console.error('Error fetching journal:', error);
          toast.error('Failed to load journal');
          return;
        }

        if (data) {
          // Map to our interface
          const journalEntry: JournalEntry = {
            id: data.id,
            title: data.title,
            audioUrl: data.audio_url,
            transcript: data.transcript || '',
            userAddress: data.profile_id,
            createdAt: new Date(data.created_at),
            scheduledFor: data.scheduled_for ? new Date(data.scheduled_for) : undefined,
            isPublished: data.is_published,
            isLocked: data.is_locked,
            unlockCondition: data.unlock_condition ? {
              type: data.unlock_condition.type,
              value: data.unlock_condition.value
            } : undefined,
            duration: data.audio_duration || 0,
            media: data.media ? data.media.map((item: any) => ({
              id: item.id,
              url: item.url,
              type: item.type
            })) : []
          };

          setJournal(journalEntry);

          // Create audio element
          const audio = new Audio(journalEntry.audioUrl);
          setAudioRef(audio);

          // Set up event listeners
          audio.addEventListener('ended', () => {
            setIsPlaying(false);
            setCurrentTime(0);
          });

          audio.addEventListener('timeupdate', () => {
            setCurrentTime(audio.currentTime);
          });
        }
      } catch (error) {
        console.error('Error in loadJournal:', error);
        toast.error('Failed to load journal');
      }
    };

    loadJournal();

    // Cleanup
    return () => {
      if (audioRef) {
        audioRef.pause();
        audioRef.removeEventListener('ended', () => { });
        audioRef.removeEventListener('timeupdate', () => { });
      }
    };
  }, [id]);

  // Toggle play/pause
  const togglePlay = () => {
    if (!audioRef) return;

    if (isPlaying) {
      audioRef.pause();
    } else {
      audioRef.play();
    }

    setIsPlaying(!isPlaying);
  };

  // Format time
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60).toString().padStart(2, '0');
    const secs = Math.floor(seconds % 60).toString().padStart(2, '0');
    return `${mins}:${secs}`;
  };

  // Calculate progress percentage
  const progressPercentage = journal?.duration ? (currentTime / journal.duration) * 100 : 0;

  // Handle unlock attempt
  const handleUnlock = () => {
    if (!journal || !journal.unlockCondition) return;

    if (journal.unlockCondition.type === 'password' && unlockValue === journal.unlockCondition.value) {
      toast.success('Journal unlocked!');
      setJournal(prev => prev ? { ...prev, isLocked: false } : null);
    } else if (journal.unlockCondition.type === 'date') {
      const unlockDate = new Date(journal.unlockCondition.value);
      if (new Date() >= unlockDate) {
        toast.success('Journal unlocked!');
        setJournal(prev => prev ? { ...prev, isLocked: false } : null);
      } else {
        toast.error(`This journal will unlock on ${format(unlockDate, 'PPP')}`);
      }
    } else {
      toast.error('Invalid unlock value');
    }
  };

  // If journal is not loaded yet
  if (!journal) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-3xl">
        <div className="flex items-center mb-4">
          <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
            <ArrowLeft size={20} />
          </Button>
          <h1 className="text-2xl font-bold ml-2">Loading Journal...</h1>
        </div>
      </div>
    );
  }

  // Get user profile
  const userProfile = getProfileByAddress(journal.userAddress);
  const isOwner = getCurrentUserAccount() === journal.userAddress;

  return (
    <div className="container mx-auto px-4 py-6 max-w-3xl">
      {/* Header */}
      <div className="flex items-center mb-4">
        <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
          <ArrowLeft size={20} />
        </Button>
        <h1 className="text-2xl font-bold ml-2">Journal</h1>
      </div>

      <Separator className="mb-6" />

      {/* Journal Content */}
      <div className="bg-secondary rounded-xl p-6">
        {/* User info and options */}
        <div className="flex justify-between items-start mb-4">
          <div className="flex items-center">
            <Avatar className="h-10 w-10 mr-3">
              <AvatarImage src={userProfile.profileImageUrl} alt={userProfile.displayName} />
              <AvatarFallback>{userProfile.displayName.charAt(0)}</AvatarFallback>
            </Avatar>
            <div>
              <h2 className="font-semibold">{userProfile.displayName}</h2>
              <p className="text-sm text-muted-foreground">@{userProfile.username}</p>
            </div>
          </div>

          <div className="flex items-center">
            {journal.isLocked && (
              <Badge variant="outline" className="mr-2 bg-amber-500/10 text-amber-500 border-amber-500/20">
                <Lock size={12} className="mr-1" />
                Locked
              </Badge>
            )}

            {journal.scheduledFor && new Date() < journal.scheduledFor && (
              <Badge variant="outline" className="mr-2 bg-blue-500/10 text-blue-500 border-blue-500/20">
                <Calendar size={12} className="mr-1" />
                Scheduled
              </Badge>
            )}

            {isOwner && (
              <PostOptionsMenu
                postId={journal.id}
                isOwner={true}
                onEdit={() => navigate(`/journal/edit/${journal.id}`)}
                onDelete={() => {
                  // Handle delete
                  toast.success('Journal deleted');
                  navigate('/journals');
                }}
              />
            )}
          </div>
        </div>

        {/* Title and date */}
        <h1 className="text-2xl font-bold mb-2">{journal.title}</h1>
        <div className="flex items-center text-sm text-muted-foreground mb-4">
          <Calendar size={14} className="mr-1" />
          <span>{format(journal.createdAt, 'PPP')}</span>
          <span className="mx-2">•</span>
          <Clock size={14} className="mr-1" />
          <span>{formatDistanceToNow(journal.createdAt, { addSuffix: true })}</span>
        </div>

        {/* Media */}
        {journal.media && journal.media.length > 0 && (
          <div className="mb-6 grid gap-4 grid-cols-1 md:grid-cols-2">
            {journal.media.map(item => (
              <div key={item.id} className="rounded-lg overflow-hidden">
                {item.type === 'image' ? (
                  <img src={item.url} alt="" className="w-full h-auto" />
                ) : (
                  <video src={item.url} controls className="w-full h-auto" />
                )}
              </div>
            ))}
          </div>
        )}

        {/* Locked content */}
        {journal.isLocked ? (
          <div className="bg-muted rounded-lg p-6 mb-6 text-center">
            <Lock size={32} className="mx-auto mb-2 text-amber-500" />
            <h3 className="text-lg font-semibold mb-2">This journal is locked</h3>

            {journal.unlockCondition?.type === 'date' && (
              <p className="mb-4">
                This journal will unlock on {format(new Date(journal.unlockCondition.value), 'PPP')}
              </p>
            )}

            {journal.unlockCondition?.type === 'password' && isOwner && (
              <div className="mb-4">
                <p>This journal is password protected</p>
                <Button
                  variant="outline"
                  className="mt-2"
                  onClick={() => setJournal(prev => prev ? { ...prev, isLocked: false } : null)}
                >
                  <Unlock size={16} className="mr-2" />
                  Unlock (Owner)
                </Button>
              </div>
            )}
          </div>
        ) : (
          <>
            {/* Transcript */}
            <div className="mb-6">
              <p className="whitespace-pre-wrap">{journal.transcript}</p>
            </div>

            {/* Audio Player */}
            <div className="relative mb-6 bg-muted/50 rounded-lg p-4 flex items-center">
              <Button
                size="icon"
                variant="ghost"
                className={`h-12 w-12 rounded-full flex-shrink-0 ${isPlaying ? 'bg-voicechain-purple' : 'bg-accent/30'}`}
                onClick={togglePlay}
              >
                {isPlaying ? <Volume2 size={24} /> : <Play size={24} />}
              </Button>

              <div className="ml-4 flex-1">
                <div className="h-2 bg-muted rounded-full overflow-hidden">
                  <div
                    className="h-full bg-voicechain-purple"
                    style={{ width: `${progressPercentage}%` }}
                  />
                </div>
              </div>

              <span className="ml-4 text-sm font-mono">
                {formatTime(currentTime)} / {formatTime(journal.duration || 0)}
              </span>
            </div>
          </>
        )}

        {/* Reactions */}
        <div className="mt-4 border-t border-border/40 pt-4">
          <PostReactions
            postId={journal.id}
            currentUserId={getCurrentUserAccount()}
          />
        </div>
      </div>
    </div>
  );
};

export default JournalDetail;
