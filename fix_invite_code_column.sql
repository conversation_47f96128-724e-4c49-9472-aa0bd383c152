-- Fix missing invite_code column in voice_chats table
-- Run this in Supabase SQL Editor

-- Add invite_code column if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'voice_chats' 
    AND column_name = 'invite_code'
  ) THEN
    ALTER TABLE public.voice_chats ADD COLUMN invite_code TEXT UNIQUE;
    CREATE INDEX IF NOT EXISTS idx_voice_chats_invite_code ON public.voice_chats(invite_code);
  END IF;
END $$;

-- Also ensure the group_invites table exists
CREATE TABLE IF NOT EXISTS public.group_invites (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
  chat_id TEXT NOT NULL REFERENCES public.voice_chats(id) ON DELETE CASCADE,
  invite_code TEXT NOT NULL UNIQUE,
  created_by TEXT NOT NULL,
  max_uses INTEGER DEFAULT NULL,
  current_uses INTEGER DEFAULT 0,
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_group_invites_code ON public.group_invites(invite_code);
CREATE INDEX IF NOT EXISTS idx_group_invites_chat ON public.group_invites(chat_id);

-- Enable RLS
ALTER TABLE public.group_invites ENABLE ROW LEVEL SECURITY;

-- RLS Policies for group_invites
CREATE POLICY "Users can view invites for their chats" ON public.group_invites
  FOR SELECT USING (
    chat_id IN (
      SELECT chat_id FROM public.voice_chat_participants 
      WHERE profile_id = auth.uid()::text AND left_at IS NULL
    )
  );

CREATE POLICY "Admins can create invites" ON public.group_invites
  FOR INSERT WITH CHECK (
    chat_id IN (
      SELECT chat_id FROM public.voice_chat_participants 
      WHERE profile_id = auth.uid()::text AND role IN ('admin', 'moderator') AND left_at IS NULL
    )
  );

CREATE POLICY "Admins can update invites" ON public.group_invites
  FOR UPDATE USING (
    chat_id IN (
      SELECT chat_id FROM public.voice_chat_participants 
      WHERE profile_id = auth.uid()::text AND role IN ('admin', 'moderator') AND left_at IS NULL
    )
  );

CREATE POLICY "Admins can delete invites" ON public.group_invites
  FOR DELETE USING (
    chat_id IN (
      SELECT chat_id FROM public.voice_chat_participants 
      WHERE profile_id = auth.uid()::text AND role IN ('admin', 'moderator') AND left_at IS NULL
    )
  );
