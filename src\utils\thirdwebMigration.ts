/**
 * Thirdweb Storage Migration Utility
 * This utility helps migrate data from Supabase to Thirdweb storage
 */

import { supabase } from '@/integrations/supabase/client';
import * as thirdwebStorage from '@/services/thirdwebStorage';
import { toast } from '@/components/ui/sonner';

/**
 * Interface for migration progress callbacks
 */
export interface MigrationProgressCallbacks {
  onStart?: (messageId: string, total: number, current: number) => void;
  onProgress?: (messageId: string, progress: number) => void;
  onSuccess?: (messageId: string, documentId: string) => void;
  onError?: (messageId: string, error: any) => void;
  onComplete?: (messageId: string) => void;
}

/**
 * Interface for batch migration progress callbacks
 */
export interface BatchMigrationCallbacks {
  onBatchStart?: (totalMessages: number) => void;
  onMessageStart?: (messageId: string, index: number, total: number) => void;
  onMessageProgress?: (messageId: string, progress: number, index: number, total: number) => void;
  onMessageSuccess?: (messageId: string, documentId: string, index: number, total: number) => void;
  onMessageError?: (messageId: string, error: any, index: number, total: number) => void;
  onMessageComplete?: (messageId: string, index: number, total: number) => void;
  onBatchProgress?: (completedCount: number, totalCount: number, successCount: number, errorCount: number) => void;
  onBatchComplete?: (results: { messageId: string; documentId: string; success: boolean }[]) => void;
}

/**
 * Migrate a single voice message from Supabase to Thirdweb storage
 * @param messageId The ID of the voice message to migrate
 * @param callbacks Optional callbacks for tracking progress
 * @param retryCount Number of retry attempts (default: 3)
 * @param currentAttempt Current retry attempt (default: 1)
 * @returns The new document ID (in this case, the IPFS URI)
 */
export async function migrateVoiceMessage(
  messageId: string,
  callbacks?: MigrationProgressCallbacks,
  retryCount: number = 3,
  currentAttempt: number = 1
): Promise<string | null> {
  try {
    // Notify start of migration
    callbacks?.onStart?.(messageId, retryCount, currentAttempt);

    // Fetch the voice message from Supabase
    const { data: message, error } = await supabase
      .from('voice_messages')
      .select(`
        id,
        profile_id,
        audio_url,
        transcript,
        audio_duration,
        parent_id,
        channel_id,
        is_pinned,
        created_at,
        voice_message_media (
          id,
          url,
          type
        )
      `)
      .eq('id', messageId)
      .single();

    if (error || !message) {
      const errorMsg = `Error fetching voice message: ${error?.message || 'Unknown error'}`;
      console.error(errorMsg);
      callbacks?.onError?.(messageId, errorMsg);

      // Retry if we haven't reached the retry limit
      if (currentAttempt < retryCount) {
        console.log(`Retrying migration for message ${messageId} (Attempt ${currentAttempt + 1}/${retryCount})`);
        return migrateVoiceMessage(messageId, callbacks, retryCount, currentAttempt + 1);
      }

      return null;
    }

    // Update progress - 10%
    callbacks?.onProgress?.(messageId, 10);

    // Check if this message has already been migrated
    if (message.blockchain_audio_url) {
      console.log(`Message ${messageId} has already been migrated to blockchain storage`);
      callbacks?.onSuccess?.(messageId, message.blockchain_audio_url);
      callbacks?.onComplete?.(messageId);
      return message.blockchain_audio_url;
    }

    // Fetch the audio file from Supabase
    const audioResponse = await fetch(message.audio_url);
    if (!audioResponse.ok) {
      const errorMsg = `Error fetching audio file: ${audioResponse.statusText}`;
      console.error(errorMsg);
      callbacks?.onError?.(messageId, errorMsg);

      // Retry if we haven't reached the retry limit
      if (currentAttempt < retryCount) {
        console.log(`Retrying migration for message ${messageId} (Attempt ${currentAttempt + 1}/${retryCount})`);
        return migrateVoiceMessage(messageId, callbacks, retryCount, currentAttempt + 1);
      }

      return null;
    }

    const audioBlob = await audioResponse.blob();

    // Update progress - 30%
    callbacks?.onProgress?.(messageId, 30);

    // Upload the audio to IPFS via Thirdweb
    const timestamp = Date.now();
    const fileName = `${message.profile_id.substring(0, 10)}_${timestamp}.webm`;

    try {
      const ipfsUri = await thirdwebStorage.uploadBlobToIPFS(audioBlob, fileName);

      // Update progress - 50%
      callbacks?.onProgress?.(messageId, 50);

      // Process media files
      const mediaPromises = (message.voice_message_media || []).map(async (item: any, index: number) => {
        try {
          // Fetch the media file from Supabase
          const mediaResponse = await fetch(item.url);
          if (!mediaResponse.ok) {
            console.warn(`Error fetching media file ${item.id}: ${mediaResponse.statusText}`);
            return {
              id: item.id,
              url: item.url, // Keep the original URL as fallback
              type: item.type
            };
          }

          const mediaBlob = await mediaResponse.blob();
          const mediaFileName = `${item.id}_${timestamp}.${item.url.split('.').pop() || 'jpg'}`;
          const mediaIpfsUri = await thirdwebStorage.uploadBlobToIPFS(mediaBlob, mediaFileName);

          // Update progress for each media file - 50% to 70% range
          const mediaProgress = 50 + (20 * (index + 1) / (message.voice_message_media?.length || 1));
          callbacks?.onProgress?.(messageId, Math.min(70, mediaProgress));

          return {
            id: item.id,
            url: mediaIpfsUri,
            type: item.type
          };
        } catch (mediaError) {
          console.warn(`Error processing media file ${item.id}:`, mediaError);
          // Return original URL as fallback
          return {
            id: item.id,
            url: item.url,
            type: item.type
          };
        }
      });

      const media = await Promise.all(mediaPromises);

      // Update progress - 70%
      callbacks?.onProgress?.(messageId, 70);

      // Store metadata in localStorage for future reference
      const metadata = {
        audio: ipfsUri,
        transcript: message.transcript || '',
        createdAt: message.created_at,
        author: message.profile_id,
        media,
        parentId: message.parent_id,
        channelId: message.channel_id,
        isPinned: message.is_pinned,
        duration: message.audio_duration
      };

      // Store metadata in localStorage
      try {
        const storedMetadata = JSON.parse(localStorage.getItem('voice_message_metadata') || '{}');
        storedMetadata[messageId] = metadata;
        localStorage.setItem('voice_message_metadata', JSON.stringify(storedMetadata));
      } catch (storageError) {
        console.warn('Error storing metadata in localStorage:', storageError);
      }

      // Update progress - 90%
      callbacks?.onProgress?.(messageId, 90);

      // Update the voice message in Supabase with the IPFS URI
      const { error: updateError } = await supabase
        .from('voice_messages')
        .update({
          blockchain_audio_url: ipfsUri,
          updated_at: new Date().toISOString()
        })
        .eq('id', messageId);

      if (updateError) {
        console.warn(`Error updating voice message in Supabase: ${updateError.message}`);
        // We'll still consider this a success since the data is on IPFS
      }

      // Update progress - 100%
      callbacks?.onProgress?.(messageId, 100);
      callbacks?.onSuccess?.(messageId, ipfsUri);
      callbacks?.onComplete?.(messageId);

      return ipfsUri;
    } catch (ipfsError) {
      const errorMsg = `Error uploading to IPFS: ${ipfsError instanceof Error ? ipfsError.message : 'Unknown error'}`;
      console.error(errorMsg);
      callbacks?.onError?.(messageId, errorMsg);

      // Retry if we haven't reached the retry limit
      if (currentAttempt < retryCount) {
        console.log(`Retrying migration for message ${messageId} (Attempt ${currentAttempt + 1}/${retryCount})`);
        return migrateVoiceMessage(messageId, callbacks, retryCount, currentAttempt + 1);
      }

      return null;
    }
  } catch (error) {
    const errorMsg = `Error migrating voice message: ${error instanceof Error ? error.message : 'Unknown error'}`;
    console.error(errorMsg);
    callbacks?.onError?.(messageId, errorMsg);

    // Retry if we haven't reached the retry limit
    if (currentAttempt < retryCount) {
      console.log(`Retrying migration for message ${messageId} (Attempt ${currentAttempt + 1}/${retryCount})`);
      return migrateVoiceMessage(messageId, callbacks, retryCount, currentAttempt + 1);
    }

    return null;
  }
}

/**
 * Migrate all voice messages for a user from Supabase to Thirdweb storage
 * @param profileId The profile ID (wallet address) of the user
 * @param callbacks Optional callbacks for tracking progress
 * @param batchSize Size of each batch to process (default: 5)
 * @param limit Optional limit on the number of messages to migrate
 * @returns Array of migrated message IDs and their document IDs
 */
export async function migrateUserVoiceMessages(
  profileId: string,
  callbacks?: BatchMigrationCallbacks,
  batchSize: number = 5,
  limit?: number
): Promise<{ messageId: string; documentId: string; success: boolean }[]> {
  try {
    // Fetch voice messages from Supabase
    let query = supabase
      .from('voice_messages')
      .select('id, blockchain_audio_url')
      .eq('profile_id', profileId)
      .order('created_at', { ascending: false });

    if (limit) {
      query = query.limit(limit);
    }

    const { data: messages, error } = await query;

    if (error) {
      console.error('Error fetching voice messages:', error);
      toast.error('Error fetching voice messages');
      return [];
    }

    if (!messages || messages.length === 0) {
      console.log('No voice messages found for user:', profileId);
      toast.info('No voice messages found to migrate');
      return [];
    }

    // Filter out messages that have already been migrated
    const messagesToMigrate = messages.filter(msg => !msg.blockchain_audio_url);
    const alreadyMigratedCount = messages.length - messagesToMigrate.length;

    if (messagesToMigrate.length === 0) {
      console.log('All voice messages have already been migrated for user:', profileId);
      toast.success(`All ${messages.length} voice messages are already on blockchain storage`);
      return messages.map(msg => ({
        messageId: msg.id,
        documentId: msg.blockchain_audio_url || '',
        success: true
      }));
    }

    // Notify batch start
    callbacks?.onBatchStart?.(messagesToMigrate.length);

    if (alreadyMigratedCount > 0) {
      toast.info(`${alreadyMigratedCount} messages already migrated. Migrating ${messagesToMigrate.length} remaining messages.`);
    }

    // Process messages in batches
    const results: { messageId: string; documentId: string; success: boolean }[] = [];
    let successCount = 0;
    let errorCount = 0;
    let completedCount = 0;

    // Process messages in batches
    for (let i = 0; i < messagesToMigrate.length; i += batchSize) {
      const batch = messagesToMigrate.slice(i, i + batchSize);

      // Process each message in the batch concurrently
      const batchPromises = batch.map(async (message, batchIndex) => {
        const messageIndex = i + batchIndex;
        const totalMessages = messagesToMigrate.length;

        try {
          // Create message-specific callbacks
          const messageCallbacks: MigrationProgressCallbacks = {
            onStart: (messageId, total, current) => {
              callbacks?.onMessageStart?.(messageId, messageIndex, totalMessages);
            },
            onProgress: (messageId, progress) => {
              callbacks?.onMessageProgress?.(messageId, progress, messageIndex, totalMessages);
            },
            onSuccess: (messageId, documentId) => {
              successCount++;
              callbacks?.onMessageSuccess?.(messageId, documentId, messageIndex, totalMessages);
            },
            onError: (messageId, error) => {
              errorCount++;
              callbacks?.onMessageError?.(messageId, error, messageIndex, totalMessages);
            },
            onComplete: (messageId) => {
              completedCount++;
              callbacks?.onMessageComplete?.(messageId, messageIndex, totalMessages);

              // Update batch progress
              callbacks?.onBatchProgress?.(completedCount, totalMessages, successCount, errorCount);
            }
          };

          // Migrate the message
          const documentId = await migrateVoiceMessage(message.id, messageCallbacks);

          if (documentId) {
            return {
              messageId: message.id,
              documentId,
              success: true
            };
          } else {
            return {
              messageId: message.id,
              documentId: '',
              success: false
            };
          }
        } catch (error) {
          console.error(`Error migrating message ${message.id}:`, error);
          errorCount++;

          return {
            messageId: message.id,
            documentId: '',
            success: false
          };
        }
      });

      // Wait for all messages in the batch to complete
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Update batch progress
      callbacks?.onBatchProgress?.(completedCount, messagesToMigrate.length, successCount, errorCount);
    }

    // Notify batch completion
    callbacks?.onBatchComplete?.(results);

    // Show toast with results
    const successfulMigrations = results.filter(r => r.success).length;
    if (successfulMigrations === messagesToMigrate.length) {
      toast.success(`Successfully migrated all ${successfulMigrations} voice messages to blockchain storage`);
    } else if (successfulMigrations > 0) {
      toast.success(`Successfully migrated ${successfulMigrations} of ${messagesToMigrate.length} voice messages to blockchain storage`);
    } else {
      toast.error(`Failed to migrate any voice messages to blockchain storage`);
    }

    return results;
  } catch (error) {
    console.error('Error migrating user voice messages:', error);
    toast.error('Error migrating voice messages');
    return [];
  }
}
