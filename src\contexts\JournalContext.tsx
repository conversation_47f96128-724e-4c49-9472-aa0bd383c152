import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { JournalEntry } from '@/types/journal';
import { journalService } from '@/services/journalService';
import { toast } from '@/components/ui/sonner';

interface JournalContextType {
  journals: JournalEntry[];
  isLoading: boolean;
  addJournal: (journal: JournalEntry) => Promise<void>;
  getJournalById: (id: string) => JournalEntry | undefined;

  // Privacy-based filtering
  getMyJournals: (userAddress: string) => JournalEntry[];
  getPublicJournals: () => JournalEntry[];
  getLockedPublicJournals: () => JournalEntry[];
  getPrivateJournals: (userAddress: string) => JournalEntry[];
  getLockedPrivateJournals: () => JournalEntry[];

  // Legacy methods for backward compatibility
  getUserJournals: (userAddress: string) => JournalEntry[];
  getUnlockedJournals: (userAddress: string) => JournalEntry[];
  getLockedJournals: (userAddress: string) => JournalEntry[];
  getAllPublicJournals: () => JournalEntry[];
  canUserUnlockJournal: (journalId: string, userAddress: string) => boolean;
  unlockJournal: (id: string, userAddress: string) => boolean;

  // Access control
  canUserAccessJournal: (journalId: string, userAddress: string) => boolean;
  summonUsersToJournal: (journalId: string, userIds: string[], summonedBy: string) => Promise<boolean>;
  tipToUnlockJournal: (journalId: string, userId: string, tipAmount: number, tipCurrency?: string) => Promise<boolean>;

  // Social features
  repostJournal: (journalId: string, userId: string) => Promise<boolean>;
  addJournalReply: (journalId: string, userId: string, content?: string, audioUrl?: string, audioDuration?: number) => Promise<string | null>;
  addJournalReaction: (journalId: string, userId: string, reactionType: 'like' | 'love' | 'laugh' | 'wow' | 'sad' | 'angry') => Promise<boolean>;

  // Utility
  deleteJournal: (id: string, userAddress: string) => Promise<void>;
  refreshJournals: () => Promise<void>;
}

const JournalContext = createContext<JournalContextType | undefined>(undefined);

export const useJournals = () => {
  const context = useContext(JournalContext);
  if (!context) {
    throw new Error('useJournals must be used within a JournalProvider');
  }
  return context;
};

interface JournalProviderProps {
  children: ReactNode;
  userAddress: string;
}

export const JournalProvider: React.FC<JournalProviderProps> = ({
  children,
  userAddress
}) => {
  const [journals, setJournals] = useState<JournalEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Load journals from database on mount and when userAddress changes
  useEffect(() => {
    if (userAddress) {
      loadJournalsFromDatabase();
    }
  }, [userAddress]);

  const loadJournalsFromDatabase = async () => {
    if (!userAddress) return;

    setIsLoading(true);
    try {
      console.log('📖 Loading accessible journals from database for user:', userAddress);

      // Load all accessible journals for the user using the new RPC function
      const accessibleJournals = await journalService.getAccessibleJournals(userAddress);

      console.log('📖 Loaded accessible journals from database:', accessibleJournals.length);
      setJournals(accessibleJournals);
    } catch (error) {
      console.error('❌ Error loading journals from database:', error);
      toast.error('Failed to load journals');
    } finally {
      setIsLoading(false);
    }
  };

  // Add a new journal to database
  const addJournal = async (journal: JournalEntry) => {
    try {
      console.log('📝 Adding journal to database:', journal);

      // Create journal in database (using legacy method until database is updated)
      const journalId = await journalService.createJournal(
        journal.userAddress,
        journal.title,
        journal.audioUrl,
        journal.transcript,
        journal.duration,
        journal.isLocked || false,
        journal.unlockCondition,
        journal.scheduledFor,
        journal.media,
        journal.isPrivate || false
      );

      console.log('✅ Journal created with ID:', journalId);

      // Refresh journals from database to get the latest data
      await loadJournalsFromDatabase();

      toast.success('Journal saved successfully!');
    } catch (error) {
      console.error('❌ Error adding journal:', error);
      toast.error('Failed to save journal');
      throw error;
    }
  };

  // Get a journal by ID
  const getJournalById = (id: string) => {
    return journals.find(journal => journal.id === id);
  };

  // Privacy-based filtering methods (simplified until database migration)
  const getMyJournals = (userAddress: string) => {
    return journals.filter(journal =>
      (journal.userAddress === userAddress || journal.userAddress.toLowerCase() === userAddress.toLowerCase()) &&
      (journal.isPrivate || journal.privacyLevel === 'my_journal')
    );
  };

  const getPublicJournals = () => {
    return journals.filter(journal =>
      (!journal.isPrivate && journal.privacyLevel !== 'my_journal') && !journal.isLocked
    );
  };

  const getLockedPublicJournals = () => {
    return journals.filter(journal =>
      (!journal.isPrivate && journal.privacyLevel !== 'my_journal') && journal.isLocked
    );
  };

  const getPrivateJournals = (userAddress: string) => {
    return journals.filter(journal =>
      journal.isPrivate &&
      (journal.userAddress === userAddress ||
       journal.userAddress.toLowerCase() === userAddress.toLowerCase() ||
       journal.summonedUsers?.includes(userAddress))
    );
  };

  const getLockedPrivateJournals = () => {
    return journals.filter(journal =>
      journal.isPrivate && journal.isLocked
    );
  };

  // Legacy methods for backward compatibility
  const getUserJournals = (userAddress: string) => {
    return journals.filter(journal =>
      journal.userAddress === userAddress ||
      journal.userAddress.toLowerCase() === userAddress.toLowerCase()
    );
  };

  const getUnlockedJournals = (userAddress: string) => {
    return journals.filter(journal => {
      // If it's the user's own journal, they can always see it
      if (journal.userAddress === userAddress || journal.userAddress.toLowerCase() === userAddress.toLowerCase()) {
        return true;
      }

      // For public journals, check if unlocked
      if (journal.privacyLevel === 'public') {
        return !journal.isLocked;
      }

      // For private journals, check if user is summoned
      if (journal.privacyLevel === 'private') {
        return journal.summonedUsers?.includes(userAddress);
      }

      return false;
    });
  };

  // Get all locked journals for a user
  const getLockedJournals = (userAddress: string) => {
    return journals.filter(journal =>
      journal.userAddress === userAddress &&
      !journal.isUnlocked &&
      !checkIfJournalIsUnlocked(journal)
    );
  };

  // Get all public journals from all users
  const getAllPublicJournals = () => {
    // Return all journals that are not marked as private
    // This makes journals visible to everyone by default
    return journals.filter(journal => !journal.isPrivate);
  };

  // Check if a specific user can unlock a journal
  const canUserUnlockJournal = (journalId: string, userAddress: string): boolean => {
    const journal = getJournalById(journalId);
    if (!journal) return false;

    // If the user is the owner, they can always unlock
    if (journal.userAddress === userAddress) return true;

    // If the journal is already unlocked for everyone, return true
    if (journal.isUnlocked) return true;

    // Check unlock conditions
    const { type, unlockDate, tokenAddress, eventId } = journal.unlockCondition;

    switch (type) {
      case 'time':
        // Time-based journals can be unlocked by anyone after the unlock date
        return unlockDate ? new Date() >= unlockDate : false;

      case 'token':
        // In a real implementation, check if the user holds the required token
        // For now, only the owner can unlock token-based journals
        return journal.userAddress === userAddress;

      case 'event':
        // In a real implementation, check if the event has occurred
        // For now, only the owner can unlock event-based journals
        return journal.userAddress === userAddress;

      default:
        return false;
    }
  };

  // Check if a journal is unlocked based on its unlock condition
  const checkIfJournalIsUnlocked = (journal: JournalEntry): boolean => {
    if (journal.isUnlocked) return true;

    // If there's no unlock condition, consider it unlocked
    if (!journal.unlockCondition) return true;

    const { type, unlockDate, tokenAddress, eventId } = journal.unlockCondition;

    switch (type) {
      case 'time':
        if (unlockDate && new Date() >= unlockDate) {
          // Update the journal to mark it as unlocked
          setJournals(prev =>
            prev.map(j =>
              j.id === journal.id
                ? { ...j, isUnlocked: true }
                : j
            )
          );
          return true;
        }
        return false;

      case 'token':
        // In a real implementation, this would check if the user holds the token
        // For now, we'll just return false
        return false;

      case 'event':
        // In a real implementation, this would check if the event has occurred
        // For now, we'll just return false
        return false;

      default:
        return false;
    }
  };

  // Unlock a journal if the user meets the conditions
  const unlockJournal = (id: string, userAddress: string): boolean => {
    const journal = getJournalById(id);
    if (!journal) return false;

    // Check if the user can unlock this journal
    if (!canUserUnlockJournal(id, userAddress)) {
      return false;
    }

    // Mark the journal as unlocked
    setJournals(prev =>
      prev.map(j =>
        j.id === id
          ? { ...j, isUnlocked: true }
          : j
      )
    );
    return true;
  };

  // Delete a journal from database
  const deleteJournal = async (id: string, userAddress: string) => {
    try {
      console.log('🗑️ Deleting journal from database:', id);

      const success = await journalService.deleteJournal(id, userAddress);

      if (success) {
        // Refresh journals from database
        await loadJournalsFromDatabase();
        toast.success('Journal deleted successfully!');
      } else {
        toast.error('Failed to delete journal - permission denied');
      }
    } catch (error) {
      console.error('❌ Error deleting journal:', error);
      toast.error('Failed to delete journal');
      throw error;
    }
  };

  // Access control methods
  const canUserAccessJournal = (journalId: string, userAddress: string): boolean => {
    const journal = journals.find(j => j.id === journalId);
    if (!journal) return false;

    // Owner can always access
    if (journal.userAddress === userAddress || journal.userAddress.toLowerCase() === userAddress.toLowerCase()) {
      return true;
    }

    switch (journal.privacyLevel) {
      case 'my_journal':
        return false;
      case 'public':
        return !journal.isLocked;
      case 'locked_public':
        return !journal.isLocked || (journal.scheduledFor && journal.scheduledFor <= new Date());
      case 'private':
        return journal.summonedUsers?.includes(userAddress) || false;
      case 'locked_private':
        // Check if user has paid to unlock (this would need to be checked in database)
        return journal.summonedUsers?.includes(userAddress) || false;
      default:
        return false;
    }
  };

  const summonUsersToJournal = async (journalId: string, userIds: string[], summonedBy: string): Promise<boolean> => {
    try {
      const success = await journalService.summonUsersToJournal(journalId, userIds, summonedBy);
      if (success) {
        await loadJournalsFromDatabase(); // Refresh to get updated data
        toast.success('Users summoned successfully!');
      } else {
        toast.error('Failed to summon users');
      }
      return success;
    } catch (error) {
      console.error('Error summoning users:', error);
      toast.error('Failed to summon users');
      return false;
    }
  };

  const tipToUnlockJournal = async (journalId: string, userId: string, tipAmount: number, tipCurrency: string = 'SOL'): Promise<boolean> => {
    try {
      const success = await journalService.tipToUnlockJournal(journalId, userId, tipAmount, tipCurrency);
      if (success) {
        await loadJournalsFromDatabase(); // Refresh to get updated access
        toast.success('Journal unlocked successfully!');
      } else {
        toast.error('Failed to unlock journal');
      }
      return success;
    } catch (error) {
      console.error('Error unlocking journal:', error);
      toast.error('Failed to unlock journal');
      return false;
    }
  };

  const repostJournal = async (journalId: string, userId: string): Promise<boolean> => {
    try {
      const success = await journalService.repostJournal(journalId, userId);
      if (success) {
        await loadJournalsFromDatabase(); // Refresh to get updated counts
        toast.success('Journal reposted!');
      } else {
        toast.error('Failed to repost journal');
      }
      return success;
    } catch (error) {
      console.error('Error reposting journal:', error);
      toast.error('Failed to repost journal');
      return false;
    }
  };

  const addJournalReply = async (journalId: string, userId: string, content?: string, audioUrl?: string, audioDuration?: number): Promise<string | null> => {
    try {
      const replyId = await journalService.addJournalReply(journalId, userId, content, audioUrl, audioDuration);
      if (replyId) {
        await loadJournalsFromDatabase(); // Refresh to get updated counts
        toast.success('Reply added!');
      } else {
        toast.error('Failed to add reply');
      }
      return replyId;
    } catch (error) {
      console.error('Error adding reply:', error);
      toast.error('Failed to add reply');
      return null;
    }
  };

  const addJournalReaction = async (journalId: string, userId: string, reactionType: 'like' | 'love' | 'laugh' | 'wow' | 'sad' | 'angry'): Promise<boolean> => {
    try {
      const success = await journalService.addJournalReaction(journalId, userId, reactionType);
      if (success) {
        await loadJournalsFromDatabase(); // Refresh to get updated counts
      }
      return success;
    } catch (error) {
      console.error('Error adding reaction:', error);
      return false;
    }
  };

  // Refresh journals from database
  const refreshJournals = async () => {
    await loadJournalsFromDatabase();
  };

  return (
    <JournalContext.Provider
      value={{
        journals,
        isLoading,
        addJournal,
        getJournalById,

        // Privacy-based filtering
        getMyJournals,
        getPublicJournals,
        getLockedPublicJournals,
        getPrivateJournals,
        getLockedPrivateJournals,

        // Legacy methods
        getUserJournals,
        getUnlockedJournals,
        getLockedJournals,
        getAllPublicJournals,
        canUserUnlockJournal,
        unlockJournal,

        // Access control
        canUserAccessJournal,
        summonUsersToJournal,
        tipToUnlockJournal,

        // Social features
        repostJournal,
        addJournalReply,
        addJournalReaction,

        // Utility
        deleteJournal,
        refreshJournals
      }}
    >
      {children}
    </JournalContext.Provider>
  );
};

export default JournalProvider;
