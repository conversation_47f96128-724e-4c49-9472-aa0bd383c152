import React, { useState } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Drawer, DrawerContent } from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import { X, DollarSign, Loader2, AlertCircle } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from '@/components/ui/sonner';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import { useNotifications } from '@/contexts/NotificationContext';
import { useWallet } from '@/contexts/WalletContext';
import walletService from '@/services/walletService';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import ethereumLogo from '/images/chains/ethereum.png';
import solanaLogo from '/images/chains/solana.png';

interface TipModalProps {
  isOpen: boolean;
  onClose: () => void;
  recipientAddress: string;
  messageId?: string; // Optional message ID for notifications
}

const TipModal: React.FC<TipModalProps> = ({
  isOpen,
  onClose,
  recipientAddress,
  messageId
}) => {
  const isMobile = useIsMobile();
  const [amount, setAmount] = useState('0.01');
  const [message, setMessage] = useState('');
  const [currency, setCurrency] = useState('ETH');
  const [isProcessing, setIsProcessing] = useState(false);
  const { getProfileByAddress } = useProfiles();
  const { addNotification } = useNotifications();
  const { wallet, sendTip, sendSolanaTip } = useWallet();

  // Get recipient profile
  const recipientProfile = getProfileByAddress(recipientAddress);
  const recipientName = recipientProfile?.displayName || walletService.formatAddress(recipientAddress);

  // Predefined tip amounts
  const tipAmounts = ['0.001', '0.01', '0.05', '0.1'];

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Only allow valid numbers
    const value = e.target.value;
    if (/^\d*\.?\d*$/.test(value)) {
      setAmount(value);
    }
  };

  const handleSendTip = async () => {
    if (!wallet) {
      toast.error('No wallet available');
      return;
    }

    if (!amount || parseFloat(amount) <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    // Check if amount is greater than balance
    const currentBalance = currency === 'ETH' ? wallet.balance : wallet.solana?.balance || '0';
    if (parseFloat(amount) > parseFloat(currentBalance)) {
      toast.error('Insufficient balance');
      return;
    }

    setIsProcessing(true);

    try {
      // Send the tip using our wallet service
      const transaction = currency === 'ETH' 
        ? await sendTip(recipientAddress, amount, message)
        : await sendSolanaTip(recipientAddress, amount, message);

      if (!transaction) {
        throw new Error('Transaction failed');
      }

      // Create a notification for the tip recipient
      const currentAccount = localStorage.getItem('connectedAccount');
      if (currentAccount && currentAccount !== recipientAddress && messageId) {
        addNotification(
          'tip',
          currentAccount,
          recipientAddress,
          messageId,
          { tipAmount: `${amount} ${currency}` }
        );
      }

      if (transaction.status === 'completed') {
        toast.success('Tip sent successfully!');
        onClose();
      } else {
        toast.error('Transaction failed. Please try again.');
      }
    } catch (error: any) {
      console.error('Error sending tip:', error);
      toast.error(`Error sending tip: ${error.message || 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
    }
  };

  const ModalContent = () => (
    <div className="flex flex-col py-6 px-4 space-y-6">
      <div className="flex justify-between items-center w-full">
        <h2 className="text-xl font-semibold">Send a tip</h2>
        <Button variant="ghost" size="icon" onClick={onClose} disabled={isProcessing}>
          <X size={20} />
        </Button>
      </div>

      <div className="space-y-4">
        {!wallet ? (
          <div className="flex items-start gap-3 p-3 bg-yellow-500/10 rounded-md border border-yellow-500/20">
            <AlertCircle className="h-5 w-5 text-yellow-500 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-yellow-500">No Wallet Available</p>
              <p className="text-xs text-muted-foreground mt-1">
                You need to create an account to get an in-app wallet for tipping.
              </p>
            </div>
          </div>
        ) : (
          <>
            <div>
              <Label htmlFor="recipient">Recipient</Label>
              <Input
                id="recipient"
                value={recipientName}
                disabled
              />
              <p className="text-xs text-muted-foreground mt-1">
                {walletService.formatAddress(recipientAddress)}
              </p>
            </div>

            <div>
              <Label htmlFor="currency">Currency</Label>
              <Select value={currency} onValueChange={setCurrency} disabled={isProcessing}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ETH">
                    <div className="flex items-center gap-2">
                      <img src={ethereumLogo} alt="Ethereum" className="w-4 h-4" />
                      ETH
                    </div>
                  </SelectItem>
                  <SelectItem value="SOL">
                    <div className="flex items-center gap-2">
                      <img src={solanaLogo} alt="Solana" className="w-4 h-4" />
                      SOL
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="amount">Amount ({currency})</Label>
              <div className="relative">
                <Input
                  id="amount"
                  value={amount}
                  onChange={handleAmountChange}
                  className="pl-8"
                  disabled={isProcessing}
                  type="number"
                  step="0.001"
                  min="0.001"
                />
                <DollarSign className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              </div>

              {/* Quick amount buttons */}
              <div className="flex flex-wrap gap-2 mt-2">
                {tipAmounts.map((amt) => (
                  <Button
                    key={amt}
                    variant="outline"
                    size="sm"
                    onClick={() => setAmount(amt)}
                    className={amount === amt ? 'bg-voicechain-purple text-white' : ''}
                    disabled={isProcessing}
                  >
                    {amt} {currency}
                  </Button>
                ))}
              </div>

              {/* Balance display */}
              <p className="text-xs text-muted-foreground mt-1">
                Your balance: {currency === 'ETH' ? (wallet?.balance || '0') : (wallet?.solana?.balance || '0')} {currency}
              </p>
            </div>

            <div>
              <Label htmlFor="message">Message (optional)</Label>
              <Textarea
                id="message"
                placeholder="Add a message with your tip..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                className="resize-none"
                rows={3}
                disabled={isProcessing}
              />
            </div>

            <Button
              onClick={handleSendTip}
              className="w-full bg-voicechain-purple hover:bg-voicechain-accent"
              disabled={
                isProcessing ||
                !wallet ||
                parseFloat(amount) <= 0 ||
                parseFloat(amount) > parseFloat(currency === 'ETH' ? (wallet?.balance || '0') : (wallet?.solana?.balance || '0'))
              }
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <DollarSign className="mr-2 h-4 w-4" />
                  Send Tip
                </>
              )}
            </Button>
          </>
        )}
      </div>
    </div>
  );

  if (isMobile) {
    return (
      <Drawer open={isOpen} onOpenChange={open => !open && onClose()}>
        <DrawerContent className="max-h-[85vh]">
          <ModalContent />
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <ModalContent />
      </DialogContent>
    </Dialog>
  );
};

export default TipModal;
