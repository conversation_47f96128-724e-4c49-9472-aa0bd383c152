
import React, { useState, useEffect } from 'react';
import { useWallet } from '@/contexts/WalletContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { toast } from '@/components/ui/sonner';
import { 
  Coins, 
  Plus, 
  RefreshCw, 
  Trash2, 
  Send, 
  ExternalLink, 
  Loader2 
} from 'lucide-react';
import { TokenData } from '@/services/walletService';
import walletService from '@/services/walletService';

const TokenManager: React.FC = () => {
  const { 
    wallet, 
    isLoading,
    refreshBalance,
    sendToken
  } = useWallet();
  
  const [tokenBalances, setTokenBalances] = useState<Record<string, string>>({});
  const [tokens, setTokens] = useState<TokenData[]>([]);
  const [isLoadingBalances, setIsLoadingBalances] = useState(false);
  const [isAddTokenOpen, setIsAddTokenOpen] = useState(false);
  const [isSendTokenOpen, setIsSendTokenOpen] = useState(false);
  const [selectedToken, setSelectedToken] = useState<TokenData | null>(null);
  const [isWalletLocked, setIsWalletLocked] = useState(false);
  
  // New token form state
  const [newToken, setNewToken] = useState<{
    address: string;
    symbol: string;
    name: string;
    decimals: string;
  }>({
    address: '',
    symbol: '',
    name: '',
    decimals: '18'
  });
  
  // Send token form state
  const [sendTokenForm, setSendTokenForm] = useState<{
    recipient: string;
    amount: string;
    message: string;
  }>({
    recipient: '',
    amount: '',
    message: ''
  });

  // Initialize tokens from wallet service common tokens
  useEffect(() => {
    if (wallet) {
      const commonTokensList = Object.values(walletService.commonTokens);
      setTokens(commonTokensList);
    }
  }, [wallet]);
  
  // Load token balances
  useEffect(() => {
    if (wallet && !isWalletLocked && tokens.length > 0) {
      loadTokenBalances();
    }
  }, [wallet, isWalletLocked, tokens]);
  
  // Load token balances
  const loadTokenBalances = async () => {
    if (!wallet) return;
    
    setIsLoadingBalances(true);
    
    try {
      const balances: Record<string, string> = {};
      
      // Load balances for each token
      for (const token of tokens) {
        const balance = await getTokenBalance(token.address, token.decimals);
        balances[token.address] = balance;
      }
      
      setTokenBalances(balances);
    } catch (error) {
      console.error('Error loading token balances:', error);
    } finally {
      setIsLoadingBalances(false);
    }
  };

  // Get token balance utility function
  const getTokenBalance = async (tokenAddress: string, decimals: number): Promise<string> => {
    if (!wallet) return '0';
    
    try {
      return await walletService.getTokenBalance(wallet, tokenAddress, decimals);
    } catch (error) {
      console.error('Error getting token balance:', error);
      return '0';
    }
  };
  
  // Handle adding a new token
  const handleAddToken = () => {
    if (!newToken.address || !newToken.symbol || !newToken.name) {
      toast.error('Please fill in all required fields');
      return;
    }
    
    // Validate address format
    if (!newToken.address.startsWith('0x') || newToken.address.length !== 42) {
      toast.error('Invalid token address format');
      return;
    }
    
    // Validate decimals
    const decimals = parseInt(newToken.decimals);
    if (isNaN(decimals) || decimals < 0 || decimals > 18) {
      toast.error('Decimals must be between 0 and 18');
      return;
    }
    
    // Add the token
    const newTokenData: TokenData = {
      address: newToken.address,
      symbol: newToken.symbol,
      name: newToken.name,
      decimals: decimals
    };
    
    setTokens(prevTokens => [...prevTokens, newTokenData]);
    
    // Reset form and close dialog
    setNewToken({
      address: '',
      symbol: '',
      name: '',
      decimals: '18'
    });
    setIsAddTokenOpen(false);
    
    // Refresh balances to include the new token
    setTimeout(loadTokenBalances, 500);
  };
  
  // Handle removing a token
  const removeToken = (tokenAddress: string) => {
    if (confirm('Are you sure you want to remove this token?')) {
      setTokens(prevTokens => prevTokens.filter(token => token.address !== tokenAddress));
    }
  };
  
  // Open send token dialog
  const openSendTokenDialog = (token: TokenData) => {
    setSelectedToken(token);
    setSendTokenForm({
      recipient: '',
      amount: '',
      message: ''
    });
    setIsSendTokenOpen(true);
  };
  
  // Handle sending a token
  const handleSendToken = async () => {
    if (!selectedToken) return;
    
    const { recipient, amount, message } = sendTokenForm;
    
    if (!recipient || !amount) {
      toast.error('Please fill in all required fields');
      return;
    }
    
    // Validate address format
    if (!recipient.startsWith('0x') || recipient.length !== 42) {
      toast.error('Invalid recipient address format');
      return;
    }
    
    // Validate amount
    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
      toast.error('Amount must be greater than zero');
      return;
    }
    
    const tokenBalance = tokenBalances[selectedToken.address] || '0';
    if (parseFloat(amount) > parseFloat(tokenBalance)) {
      toast.error(`Insufficient ${selectedToken.symbol} balance`);
      return;
    }
    
    // Close dialog
    setIsSendTokenOpen(false);
    
    // Send token using the sendToken function from WalletContext
    try {
      const result = await sendToken(recipient, amount, selectedToken.address, message);
      
      if (result) {
        // Refresh balances after successful send
        setTimeout(() => {
          refreshBalance();
          loadTokenBalances();
        }, 1000);
      }
    } catch (error) {
      console.error('Error sending token:', error);
    }
  };
  
  if (!wallet) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Tokens</CardTitle>
          <CardDescription>You don't have a wallet yet</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            A wallet will be created for you automatically during registration.
          </p>
        </CardContent>
      </Card>
    );
  }
  
  if (isWalletLocked) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Tokens</CardTitle>
          <CardDescription>Your wallet is locked</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            Please unlock your wallet to view your tokens.
          </p>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Coins className="h-5 w-5" />
              <span>Tokens</span>
            </CardTitle>
            <CardDescription>Manage your tokens</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              size="icon" 
              onClick={loadTokenBalances}
              disabled={isLoadingBalances}
            >
              <RefreshCw className={`h-4 w-4 ${isLoadingBalances ? 'animate-spin' : ''}`} />
            </Button>
            <Dialog open={isAddTokenOpen} onOpenChange={setIsAddTokenOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="icon">
                  <Plus className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add Token</DialogTitle>
                  <DialogDescription>
                    Add a custom token to your wallet
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="tokenAddress">Token Address</Label>
                    <Input 
                      id="tokenAddress" 
                      placeholder="0x..." 
                      value={newToken.address}
                      onChange={(e) => setNewToken({...newToken, address: e.target.value})}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="tokenSymbol">Token Symbol</Label>
                    <Input 
                      id="tokenSymbol" 
                      placeholder="e.g. USDC" 
                      value={newToken.symbol}
                      onChange={(e) => setNewToken({...newToken, symbol: e.target.value})}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="tokenName">Token Name</Label>
                    <Input 
                      id="tokenName" 
                      placeholder="e.g. USD Coin" 
                      value={newToken.name}
                      onChange={(e) => setNewToken({...newToken, name: e.target.value})}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="tokenDecimals">Decimals</Label>
                    <Input 
                      id="tokenDecimals" 
                      type="number" 
                      min="0" 
                      max="18" 
                      value={newToken.decimals}
                      onChange={(e) => setNewToken({...newToken, decimals: e.target.value})}
                    />
                  </div>
                </div>
                
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsAddTokenOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleAddToken}>
                    Add Token
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {tokens.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No tokens added yet</p>
              <Button 
                variant="outline" 
                size="sm" 
                className="mt-4"
                onClick={() => setIsAddTokenOpen(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Token
              </Button>
            </div>
          ) : (
            tokens.map((token) => (
              <div 
                key={token.address} 
                className="flex items-center justify-between p-3 bg-secondary/50 rounded-md"
              >
                <div className="flex items-center gap-3">
                  {token.logoUrl ? (
                    <img 
                      src={token.logoUrl} 
                      alt={token.symbol} 
                      className="w-8 h-8 rounded-full"
                    />
                  ) : (
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <Coins className="h-4 w-4" />
                    </div>
                  )}
                  <div>
                    <p className="text-sm font-medium">{token.symbol}</p>
                    <p className="text-xs text-muted-foreground">{token.name}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="text-right">
                    {isLoadingBalances ? (
                      <Skeleton className="h-5 w-20" />
                    ) : (
                      <p className="text-sm font-medium">
                        {tokenBalances[token.address] || '0'} {token.symbol}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center">
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      onClick={() => openSendTokenDialog(token)}
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      onClick={() => removeToken(token.address)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
      
      <CardFooter className="flex justify-between">
        <p className="text-xs text-muted-foreground">
          Network: {wallet.network === 'base' ? 'Base Mainnet' : 'Base Goerli Testnet'}
        </p>
      </CardFooter>
      
      {/* Send Token Dialog */}
      <Dialog open={isSendTokenOpen} onOpenChange={setIsSendTokenOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Send {selectedToken?.symbol}</DialogTitle>
            <DialogDescription>
              Send tokens to another address
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="recipient">Recipient Address</Label>
              <Input 
                id="recipient" 
                placeholder="0x..." 
                value={sendTokenForm.recipient}
                onChange={(e) => setSendTokenForm({...sendTokenForm, recipient: e.target.value})}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="amount">Amount</Label>
              <Input 
                id="amount" 
                type="number" 
                min="0" 
                step="0.000001"
                placeholder="0.0" 
                value={sendTokenForm.amount}
                onChange={(e) => setSendTokenForm({...sendTokenForm, amount: e.target.value})}
              />
              <p className="text-xs text-muted-foreground">
                Balance: {selectedToken ? (tokenBalances[selectedToken.address] || '0') : '0'} {selectedToken?.symbol}
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="message">Message (optional)</Label>
              <Input 
                id="message" 
                placeholder="Add a message..." 
                value={sendTokenForm.message}
                onChange={(e) => setSendTokenForm({...sendTokenForm, message: e.target.value})}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsSendTokenOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleSendToken}
              disabled={isLoading || !sendTokenForm.recipient || !sendTokenForm.amount}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Send
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default TokenManager;
