import { v4 as uuidv4 } from 'uuid';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/sonner';

/**
 * Normalize a user address for database consistency
 * @param {string} userAddress - The user wallet address
 * @returns {Promise<string>} - The normalized profile ID
 */
async function getNormalizedProfileId(userAddress) {
  try {
    // Ensure userAddress is in the correct format for the database
    const addressStr = typeof userAddress === 'string' ? userAddress : String(userAddress);
    let profileId = addressStr.toLowerCase();

    // Check if the address is an Ethereum address
    if (profileId.startsWith('0x')) {
      console.log('🔄 Converting Ethereum address to UUID format for database compatibility');

      // Try to find the user's UUID from profiles table
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .ilike('wallet_address', profileId)
        .maybeSingle();

      if (profileError) {
        console.error('❌ Error finding profile by wallet address:', profileError);
      }

      if (profileData && profileData.id) {
        // Use the UUID from the profile
        profileId = profileData.id;
        console.log(`✅ Found matching profile ID: ${profileId}`);
      } else {
        // If we can't find a matching profile, create one
        console.log('🔄 No matching profile found for wallet address, creating new profile');

        // Generate a UUID for the new profile
        const newProfileId = uuidv4();

        // Insert with explicit UUID
        const { data: newProfile, error: createError } = await supabase
          .from('profiles')
          .insert({
            id: newProfileId,
            wallet_address: profileId,
            username: `user_${profileId.substring(2, 8)}`,
            display_name: `User ${profileId.substring(2, 8)}`,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (createError) {
          console.error('❌ Error creating profile:', createError);
        } else if (newProfile) {
          profileId = newProfile.id;
          console.log(`✅ Created new profile with ID: ${profileId}`);
        }
      }
    }

    return profileId;
  } catch (error) {
    console.error('❌ Error normalizing profile ID:', error);
    return typeof userAddress === 'string' ? userAddress.toLowerCase() : String(userAddress).toLowerCase();
  }
}

/**
 * Filter out deleted posts from a list
 * @param {Array} posts - The list of posts to filter
 * @returns {Array} - Filtered list without deleted posts
 */
export function filterDeletedPosts(posts) {
  if (!posts || !Array.isArray(posts)) {
    return [];
  }

  // Filter out any posts that have been marked as deleted in the database
  // This is a safety check in case the database query didn't filter them out
  return posts.filter(post => !post.deleted_at);
}

/**
 * Save a voice message
 * @param {string} audioUrl - The audio URL
 * @param {string} transcript - The transcript
 * @param {string} userAddress - The user wallet address
 * @param {number} duration - The duration of the audio
 * @param {Array} media - Optional media attachments
 * @param {string} customId - Optional custom ID for the message
 * @returns {Object} - The saved voice message
 */
export async function saveVoiceMessage(
  audioUrl,
  transcript,
  userAddress,
  duration,
  media = [],
  customId = null
) {
  try {
    console.log('Saving voice message:', { audioUrl, transcript, userAddress, duration });

    // Use the provided custom ID or generate a new UUID
    const messageId = customId || uuidv4();

    // Ensure userAddress is in the correct format for the database
    // If it's an Ethereum address (starts with 0x), convert it to a UUID format
    let profileId = userAddress.toLowerCase();

    // Check if the address is an Ethereum address
    if (profileId.startsWith('0x')) {
      console.log('Converting Ethereum address to UUID format for database compatibility');

      // Try to find the user's UUID from profiles table
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .ilike('wallet_address', profileId)
        .maybeSingle();

      if (profileError) {
        console.error('Error finding profile by wallet address:', profileError);
      }

      if (profileData && profileData.id) {
        // Use the UUID from the profile
        profileId = profileData.id;
        console.log(`Found matching profile ID: ${profileId}`);
      } else {
        // If we can't find a matching profile, use a fallback approach
        // This is not ideal but prevents the error
        console.warn('No matching profile found for wallet address, using fallback UUID');
        profileId = '2cd24c86-d3c5-4406-a92c-1f0892495e0a'; // Owner's UUID as fallback
      }
    }

    // Create message object
    const message = {
      id: messageId,
      audio_url: audioUrl,
      transcript: transcript || '',
      profile_id: profileId, // Now using the correctly formatted profile ID
      audio_duration: duration,
      created_at: new Date().toISOString(),
      is_pinned: false
    };

    // Store in Supabase
    console.log('Storing message in Supabase:', message);
    const { error } = await supabase
      .from('voice_messages')
      .insert(message);

    if (error) {
      console.error('Error saving voice message to Supabase:', error);
      throw new Error(`Failed to save voice message: ${error.message}`);
    }

    // If there's media, store it in the database too
    if (media && media.length > 0) {
      console.log('📸 MEDIA SAVE START: Storing media attachments:', media);
      const mediaItems = media.map(item => ({
        id: item.id || uuidv4(),
        voice_message_id: messageId, // messageId is already a UUID string
        url: item.url,
        type: item.type,
        created_at: new Date().toISOString()
      }));
      
      console.log('📸 Checking voice_message_id format:', { messageId, isUUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(messageId) });

      console.log('📸 MEDIA ITEMS TO INSERT:', mediaItems);

      const { data: mediaInsertData, error: mediaError } = await supabase
        .from('voice_message_media')
        .insert(mediaItems)
        .select();

      if (mediaError) {
        console.error('❌ MEDIA SAVE ERROR:', mediaError);
        console.error('Failed media items:', mediaItems);
      } else {
        console.log(`Successfully stored ${mediaItems.length} media attachments`);
      }
    }

    return {
      id: messageId,
      userAddress,
      audioUrl,
      transcript,
      timestamp: new Date(),
      duration,
      isPinned: false,
      media,
      replies: []
    };
  } catch (error) {
    console.error('Error saving voice message:', error);
    toast.error('Failed to save your voice message');
    throw error;
  }
}

/**
 * Save a reply to a voice message
 * @param {string} parentId - The ID of the parent message
 * @param {string} audioUrl - The audio URL
 * @param {string} transcript - The transcript
 * @param {string} userAddress - The user wallet address
 * @param {number} duration - The duration of the audio
 * @param {Array} media - Optional media attachments
 * @returns {Promise<Object>} - The saved reply
 */
export async function saveReply(audioUrl, transcript, userAddress, duration, parentId, media = []) {
  try {
    console.log('🔄 saveReply: Starting reply save process');
    console.log('🔄 saveReply: Audio URL:', audioUrl);
    console.log('🔄 saveReply: Transcript:', transcript);
    console.log('🔄 saveReply: User Address:', userAddress);
    console.log('🔄 saveReply: Parent ID:', parentId);
    console.log('🔄 saveReply: Duration:', duration);

    // Get the profile UUID from the profiles table using wallet address
    const addressStr = typeof userAddress === 'string' ? userAddress : String(userAddress);
    console.log('🔄 saveReply: Looking up profile for wallet address:', addressStr);

    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('id')
      .eq('wallet_address', addressStr)
      .single();

    if (profileError || !profileData) {
      console.error('🔄 saveReply: Profile not found for address:', addressStr, profileError);
      throw new Error(`Profile not found for wallet address: ${addressStr}`);
    }

    const profileId = profileData.id;
    console.log('🔄 saveReply: Found profile UUID:', profileId);

    // Create reply object for database with proper UUID types
    const reply = {
      audio_url: audioUrl,
      transcript: transcript || '',
      profile_id: profileId, // UUID from profiles table
      audio_duration: duration,
      parent_id: parentId, // Should be UUID of parent message
      created_at: new Date().toISOString(),
      is_pinned: false
    };

    // Store in Supabase and get the returned reply data
    console.log('🔄 saveReply: Inserting reply into database:', reply);
    const { data: savedReply, error } = await supabase
      .from('voice_messages')
      .insert(reply)
      .select()
      .single();

    if (error) {
      console.error('❌ saveReply: Error saving reply to Supabase:', error);
      console.error('❌ saveReply: Error details:', error.details);
      console.error('❌ saveReply: Error hint:', error.hint);
      throw new Error(`Failed to save reply: ${error.message}`);
    }

    console.log('✅ saveReply: Reply inserted successfully into database:', savedReply);

    // If there's media, store it in the database too
    if (media && media.length > 0) {
      console.log('Storing reply media attachments:', media);
      const mediaItems = media.map(item => ({
        voice_message_id: savedReply.id, // Use the actual UUID returned from database
        url: item.url,
        type: item.type,
        created_at: new Date().toISOString()
      }));

      const { error: mediaError } = await supabase
        .from('voice_message_media')
        .insert(mediaItems);

      if (mediaError) {
        console.warn('Error storing reply media attachments:', mediaError);
      } else {
        console.log(`Successfully stored ${mediaItems.length} reply media attachments`);
      }
    }

    console.log('✅ saveReply: Reply save process completed successfully');

    return {
      id: savedReply.id,
      userAddress,
      audioUrl,
      transcript,
      timestamp: new Date(savedReply.created_at),
      duration,
      isPinned: false,
      media,
      parentId,
      isReply: true,
      replies: []
    };
  } catch (error) {
    console.error('❌ saveReply: Error saving reply:', error);
    throw error;
  }
}

/**
 * Save a voice summon to the database
 * @param {string} audioUrl - The audio URL
 * @param {string} transcript - The transcript/question
 * @param {string} userAddress - The summoner's wallet address
 * @param {string} recipientAddress - The recipient's wallet address
 * @param {number} duration - The duration of the audio
 * @param {Array} media - Optional media attachments
 * @param {string} context - The context (e.g., 'feed', 'profile')
 * @param {string} messageId - Optional related message ID
 * @returns {Promise<Object>} - The saved summon
 */
export async function saveSummon(audioUrl, transcript, userAddress, recipientAddress, duration, media = [], context = 'feed', messageId = null) {
  try {
    console.log('Saving summon:', { audioUrl, transcript, userAddress, recipientAddress, duration, context, messageId });

    // Generate a unique ID for the summon
    const summonId = uuidv4();

    // Get normalized profile IDs
    const summonerAddressStr = typeof userAddress === 'string' ? userAddress : String(userAddress);
    const recipientAddressStr = typeof recipientAddress === 'string' ? recipientAddress : String(recipientAddress);

    const summonerProfileId = await getNormalizedProfileId(summonerAddressStr);
    const recipientProfileId = await getNormalizedProfileId(recipientAddressStr);

    // Create summon object for database
    const summon = {
      id: summonId,
      audio_url: audioUrl,
      transcript: transcript || '',
      profile_id: summonerProfileId,
      audio_duration: duration,
      created_at: new Date().toISOString(),
      is_pinned: false,
      // Add summon-specific metadata
      summon_recipient_id: recipientProfileId,
      summon_context: context,
      summon_message_id: messageId
    };

    // Store in Supabase voice_messages table with summon metadata
    console.log('Storing summon in Supabase:', summon);
    const { error } = await supabase
      .from('voice_messages')
      .insert(summon);

    if (error) {
      console.error('Error saving summon to Supabase:', error);
      throw new Error(`Failed to save summon: ${error.message}`);
    }

    // If there's media, store it in the database too
    if (media && media.length > 0) {
      console.log('Storing summon media attachments:', media);
      const mediaItems = media.map(item => ({
        id: item.id || uuidv4(),
        voice_message_id: summonId,
        url: item.url,
        type: item.type,
        created_at: new Date().toISOString()
      }));

      const { error: mediaError } = await supabase
        .from('voice_message_media')
        .insert(mediaItems);

      if (mediaError) {
        console.warn('Error storing summon media attachments:', mediaError);
      } else {
        console.log(`Successfully stored ${mediaItems.length} summon media attachments`);
      }
    }

    return {
      id: summonId,
      userAddress: summonerAddressStr,
      recipientAddress: recipientAddressStr,
      audioUrl,
      transcript,
      timestamp: new Date(),
      duration,
      isPinned: false,
      media,
      context,
      messageId,
      isSummon: true,
      replies: []
    };
  } catch (error) {
    console.error('Error saving summon:', error);
    throw error;
  }
}

/**
 * Delete a voice message
 * @param {string} messageId - The ID of the message to delete
 * @param {string} userAddress - The user wallet address (for authorization)
 * @returns {Promise<boolean>} - Whether the deletion was successful
 */
export async function deleteVoiceMessage(messageId, userAddress) {
  try {
    console.log(`Deleting voice message ${messageId} by user ${userAddress}`);

    // Ensure we have valid inputs
    if (!messageId || !userAddress) {
      console.error('Invalid inputs for deleteVoiceMessage:', { messageId, userAddress });
      return false;
    }

    // Ensure userAddress is in the correct format for the database
    let profileId = userAddress.toLowerCase();

    // Check if the address is an Ethereum address
    if (profileId.startsWith('0x')) {
      // Try to find the user's UUID from profiles table
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .ilike('wallet_address', profileId)
        .maybeSingle();

      if (profileError) {
        console.error('Error finding profile by wallet address:', profileError);
      }

      if (profileData && profileData.id) {
        // Use the UUID from the profile
        profileId = profileData.id;
      } else {
        // If we can't find a matching profile, use a fallback approach
        console.warn('No matching profile found for wallet address, using fallback UUID');
        profileId = '2cd24c86-d3c5-4406-a92c-1f0892495e0a'; // Owner's UUID as fallback
      }
    }

    // First check if the message exists and if it's already deleted
    const { data: messageData, error: messageError } = await supabase
      .from('voice_messages')
      .select('profile_id, deleted_at')
      .eq('id', messageId)
      .maybeSingle();

    if (messageError) {
      console.error('Error checking message:', messageError);
      return false;
    }

    // If message not found, consider it already deleted
    if (!messageData) {
      console.log('Message not found, considering it already deleted');
      return true;
    }

    // If message is already deleted, return success
    if (messageData.deleted_at) {
      console.log('Message already marked as deleted');
      return true;
    }

    // Check if user is the owner or the admin
    const isOwner = messageData.profile_id === profileId;
    const isAdmin = profileId === '2cd24c86-d3c5-4406-a92c-1f0892495e0a'; // Owner's UUID

    if (!isOwner && !isAdmin) {
      console.error('User is not authorized to delete this message');
      return false;
    }

    // Soft delete the message by setting deleted_at
    const { error: deleteError } = await supabase
      .from('voice_messages')
      .update({ deleted_at: new Date().toISOString() })
      .eq('id', messageId);

    if (deleteError) {
      console.error('Error deleting voice message:', deleteError);
      return false;
    }

    console.log('Message deleted successfully');

    return true;
  } catch (error) {
    console.error('Error deleting voice message:', error);
    return false;
  }
}

/**
 * Toggle pin status of a voice message
 * @param {string} messageId - The ID of the message to pin/unpin
 * @param {string} userAddress - The user wallet address (for authorization)
 * @param {boolean} isPinned - Whether to pin or unpin the message
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function togglePinMessage(messageId, userAddress, isPinned) {
  try {
    console.log(`${isPinned ? 'Unpinning' : 'Pinning'} voice message ${messageId} by user ${userAddress}`);

    // Ensure userAddress is in the correct format for the database
    let profileId = userAddress.toLowerCase();

    // Check if the address is an Ethereum address
    if (profileId.startsWith('0x')) {
      // Try to find the user's UUID from profiles table
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .ilike('wallet_address', profileId)
        .maybeSingle();

      if (profileError) {
        console.error('Error finding profile by wallet address:', profileError);
      }

      if (profileData && profileData.id) {
        // Use the UUID from the profile
        profileId = profileData.id;
      } else {
        // If we can't find a matching profile, use a fallback approach
        console.warn('No matching profile found for wallet address, using fallback UUID');
        profileId = '2cd24c86-d3c5-4406-a92c-1f0892495e0a'; // Owner's UUID as fallback
      }
    }

    // First check if the user is the owner of the message
    const { data: messageData, error: messageError } = await supabase
      .from('voice_messages')
      .select('profile_id')
      .eq('id', messageId)
      .maybeSingle();

    if (messageError) {
      console.error('Error checking message ownership:', messageError);
      return false;
    }

    // If message not found, return false
    if (!messageData) {
      console.log('Message not found');
      return false;
    }

    // Check if user is the owner
    const isOwner = messageData.profile_id === profileId;

    if (!isOwner) {
      console.error('User is not authorized to pin/unpin this message');
      return false;
    }

    // Update the pin status
    const { error: updateError } = await supabase
      .from('voice_messages')
      .update({ is_pinned: !isPinned })
      .eq('id', messageId);

    if (updateError) {
      console.error('Error updating pin status:', updateError);
      return false;
    }

    console.log('Pin status updated successfully');
    return true;
  } catch (error) {
    console.error('Error toggling pin status:', error);
    return false;
  }
}

/**
 * Add a reaction to a voice message
 * @param {string} messageId - The ID of the voice message
 * @param {string} userAddress - The user's address
 * @param {string} reactionType - The type of reaction
 * @returns {Promise<boolean>} - True if the reaction was added successfully
 */
export async function addReaction(messageId, userAddress, reactionType) {
  try {
    // Check if user already has ANY reaction on this message
    const { data: existingReaction } = await supabase
      .from('voice_reactions')
      .select('id, emoji')
      .eq('voice_message_id', messageId)
      .eq('profile_id', userAddress)
      .maybeSingle();

    // If the user already has the same reaction, remove it (toggle off)
    if (existingReaction && existingReaction.emoji === reactionType) {
      const { error: deleteError } = await supabase
        .from('voice_reactions')
        .delete()
        .eq('id', existingReaction.id);

      if (deleteError) {
        console.error('Error removing reaction:', deleteError);
        return false;
      }
      console.log(`Removed existing reaction ${reactionType}`);
      return true;
    }

    // If the user has a different reaction, update it
    if (existingReaction && existingReaction.emoji !== reactionType) {
      const { error: updateError } = await supabase
        .from('voice_reactions')
        .update({
          emoji: reactionType,
          created_at: new Date().toISOString()
        })
        .eq('id', existingReaction.id);

      if (updateError) {
        console.error('Error updating reaction:', updateError);
        return false;
      }
      console.log(`Updated reaction from ${existingReaction.emoji} to ${reactionType}`);
      return true;
    }

    // Add new reaction if user doesn't have any
    const reactionId = uuidv4();
    const { error } = await supabase
      .from('voice_reactions')
      .insert([
        {
          id: reactionId,
          voice_message_id: messageId,
          profile_id: userAddress,
          emoji: reactionType,
          created_at: new Date().toISOString()
        }
      ]);

    if (error) {
      console.error('Error adding reaction:', error);
      return false;
    }

    console.log(`Added reaction ${reactionType} successfully`);
    return true;
  } catch (error) {
    console.error('Error in addReaction:', error);
    return false;
  }
}

/**
 * Get reactions for a voice message
 * @param {string} messageId - The ID of the voice message
 * @returns {Promise<Array>} - Array of reactions
 */
export async function getReactions(messageId) {
  try {
    const { data, error } = await supabase
      .from('voice_reactions')
      .select('*')
      .eq('voice_message_id', messageId);

    if (error) {
      console.error('Error fetching reactions:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getReactions:', error);
    return [];
  }
}

export default {
  filterDeletedPosts,
  saveVoiceMessage,
  deleteVoiceMessage,
  togglePinMessage,
  addReaction,
  getReactions
};
