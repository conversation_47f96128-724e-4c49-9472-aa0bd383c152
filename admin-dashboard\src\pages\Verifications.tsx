import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CheckCheck, X, ExternalLink, Shield, BarChart } from 'lucide-react';
import { getVerificationApplications, updateVerificationStatus, supabase } from '@/services/supabase';
import { formatDistanceToNow } from 'date-fns';
import VerificationAnalytics from '@/components/VerificationAnalytics';

interface VerificationApplication {
  id: string;
  user_address: string;
  type: string;
  reason: string;
  social_proof: string;
  status: 'pending' | 'approved' | 'rejected';
  submitted_at: string;
  reviewed_at?: string;
  profiles: {
    username: string;
    display_name: string;
    avatar_url?: string;
  };
}

const Verifications: React.FC = () => {
  const [applications, setApplications] = useState<VerificationApplication[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('pending');
  const [processingId, setProcessingId] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d');

  useEffect(() => {
    fetchApplications(activeTab);
  }, [activeTab]);

  const fetchApplications = async (status: string) => {
    try {
      setIsLoading(true);
      const { data } = await getVerificationApplications(status === 'all' ? 'all' : status);
      setApplications(data || []);
    } catch (error) {
      console.error('Error fetching verification applications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleApprove = async (id: string) => {
    try {
      setProcessingId(id);

      try {
        // Try to update with admin_notes
        await updateVerificationStatus(id, 'approved', 'Approved by admin');
      } catch (updateError: any) {
        // If the error is about admin_notes column, try without it
        if (updateError?.message?.includes('admin_notes')) {
          console.warn('Falling back to update without admin_notes');

          // Fallback to direct update without admin_notes
          const { error } = await supabase
            .from('verification_applications')
            .update({
              status: 'approved',
              reviewed_at: new Date().toISOString()
            })
            .eq('id', id);

          if (error) throw error;

          // Get the application to get user details
          const { data: application, error: fetchError } = await supabase
            .from('verification_applications')
            .select('*')
            .eq('id', id)
            .single();

          if (fetchError) {
            console.error('Error fetching application:', fetchError);
            throw fetchError;
          }

          // Try to update the user's profile with verification status
          try {
            // Check if the user_address is a UUID
            const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
            const isUuid = uuidPattern.test(application.user_address);

            let updateQuery = supabase
              .from('profiles')
              .update({
                is_verified: true,
                verification_type: application.type,
                verified_at: new Date().toISOString()
              });

            if (isUuid) {
              updateQuery = updateQuery.eq('id', application.user_address);
            } else {
              updateQuery = updateQuery.ilike('wallet_address', application.user_address);
            }

            const { error: profileError } = await updateQuery;

            if (profileError) throw profileError;
          } catch (profileError: any) {
            // If the error is about is_verified column, try with just verification_type
            if (profileError?.message?.includes('is_verified') ||
              profileError?.message?.includes('verified_at')) {
              console.warn('Falling back to update with just verification_type');

              // Fallback to just updating verification_type
              // Check if the user_address is a UUID
              const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
              const isUuid = uuidPattern.test(application.user_address);

              let fallbackQuery = supabase
                .from('profiles')
                .update({
                  verification_type: application.type
                });

              if (isUuid) {
                fallbackQuery = fallbackQuery.eq('id', application.user_address);
              } else {
                fallbackQuery = fallbackQuery.ilike('wallet_address', application.user_address);
              }

              await fallbackQuery;
            } else {
              console.error('Error updating profile:', profileError);
            }
          }
        } else {
          // If it's a different error, rethrow it
          throw updateError;
        }
      }

      // Update the local state
      setApplications(applications.map(app =>
        app.id === id ? { ...app, status: 'approved', reviewed_at: new Date().toISOString() } : app
      ));
    } catch (error) {
      console.error('Error approving verification:', error);
    } finally {
      setProcessingId(null);
    }
  };

  const handleReject = async (id: string) => {
    try {
      setProcessingId(id);

      try {
        // Try to update with admin_notes
        await updateVerificationStatus(id, 'rejected', 'Rejected by admin');
      } catch (updateError: any) {
        // If the error is about admin_notes column, try without it
        if (updateError?.message?.includes('admin_notes')) {
          console.warn('Falling back to update without admin_notes');

          // Fallback to direct update without admin_notes
          const { error } = await supabase
            .from('verification_applications')
            .update({
              status: 'rejected',
              reviewed_at: new Date().toISOString()
            })
            .eq('id', id);

          if (error) throw error;
        } else {
          // If it's a different error, rethrow it
          throw updateError;
        }
      }

      // Update the local state
      setApplications(applications.map(app =>
        app.id === id ? { ...app, status: 'rejected', reviewed_at: new Date().toISOString() } : app
      ));
    } catch (error) {
      console.error('Error rejecting verification:', error);
    } finally {
      setProcessingId(null);
    }
  };

  const getVerificationTypeLabel = (type: string) => {
    const types: Record<string, { label: string, color: string }> = {
      owner: { label: 'Owner', color: 'bg-purple-500' },
      creator: { label: 'Creator', color: 'bg-blue-500' },
      developer: { label: 'Developer', color: 'bg-green-500' },
      community: { label: 'Community', color: 'bg-yellow-500' },
      partner: { label: 'Partner', color: 'bg-orange-500' },
      investor: { label: 'Investor', color: 'bg-pink-500' },
      early: { label: 'Early Adopter', color: 'bg-teal-500' }
    };

    return types[type] || { label: type, color: 'bg-gray-500' };
  };

  return (
    <div className="space-y-6">
      {/* Analytics Card */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <BarChart className="h-5 w-5" />
              Verification Analytics
            </CardTitle>
            <CardDescription>
              Insights into verification applications and trends
            </CardDescription>
          </div>
          <Select value={timeRange} onValueChange={(value) => setTimeRange(value as any)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="all">All time</SelectItem>
            </SelectContent>
          </Select>
        </CardHeader>
        <CardContent>
          <VerificationAnalytics timeRange={timeRange} />
        </CardContent>
      </Card>

      {/* Verification Management Card */}
      <Card>
        <CardHeader>
          <CardTitle>Verification Management</CardTitle>
          <CardDescription>
            Review and manage verification requests from users
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="pending" onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-3 mb-6">
              <TabsTrigger value="pending">Pending</TabsTrigger>
              <TabsTrigger value="approved">Approved</TabsTrigger>
              <TabsTrigger value="rejected">Rejected</TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="space-y-4">
              {isLoading ? (
                <div className="text-center py-8">
                  <p>Loading verification applications...</p>
                </div>
              ) : applications.length === 0 ? (
                <div className="text-center py-8">
                  <Shield className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No {activeTab} verification requests</h3>
                  <p className="text-muted-foreground">
                    {activeTab === 'pending'
                      ? 'All verification requests have been processed.'
                      : `No ${activeTab} verification requests found.`}
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {applications.map((application) => (
                    <Card key={application.id} className="overflow-hidden">
                      <div className="p-6">
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4">
                          <div className="flex items-center mb-2 sm:mb-0">
                            <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden mr-3">
                              {application.profiles.avatar_url ? (
                                <img
                                  src={application.profiles.avatar_url}
                                  alt={application.profiles.display_name}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <span className="text-gray-500 font-medium">
                                  {application.profiles.display_name.charAt(0)}
                                </span>
                              )}
                            </div>
                            <div>
                              <h3 className="font-medium">{application.profiles.display_name}</h3>
                              <p className="text-sm text-muted-foreground">@{application.profiles.username}</p>
                            </div>
                          </div>

                          <div className="flex flex-wrap gap-2">
                            <Badge
                              className={`${getVerificationTypeLabel(application.type).color} text-white`}
                            >
                              {getVerificationTypeLabel(application.type).label}
                            </Badge>

                            <Badge variant={
                              application.status === 'approved' ? 'default' :
                                application.status === 'rejected' ? 'destructive' : 'outline'
                            }>
                              {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
                            </Badge>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <div>
                            <h4 className="text-sm font-medium">Reason for Verification</h4>
                            <p className="text-sm mt-1">{application.reason}</p>
                          </div>

                          {application.social_proof && (
                            <div>
                              <h4 className="text-sm font-medium">Social Proof</h4>
                              <p className="text-sm mt-1 break-all">
                                {application.social_proof.startsWith('http') ? (
                                  <a
                                    href={application.social_proof}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-blue-500 hover:underline flex items-center"
                                  >
                                    {application.social_proof}
                                    <ExternalLink className="h-3 w-3 ml-1" />
                                  </a>
                                ) : (
                                  application.social_proof
                                )}
                              </p>
                            </div>
                          )}

                          <div>
                            <h4 className="text-sm font-medium">Wallet Address</h4>
                            <p className="text-sm mt-1 font-mono">{application.user_address}</p>
                          </div>

                          <div>
                            <h4 className="text-sm font-medium">Submitted</h4>
                            <p className="text-sm mt-1">
                              {formatDistanceToNow(new Date(application.submitted_at), { addSuffix: true })}
                            </p>
                          </div>

                          {application.reviewed_at && (
                            <div>
                              <h4 className="text-sm font-medium">Reviewed</h4>
                              <p className="text-sm mt-1">
                                {formatDistanceToNow(new Date(application.reviewed_at), { addSuffix: true })}
                              </p>
                            </div>
                          )}
                        </div>

                        {application.status === 'pending' && (
                          <div className="flex gap-2 justify-end mt-6">
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-red-500 text-red-500 hover:bg-red-50"
                              onClick={() => handleReject(application.id)}
                              disabled={!!processingId}
                            >
                              <X className="mr-1 h-4 w-4" />
                              Reject
                            </Button>
                            <Button
                              size="sm"
                              className="bg-purple-600 hover:bg-purple-700 text-white"
                              onClick={() => handleApprove(application.id)}
                              disabled={!!processingId}
                            >
                              <CheckCheck className="mr-1 h-4 w-4" />
                              Approve
                            </Button>
                          </div>
                        )}
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default Verifications;
