import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Search,
  User,
  Shield,
  AlertTriangle,
  MoreHorizontal,
  Check,
  X,
  Ban,
  Unlock,
  Eye,
  Edit,
  Trash2,
  MessageSquare,
  Loader2
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { toast } from '@/components/ui/toast';
import { supabase } from '@/services/supabase';

interface UserProfile {
  id: string;
  username: string;
  display_name: string;
  email?: string;
  bio?: string;
  avatar_url?: string;
  wallet_address?: string;
  is_verified: boolean;
  verification_type?: string;
  is_suspended: boolean;
  suspension_reason?: string;
  created_at: string;
  last_login?: string;
  post_count: number;
  follower_count: number;
  following_count: number;
}

const Users: React.FC = () => {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserProfile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [processingUserId, setProcessingUserId] = useState<string | null>(null);
  const [userDetailsOpen, setUserDetailsOpen] = useState<Record<string, boolean>>({});
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [isBulkProcessing, setIsBulkProcessing] = useState(false);

  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    filterUsers();
  }, [searchQuery, activeTab, users]);

  const fetchUsers = async () => {
    try {
      setIsLoading(true);

      // Fetch users from Supabase
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (profilesError) {
        throw profilesError;
      }

      // Fetch verification data
      const { data: verificationData, error: verificationError } = await supabase
        .from('verification')
        .select('user_id, verification_type');

      if (verificationError) {
        console.error('Error fetching verification data:', verificationError);
      }

      // Create a map of user_id to verification_type
      const verificationMap = new Map();
      verificationData?.forEach(v => {
        verificationMap.set(v.user_id, v.verification_type);
      });

      // Fetch post counts - using a workaround since group function might not be available
      let postCountMap = new Map();
      try {
        // Try using the group function first
        const { data: postCountData, error: postCountError } = await supabase
          .from('voice_messages')
          .select('profile_id, count')
          .limit(1000);

        if (postCountError) {
          console.error('Error fetching post counts:', postCountError);
        } else if (postCountData) {
          // Manually group the data
          const counts: Record<string, number> = {};
          postCountData?.forEach(post => {
            if (post.profile_id) {
              if (!counts[post.profile_id]) {
                counts[post.profile_id] = 0;
              }
              counts[post.profile_id]++;
            }
          });

          // Convert to map
          postCountMap = new Map(Object.entries(counts));
        }
      } catch (error) {
        console.error('Error processing post counts:', error);
      }

      // Transform the data to match our UserProfile interface
      const transformedUsers: UserProfile[] = profilesData.map(profile => {
        // Generate avatar URL if not present
        const avatarUrl = profile.avatar_url || `https://api.dicebear.com/7.x/avataaars/svg?seed=${profile.id}`;

        // Check if user is verified
        const isVerified = verificationMap.has(profile.id);
        const verificationType = isVerified ? verificationMap.get(profile.id) : undefined;

        // Get post count
        const postCount = postCountMap.get(profile.id) || 0;

        return {
          id: profile.id,
          username: profile.username || `user_${profile.id.substring(0, 8)}`,
          display_name: profile.display_name || profile.username || 'Anonymous User',
          email: profile.email,
          bio: profile.bio || '',
          avatar_url: avatarUrl,
          wallet_address: profile.wallet_address,
          is_verified: isVerified,
          verification_type: verificationType,
          is_suspended: profile.is_suspended || false,
          suspension_reason: profile.suspension_reason,
          created_at: profile.created_at,
          last_login: profile.last_login,
          post_count: postCount,
          follower_count: profile.follower_count || 0,
          following_count: profile.following_count || 0
        };
      });

      setUsers(transformedUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch users from database',
        duration: 3000
      });

      // Fallback to mock data if there's an error
      const mockUsers: UserProfile[] = [
        {
          id: '1',
          username: 'johndoe',
          display_name: 'John Doe',
          email: '<EMAIL>',
          bio: 'Web3 enthusiast and voice creator',
          avatar_url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=john',
          wallet_address: '0x1234...5678',
          is_verified: true,
          verification_type: 'creator',
          is_suspended: false,
          created_at: new Date(Date.now() - 7776000000).toISOString(), // 90 days ago
          last_login: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
          post_count: 42,
          follower_count: 156,
          following_count: 89
        },
        {
          id: '4',
          username: 'owner',
          display_name: 'Audra Owner',
          email: '<EMAIL>',
          bio: 'Creator and owner of Audra',
          avatar_url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=owner',
          wallet_address: '******************************************',
          is_verified: true,
          verification_type: 'owner',
          is_suspended: false,
          created_at: new Date(Date.now() - 63072000000).toISOString(), // 2 years ago
          last_login: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
          post_count: 215,
          follower_count: 1024,
          following_count: 128
        }
      ];

      setUsers(mockUsers);
    } finally {
      setIsLoading(false);
    }
  };

  const filterUsers = () => {
    let filtered = [...users];

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(user =>
        user.username.toLowerCase().includes(query) ||
        user.display_name.toLowerCase().includes(query) ||
        (user.email && user.email.toLowerCase().includes(query)) ||
        (user.wallet_address && user.wallet_address.toLowerCase().includes(query))
      );
    }

    // Filter by tab
    if (activeTab === 'verified') {
      filtered = filtered.filter(user => user.is_verified);
    } else if (activeTab === 'suspended') {
      filtered = filtered.filter(user => user.is_suspended);
    }

    setFilteredUsers(filtered);
  };

  const handleSuspendUser = async (userId: string) => {
    try {
      setProcessingUserId(userId);

      // Get user details for logging
      const userToSuspend = users.find(user => user.id === userId);

      // Update the user in Supabase
      const { error } = await supabase
        .from('profiles')
        .update({
          is_suspended: true,
          suspension_reason: 'Suspended by admin',
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        throw error;
      }

      // Log the admin action
      await supabase.rpc('log_admin_action', {
        p_action: 'suspend',
        p_entity_type: 'user',
        p_entity_id: userId,
        p_details: {
          username: userToSuspend?.username,
          display_name: userToSuspend?.display_name,
          email: userToSuspend?.email
        }
      });

      // Update local state
      setUsers(users.map(user =>
        user.id === userId ? {
          ...user,
          is_suspended: true,
          suspension_reason: 'Suspended by admin'
        } : user
      ));

      toast({
        title: 'User Suspended',
        description: 'The user has been suspended',
        duration: 3000
      });
    } catch (error) {
      console.error('Error suspending user:', error);
      toast({
        title: 'Error',
        description: 'Failed to suspend user in database',
        duration: 3000
      });
    } finally {
      setProcessingUserId(null);
    }
  };

  const handleUnsuspendUser = async (userId: string) => {
    try {
      setProcessingUserId(userId);

      // Update the user in Supabase
      const { error } = await supabase
        .from('profiles')
        .update({
          is_suspended: false,
          suspension_reason: null,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        throw error;
      }

      // Update local state
      setUsers(users.map(user =>
        user.id === userId ? {
          ...user,
          is_suspended: false,
          suspension_reason: undefined
        } : user
      ));

      toast({
        title: 'User Unsuspended',
        description: 'The user has been unsuspended',
        duration: 3000
      });
    } catch (error) {
      console.error('Error unsuspending user:', error);
      toast({
        title: 'Error',
        description: 'Failed to unsuspend user in database',
        duration: 3000
      });
    } finally {
      setProcessingUserId(null);
    }
  };

  const handleVerifyUser = async (userId: string, type: string) => {
    try {
      setProcessingUserId(userId);

      // Get user details for logging
      const userToVerify = users.find(user => user.id === userId);

      // First check if there's an existing verification record
      const { data: existingVerification, error: checkError } = await supabase
        .from('verification')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (checkError && checkError.code !== 'PGRST116') { // PGRST116 means no rows returned
        throw checkError;
      }

      let error;

      if (existingVerification) {
        // Update existing verification
        const { error: updateError } = await supabase
          .from('verification')
          .update({
            verification_type: type,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', userId);

        error = updateError;
      } else {
        // Insert new verification
        const { error: insertError } = await supabase
          .from('verification')
          .insert({
            user_id: userId,
            verification_type: type,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        error = insertError;
      }

      if (error) {
        throw error;
      }

      // Log the admin action
      await supabase.rpc('log_admin_action', {
        p_action: 'verify',
        p_entity_type: 'user',
        p_entity_id: userId,
        p_details: {
          username: userToVerify?.username,
          display_name: userToVerify?.display_name,
          email: userToVerify?.email,
          verification_type: type,
          is_new: !existingVerification
        }
      });

      // Update local state
      setUsers(users.map(user =>
        user.id === userId ? {
          ...user,
          is_verified: true,
          verification_type: type
        } : user
      ));

      toast({
        title: 'User Verified',
        description: `The user has been verified as ${type}`,
        duration: 3000
      });
    } catch (error) {
      console.error('Error verifying user:', error);
      toast({
        title: 'Error',
        description: 'Failed to verify user in database',
        duration: 3000
      });
    } finally {
      setProcessingUserId(null);
    }
  };

  const handleUnverifyUser = async (userId: string) => {
    try {
      setProcessingUserId(userId);

      // Get user details for logging
      const userToUnverify = users.find(user => user.id === userId);
      const verificationType = userToUnverify?.verification_type;

      // First update the profile to remove verification status
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          is_verified: false,
          verification_type: null,
          verified_at: null
        })
        .eq('id', userId);

      if (profileError) {
        console.error('Error updating profile verification status:', profileError);
      }

      // Delete verification record
      const { error } = await supabase
        .from('verification')
        .delete()
        .eq('user_id', userId);

      if (error) {
        throw error;
      }

      // Log the admin action
      await supabase.rpc('log_admin_action', {
        p_action: 'unverify',
        p_entity_type: 'user',
        p_entity_id: userId,
        p_details: {
          username: userToUnverify?.username,
          display_name: userToUnverify?.display_name,
          email: userToUnverify?.email,
          previous_verification_type: verificationType
        }
      });

      // Update local state
      setUsers(users.map(user =>
        user.id === userId ? {
          ...user,
          is_verified: false,
          verification_type: undefined
        } : user
      ));

      toast({
        title: 'User Unverified',
        description: 'The verification status has been removed',
        duration: 3000
      });
    } catch (error) {
      console.error('Error unverifying user:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove verification from database',
        duration: 3000
      });
    } finally {
      setProcessingUserId(null);
    }
  };

  const toggleUserDetails = (userId: string) => {
    setUserDetailsOpen(prev => ({
      ...prev,
      [userId]: !prev[userId]
    }));
  };

  const handleBulkVerify = async (type: string) => {
    if (selectedUsers.length === 0) {
      toast({
        title: 'No Users Selected',
        description: 'Please select users to verify',
        duration: 3000
      });
      return;
    }

    try {
      setIsBulkProcessing(true);

      // Process each user
      const promises = selectedUsers.map(async (userId) => {
        // First check if there's an existing verification record
        const { data: existingVerification, error: checkError } = await supabase
          .from('verification')
          .select('*')
          .eq('user_id', userId)
          .single();

        if (checkError && checkError.code !== 'PGRST116') {
          throw checkError;
        }

        if (existingVerification) {
          // Update existing verification
          return supabase
            .from('verification')
            .update({
              verification_type: type,
              updated_at: new Date().toISOString()
            })
            .eq('user_id', userId);
        } else {
          // Insert new verification
          return supabase
            .from('verification')
            .insert({
              user_id: userId,
              verification_type: type,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });
        }
      });

      await Promise.all(promises);

      // Update local state
      setUsers(users.map(user =>
        selectedUsers.includes(user.id) ? {
          ...user,
          is_verified: true,
          verification_type: type
        } : user
      ));

      toast({
        title: 'Users Verified',
        description: `${selectedUsers.length} users have been verified as ${type}`,
        duration: 3000
      });

      // Clear selection
      setSelectedUsers([]);
    } catch (error) {
      console.error('Error bulk verifying users:', error);
      toast({
        title: 'Error',
        description: 'Failed to verify some users',
        duration: 3000
      });
    } finally {
      setIsBulkProcessing(false);
    }
  };

  const handleBulkSuspend = async () => {
    if (selectedUsers.length === 0) {
      toast({
        title: 'No Users Selected',
        description: 'Please select users to suspend',
        duration: 3000
      });
      return;
    }

    try {
      setIsBulkProcessing(true);

      // Process each user
      const { error } = await supabase
        .from('profiles')
        .update({
          is_suspended: true,
          suspension_reason: 'Suspended by admin',
          updated_at: new Date().toISOString()
        })
        .in('id', selectedUsers);

      if (error) {
        throw error;
      }

      // Update local state
      setUsers(users.map(user =>
        selectedUsers.includes(user.id) ? {
          ...user,
          is_suspended: true,
          suspension_reason: 'Suspended by admin'
        } : user
      ));

      toast({
        title: 'Users Suspended',
        description: `${selectedUsers.length} users have been suspended`,
        duration: 3000
      });

      // Clear selection
      setSelectedUsers([]);
    } catch (error) {
      console.error('Error bulk suspending users:', error);
      toast({
        title: 'Error',
        description: 'Failed to suspend some users',
        duration: 3000
      });
    } finally {
      setIsBulkProcessing(false);
    }
  };

  const handleBulkUnsuspend = async () => {
    if (selectedUsers.length === 0) {
      toast({
        title: 'No Users Selected',
        description: 'Please select users to unsuspend',
        duration: 3000
      });
      return;
    }

    try {
      setIsBulkProcessing(true);

      // Process each user
      const { error } = await supabase
        .from('profiles')
        .update({
          is_suspended: false,
          suspension_reason: null,
          updated_at: new Date().toISOString()
        })
        .in('id', selectedUsers);

      if (error) {
        throw error;
      }

      // Update local state
      setUsers(users.map(user =>
        selectedUsers.includes(user.id) ? {
          ...user,
          is_suspended: false,
          suspension_reason: undefined
        } : user
      ));

      toast({
        title: 'Users Unsuspended',
        description: `${selectedUsers.length} users have been unsuspended`,
        duration: 3000
      });

      // Clear selection
      setSelectedUsers([]);
    } catch (error) {
      console.error('Error bulk unsuspending users:', error);
      toast({
        title: 'Error',
        description: 'Failed to unsuspend some users',
        duration: 3000
      });
    } finally {
      setIsBulkProcessing(false);
    }
  };

  const toggleSelectUser = (userId: string) => {
    setSelectedUsers(prev =>
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const toggleSelectAll = () => {
    if (selectedUsers.length === filteredUsers.length) {
      // Deselect all
      setSelectedUsers([]);
    } else {
      // Select all
      setSelectedUsers(filteredUsers.map(user => user.id));
    }
  };

  const getVerificationBadge = (user: UserProfile) => {
    if (!user.is_verified) return null;

    let color = 'bg-blue-500';
    if (user.verification_type === 'owner') {
      color = 'bg-purple-600';
    } else if (user.verification_type === 'creator') {
      color = 'bg-green-500';
    } else if (user.verification_type === 'developer') {
      color = 'bg-orange-500';
    }

    return (
      <Badge className={`${color} text-white`}>
        <Shield className="h-3 w-3 mr-1" />
        {user.verification_type || 'Verified'}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>User Management</CardTitle>
          <CardDescription>
            View and manage all users on the platform
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search users..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Tabs defaultValue="all" onValueChange={setActiveTab} className="w-full md:w-auto">
              <TabsList>
                <TabsTrigger value="all">All Users</TabsTrigger>
                <TabsTrigger value="verified">Verified</TabsTrigger>
                <TabsTrigger value="suspended">Suspended</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          {/* Bulk Actions */}
          {selectedUsers.length > 0 && (
            <div className="bg-muted p-4 rounded-md mb-6">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                <div>
                  <h3 className="font-medium">{selectedUsers.length} users selected</h3>
                  <p className="text-sm text-muted-foreground">
                    Select an action to perform on all selected users
                  </p>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Select
                    disabled={isBulkProcessing}
                    onValueChange={(value) => {
                      if (value === 'verify-creator') handleBulkVerify('creator');
                      else if (value === 'verify-developer') handleBulkVerify('developer');
                      else if (value === 'suspend') handleBulkSuspend();
                      else if (value === 'unsuspend') handleBulkUnsuspend();
                    }}
                    value=""
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Bulk Actions" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="verify-creator">Verify as Creator</SelectItem>
                      <SelectItem value="verify-developer">Verify as Developer</SelectItem>
                      <SelectItem value="suspend">Suspend Users</SelectItem>
                      <SelectItem value="unsuspend">Unsuspend Users</SelectItem>
                    </SelectContent>
                  </Select>

                  <Button
                    variant="outline"
                    size="default"
                    onClick={() => setSelectedUsers([])}
                    disabled={isBulkProcessing}
                  >
                    Clear Selection
                  </Button>
                </div>
              </div>
            </div>
          )}

          {isLoading ? (
            <div className="text-center py-8">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
              <p>Loading users...</p>
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className="text-center py-8">
              <User className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-medium">No users found</h3>
              <p className="text-muted-foreground">
                {searchQuery ? 'Try a different search term' : 'No users match the current filters'}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center mb-4">
                <Button
                  variant="outline"
                  size="sm"
                  className="mr-2"
                  onClick={toggleSelectAll}
                  disabled={isBulkProcessing}
                >
                  {selectedUsers.length === filteredUsers.length ? 'Deselect All' : 'Select All'}
                </Button>
              </div>

              {filteredUsers.map((user) => (
                <Card
                  key={user.id}
                  className={`${user.is_suspended ? 'border-red-200 bg-red-50 dark:bg-red-950/10 dark:border-red-900' : ''}
                    ${selectedUsers.includes(user.id) ? 'border-purple-300 bg-purple-50 dark:bg-purple-950/10 dark:border-purple-900' : ''}`}
                >
                  <CardContent className="p-4">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between">
                      <div className="flex items-center mb-4 sm:mb-0">
                        <div className="flex items-center mr-3">
                          <input
                            type="checkbox"
                            checked={selectedUsers.includes(user.id)}
                            onChange={() => toggleSelectUser(user.id)}
                            disabled={isBulkProcessing}
                            className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                          />
                        </div>
                        <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden mr-4">
                          {user.avatar_url ? (
                            <img
                              src={user.avatar_url}
                              alt={user.display_name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <span className="text-gray-500 font-medium text-lg">
                              {user.display_name.charAt(0)}
                            </span>
                          )}
                        </div>
                        <div>
                          <div className="flex items-center">
                            <h3 className="font-medium">{user.display_name}</h3>
                            {user.is_suspended && (
                              <Badge variant="destructive" className="ml-2">
                                Suspended
                              </Badge>
                            )}
                            {getVerificationBadge(user)}
                          </div>
                          <p className="text-sm text-muted-foreground">@{user.username}</p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => toggleUserDetails(user.id)}
                        >
                          {userDetailsOpen[user.id] ? 'Hide Details' : 'View Details'}
                        </Button>

                        <div className="relative">
                          <Button
                            variant="ghost"
                            size="sm"
                            disabled={!!processingUserId}
                          >
                            {processingUserId === user.id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <MoreHorizontal className="h-4 w-4" />
                            )}
                          </Button>

                          {/* Dropdown menu would go here in a real implementation */}
                        </div>
                      </div>
                    </div>

                    {userDetailsOpen[user.id] && (
                      <div className="mt-4 pt-4 border-t">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h4 className="text-sm font-medium mb-2">User Information</h4>
                            <div className="space-y-2">
                              {user.email && (
                                <div className="flex">
                                  <span className="text-sm font-medium w-24">Email:</span>
                                  <span className="text-sm">{user.email}</span>
                                </div>
                              )}
                              {user.wallet_address && (
                                <div className="flex">
                                  <span className="text-sm font-medium w-24">Wallet:</span>
                                  <span className="text-sm font-mono">{user.wallet_address}</span>
                                </div>
                              )}
                              <div className="flex">
                                <span className="text-sm font-medium w-24">Joined:</span>
                                <span className="text-sm">{formatDistanceToNow(new Date(user.created_at), { addSuffix: true })}</span>
                              </div>
                              {user.last_login && (
                                <div className="flex">
                                  <span className="text-sm font-medium w-24">Last Login:</span>
                                  <span className="text-sm">{formatDistanceToNow(new Date(user.last_login), { addSuffix: true })}</span>
                                </div>
                              )}
                            </div>
                          </div>

                          <div>
                            <h4 className="text-sm font-medium mb-2">Activity</h4>
                            <div className="space-y-2">
                              <div className="flex">
                                <span className="text-sm font-medium w-24">Posts:</span>
                                <span className="text-sm">{user.post_count}</span>
                              </div>
                              <div className="flex">
                                <span className="text-sm font-medium w-24">Followers:</span>
                                <span className="text-sm">{user.follower_count}</span>
                              </div>
                              <div className="flex">
                                <span className="text-sm font-medium w-24">Following:</span>
                                <span className="text-sm">{user.following_count}</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        {user.bio && (
                          <div className="mt-4">
                            <h4 className="text-sm font-medium mb-2">Bio</h4>
                            <p className="text-sm">{user.bio}</p>
                          </div>
                        )}

                        {user.is_suspended && user.suspension_reason && (
                          <div className="mt-4 p-3 bg-red-100 dark:bg-red-900/20 rounded-md">
                            <h4 className="text-sm font-medium mb-1">Suspension Reason</h4>
                            <p className="text-sm">{user.suspension_reason}</p>
                          </div>
                        )}

                        <div className="mt-4 pt-4 border-t flex flex-wrap gap-2">
                          {user.is_suspended ? (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleUnsuspendUser(user.id)}
                              disabled={!!processingUserId}
                            >
                              <Unlock className="mr-1 h-4 w-4" />
                              Unsuspend User
                            </Button>
                          ) : (
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-red-500 text-red-500 hover:bg-red-50"
                              onClick={() => handleSuspendUser(user.id)}
                              disabled={!!processingUserId}
                            >
                              <Ban className="mr-1 h-4 w-4" />
                              Suspend User
                            </Button>
                          )}

                          {user.is_verified ? (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleUnverifyUser(user.id)}
                              disabled={!!processingUserId}
                            >
                              <X className="mr-1 h-4 w-4" />
                              Remove Verification
                            </Button>
                          ) : (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleVerifyUser(user.id, 'creator')}
                              disabled={!!processingUserId}
                            >
                              <Shield className="mr-1 h-4 w-4" />
                              Verify User
                            </Button>
                          )}

                          <Button
                            variant="outline"
                            size="sm"
                            disabled={!!processingUserId}
                          >
                            <MessageSquare className="mr-1 h-4 w-4" />
                            View Posts
                          </Button>

                          <Button
                            variant="outline"
                            size="sm"
                            disabled={!!processingUserId}
                          >
                            <Edit className="mr-1 h-4 w-4" />
                            Edit Profile
                          </Button>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Users;
