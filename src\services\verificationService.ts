
import { supabase } from '@/integrations/supabase/client';
import { VerificationType } from '@/components/VerificationBadge';
import { VerificationApplication } from '@/types/verification';
import {
  sendVerificationSubmittedEmail,
  sendVerificationApprovedEmail,
  sendVerificationRejectedEmail,
  sendVerificationRevokedEmail
} from './emailService';

// Submit verification application
export const submitVerificationApplication = async (
  userAddress: string,
  type: VerificationType,
  reason: string,
  socialProof: string
): Promise<VerificationApplication> => {
  try {
    // First check if the user can apply
    const { canApply, reason: cannotApplyReason, cooldownEnds } = await canApplyForVerification(userAddress);

    if (!canApply) {
      console.error('User cannot apply for verification:', cannotApplyReason);
      throw new Error(cannotApplyReason || 'Cannot apply for verification at this time');
    }

    const newApplication = {
      id: crypto.randomUUID(),
      user_address: userAddress,
      type,
      reason,
      social_proof: socialProof,
      status: 'pending',
      submitted_at: new Date().toISOString()
    };

    const { error } = await supabase
      .from('verification_applications')
      .insert(newApplication);

    if (error) {
      console.error('Error submitting verification application:', error);
      throw new Error('Failed to submit application');
    }

    // Send email notification
    try {
      await sendVerificationSubmittedEmail(userAddress, type);
    } catch (emailError) {
      console.error('Error sending verification submitted email:', emailError);
      // Continue anyway, as the application was submitted successfully
    }

    return {
      id: newApplication.id,
      userAddress,
      type,
      reason,
      socialProof,
      status: 'pending',
      submittedAt: newApplication.submitted_at,
    };
  } catch (error) {
    console.error('Error submitting verification application:', error);
    if (error instanceof Error) {
      throw error; // Rethrow the original error with the reason
    } else {
      throw new Error('Failed to submit application');
    }
  }
};

// Get all verification applications
export const getVerificationApplications = async (): Promise<VerificationApplication[]> => {
  try {
    const { data, error } = await supabase
      .from('verification_applications')
      .select('*');

    if (error) {
      console.error('Error getting verification applications:', error);
      return [];
    }

    return data.map((app: any) => ({
      id: app.id,
      userAddress: app.user_address,
      type: app.type as VerificationType,
      reason: app.reason,
      socialProof: app.social_proof,
      status: app.status,
      submittedAt: app.submitted_at,
      reviewedAt: app.reviewed_at
    }));
  } catch (error) {
    console.error('Error getting verification applications:', error);
    return [];
  }
};

// Check if a user can apply for verification
export const canApplyForVerification = async (userAddress: string): Promise<{
  canApply: boolean;
  reason?: string;
  cooldownEnds?: string;
}> => {
  try {
    // Check for pending applications
    const { data: pendingData, error: pendingError } = await supabase
      .from('verification_applications')
      .select('*')
      .eq('user_address', userAddress)
      .eq('status', 'pending')
      .order('submitted_at', { ascending: false })
      .limit(1);

    if (pendingError) {
      console.error('Error checking pending applications:', pendingError);
      return { canApply: false, reason: 'Error checking application status' };
    }

    if (pendingData && pendingData.length > 0) {
      return {
        canApply: false,
        reason: 'You already have a pending verification application'
      };
    }

    // Check for recently rejected applications (within the last week)
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const { data: rejectedData, error: rejectedError } = await supabase
      .from('verification_applications')
      .select('*')
      .eq('user_address', userAddress)
      .eq('status', 'rejected')
      .gte('reviewed_at', oneWeekAgo.toISOString())
      .order('reviewed_at', { ascending: false })
      .limit(1);

    if (rejectedError) {
      console.error('Error checking rejected applications:', rejectedError);
      return { canApply: false, reason: 'Error checking application status' };
    }

    if (rejectedData && rejectedData.length > 0) {
      // Calculate when they can apply again
      const rejectedDate = new Date(rejectedData[0].reviewed_at);
      const cooldownEnds = new Date(rejectedDate);
      cooldownEnds.setDate(cooldownEnds.getDate() + 7);

      return {
        canApply: false,
        reason: 'Your previous application was rejected. Please wait before applying again.',
        cooldownEnds: cooldownEnds.toISOString()
      };
    }

    // User can apply
    return { canApply: true };
  } catch (error) {
    console.error('Error checking verification eligibility:', error);
    return { canApply: false, reason: 'Error checking application status' };
  }
};

// Check if a user has a pending verification application
export const checkPendingVerification = async (userAddress: string): Promise<{
  isPending: boolean;
  application?: VerificationApplication;
}> => {
  try {
    const { data, error } = await supabase
      .from('verification_applications')
      .select('*')
      .eq('user_address', userAddress)
      .eq('status', 'pending')
      .order('submitted_at', { ascending: false })
      .limit(1);

    if (error) {
      console.error('Error checking pending verification:', error);
      return { isPending: false };
    }

    if (data && data.length > 0) {
      const app = data[0];
      return {
        isPending: true,
        application: {
          id: app.id,
          userAddress: app.user_address,
          type: app.type as VerificationType,
          reason: app.reason,
          socialProof: app.social_proof,
          status: app.status,
          submittedAt: app.submitted_at,
          reviewedAt: app.reviewed_at
        }
      };
    }

    return { isPending: false };
  } catch (error) {
    console.error('Error checking pending verification:', error);
    return { isPending: false };
  }
};

// Check if a user is verified and get verification details
export const checkVerificationStatus = async (userAddress: string): Promise<{
  isVerified: boolean;
  type?: VerificationType;
  verifiedAt?: string;
}> => {
  try {
    console.log(`Checking verification status for ${userAddress}`);

    // Normalize the address for consistent comparison
    const normalizedAddress = userAddress.toLowerCase();

    // IMPORTANT: First check the profiles table as the source of truth
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .or(`id.eq.${normalizedAddress},wallet_address.ilike.${normalizedAddress}`)
      .maybeSingle();

    if (profileError) {
      console.error('Error checking profile verification status:', profileError);
    } else if (profileData) {
      // Use type assertion to safely access properties
      const typedProfileData = profileData as any;

      if (typedProfileData.is_verified && typedProfileData.verification_type) {
        console.log('Found verified profile in database:', {
          id: profileData.id,
          wallet_address: profileData.wallet_address,
          is_verified: typedProfileData.is_verified,
          verification_type: typedProfileData.verification_type,
          verified_at: typedProfileData.verified_at
        });

        return {
          isVerified: true,
          type: typedProfileData.verification_type as VerificationType,
          verifiedAt: typedProfileData.verified_at
        };
      }
    }

    // As a fallback, check if the user has an approved verification in the applications table
    const { data: appData, error: appError } = await supabase
      .from('verification_applications')
      .select('*')
      .eq('user_address', userAddress)
      .eq('status', 'approved')
      .order('reviewed_at', { ascending: false })
      .limit(1);

    if (appError) {
      console.error('Error checking verification applications:', appError);
    } else if (appData && appData.length > 0) {
      console.log('Found approved verification application:', appData[0]);

      // If we found an approved application but the profile wasn't updated,
      // let's update the profile now to ensure consistency
      if (profileData) {
        console.log('Updating profile with verification from application');

        try {
          const { error: updateError } = await supabase
            .from('profiles')
            .update({
              is_verified: true,
              verification_type: appData[0].type,
              verified_at: appData[0].reviewed_at || new Date().toISOString()
            })
            .eq('id', profileData.id);

          if (updateError) {
            console.error('Error updating profile with verification:', updateError);
          } else {
            console.log('Successfully updated profile with verification status');
          }
        } catch (updateError) {
          console.error('Error updating profile:', updateError);
        }
      }

      return {
        isVerified: true,
        type: appData[0].type as VerificationType,
        verifiedAt: appData[0].reviewed_at
      };
    }

    console.log('User is not verified');
    return { isVerified: false };
  } catch (error) {
    console.error('Error checking verification status:', error);
    return { isVerified: false };
  }
};

// Update verification application status
export const updateVerificationStatus = async (
  applicationId: string,
  status: 'approved' | 'rejected',
  adminNotes?: string
): Promise<boolean> => {
  try {
    console.log(`Updating verification application ${applicationId} to ${status}`);

    // First, get the application to get the user address and verification type
    const { data: applicationData, error: fetchError } = await supabase
      .from('verification_applications')
      .select('*')
      .eq('id', applicationId)
      .single();

    if (fetchError) {
      console.error('Error fetching verification application:', fetchError);
      return false;
    }

    console.log('Found application:', applicationData);

    // Update the application status
    const { error: updateError } = await supabase
      .from('verification_applications')
      .update({
        status,
        reviewed_at: new Date().toISOString(),
        admin_notes: adminNotes || null
      })
      .eq('id', applicationId);

    if (updateError) {
      console.error('Error updating verification status:', updateError);
      return false;
    }

    console.log(`Successfully updated application ${applicationId} to ${status}`);

    // If approved, update the user's profile with the verification type
    if (status === 'approved') {
      // First, log the verification data we're about to save
      console.log('Updating verification status for user:', {
        userAddress: applicationData.user_address,
        type: applicationData.type,
        status: 'approved',
        timestamp: new Date().toISOString()
      });

      // The database trigger will automatically update the profile when the application status changes
      // We don't need to manually update the profile anymore
      console.log('Database trigger will automatically update the profile verification status');

      // For debugging purposes, let's check if the profile was updated after a short delay
      setTimeout(async () => {
        try {
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .or(`id.eq.${applicationData.user_address},wallet_address.ilike.${applicationData.user_address.toLowerCase()}`)
            .maybeSingle();

          if (profileError) {
            console.error('Error checking profile after verification update:', profileError);
          } else if (profileData) {
            console.log('Profile after verification update:', profileData);
            console.log('Verification status:', {
              is_verified: profileData.is_verified,
              verification_type: profileData.verification_type,
              verified_at: profileData.verified_at
            });
          } else {
            console.warn('Profile not found after verification update');
          }
        } catch (checkError) {
          console.error('Error checking profile after verification update:', checkError);
        }
      }, 1000); // Check after 1 second to allow the trigger to complete

      // 5. Also update the profile in localStorage for immediate access
      try {
        // Get the current profile from localStorage
        const normalizedAddress = applicationData.user_address.toLowerCase();
        const profileKey = `profile_${normalizedAddress}`;
        const storedProfileData = localStorage.getItem(profileKey);

        if (storedProfileData) {
          const storedProfile = JSON.parse(storedProfileData);

          // Update the verification status
          if (storedProfile.profile) {
            storedProfile.profile.verification = {
              isVerified: true,
              type: applicationData.type,
              since: new Date().toISOString(),
              verifiedAt: new Date().toISOString()
            };

            // Save back to localStorage
            localStorage.setItem(profileKey, JSON.stringify({
              ...storedProfile,
              timestamp: Date.now()
            }));

            console.log('Updated verification status in localStorage');
          }
        }

        // Also update in the global profiles object
        const profilesData = localStorage.getItem('profiles');
        if (profilesData) {
          const profiles = JSON.parse(profilesData);

          if (profiles[normalizedAddress]) {
            profiles[normalizedAddress].verification = {
              isVerified: true,
              type: applicationData.type,
              since: new Date().toISOString(),
              verifiedAt: new Date().toISOString()
            };

            localStorage.setItem('profiles', JSON.stringify(profiles));
            console.log('Updated verification status in global profiles object');
          }
        }

        // 6. Try to sync the updated profile to Supabase using the profileSyncService
        try {
          // Import the profileSyncService
          const { syncProfileFromSupabase, syncProfileToSupabase } = await import('./profileSyncService');

          // First get the latest profile from Supabase
          const latestProfile = await syncProfileFromSupabase(normalizedAddress);

          if (latestProfile) {
            // Update the verification status
            latestProfile.verification = {
              isVerified: true,
              type: applicationData.type as any,
              since: new Date(),
              verifiedAt: new Date()
            };

            // Sync back to Supabase
            const success = await syncProfileToSupabase(latestProfile);

            if (success) {
              console.log('Successfully synced updated profile with verification to Supabase');
            } else {
              console.error('Failed to sync updated profile with verification to Supabase');
            }
          }
        } catch (syncError) {
          console.error('Error syncing updated profile with verification to Supabase:', syncError);
        }
      } catch (storageError) {
        console.warn('Error updating verification status in localStorage:', storageError);
      }

      // Send approval email notification
      try {
        await sendVerificationApprovedEmail(
          applicationData.user_address,
          applicationData.type as VerificationType
        );
      } catch (emailError) {
        console.error('Error sending verification approved email:', emailError);
        // Continue anyway, as the verification was approved
      }
    } else if (status === 'rejected') {
      // Send rejection email notification
      try {
        await sendVerificationRejectedEmail(
          applicationData.user_address,
          applicationData.type as VerificationType,
          applicationData.admin_notes
        );
      } catch (emailError) {
        console.error('Error sending verification rejected email:', emailError);
        // Continue anyway, as the verification was rejected
      }
    }

    return true;
  } catch (error) {
    console.error('Error updating verification status:', error);
    return false;
  }
};

// Get verification application history for a user
export const getVerificationApplicationHistory = async (userAddress: string): Promise<any[]> => {
  try {
    const { data, error } = await supabase
      .from('verification_applications')
      .select('*')
      .eq('user_address', userAddress)
      .order('submitted_at', { ascending: false });

    if (error) {
      console.error('Error getting verification history:', error);
      return [];
    }

    return data.map((app: any) => ({
      id: app.id,
      userAddress: app.user_address,
      type: app.type as VerificationType,
      reason: app.reason,
      socialProof: app.social_proof,
      status: app.status,
      submittedAt: app.submitted_at,
      reviewedAt: app.reviewed_at,
      adminNotes: app.admin_notes
    }));
  } catch (error) {
    console.error('Error getting verification history:', error);
    return [];
  }
};

// Submit a verification appeal
export const submitVerificationAppeal = async (
  userAddress: string,
  originalApplicationId: string,
  type: VerificationType,
  appealReason: string,
  additionalInfo: string
): Promise<VerificationApplication> => {
  try {
    // Check if the user can appeal (7 days after rejection)
    const { data: rejectedApp, error: fetchError } = await supabase
      .from('verification_applications')
      .select('*')
      .eq('id', originalApplicationId)
      .single();

    if (fetchError) {
      console.error('Error fetching original application:', fetchError);
      throw new Error('Could not find the original application');
    }

    if (rejectedApp.status !== 'rejected') {
      throw new Error('You can only appeal rejected applications');
    }

    const rejectedDate = new Date(rejectedApp.reviewed_at || rejectedApp.submitted_at);
    const appealAvailableDate = new Date(rejectedDate);
    appealAvailableDate.setDate(appealAvailableDate.getDate() + 7);

    if (new Date() < appealAvailableDate) {
      throw new Error(`You can appeal this rejection on ${appealAvailableDate.toLocaleDateString()}`);
    }

    // Create a new application with appeal information
    const newApplication = {
      id: crypto.randomUUID(),
      user_address: userAddress,
      type,
      reason: `APPEAL: ${appealReason}`,
      social_proof: additionalInfo || rejectedApp.social_proof,
      status: 'pending',
      submitted_at: new Date().toISOString(),
      original_application_id: originalApplicationId,
      is_appeal: true
    };

    const { error } = await supabase
      .from('verification_applications')
      .insert(newApplication);

    if (error) {
      console.error('Error submitting verification appeal:', error);
      throw new Error('Failed to submit appeal');
    }

    // Send email notification
    try {
      await sendVerificationSubmittedEmail(userAddress, type);
    } catch (emailError) {
      console.error('Error sending verification appeal email:', emailError);
      // Continue anyway, as the appeal was submitted successfully
    }

    return {
      id: newApplication.id,
      userAddress,
      type,
      reason: appealReason,
      socialProof: additionalInfo || rejectedApp.social_proof,
      status: 'pending',
      submittedAt: newApplication.submitted_at,
    };
  } catch (error) {
    console.error('Error submitting verification appeal:', error);
    if (error instanceof Error) {
      throw error; // Rethrow the original error with the reason
    } else {
      throw new Error('Failed to submit appeal');
    }
  }
};

// Revoke a user's verification
export const revokeVerification = async (
  userAddress: string,
  reason: string
): Promise<boolean> => {
  try {
    // Update the user's profile to remove verification
    const { error: profileError } = await supabase
      .from('profiles')
      .update({
        is_verified: false,
        verification_type: null,
        verified_at: null
      })
      .eq('id', userAddress);

    if (profileError) {
      console.error('Error revoking verification:', profileError);
      return false;
    }

    // Create a record of the revocation
    const { error: logError } = await supabase
      .from('verification_applications')
      .insert({
        id: crypto.randomUUID(),
        user_address: userAddress,
        type: 'revoked',
        reason: `Verification revoked: ${reason}`,
        social_proof: 'N/A',
        status: 'rejected',
        submitted_at: new Date().toISOString(),
        reviewed_at: new Date().toISOString()
      });

    if (logError) {
      console.error('Error logging verification revocation:', logError);
      // Continue anyway as the profile was updated
    }

    // Send revocation email notification
    try {
      await sendVerificationRevokedEmail(userAddress, reason);
    } catch (emailError) {
      console.error('Error sending verification revoked email:', emailError);
      // Continue anyway, as the verification was revoked
    }

    return true;
  } catch (error) {
    console.error('Error revoking verification:', error);
    return false;
  }
};
