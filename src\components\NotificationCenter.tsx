
import React, { useState } from 'react';
import {
  Bell,
  Heart,
  MessageCircle,
  DollarSign,
  AtSign,
  Check,
  Trash2,
  X
} from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/sonner';
import { formatDistanceToNow } from 'date-fns';
import { useNotifications } from '@/contexts/NotificationContext';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import SummonResponseModal from './SummonResponseModal';
import { Notification } from '@/contexts/NotificationContext';

const NotificationCenter: React.FC = () => {
  const { notifications, unreadCount, markAsRead, markAllAsRead } = useNotifications();
  const { getProfileByAddress } = useProfiles();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedSummon, setSelectedSummon] = useState<Notification | null>(null);

  // Handle notification click
  const handleNotificationClick = (notification: Notification) => {
    markAsRead(notification.id);

    // If it's a summon notification, open the response modal
    if (notification.type === 'summon') {
      setSelectedSummon(notification);
    }
  };

  // Get icon based on notification type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'like':
        return <Heart size={14} className="text-red-500" />;
      case 'reply':
        return <MessageCircle size={14} className="text-blue-500" />;
      case 'tip':
        return <DollarSign size={14} className="text-green-500" />;
      case 'summon':
        return <AtSign size={14} className="text-voicechain-purple" />;
      default:
        return <Bell size={14} />;
    }
  };

  // Get notification text
  const getNotificationText = (notification: Notification) => {
    const fromProfile = getProfileByAddress(notification.from_address);
    const fromName = fromProfile?.displayName || `User ${notification.from_address.substring(0, 6)}`;

    // Get context description
    const getContextDescription = () => {
      const context = notification.data?.context || 'feed';
      const channelId = notification.data?.channelId;

      switch (context) {
        case 'channel':
          return channelId ? ' in a channel' : '';
        case 'profile':
          return ' from your profile';
        case 'feed':
        default:
          return ' from the main feed';
      }
    };

    switch (notification.type) {
      case 'like':
        return `${fromName} liked your voice message${getContextDescription()}`;
      case 'reply':
        return `${fromName} replied to your voice message${getContextDescription()}`;
      case 'tip':
        const amount = notification.data?.tipAmount || '0';
        return `${fromName} tipped you ${amount} SOL${getContextDescription()}`;
      case 'summon':
        return `${fromName} summoned you for a voice response${getContextDescription()}`;
      case 'reaction':
        const emoji = notification.data?.emoji || '👍';
        return `${fromName} reacted ${emoji} to your voice message${getContextDescription()}`;
      case 'repost':
        return `${fromName} reposted your voice message${getContextDescription()}`;
      default:
        return 'New notification';
    }
  };

  return (
    <>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button variant="ghost" size="icon" className="relative">
            <Bell size={20} />
            {unreadCount > 0 && (
              <Badge
                className="absolute -top-1 -right-1 px-1.5 py-0.5 min-w-[18px] h-[18px] flex items-center justify-center bg-voicechain-purple text-white"
              >
                {unreadCount > 9 ? '9+' : unreadCount}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0 max-h-[70vh] overflow-hidden flex flex-col">
          <div className="p-4 flex items-center justify-between">
            <h3 className="font-medium">Notifications</h3>
            <div className="flex gap-1">
              {notifications.length > 0 && (
                <>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={markAllAsRead}
                    title="Mark all as read"
                  >
                    <Check size={14} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => {
                      // We'll implement this functionality later
                      toast.info('Clear notifications feature coming soon');
                    }}
                    title="Clear all notifications"
                  >
                    <Trash2 size={14} />
                  </Button>
                </>
              )}
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => setIsOpen(false)}
                title="Close"
              >
                <X size={14} />
              </Button>
            </div>
          </div>
          <Separator />

          <div className="overflow-y-auto flex-1">
            {notifications.length === 0 ? (
              <div className="p-8 text-center text-muted-foreground">
                <Bell size={24} className="mx-auto mb-2 opacity-50" />
                <p>No notifications yet</p>
              </div>
            ) : (
              <div className="divide-y divide-border">
                {notifications.map((notification) => {
                  const fromProfile = getProfileByAddress(notification.from_address);
                  return (
                    <div
                      key={notification.id}
                      className={`p-3 hover:bg-secondary/50 cursor-pointer ${!notification.read ? 'bg-secondary/20' : ''}`}
                      onClick={() => handleNotificationClick(notification)}
                    >
                      <div className="flex items-start gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={fromProfile?.profileImageUrl} />
                          <AvatarFallback className="bg-voicechain-accent text-white text-xs">
                            {fromProfile?.displayName?.charAt(0) || '?'}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="flex justify-between items-start gap-2">
                            <p className="text-sm font-medium truncate">
                              {getNotificationText(notification)}
                            </p>
                            <div className="flex items-center gap-1">
                              {getNotificationIcon(notification.type)}
                              {!notification.read && (
                                <div className="h-2 w-2 rounded-full bg-voicechain-purple" />
                              )}
                            </div>
                          </div>
                          <p className="text-xs text-muted-foreground mt-1">
                            {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>

      {/* Summon Response Modal */}
      {selectedSummon && (
        <SummonResponseModal
          isOpen={!!selectedSummon}
          onClose={() => setSelectedSummon(null)}
          notification={selectedSummon as any} // Type cast to fix incompatible types
        />
      )}
    </>
  );
};

export default NotificationCenter;
