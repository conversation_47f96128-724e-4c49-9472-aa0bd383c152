import React, { useState } from 'react';
import { useWallet } from '@/contexts/WalletContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { 
  Alert,
  AlertDescription,
  AlertTitle
} from '@/components/ui/alert';
import { toast } from '@/components/ui/sonner';
import { 
  Shield, 
  Lock, 
  Unlock, 
  KeyRound, 
  Eye, 
  EyeOff, 
  AlertTriangle, 
  CheckCircle2, 
  Loader2 
} from 'lucide-react';

const WalletSecurity: React.FC = () => {
  const { 
    wallet, 
    isWalletLocked, 
    hasBackup, 
    isLoading, 
    lockWallet, 
    unlockWallet, 
    backupWallet, 
    restoreWallet 
  } = useWallet();
  
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLockDialogOpen, setIsLockDialogOpen] = useState(false);
  const [isUnlockDialogOpen, setIsUnlockDialogOpen] = useState(false);
  const [isBackupDialogOpen, setIsBackupDialogOpen] = useState(false);
  const [isRestoreDialogOpen, setIsRestoreDialogOpen] = useState(false);
  const [recoveryPhrase, setRecoveryPhrase] = useState('');
  const [showRecoveryPhrase, setShowRecoveryPhrase] = useState(false);
  const [restorationPhrase, setRestorationPhrase] = useState('');
  
  // Handle locking the wallet
  const handleLockWallet = async () => {
    if (!password) {
      toast.error('Please enter a password');
      return;
    }
    
    if (password !== confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }
    
    const success = await lockWallet(password);
    
    if (success) {
      setPassword('');
      setConfirmPassword('');
      setIsLockDialogOpen(false);
    }
  };
  
  // Handle unlocking the wallet
  const handleUnlockWallet = async () => {
    if (!password) {
      toast.error('Please enter your password');
      return;
    }
    
    const success = await unlockWallet(password);
    
    if (success) {
      setPassword('');
      setIsUnlockDialogOpen(false);
    }
  };
  
  // Handle backing up the wallet
  const handleBackupWallet = async () => {
    const phrase = await backupWallet();
    
    if (phrase) {
      setRecoveryPhrase(phrase);
    }
  };
  
  // Handle restoring the wallet
  const handleRestoreWallet = async () => {
    if (!restorationPhrase) {
      toast.error('Please enter your recovery phrase');
      return;
    }
    
    const success = await restoreWallet(restorationPhrase);
    
    if (success) {
      setRestorationPhrase('');
      setIsRestoreDialogOpen(false);
    }
  };
  
  if (!wallet) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Wallet Security</CardTitle>
          <CardDescription>You don't have a wallet yet</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            A wallet will be created for you automatically during registration.
          </p>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              <span>Wallet Security</span>
            </CardTitle>
            <CardDescription>Secure and backup your wallet</CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Lock/Unlock Wallet */}
        <div className="p-4 bg-secondary/50 rounded-lg">
          <div className="flex items-start gap-3">
            {isWalletLocked ? (
              <Lock className="h-5 w-5 text-yellow-500 mt-0.5" />
            ) : (
              <Unlock className="h-5 w-5 text-green-500 mt-0.5" />
            )}
            <div className="flex-1">
              <h3 className="text-sm font-medium">
                {isWalletLocked ? 'Wallet is Locked' : 'Wallet is Unlocked'}
              </h3>
              <p className="text-xs text-muted-foreground mt-1">
                {isWalletLocked 
                  ? 'Your wallet is locked with a password. Unlock it to use it.' 
                  : 'Your wallet is unlocked. Lock it when not in use for security.'}
              </p>
              <Button
                variant="outline"
                size="sm"
                className="mt-3"
                onClick={() => isWalletLocked ? setIsUnlockDialogOpen(true) : setIsLockDialogOpen(true)}
              >
                {isWalletLocked ? (
                  <>
                    <Unlock className="h-4 w-4 mr-2" />
                    Unlock Wallet
                  </>
                ) : (
                  <>
                    <Lock className="h-4 w-4 mr-2" />
                    Lock Wallet
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
        
        {/* Backup Status */}
        <div className="p-4 bg-secondary/50 rounded-lg">
          <div className="flex items-start gap-3">
            {hasBackup ? (
              <CheckCircle2 className="h-5 w-5 text-green-500 mt-0.5" />
            ) : (
              <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
            )}
            <div className="flex-1">
              <h3 className="text-sm font-medium">
                {hasBackup ? 'Wallet is Backed Up' : 'Wallet Not Backed Up'}
              </h3>
              <p className="text-xs text-muted-foreground mt-1">
                {hasBackup 
                  ? 'Your wallet has been backed up with a recovery phrase.' 
                  : 'Back up your wallet with a recovery phrase to prevent loss of funds.'}
              </p>
              <Button
                variant="outline"
                size="sm"
                className="mt-3"
                onClick={() => setIsBackupDialogOpen(true)}
                disabled={isWalletLocked}
              >
                <KeyRound className="h-4 w-4 mr-2" />
                {hasBackup ? 'View Backup' : 'Backup Wallet'}
              </Button>
            </div>
          </div>
        </div>
        
        {/* Restore Wallet */}
        <div className="p-4 bg-secondary/50 rounded-lg">
          <div className="flex items-start gap-3">
            <KeyRound className="h-5 w-5 text-blue-500 mt-0.5" />
            <div className="flex-1">
              <h3 className="text-sm font-medium">Restore Wallet</h3>
              <p className="text-xs text-muted-foreground mt-1">
                Restore your wallet using a recovery phrase.
              </p>
              <Button
                variant="outline"
                size="sm"
                className="mt-3"
                onClick={() => setIsRestoreDialogOpen(true)}
              >
                <KeyRound className="h-4 w-4 mr-2" />
                Restore Wallet
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
      
      <CardFooter>
        <p className="text-xs text-muted-foreground">
          Keep your password and recovery phrase safe. They cannot be recovered if lost.
        </p>
      </CardFooter>
      
      {/* Lock Wallet Dialog */}
      <Dialog open={isLockDialogOpen} onOpenChange={setIsLockDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Lock Wallet</DialogTitle>
            <DialogDescription>
              Create a password to lock your wallet
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <Alert variant="destructive" className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Important</AlertTitle>
              <AlertDescription>
                This password cannot be recovered. If you forget it, you'll need your recovery phrase to restore access.
              </AlertDescription>
            </Alert>
            
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Input 
                  id="password" 
                  type={showPassword ? 'text' : 'password'} 
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-0 top-0 h-full"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <div className="relative">
                <Input 
                  id="confirmPassword" 
                  type={showPassword ? 'text' : 'password'} 
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-0 top-0 h-full"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsLockDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleLockWallet}
              disabled={isLoading || !password || password !== confirmPassword}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Locking...
                </>
              ) : (
                <>
                  <Lock className="mr-2 h-4 w-4" />
                  Lock Wallet
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Unlock Wallet Dialog */}
      <Dialog open={isUnlockDialogOpen} onOpenChange={setIsUnlockDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Unlock Wallet</DialogTitle>
            <DialogDescription>
              Enter your password to unlock your wallet
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="unlockPassword">Password</Label>
              <div className="relative">
                <Input 
                  id="unlockPassword" 
                  type={showPassword ? 'text' : 'password'} 
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-0 top-0 h-full"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsUnlockDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleUnlockWallet}
              disabled={isLoading || !password}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Unlocking...
                </>
              ) : (
                <>
                  <Unlock className="mr-2 h-4 w-4" />
                  Unlock Wallet
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Backup Wallet Dialog */}
      <Dialog open={isBackupDialogOpen} onOpenChange={setIsBackupDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Backup Wallet</DialogTitle>
            <DialogDescription>
              {recoveryPhrase ? 'Your recovery phrase' : 'Generate a recovery phrase for your wallet'}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            {!recoveryPhrase ? (
              <>
                <Alert variant="destructive" className="mb-4">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>Important</AlertTitle>
                  <AlertDescription>
                    Write down your recovery phrase and keep it in a safe place. It's the only way to recover your wallet if you forget your password.
                  </AlertDescription>
                </Alert>
                
                <Button 
                  onClick={handleBackupWallet}
                  disabled={isLoading}
                  className="w-full"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <KeyRound className="mr-2 h-4 w-4" />
                      Generate Recovery Phrase
                    </>
                  )}
                </Button>
              </>
            ) : (
              <>
                <Alert variant="destructive" className="mb-4">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>Keep this safe!</AlertTitle>
                  <AlertDescription>
                    Anyone with this phrase can access your wallet. Never share it with anyone.
                  </AlertDescription>
                </Alert>
                
                <div className="p-4 bg-secondary rounded-lg">
                  <div className="relative">
                    <p className={`text-sm font-mono break-all ${showRecoveryPhrase ? '' : 'blur-sm'}`}>
                      {recoveryPhrase}
                    </p>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Button
                        variant="outline"
                        size="sm"
                        className={showRecoveryPhrase ? 'opacity-0' : ''}
                        onClick={() => setShowRecoveryPhrase(true)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        Show Phrase
                      </Button>
                    </div>
                  </div>
                </div>
                
                <Button 
                  variant="outline"
                  onClick={() => {
                    navigator.clipboard.writeText(recoveryPhrase);
                    toast.success('Recovery phrase copied to clipboard');
                  }}
                  className="w-full"
                >
                  Copy to Clipboard
                </Button>
              </>
            )}
          </div>
          
          <DialogFooter>
            <Button onClick={() => {
              setIsBackupDialogOpen(false);
              setRecoveryPhrase('');
              setShowRecoveryPhrase(false);
            }}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Restore Wallet Dialog */}
      <Dialog open={isRestoreDialogOpen} onOpenChange={setIsRestoreDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Restore Wallet</DialogTitle>
            <DialogDescription>
              Enter your recovery phrase to restore your wallet
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <Alert variant="destructive" className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Warning</AlertTitle>
              <AlertDescription>
                This will replace your current wallet. Make sure you have backed up your current wallet if needed.
              </AlertDescription>
            </Alert>
            
            <div className="space-y-2">
              <Label htmlFor="recoveryPhrase">Recovery Phrase</Label>
              <Input 
                id="recoveryPhrase" 
                placeholder="Enter your 12-word recovery phrase" 
                value={restorationPhrase}
                onChange={(e) => setRestorationPhrase(e.target.value)}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRestoreDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleRestoreWallet}
              disabled={isLoading || !restorationPhrase}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Restoring...
                </>
              ) : (
                <>
                  <KeyRound className="mr-2 h-4 w-4" />
                  Restore Wallet
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default WalletSecurity;
