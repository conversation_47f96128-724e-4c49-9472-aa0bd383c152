import { supabase } from '@/integrations/supabase/client';

/**
 * Service for managing pin/unpin functionality for posts
 */
export class PinService {
  /**
   * Pin a post to user's profile
   */
  static async pinPost(postId: string, userAddress: string): Promise<boolean> {
    try {
      console.log(`Pinning post ${postId} for user ${userAddress}`);
      
      // Normalize address
      const normalizedAddress = userAddress.toLowerCase();
      
      // First, get the user's profile ID
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('wallet_address', normalizedAddress)
        .single();
      
      if (profileError || !profileData) {
        console.error('Error getting user profile:', profileError);
        return false;
      }
      
      // Check if the post belongs to this user
      const { data: postData, error: postError } = await supabase
        .from('voice_messages')
        .select('profile_id')
        .eq('id', postId)
        .single();
      
      if (postError || !postData) {
        console.error('Error getting post data:', postError);
        return false;
      }
      
      if (postData.profile_id !== profileData.id) {
        console.error('User can only pin their own posts');
        return false;
      }
      
      // Unpin all other posts by this user first (only one pinned post allowed)
      const { error: unpinError } = await supabase
        .from('voice_messages')
        .update({ is_pinned: false })
        .eq('profile_id', profileData.id)
        .eq('is_pinned', true);
      
      if (unpinError) {
        console.error('Error unpinning other posts:', unpinError);
        // Continue anyway, this is not critical
      }
      
      // Pin the selected post
      const { error: pinError } = await supabase
        .from('voice_messages')
        .update({ is_pinned: true })
        .eq('id', postId);
      
      if (pinError) {
        console.error('Error pinning post:', pinError);
        return false;
      }
      
      console.log('Successfully pinned post');
      return true;
    } catch (error) {
      console.error('Error in pinPost:', error);
      return false;
    }
  }
  
  /**
   * Unpin a post from user's profile
   */
  static async unpinPost(postId: string, userAddress: string): Promise<boolean> {
    try {
      console.log(`Unpinning post ${postId} for user ${userAddress}`);
      
      // Normalize address
      const normalizedAddress = userAddress.toLowerCase();
      
      // Get the user's profile ID
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('wallet_address', normalizedAddress)
        .single();
      
      if (profileError || !profileData) {
        console.error('Error getting user profile:', profileError);
        return false;
      }
      
      // Check if the post belongs to this user
      const { data: postData, error: postError } = await supabase
        .from('voice_messages')
        .select('profile_id')
        .eq('id', postId)
        .single();
      
      if (postError || !postData) {
        console.error('Error getting post data:', postError);
        return false;
      }
      
      if (postData.profile_id !== profileData.id) {
        console.error('User can only unpin their own posts');
        return false;
      }
      
      // Unpin the post
      const { error: unpinError } = await supabase
        .from('voice_messages')
        .update({ is_pinned: false })
        .eq('id', postId);
      
      if (unpinError) {
        console.error('Error unpinning post:', unpinError);
        return false;
      }
      
      console.log('Successfully unpinned post');
      return true;
    } catch (error) {
      console.error('Error in unpinPost:', error);
      return false;
    }
  }
  
  /**
   * Toggle pin status of a post
   */
  static async togglePin(postId: string, userAddress: string, currentlyPinned: boolean): Promise<boolean> {
    if (currentlyPinned) {
      return await this.unpinPost(postId, userAddress);
    } else {
      return await this.pinPost(postId, userAddress);
    }
  }
  
  /**
   * Get pinned post for a user
   */
  static async getPinnedPost(userAddress: string): Promise<string | null> {
    try {
      const normalizedAddress = userAddress.toLowerCase();
      
      // Get the user's profile ID
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('wallet_address', normalizedAddress)
        .single();
      
      if (profileError || !profileData) {
        console.error('Error getting user profile:', profileError);
        return null;
      }
      
      // Get the pinned post
      const { data: pinnedPost, error: pinnedError } = await supabase
        .from('voice_messages')
        .select('id')
        .eq('profile_id', profileData.id)
        .eq('is_pinned', true)
        .single();
      
      if (pinnedError || !pinnedPost) {
        return null; // No pinned post found
      }
      
      return pinnedPost.id;
    } catch (error) {
      console.error('Error in getPinnedPost:', error);
      return null;
    }
  }
  
  /**
   * Check if a post is pinned
   */
  static async isPostPinned(postId: string): Promise<boolean> {
    try {
      const { data: postData, error } = await supabase
        .from('voice_messages')
        .select('is_pinned')
        .eq('id', postId)
        .single();
      
      if (error || !postData) {
        return false;
      }
      
      return postData.is_pinned || false;
    } catch (error) {
      console.error('Error checking if post is pinned:', error);
      return false;
    }
  }
}
