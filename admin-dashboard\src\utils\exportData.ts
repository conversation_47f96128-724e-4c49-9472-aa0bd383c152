/**
 * Utility functions for exporting data from the admin dashboard
 */

/**
 * Convert an array of objects to CSV format
 * @param data Array of objects to convert
 * @param columns Optional array of column names to include
 * @returns CSV string
 */
export const convertToCSV = (data: any[], columns?: string[]): string => {
  if (!data || data.length === 0) {
    return '';
  }

  // If columns are not provided, use the keys from the first object
  const headers = columns || Object.keys(data[0]);

  // Create the header row
  const headerRow = headers.map(header => `"${header}"`).join(',');

  // Create the data rows
  const rows = data.map(item => {
    return headers.map(header => {
      const value = item[header];
      
      // Handle different data types
      if (value === null || value === undefined) {
        return '""';
      } else if (typeof value === 'object') {
        // Convert objects to JSON strings
        return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
      } else {
        // Convert other values to strings and escape quotes
        return `"${String(value).replace(/"/g, '""')}"`;
      }
    }).join(',');
  });

  // Combine header and rows
  return [headerRow, ...rows].join('\n');
};

/**
 * Convert an array of objects to Excel-compatible CSV format
 * @param data Array of objects to convert
 * @param columns Optional array of column names to include
 * @returns CSV string with Excel-compatible encoding
 */
export const convertToExcelCSV = (data: any[], columns?: string[]): string => {
  // Add BOM for Excel compatibility
  return '\ufeff' + convertToCSV(data, columns);
};

/**
 * Download data as a CSV file
 * @param data Array of objects to download
 * @param filename Filename for the downloaded file
 * @param columns Optional array of column names to include
 */
export const downloadCSV = (data: any[], filename: string, columns?: string[]): void => {
  const csv = convertToExcelCSV(data, columns);
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * Convert an array of objects to JSON format
 * @param data Array of objects to convert
 * @returns JSON string
 */
export const convertToJSON = (data: any[]): string => {
  return JSON.stringify(data, null, 2);
};

/**
 * Download data as a JSON file
 * @param data Array of objects to download
 * @param filename Filename for the downloaded file
 */
export const downloadJSON = (data: any[], filename: string): void => {
  const json = convertToJSON(data);
  const blob = new Blob([json], { type: 'application/json;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * Format a date for use in filenames
 * @returns Formatted date string (YYYY-MM-DD)
 */
export const getFormattedDate = (): string => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};

/**
 * Generate a filename with date for exports
 * @param prefix Prefix for the filename
 * @param extension File extension (default: csv)
 * @returns Formatted filename with date
 */
export const generateFilename = (prefix: string, extension: string = 'csv'): string => {
  return `${prefix}_${getFormattedDate()}.${extension}`;
};

/**
 * Export data with proper formatting for different data types
 * @param data Array of objects to export
 * @param type Export type ('csv' or 'json')
 * @param prefix Prefix for the filename
 * @param columns Optional array of column names to include
 */
export const exportData = (
  data: any[], 
  type: 'csv' | 'json', 
  prefix: string,
  columns?: string[]
): void => {
  if (type === 'csv') {
    downloadCSV(data, generateFilename(prefix, 'csv'), columns);
  } else if (type === 'json') {
    downloadJSON(data, generateFilename(prefix, 'json'));
  }
};
