import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Shield, Clock, CheckCircle, XCircle, AlertCircle, RefreshCw } from 'lucide-react';
import { formatDistanceToNow, format, addDays, isAfter } from 'date-fns';
import { VerificationType } from './VerificationBadge';
import { getVerificationApplicationHistory } from '@/services/verificationService';
import { Skeleton } from '@/components/ui/skeleton';
import VerificationAppealModal from './VerificationAppealModal';

interface VerificationHistoryProps {
  userAddress: string;
}

type VerificationStatus = 'pending' | 'approved' | 'rejected';

interface VerificationHistoryItem {
  id: string;
  type: VerificationType;
  status: VerificationStatus;
  reason: string;
  socialProof: string;
  submittedAt: string;
  reviewedAt?: string;
  adminNotes?: string;
}

const VerificationHistory: React.FC<VerificationHistoryProps> = ({ userAddress }) => {
  const [history, setHistory] = useState<VerificationHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAppealModalOpen, setIsAppealModalOpen] = useState(false);
  const [selectedRejection, setSelectedRejection] = useState<{
    id: string;
    type: VerificationType;
    rejectedAt: string;
    reason?: string;
  } | null>(null);

  useEffect(() => {
    const fetchHistory = async () => {
      try {
        setIsLoading(true);
        const historyData = await getVerificationApplicationHistory(userAddress);
        setHistory(historyData);
      } catch (error) {
        console.error('Error fetching verification history:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (userAddress) {
      fetchHistory();
    }
  }, [userAddress]);

  const getStatusIcon = (status: VerificationStatus) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'pending':
      default:
        return <Clock className="h-4 w-4 text-amber-500" />;
    }
  };

  const getStatusBadge = (status: VerificationStatus) => {
    switch (status) {
      case 'approved':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Approved</Badge>;
      case 'rejected':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Rejected</Badge>;
      case 'pending':
      default:
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">Pending</Badge>;
    }
  };

  const handleAppeal = (item: VerificationHistoryItem) => {
    setSelectedRejection({
      id: item.id,
      type: item.type,
      rejectedAt: item.reviewedAt || item.submittedAt,
      reason: item.adminNotes
    });
    setIsAppealModalOpen(true);
  };

  const canAppeal = (item: VerificationHistoryItem) => {
    if (item.status !== 'rejected' || !item.reviewedAt) return false;

    // Check if 7 days have passed since rejection
    const rejectedDate = new Date(item.reviewedAt);
    const appealAvailableDate = addDays(rejectedDate, 7);
    return isAfter(new Date(), appealAvailableDate);
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-muted-foreground" />
            <Skeleton className="h-6 w-40" />
          </CardTitle>
          <CardDescription>
            <Skeleton className="h-4 w-full" />
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {[1, 2].map((i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-16 w-full" />
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  if (history.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-muted-foreground" />
            Verification History
          </CardTitle>
          <CardDescription>
            You haven't applied for verification yet.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <AlertCircle className="h-10 w-10 text-muted-foreground mb-2" />
            <p className="text-muted-foreground">No verification applications found</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5 text-primary" />
          Verification History
        </CardTitle>
        <CardDescription>
          Your verification application history
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {history.map((item) => (
          <div key={item.id} className="border rounded-md p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                {getStatusIcon(item.status)}
                <span className="font-medium">{item.type.charAt(0).toUpperCase() + item.type.slice(1)} Verification</span>
              </div>
              {getStatusBadge(item.status)}
            </div>

            <div className="text-sm text-muted-foreground mb-3">
              Submitted {formatDistanceToNow(new Date(item.submittedAt), { addSuffix: true })}
              {item.reviewedAt && (
                <> • Reviewed {formatDistanceToNow(new Date(item.reviewedAt), { addSuffix: true })}</>
              )}
            </div>

            <Separator className="my-2" />

            <div className="space-y-2 mt-3">
              <div>
                <h4 className="text-xs font-medium text-muted-foreground">YOUR REASON</h4>
                <p className="text-sm mt-1">{item.reason}</p>
              </div>

              {item.adminNotes && item.status !== 'pending' && (
                <div className="mt-3">
                  <h4 className="text-xs font-medium text-muted-foreground">ADMIN NOTES</h4>
                  <p className="text-sm mt-1">{item.adminNotes}</p>
                </div>
              )}

              {item.status === 'rejected' && (
                <div className="mt-3 bg-red-50 p-2 rounded-md text-sm">
                  {canAppeal(item) ? (
                    <div className="flex flex-col space-y-2">
                      <p className="text-red-700">
                        Your verification was rejected on {format(new Date(item.reviewedAt || item.submittedAt), 'MMMM d, yyyy')}
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-red-300 text-red-700 hover:bg-red-50 w-full"
                        onClick={() => handleAppeal(item)}
                      >
                        <RefreshCw className="h-3.5 w-3.5 mr-1" />
                        Appeal Rejection
                      </Button>
                    </div>
                  ) : (
                    <p className="text-red-700">
                      You can appeal after {format(new Date(new Date(item.reviewedAt || item.submittedAt).getTime() + 7 * 24 * 60 * 60 * 1000), 'MMMM d, yyyy')}
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>
        ))}
      </CardContent>

      {/* Appeal Modal */}
      {selectedRejection && (
        <VerificationAppealModal
          isOpen={isAppealModalOpen}
          onClose={() => {
            setIsAppealModalOpen(false);
            setSelectedRejection(null);
          }}
          userAddress={userAddress}
          rejectedApplication={selectedRejection}
        />
      )}
    </Card>
  );
};

export default VerificationHistory;
