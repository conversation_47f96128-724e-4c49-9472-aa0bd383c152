import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/sonner';
import { MediaFile } from '@/components/MediaUploader';

export interface UploadedMediaFile {
  id: string;
  url: string;
  type: 'image' | 'video';
}

/**
 * Upload media files to Supabase storage
 * @param files - Array of MediaFile objects with File data
 * @param userId - User ID for organizing uploads
 * @returns Array of uploaded media with permanent URLs
 */
export async function uploadMediaFiles(files: MediaFile[], userId: string): Promise<UploadedMediaFile[]> {
  const uploadedFiles: UploadedMediaFile[] = [];

  try {
    // Create the media bucket if it doesn't exist
    const { data: buckets } = await supabase.storage.listBuckets();
    const mediaBucket = buckets?.find(bucket => bucket.name === 'media');
    
    if (!mediaBucket) {
      // Create the bucket
      await supabase.storage.createBucket('media', {
        public: true,
        allowedMimeTypes: ['image/*', 'video/*'],
        fileSizeLimit: 10 * 1024 * 1024 // 10MB
      });
    }

    for (const mediaFile of files) {
      try {
        // Generate a unique filename
        const timestamp = Date.now();
        const randomString = Math.random().toString(36).substring(2, 15);
        const fileExtension = mediaFile.file.name.split('.').pop() || 'bin';
        const fileName = `${userId}/${timestamp}_${randomString}.${fileExtension}`;

        console.log(`Uploading file: ${mediaFile.file.name} as ${fileName}`);

        // Upload to Supabase Storage
        const { data, error } = await supabase.storage
          .from('media')
          .upload(fileName, mediaFile.file, {
            cacheControl: '3600',
            upsert: false
          });

        if (error) {
          console.error('Error uploading file:', error);
          toast.error(`Failed to upload ${mediaFile.file.name}`);
          continue;
        }

        // Get the public URL
        const { data: urlData } = supabase.storage
          .from('media')
          .getPublicUrl(fileName);

        if (urlData?.publicUrl) {
          uploadedFiles.push({
            id: mediaFile.id,
            url: urlData.publicUrl,
            type: mediaFile.type
          });

          console.log(`Successfully uploaded: ${mediaFile.file.name} -> ${urlData.publicUrl}`);
        }
      } catch (uploadError) {
        console.error(`Error uploading file ${mediaFile.file.name}:`, uploadError);
        toast.error(`Failed to upload ${mediaFile.file.name}`);
      }
    }

    if (uploadedFiles.length > 0) {
      toast.success(`Successfully uploaded ${uploadedFiles.length} file(s)`);
    }

    return uploadedFiles;
  } catch (error) {
    console.error('Error in media upload service:', error);
    toast.error('Failed to upload media files');
    return [];
  }
}

/**
 * Delete a media file from storage
 * @param url - The public URL of the file to delete
 */
export async function deleteMediaFile(url: string): Promise<boolean> {
  try {
    // Extract the file path from the URL
    const urlParts = url.split('/');
    const fileName = urlParts[urlParts.length - 1];
    const userFolder = urlParts[urlParts.length - 2];
    const filePath = `${userFolder}/${fileName}`;

    const { error } = await supabase.storage
      .from('media')
      .remove([filePath]);

    if (error) {
      console.error('Error deleting media file:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in deleteMediaFile:', error);
    return false;
  }
}

export default {
  uploadMediaFiles,
  deleteMediaFile
};