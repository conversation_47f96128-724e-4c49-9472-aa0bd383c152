-- Create drafts table
CREATE TABLE IF NOT EXISTS public.drafts (
  id UUID PRIMARY KEY,
  user_id TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('voice_message', 'journal')),
  title TEXT,
  audio_url TEXT,
  transcript TEXT,
  audio_duration INTEGER,
  media JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  metadata JSONB
);

-- Enable RLS on drafts table
ALTER TABLE public.drafts ENABLE ROW LEVEL SECURITY;

-- Create policies for drafts table

-- 1. Allow users to view their own drafts
CREATE POLICY "Users can view their own drafts"
ON public.drafts
FOR SELECT
USING (user_id = auth.uid());

-- 2. Allow users to insert their own drafts
CREATE POLICY "Users can insert their own drafts"
ON public.drafts
FOR INSERT
WITH CHECK (user_id = auth.uid());

-- 3. Allow users to update their own drafts
CREATE POLICY "Users can update their own drafts"
ON public.drafts
FOR UPDATE
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

-- 4. Allow users to delete their own drafts
CREATE POLICY "Users can delete their own drafts"
ON public.drafts
FOR DELETE
USING (user_id = auth.uid());

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS drafts_user_id_idx ON public.drafts(user_id);
CREATE INDEX IF NOT EXISTS drafts_type_idx ON public.drafts(type);
CREATE INDEX IF NOT EXISTS drafts_updated_at_idx ON public.drafts(updated_at);

-- Create function to get drafts for a user
CREATE OR REPLACE FUNCTION get_user_drafts(user_id_param TEXT, draft_type TEXT DEFAULT NULL)
RETURNS TABLE (
  id UUID,
  user_id TEXT,
  type TEXT,
  title TEXT,
  audio_url TEXT,
  transcript TEXT,
  audio_duration INTEGER,
  media JSONB,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  metadata JSONB
) AS $$
BEGIN
  IF draft_type IS NULL THEN
    RETURN QUERY
    SELECT d.id, d.user_id, d.type, d.title, d.audio_url, d.transcript, d.audio_duration, d.media, d.created_at, d.updated_at, d.metadata
    FROM public.drafts d
    WHERE d.user_id = user_id_param
    ORDER BY d.updated_at DESC;
  ELSE
    RETURN QUERY
    SELECT d.id, d.user_id, d.type, d.title, d.audio_url, d.transcript, d.audio_duration, d.media, d.created_at, d.updated_at, d.metadata
    FROM public.drafts d
    WHERE d.user_id = user_id_param
    AND d.type = draft_type
    ORDER BY d.updated_at DESC;
  END IF;
END;
$$ LANGUAGE plpgsql;
