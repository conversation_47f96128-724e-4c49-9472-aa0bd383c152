/**
 * Unified Profile Update Hook
 * Consolidates all profile update logic into a single, reliable system
 */

import { useState } from 'react';
import { UserProfile, UserProfileUpdate } from '@/types/user-profile';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/sonner';

interface UseProfileUpdateResult {
  updateProfile: (address: string, update: UserProfileUpdate, profileImageFile?: File, coverImageFile?: File) => Promise<boolean>;
  isUpdating: boolean;
  lastError: string | null;
}

export function useProfileUpdate(): UseProfileUpdateResult {
  const [isUpdating, setIsUpdating] = useState(false);
  const [lastError, setLastError] = useState<string | null>(null);

  const uploadImageToStorage = async (file: File, address: string, type: 'profile' | 'cover'): Promise<string> => {
    try {
      console.log(`📤 Uploading ${type} image for ${address}`);
      
      // Generate unique filename
      const fileExt = file.name.split('.').pop() || 'jpg';
      const fileName = `profiles/${address}/${type}-${Date.now()}.${fileExt}`;

      // Upload to Supabase storage
      const { data, error } = await supabase.storage
        .from('profiles')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (error) {
        console.error(`❌ ${type} upload error:`, error);
        throw new Error(`Failed to upload ${type} image: ${error.message}`);
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('profiles')
        .getPublicUrl(data.path);

      console.log(`✅ ${type} uploaded:`, urlData.publicUrl);
      return urlData.publicUrl;
    } catch (error) {
      console.error(`❌ Error uploading ${type}:`, error);
      throw error;
    }
  };

  const updateProfileInDatabase = async (address: string, profileData: any): Promise<boolean> => {
    try {
      console.log(`💾 Updating profile in database for ${address}`, profileData);

      // First try to get the current profile to check if it exists
      const { data: existingProfile, error: checkError } = await supabase
        .from('profiles')
        .select('id, wallet_address')
        .or(`wallet_address.eq.${address.toLowerCase()},id.eq.${address}`)
        .maybeSingle();

      if (checkError) {
        console.error('Error checking existing profile:', checkError);
      }

      let result;
      
      if (existingProfile) {
        // Update existing profile
        console.log(`📝 Updating existing profile with ID: ${existingProfile.id}`);
        const { data, error } = await supabase
          .from('profiles')
          .update({
            username: profileData.username,
            display_name: profileData.displayName,
            bio: profileData.bio || '',
            avatar_url: profileData.profileImageUrl || '',
            cover_image_url: profileData.coverImageUrl || '',
            social_links: profileData.socialLinks || {},
            updated_at: new Date().toISOString()
          })
          .eq('id', existingProfile.id)
          .select()
          .single();

        if (error) {
          console.error('❌ Error updating profile:', error);
          throw new Error(`Failed to update profile: ${error.message}`);
        }
        
        result = data;
      } else {
        // Create new profile
        console.log(`➕ Creating new profile for ${address}`);
        const { data, error } = await supabase
          .from('profiles')
          .insert({
            wallet_address: address.toLowerCase(),
            username: profileData.username,
            display_name: profileData.displayName,
            bio: profileData.bio || '',
            avatar_url: profileData.profileImageUrl || '',
            cover_image_url: profileData.coverImageUrl || '',
            social_links: profileData.socialLinks || {},
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (error) {
          console.error('❌ Error creating profile:', error);
          throw new Error(`Failed to create profile: ${error.message}`);
        }
        
        result = data;
      }

      console.log('✅ Profile updated in database:', result);
      return true;
    } catch (error) {
      console.error('❌ Database update failed:', error);
      throw error;
    }
  };

  const updateProfile = async (
    address: string, 
    update: UserProfileUpdate, 
    profileImageFile?: File, 
    coverImageFile?: File
  ): Promise<boolean> => {
    try {
      setIsUpdating(true);
      setLastError(null);
      
      console.log(`🔄 Starting profile update for ${address}`, update);

      // Prepare the update data
      const updateData = { ...update };

      // Upload profile image if provided
      if (profileImageFile) {
        try {
          updateData.profileImageUrl = await uploadImageToStorage(profileImageFile, address, 'profile');
        } catch (error) {
          console.error('Profile image upload failed:', error);
          setLastError(`Profile image upload failed: ${error.message}`);
          return false;
        }
      }

      // Upload cover image if provided
      if (coverImageFile) {
        try {
          updateData.coverImageUrl = await uploadImageToStorage(coverImageFile, address, 'cover');
        } catch (error) {
          console.error('Cover image upload failed:', error);
          setLastError(`Cover image upload failed: ${error.message}`);
          return false;
        }
      }

      // Update profile in database
      await updateProfileInDatabase(address, updateData);

      // Force refresh the profile cache to show updated data immediately
      try {
        const simpleProfileService = await import('@/services/simpleProfileService');
        // Clear cache and force fresh fetch
        simpleProfileService.default.clearCacheForAddress(address);
        await simpleProfileService.default.getProfileByAddress(address);
        console.log('✅ Profile cache refreshed after update');
      } catch (cacheError) {
        console.warn('⚠️ Could not refresh profile cache:', cacheError);
      }

      console.log('✅ Profile update completed successfully');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.error('❌ Profile update failed:', errorMessage);
      setLastError(errorMessage);
      return false;
    } finally {
      setIsUpdating(false);
    }
  };

  return {
    updateProfile,
    isUpdating,
    lastError
  };
}

export default useProfileUpdate;