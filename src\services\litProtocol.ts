/**
 * Lit Protocol service for encryption
 * This service handles encrypting and decrypting files using Lit Protocol
 */

// Note: You'll need to install the Lit Protocol SDK with: npm install lit-js-sdk
import { LitNodeClient } from 'lit-js-sdk';

// Initialize Lit client
const litClient = new LitNodeClient();
let litConnected = false;

/**
 * Connect to Lit Protocol network
 */
export async function connectLit(): Promise<void> {
  if (!litConnected) {
    await litClient.connect();
    litConnected = true;
  }
}

/**
 * Encrypt a file using Lit Protocol
 * @param fileBuffer The file buffer to encrypt
 * @param accessControlConditions The access control conditions for decryption
 * @returns The encrypted file and encrypted symmetric key
 */
export async function encryptWithLit(
  fileBuffer: ArrayBuffer,
  accessControlConditions: any[]
): Promise<{ encryptedFile: Blob; encryptedSymmetricKey: string }> {
  try {
    // Ensure Lit is connected
    await connectLit();

    // Create a Blob from the ArrayBuffer
    const file = new Blob([fileBuffer]);

    // Encrypt the file
    const { encryptedFile, symmetricKey } = await litClient.encryptFile({ file });

    // Save the encryption key with access control conditions
    const encryptedSymmetricKey = await litClient.saveEncryptionKey({
      accessControlConditions,
      symmetricKey,
      chain: 'ethereum',
      authSig: await getAuthSig()
    });

    return { encryptedFile, encryptedSymmetricKey };
  } catch (error) {
    console.error('Error encrypting with Lit Protocol:', error);
    throw error;
  }
}

/**
 * Decrypt a file using Lit Protocol
 * @param encryptedFile The encrypted file
 * @param encryptedSymmetricKey The encrypted symmetric key
 * @param accessControlConditions The access control conditions for decryption
 * @returns The decrypted file
 */
export async function decryptWithLit(
  encryptedFile: Blob,
  encryptedSymmetricKey: string,
  accessControlConditions: any[]
): Promise<Blob> {
  try {
    // Ensure Lit is connected
    await connectLit();

    // Get the decryption key
    const symmetricKey = await litClient.getEncryptionKey({
      accessControlConditions,
      toDecrypt: encryptedSymmetricKey,
      chain: 'ethereum',
      authSig: await getAuthSig()
    });

    // Decrypt the file
    const decryptedFile = await litClient.decryptFile({
      file: encryptedFile,
      symmetricKey
    });

    return decryptedFile;
  } catch (error) {
    console.error('Error decrypting with Lit Protocol:', error);
    throw error;
  }
}

/**
 * Get authentication signature for Lit Protocol
 * This uses the connected wallet to sign a message
 */
async function getAuthSig(): Promise<any> {
  try {
    // Check if we have a cached auth sig
    const cachedAuthSig = localStorage.getItem('lit-auth-signature');
    if (cachedAuthSig) {
      return JSON.parse(cachedAuthSig);
    }

    // Get the Ethereum provider
    const { ethereum } = window as any;
    if (!ethereum) {
      throw new Error('No Ethereum wallet found');
    }

    // Get the connected account
    const accounts = await ethereum.request({ method: 'eth_requestAccounts' });
    const account = accounts[0];

    // Message to sign
    const message = `I am signing this message to authenticate with Lit Protocol at ${new Date().toISOString()}`;

    // Sign the message
    const signature = await ethereum.request({
      method: 'personal_sign',
      params: [message, account]
    });

    // Create the auth sig
    const authSig = {
      sig: signature,
      derivedVia: 'web3.eth.personal.sign',
      signedMessage: message,
      address: account
    };

    // Cache the auth sig
    localStorage.setItem('lit-auth-signature', JSON.stringify(authSig));

    return authSig;
  } catch (error) {
    console.error('Error getting auth signature:', error);
    throw error;
  }
}

/**
 * Create access control conditions for a specific wallet address
 * @param walletAddress The wallet address that should have access
 * @returns Access control conditions array
 */
export function createWalletAccessControl(walletAddress: string): any[] {
  return [
    {
      conditionType: 'evmBasic',
      contractAddress: '',
      standardContractType: '',
      chain: 'ethereum',
      method: '',
      parameters: [':userAddress'],
      returnValueTest: {
        comparator: '=',
        value: walletAddress.toLowerCase()
      }
    }
  ];
}
