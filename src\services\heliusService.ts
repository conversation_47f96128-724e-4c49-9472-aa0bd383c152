import { ChainEvent, ChainEventType } from './chainTypes';
import { apiKeyManager, ApiKeyType } from './apiKeyManager';

/**
 * Simplified Helius Service
 *
 * This service is intentionally minimal with NO blockchain API calls.
 * It only provides methods to create sample chain events for testing purposes.
 */
class HeliusService {
  private apiKey: string = '';

  constructor() {
    // Get API key from the API Key Manager
    this.apiKey = apiKeyManager.getApiKey(ApiKeyType.HELIUS);

    // Listen for API key changes
    apiKeyManager.addListener(ApiKeyType.HELIUS, (apiKey) => {
      this.apiKey = apiKey;
      console.log('Helius API key updated');
    });
  }

  /**
   * Set the API key for Helius
   */
  public setApiKey(apiKey: string): void {
    // Update the API key in the API Key Manager
    apiKeyManager.setApiKey(ApiKeyType.HELIUS, apiKey);
    console.log('Helius API key set successfully');
  }

  /**
   * Create a sample DAO proposal event for testing
   */
  public createSampleDAOEvent(): ChainEvent {
    // Create a more detailed and realistic DAO proposal event
    const daoOptions = [
      {
        name: 'DeveloperDAO',
        chain: 'ethereum',
        proposals: [
          'Fund 5 hackathons across Latin America to boost regional Web3 development',
          'Allocate 500,000 USD for developer grants focused on privacy-preserving technologies',
          'Create a mentorship program connecting senior Web3 developers with newcomers'
        ]
      },
      {
        name: 'Uniswap DAO',
        chain: 'ethereum',
        proposals: [
          'Deploy Uniswap v4 on Solana to expand cross-chain liquidity',
          'Implement fee reduction for small-cap token pairs to encourage new project listings',
          'Establish a 2 million USD security audit fund for protocol improvements'
        ]
      },
      {
        name: 'Marinade DAO',
        chain: 'solana',
        proposals: [
          'Increase validator commission to 8% to ensure network security',
          'Launch liquid staking derivatives for institutional partners',
          'Create a 1 million SOL insurance fund against slashing events'
        ]
      }
    ];

    // Select a random DAO
    const selectedDao = daoOptions[Math.floor(Math.random() * daoOptions.length)];
    // Select a random proposal
    const selectedProposal = selectedDao.proposals[Math.floor(Math.random() * selectedDao.proposals.length)];

    return {
      id: `dao-${Date.now()}`,
      type: ChainEventType.DAO_PROPOSAL_EXECUTED,
      timestamp: Date.now(),
      data: {
        dao: selectedDao.name,
        proposal: selectedProposal,
        voteCount: 300 + Math.floor(Math.random() * 700), // 300-1000 votes
        approved: true,
        quorum: '72%',
        votingPeriod: '5 days',
        implementation: 'Immediate execution via timelock controller'
      },
      source: {
        chain: selectedDao.chain,
        dao: selectedDao.name
      }
    };
  }

  /**
   * Create a funding round event for testing
   */
  public createFundingRoundEvent(): ChainEvent {
    // Create a more detailed and realistic funding round event
    const fundingOptions = [
      {
        project: 'Zeta Markets',
        chain: 'solana',
        amounts: [2500000, 5000000, 10000000],
        funders: ['Solana Foundation', 'Alameda Research', 'Jump Crypto']
      },
      {
        project: 'LayerZero',
        chain: 'ethereum',
        amounts: [15000000, 25000000, 50000000],
        funders: ['a16z', 'Sequoia Capital', 'Paradigm']
      },
      {
        project: 'Drift Protocol',
        chain: 'solana',
        amounts: [3000000, 7000000, 12000000],
        funders: ['Jump Crypto', 'Multicoin Capital', 'Solana Ventures']
      },
      {
        project: 'Pyth Network',
        chain: 'solana',
        amounts: [8000000, 15000000, 20000000],
        funders: ['Jump Crypto', 'Solana Foundation', 'Coinbase Ventures']
      }
    ];

    // Select a random project
    const selectedFunding = fundingOptions[Math.floor(Math.random() * fundingOptions.length)];
    // Select a random amount
    const selectedAmount = selectedFunding.amounts[Math.floor(Math.random() * selectedFunding.amounts.length)];
    // Select a random funder
    const selectedFunder = selectedFunding.funders[Math.floor(Math.random() * selectedFunding.funders.length)];

    return {
      id: `funding-${Date.now()}`,
      type: ChainEventType.FUNDING_ROUND,
      timestamp: Date.now(),
      data: {
        project: selectedFunding.project,
        amount: selectedAmount,
        currency: 'USD',
        funder: selectedFunder,
        round: `Series ${['A', 'B', 'C'][Math.floor(Math.random() * 3)]}`,
        valuation: selectedAmount * (5 + Math.floor(Math.random() * 10)), // 5-15x the funding amount
        details: 'The funding will be used for protocol development, security audits, and expanding the team.'
      },
      source: {
        chain: selectedFunding.chain,
        project: selectedFunding.project
      }
    };
  }

  /**
   * Create a security incident event for testing
   */
  public createSecurityIncidentEvent(): ChainEvent {
    // Create a detailed security incident event
    const securityOptions = [
      {
        protocol: 'SushiSwap',
        chain: 'ethereum',
        amounts: [2000000, 5000000, 10000000],
        issues: ['smart contract vulnerability', 'oracle manipulation', 'flash loan attack']
      },
      {
        protocol: 'Mango Markets',
        chain: 'solana',
        amounts: [1000000, 3000000, 7000000],
        issues: ['price oracle manipulation', 'governance exploit', 'flash loan attack']
      },
      {
        protocol: 'Wormhole Bridge',
        chain: 'solana',
        amounts: [5000000, 10000000, 20000000],
        issues: ['signature verification bypass', 'validator compromise', 'smart contract vulnerability']
      }
    ];

    // Select a random protocol
    const selectedSecurity = securityOptions[Math.floor(Math.random() * securityOptions.length)];
    // Select a random amount
    const selectedAmount = selectedSecurity.amounts[Math.floor(Math.random() * selectedSecurity.amounts.length)];
    // Select a random issue
    const selectedIssue = selectedSecurity.issues[Math.floor(Math.random() * selectedSecurity.issues.length)];

    return {
      id: `security-${Date.now()}`,
      type: ChainEventType.SECURITY_INCIDENT,
      timestamp: Date.now(),
      data: {
        protocol: selectedSecurity.protocol,
        amount: selectedAmount,
        currency: 'USD',
        issue: selectedIssue,
        status: 'Ongoing investigation',
        recommendation: 'Users are advised to withdraw funds immediately',
        details: `The ${selectedSecurity.protocol} team is working with security firms to address the ${selectedIssue} and recover funds.`
      },
      source: {
        chain: selectedSecurity.chain,
        project: selectedSecurity.protocol
      }
    };
  }
}

// Export a singleton instance
export const heliusService = new HeliusService();
