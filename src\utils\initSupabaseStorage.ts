
import { supabase } from '@/lib/supabase';

/**
 * Initialize the required Supabase storage buckets
 */
export async function initializeSupabaseStorage() {
  try {
    // Check if audio bucket exists, create if it doesn't
    const { data: buckets, error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.error('Error listing buckets:', error);
      return;
    }
    
    const audioExists = buckets?.some(bucket => bucket.name === 'audio');
    
    if (!audioExists) {
      const { error: createError } = await supabase.storage.createBucket('audio', {
        public: true,
        fileSizeLimit: 50000000, // 50MB
        allowedMimeTypes: ['audio/webm', 'audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/ogg']
      });
      
      if (createError) {
        console.error('Error creating audio bucket:', createError);
      } else {
        console.log('Created audio bucket successfully');
      }
    }
  } catch (error) {
    console.error('Error initializing storage buckets:', error);
  }
}

/**
 * Make sure transcriptions table exists in Supabase
 */
export async function ensureTranscriptionsTable() {
  try {
    // Check if the transcriptions table exists
    const { error } = await supabase.from('transcriptions').select('count').limit(1);
    
    if (error) {
      console.warn('Transcriptions table may not exist:', error.message);
      
      // You should create this table via a migration in production
      // For this example, we'll assume it exists or will be created manually
    }
    
  } catch (error) {
    console.error('Error checking transcriptions table:', error);
  }
}

// Export a function to initialize everything
export function initializeStorage() {
  // Initialize storage when the app starts
  initializeSupabaseStorage().catch(error => {
    console.error('Error initializing storage:', error);
  });
  
  // Ensure the transcriptions table exists
  ensureTranscriptionsTable().catch(error => {
    console.error('Error ensuring transcriptions table:', error);
  });
}

export default initializeStorage;
