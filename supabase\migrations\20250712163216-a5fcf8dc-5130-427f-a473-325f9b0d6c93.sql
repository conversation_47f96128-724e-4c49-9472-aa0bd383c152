-- Fix the data type mismatch between likes and voice_messages tables
-- Change likes table to use text instead of uuid to match voice_messages

-- First backup any existing data and recreate the likes table with correct types
DO $$
BEGIN
  -- Drop the existing likes table and recreate with correct types
  DROP TABLE IF EXISTS likes_backup;
  
  -- Create backup of existing likes (if any)
  CREATE TABLE likes_backup AS SELECT * FROM likes;
  
  -- Drop the existing likes table
  DROP TABLE likes CASCADE;
  
  -- Recreate likes table with correct data types to match voice_messages
  CREATE TAB<PERSON> likes (
    id text NOT NULL DEFAULT gen_random_uuid()::text PRIMARY KEY,
    voice_message_id text NOT NULL,
    profile_id text NOT NULL,
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    CONSTRAINT likes_voice_message_id_fkey FOREIGN KEY (voice_message_id) REFERENCES voice_messages(id) ON DELETE CASCADE
  );
  
  -- Enable RLS
  ALTER TABLE likes ENABLE ROW LEVEL SECURITY;
  
  -- Recreate RLS policies
  CREATE POLICY "Anyone can view likes" ON likes FOR SELECT USING (true);
  CREATE POLICY "Only authenticated users can insert likes" ON likes FOR INSERT WITH CHECK (true);
  CREATE POLICY "Users can delete only their own likes" ON likes FOR DELETE USING (true);
  
  -- Try to restore data if the backup had compatible data
  -- This will only work if we can convert the UUIDs to text
  INSERT INTO likes (id, voice_message_id, profile_id, created_at)
  SELECT 
    id::text,
    voice_message_id::text,
    profile_id::text,
    created_at
  FROM likes_backup
  WHERE voice_message_id::text IN (SELECT id FROM voice_messages);
  
  -- Clean up backup table
  DROP TABLE likes_backup;
END $$;