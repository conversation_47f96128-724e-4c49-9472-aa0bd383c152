# 🔔 Notification System Documentation

## Overview

The notification system provides real-time notifications for all social interactions in the voice-first social platform. Users receive notifications for likes, replies, tips, follows, summons, reposts, and reactions.

## ✅ What's Been Implemented

### Database Infrastructure
- **Notifications Table**: Complete table with proper schema
- **Row Level Security**: Users can only see their own notifications
- **Indexes**: Optimized for performance
- **Real-time Subscriptions**: Live updates via Supabase

### UI Components
- **NotificationCenter**: Popover component in header with unread count badge
- **Notifications Page**: Full-page view at `/notifications`
- **Toast Notifications**: Real-time popup notifications
- **Rich Formatting**: Different icons and text for each notification type

### Notification Triggers
✅ **Reactions**: When users react with emojis to voice messages
✅ **Replies**: When users reply to voice messages (text or voice)
✅ **Tips**: When users send cryptocurrency tips
✅ **Follows**: When users follow each other
✅ **Summons**: When users summon others for voice responses
✅ **Reposts**: When users repost voice messages

### Real-time Features
- **Live Updates**: Notifications appear instantly
- **Unread Count**: Badge shows number of unread notifications
- **Mark as Read**: Individual and bulk mark as read
- **Auto-refresh**: Notifications sync across browser tabs

## 🚀 Setup Instructions

### 1. Create Database Table
```bash
# Run the setup script
node setup_notifications.js

# OR manually run the SQL in your Supabase SQL editor
# (see create_notifications_table.sql)
```

### 2. Test the System
```bash
# Verify everything is working
node test_notifications.js
```

### 3. Verify in App
1. Log in to your app
2. Look for the bell icon in the header
3. Perform actions that should trigger notifications
4. Check that notifications appear in real-time

## 📊 Database Schema

```sql
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  type TEXT NOT NULL CHECK (type IN ('like', 'reply', 'tip', 'follow', 'summon', 'repost', 'reaction')),
  from_address TEXT NOT NULL,
  to_address TEXT NOT NULL,
  message_id UUID,
  data JSONB,
  read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🔧 How It Works

### Notification Flow
1. **User Action**: User performs an action (like, reply, tip, etc.)
2. **Trigger**: Component calls `addNotification()` from NotificationContext
3. **Database**: Notification is saved to Supabase
4. **Real-time**: Supabase broadcasts the change
5. **UI Update**: NotificationContext receives the update and shows it

### Context Integration
```typescript
// In any component
import { useNotifications } from '@/contexts/NotificationContext';

const { addNotification } = useNotifications();

// Create a notification
addNotification(
  'like',           // type
  currentUserId,    // from
  targetUserId,     // to
  messageId,        // optional message reference
  { extra: 'data' } // optional additional data
);
```

### Notification Types
- **`like`**: User liked a voice message
- **`reply`**: User replied to a voice message
- **`tip`**: User sent a cryptocurrency tip
- **`follow`**: User followed another user
- **`summon`**: User summoned another for voice response
- **`repost`**: User reposted a voice message
- **`reaction`**: User reacted with emoji to a voice message

## 🎨 UI Components

### NotificationCenter (Header Dropdown)
- Bell icon with unread count badge
- Dropdown with recent notifications
- Mark as read functionality
- Links to full notifications page

### Notifications Page
- Full list of all notifications
- Mark all as read button
- Rich formatting with user avatars
- Time stamps and context

## 🔒 Security

### Row Level Security (RLS)
- Users can only see their own notifications
- Authenticated users can create notifications
- Proper policies prevent data leaks

### Data Validation
- Notification types are constrained
- Required fields are enforced
- User addresses are validated

## 🐛 Troubleshooting

### Common Issues

**Notifications not appearing:**
1. Check if notifications table exists
2. Verify RLS policies are correct
3. Check browser console for errors
4. Ensure user is authenticated

**Real-time not working:**
1. Check Supabase real-time is enabled
2. Verify subscription is active
3. Check network connectivity
4. Look for WebSocket errors

**Database errors:**
1. Run the setup script again
2. Check Supabase connection
3. Verify table permissions
4. Check for missing indexes

### Debug Commands
```bash
# Test database connection
node test_notifications.js

# Check table structure in Supabase SQL editor
SELECT * FROM notifications LIMIT 5;

# Check RLS policies
SELECT * FROM pg_policies WHERE tablename = 'notifications';
```

## 📈 Performance

### Optimizations
- **Indexes**: Fast queries on common fields
- **Pagination**: Large notification lists are paginated
- **Caching**: Recent notifications are cached
- **Debouncing**: Prevents spam notifications

### Monitoring
- Monitor notification creation rate
- Track real-time subscription health
- Watch for database performance issues
- Monitor user engagement with notifications

## 🔮 Future Enhancements

### Planned Features
- **Push Notifications**: Browser push notifications
- **Email Notifications**: Optional email summaries
- **Notification Preferences**: User-configurable settings
- **Notification Grouping**: Group similar notifications
- **Rich Media**: Include images/audio in notifications

### Integration Ideas
- **Mobile App**: React Native notifications
- **Discord Bot**: Cross-platform notifications
- **Analytics**: Notification engagement tracking
- **AI Summaries**: Smart notification summaries

## 📝 Code Examples

### Creating Notifications
```typescript
// In a component
const { addNotification } = useNotifications();

// When user likes a post
addNotification('like', currentUserId, postAuthorId, postId);

// When user tips
addNotification('tip', currentUserId, recipientId, messageId, { 
  amount: '0.1', 
  currency: 'SOL' 
});
```

### Listening for Notifications
```typescript
// The NotificationContext automatically handles real-time updates
const { notifications, unreadCount } = useNotifications();

// Display unread count
<Badge>{unreadCount}</Badge>

// Show notifications
{notifications.map(notif => (
  <NotificationItem key={notif.id} notification={notif} />
))}
```

---

🎉 **Your notification system is now fully connected and ready to enhance user engagement!**
