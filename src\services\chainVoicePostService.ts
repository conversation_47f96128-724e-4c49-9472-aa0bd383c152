import { ContextualizedEvent } from './eventContextualizer';
import { VoiceGenerationResult } from './voiceGenerator';
import { ChainEvent, ChainEventType } from './chainTypes';
import { chainListener } from './chainListener';
import { eventContextualizer } from './eventContextualizer';
import { voiceGenerator } from './voiceGenerator';

// Interface for chain voice post
export interface ChainVoicePost {
  id: string;
  title: string;
  audioUrl: string;
  audioDuration: number;
  transcription: string;
  timestamp: number;
  channels: string[];
  tags: string[];
  eventType: ChainEventType;
  sourceChain: string;
  sourceProject?: string;
  sourceDao?: string;
  metadata: {
    transactionHash?: string;
    blockNumber?: number;
    [key: string]: any;
  };
}

class ChainVoicePostService {
  private isProcessing: boolean = false;
  private eventQueue: ChainEvent[] = [];
  private posts: ChainVoicePost[] = [];

  constructor() {
    // Subscribe to chain events
    this.subscribeToChainEvents();

    // Also subscribe to manual events
    this.subscribeToManualEvents();
  }

  /**
   * Start processing chain events and creating voice posts
   */
  public start(): void {
    console.log('Starting Chain Voice Post Service');

    // Check if this is the first time the user is using the app
    const isFirstTime = !localStorage.getItem('audra_first_time_welcome');

    // If it's the first time, create a welcome event
    if (isFirstTime) {
      console.log('First time user, creating welcome event');

      // Create a welcome event
      const welcomeEvent: ChainEvent = {
        id: `welcome-${Date.now()}`,
        type: 'welcome',
        timestamp: Date.now(),
        data: {
          message: 'Welcome to Audra!',
          isFirstTime: true
        },
        source: {
          chain: 'audra',
          project: 'audra',
          dao: 'audra'
        }
      };

      // Process the welcome event
      this.eventQueue.push(welcomeEvent);
      this.processNextEvent();

      // Mark that the user has seen the welcome message
      localStorage.setItem('audra_first_time_welcome', 'true');
    }

    // Start listening for chain events
    chainListener.startListening();
  }

  /**
   * Stop processing chain events
   */
  public stop(): void {
    console.log('Stopping Chain Voice Post Service');
    chainListener.stopListening();
  }

  /**
   * Get all chain voice posts
   */
  public getAllPosts(): ChainVoicePost[] {
    return [...this.posts];
  }

  /**
   * Get posts for a specific channel
   */
  public getPostsByChannel(channel: string): ChainVoicePost[] {
    return this.posts.filter(post => post.channels.includes(channel));
  }

  /**
   * Get posts for a specific tag
   */
  public getPostsByTag(tag: string): ChainVoicePost[] {
    return this.posts.filter(post => post.tags.includes(tag));
  }

  /**
   * Get posts for a specific chain
   */
  public getPostsByChain(chain: string): ChainVoicePost[] {
    return this.posts.filter(post => post.sourceChain === chain);
  }

  /**
   * Subscribe to chain events
   */
  private subscribeToChainEvents(): void {
    console.log('Subscribing to chain events');

    const handleChainEvent = (event: ChainEvent) => {
      console.log('Received chain event:', event.type, event);
      this.eventQueue.push(event);
      this.processNextEvent();
    };

    chainListener.on('chainEvent', handleChainEvent);
    console.log('Subscribed to chainEvent');
  }

  /**
   * Subscribe to manual events (triggered by UI)
   */
  private subscribeToManualEvents(): void {
    console.log('Subscribing to manual events');

    const handleManualEvent = (e: Event) => {
      const customEvent = e as CustomEvent<ChainEvent>;
      if (customEvent.detail) {
        console.log('Received manual chain event:', customEvent.detail.type, customEvent.detail);
        this.eventQueue.push(customEvent.detail);
        this.processNextEvent();
      }
    };

    document.addEventListener('manualChainEvent', handleManualEvent);
    console.log('Subscribed to manualChainEvent');
  }

  /**
   * Process the next event in the queue
   */
  private async processNextEvent(): Promise<void> {
    if (this.isProcessing || this.eventQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      const event = this.eventQueue.shift()!;
      await this.processEvent(event);
    } catch (error) {
      console.error('Error processing chain event:', error);
    } finally {
      this.isProcessing = false;

      // Process next event if there are more in the queue
      if (this.eventQueue.length > 0) {
        this.processNextEvent();
      }
    }
  }

  /**
   * Process a single chain event
   */
  private async processEvent(event: ChainEvent): Promise<void> {
    // Step 1: Contextualize the event
    const contextualizedEvent = eventContextualizer.contextualizeEvent(event);

    // Step 2: Generate voice audio
    const voiceResult = await voiceGenerator.generateVoiceAudio(contextualizedEvent);

    // Step 3: Create a chain voice post
    const post = this.createPost(contextualizedEvent, voiceResult);

    // Step 4: Store the post
    this.posts.unshift(post); // Add to beginning of array

    // Step 5: Notify subscribers
    this.notifyNewPost(post);

    console.log('Created chain voice post:', post.title);
  }

  /**
   * Create a chain voice post from a contextualized event and voice generation result
   */
  private createPost(
    contextualizedEvent: ContextualizedEvent,
    voiceResult: VoiceGenerationResult
  ): ChainVoicePost {
    const { event, title, tags, relevantChannels } = contextualizedEvent;

    return {
      id: `chain-voice-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      title,
      audioUrl: voiceResult.audioUrl,
      audioDuration: voiceResult.audioDuration,
      transcription: voiceResult.transcription,
      timestamp: Date.now(),
      channels: relevantChannels,
      tags,
      eventType: event.type as ChainEventType,
      sourceChain: event.source.chain,
      sourceProject: event.source.project,
      sourceDao: event.source.dao,
      metadata: {
        ...event.data,
        originalEventId: event.id,
        originalEventTimestamp: event.timestamp
      }
    };
  }

  /**
   * Notify subscribers about a new post
   */
  private notifyNewPost(post: ChainVoicePost): void {
    // In a real implementation, this would:
    // 1. Notify UI components
    // 2. Send push notifications
    // 3. Update feeds
    // etc.

    // For now, we'll just emit a custom event
    try {
      document.dispatchEvent(new CustomEvent('newChainVoicePost', { detail: post }));
    } catch (error) {
      console.error('Error dispatching newChainVoicePost event:', error);
    }
  }
}

// Export a singleton instance
export const chainVoicePostService = new ChainVoicePostService();
