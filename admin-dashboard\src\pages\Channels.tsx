import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MessageSquare, ExternalLink } from 'lucide-react';

const Channels: React.FC = () => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Channel Management</CardTitle>
          <CardDescription>
            View and manage communication channels
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">Channel Management</h3>
            <p className="text-muted-foreground max-w-md mb-6">
              Channel management is handled in the main application. Please use the main Audra application to create, join, and manage channels.
            </p>
            <Button 
              variant="outline" 
              className="flex items-center gap-2"
              onClick={() => window.open('/', '_blank')}
            >
              <ExternalLink className="h-4 w-4" />
              Open Main Application
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Channels;
