import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { toggleReaction, getReactions, getUserReaction, type Reaction } from '@/services/reactionService';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/sonner';
import { useIsMobile } from '@/hooks/use-mobile';

const REACTION_EMOJIS = [
  { emoji: '👍', label: 'Like' },
  { emoji: '❤️', label: 'Love' },
  { emoji: '😂', label: 'Laugh' },
  { emoji: '😮', label: 'Wow' },
  { emoji: '😢', label: 'Sad' },
  { emoji: '😡', label: 'Angry' },
  { emoji: '🔥', label: 'Fire' },
  { emoji: '👏', label: 'Clap' },
  { emoji: '🎉', label: 'Party' },
  { emoji: '💯', label: 'Perfect' },
  { emoji: '⚡', label: 'Energy' },
  { emoji: '💎', label: 'Gem' },
  { emoji: '🚀', label: 'Rocket' },
  { emoji: '🎯', label: 'Target' },
  { emoji: '💪', label: 'Strong' },
  { emoji: '🧠', label: 'Smart' },
  { emoji: '✨', label: 'Sparkles' },
  { emoji: '👀', label: 'Eyes' },
  { emoji: '🤔', label: 'Thinking' },
  { emoji: '🤯', label: 'Mind Blown' },
  { emoji: '😍', label: 'Heart Eyes' },
  { emoji: '🤩', label: 'Star Eyes' },
  { emoji: '😎', label: 'Cool' },
  { emoji: '🙏', label: 'Pray' },
  { emoji: '💀', label: 'Dead' },
  { emoji: '🤡', label: 'Clown' },
  { emoji: '👑', label: 'Crown' },
  { emoji: '🎭', label: 'Drama' },
  { emoji: '🌟', label: 'Star' },
  { emoji: '🎨', label: 'Art' },
  { emoji: '🌈', label: 'Rainbow' },
  { emoji: '🦄', label: 'Unicorn' },
  { emoji: '🎵', label: 'Music' },
  { emoji: '📈', label: 'Chart' },
  { emoji: '🏆', label: 'Trophy' },
  { emoji: '🎪', label: 'Circus' },
  { emoji: '🎲', label: 'Dice' },
  { emoji: '🌶️', label: 'Spicy' },
  { emoji: '🍕', label: 'Pizza' },
  { emoji: '☕', label: 'Coffee' },
  { emoji: '🍻', label: 'Cheers' },
  { emoji: '🌮', label: 'Taco' },
  { emoji: '🎂', label: 'Cake' },
  { emoji: '🌺', label: 'Flower' },
  { emoji: '🌊', label: 'Wave' },
  { emoji: '❄️', label: 'Snow' },
  { emoji: '☀️', label: 'Sun' }
];

interface MessageReactionsProps {
  messageId: string;
  userId: string;
}

interface UserProfile {
  id: string;
  avatar_url: string | null;
  display_name: string | null;
  username: string | null;
}

interface FloatingEmoji {
  id: string;
  emoji: string;
  x: number;
  y: number;
}

export default function MessageReactions({ messageId, userId }: MessageReactionsProps) {
  const [reactions, setReactions] = useState<Reaction[]>([]);
  const [userReaction, setUserReaction] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [userProfiles, setUserProfiles] = useState<Record<string, UserProfile>>({});
  const [showReactionsModal, setShowReactionsModal] = useState(false);
  const [floatingEmojis, setFloatingEmojis] = useState<FloatingEmoji[]>([]);
  const isMobile = useIsMobile();
  const containerRef = React.useRef<HTMLDivElement>(null);

  const loadReactions = async () => {
    if (!messageId) return;
    
    const [allReactions, currentUserReaction] = await Promise.all([
      getReactions(messageId),
      getUserReaction(messageId, userId)
    ]);
    
    setReactions(allReactions);
    setUserReaction(currentUserReaction);
    
    // Load user profiles for reactions
    await loadUserProfiles(allReactions);
  };

  const loadUserProfiles = async (reactionList: Reaction[]) => {
    const userIds = [...new Set(reactionList.map(r => r.userId))];
    if (userIds.length === 0) return;

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, avatar_url, display_name, username')
        .in('id', userIds);

      if (error) {
        console.error('Error loading user profiles:', error);
        return;
      }

      const profilesMap = (data || []).reduce((acc, profile) => {
        acc[profile.id] = profile;
        return acc;
      }, {} as Record<string, UserProfile>);

      setUserProfiles(profilesMap);
    } catch (error) {
      console.error('Error in loadUserProfiles:', error);
    }
  };

  const createFloatingEmoji = (emoji: string, event?: React.MouseEvent) => {
    if (!containerRef.current) return;
    
    const rect = containerRef.current.getBoundingClientRect();
    const x = event ? event.clientX - rect.left : rect.width / 2;
    const y = event ? event.clientY - rect.top : rect.height / 2;
    
    const floatingEmoji: FloatingEmoji = {
      id: Date.now().toString(),
      emoji,
      x,
      y
    };
    
    setFloatingEmojis(prev => [...prev, floatingEmoji]);
    
    // Remove the floating emoji after animation completes
    setTimeout(() => {
      setFloatingEmojis(prev => prev.filter(e => e.id !== floatingEmoji.id));
    }, 2000);
  };

  const handleReaction = async (emoji: string, event?: React.MouseEvent) => {
    if (!userId) {
      toast.error('Please log in to react');
      return;
    }

    setIsOpen(false);
    setLoading(true);

    // Create floating emoji animation
    createFloatingEmoji(emoji, event);

    try {
      // First, get the message owner to create notification
      const { data: messageData } = await supabase
        .from('voice_messages')
        .select('profile_id')
        .eq('id', messageId)
        .single();

      const messageOwnerId = messageData?.profile_id;
      
      const result = await toggleReaction(messageId, userId, emoji, messageOwnerId);
      
      if (result.success) {
        if (result.action === 'removed') {
          setUserReaction(null);
          setReactions(prev => prev.filter(r => !(r.userId === userId && r.emoji === emoji)));
        } else if (result.action === 'added') {
          setUserReaction(emoji);
          setReactions(prev => [...prev, {
            id: 'temp-' + Date.now(),
            messageId,
            userId,
            emoji,
            createdAt: new Date().toISOString()
          }]);
        }
        
        setTimeout(loadReactions, 100);
      } else {
        toast.error('Failed to update reaction');
      }
    } catch (error) {
      console.error('Error handling reaction:', error);
      toast.error('Failed to update reaction');
    } finally {
      setLoading(false);
    }
  };

  const groupedReactions = reactions.reduce((acc, reaction) => {
    if (!acc[reaction.emoji]) {
      acc[reaction.emoji] = [];
    }
    acc[reaction.emoji].push(reaction);
    return acc;
  }, {} as Record<string, Reaction[]>);

  useEffect(() => {
    loadReactions();
  }, [messageId, userId]);

  useEffect(() => {
    if (!messageId) return;

    const channel = supabase
      .channel(`reactions_${messageId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'voice_reactions',
        filter: `voice_message_id=eq.${messageId}`
      }, () => {
        loadReactions();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [messageId]);

  const uniqueUsers = [...new Set(reactions.map(r => r.userId))];
  
  const handleShowReactions = () => {
    if (isMobile) {
      setShowReactionsModal(true);
    }
  };

  return (
    <TooltipProvider delayDuration={300}>
      <div ref={containerRef} className="flex flex-wrap items-center gap-2 relative">
        {/* Floating emojis */}
        {floatingEmojis.map((floatingEmoji) => (
          <div
            key={floatingEmoji.id}
            className="absolute pointer-events-none z-50 text-2xl animate-float-up"
            style={{
              left: floatingEmoji.x,
              top: floatingEmoji.y,
              animation: 'float-up 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards'
            }}
          >
            {floatingEmoji.emoji}
          </div>
        ))}

        {/* Reaction buttons */}
        {Object.entries(groupedReactions).map(([emoji, reactionList]) => (
          <Button
            key={emoji}
            variant={userReaction === emoji ? "default" : "outline"}
            size="sm"
            className="h-8 px-2 text-xs flex items-center gap-1 rounded-full hover:scale-105 transition-transform"
            onClick={(e) => handleReaction(emoji, e)}
            disabled={loading}
          >
            <span className="text-lg">{emoji}</span>
            <span className="bg-background/20 rounded-full px-1.5 text-xs font-medium">
              {reactionList.length}
            </span>
          </Button>
        ))}

        {/* Add reaction button */}
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="h-8 px-2 rounded-full flex items-center gap-1"
              disabled={loading}
            >
              <Plus size={16} />
              <span className="text-xs">React</span>
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-2">
            <div className="grid grid-cols-6 gap-1 max-w-lg">
              {REACTION_EMOJIS.map(({ emoji, label }) => (
                <Button
                  key={emoji}
                  variant={userReaction === emoji ? "default" : "ghost"}
                  size="sm"
                  className="h-auto p-2 flex flex-col items-center rounded-lg aspect-square"
                  onClick={(e) => handleReaction(emoji, e)}
                  title={label}
                  disabled={loading}
                >
                  <span className="text-lg">{emoji}</span>
                </Button>
              ))}
            </div>
          </PopoverContent>
        </Popover>

        {/* User avatars for reactions - positioned after add reaction button */}
        {reactions.length > 0 && (
          <>
            {/* Desktop: Show tooltip */}
            {!isMobile && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center gap-1 cursor-pointer">
                    {/* Show first 5 user avatars with proper profile pictures */}
                    <div className="flex -space-x-2">
                      {uniqueUsers.slice(0, 5).map((userId, index) => {
                        const profile = userProfiles[userId];
                        return (
                          <Avatar
                            key={userId}
                            className="w-6 h-6 border-2 border-background"
                            style={{ zIndex: 5 - index }}
                          >
                            <AvatarImage src={profile?.avatar_url || ''} alt={profile?.display_name || 'User'} />
                            <AvatarFallback className="text-xs">
                              {profile?.display_name?.substring(0, 2).toUpperCase() || 
                               profile?.username?.substring(0, 2).toUpperCase() ||
                               userId.substring(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                        );
                      })}
                    </div>
                    
                    {/* Show +X for additional reactions */}
                    {uniqueUsers.length > 5 && (
                      <div className="w-6 h-6 rounded-full bg-muted border-2 border-background flex items-center justify-center text-xs font-medium">
                        +{uniqueUsers.length - 5}
                      </div>
                    )}
                  </div>
                </TooltipTrigger>
                <TooltipContent 
                  side="top"
                  className="max-w-xs"
                >
                  <div className="space-y-1">
                    {Object.entries(groupedReactions).map(([emoji, reactionList]) => (
                      <div key={emoji} className="flex items-center gap-2 text-sm">
                        <span className="text-base">{emoji}</span>
                        <span className="text-muted-foreground">
                          {reactionList.map(r => {
                            const profile = userProfiles[r.userId];
                            return profile?.display_name || profile?.username || `User ${r.userId.substring(0, 8)}`;
                          }).join(', ')}
                        </span>
                      </div>
                    ))}
                  </div>
                </TooltipContent>
              </Tooltip>
            )}

            {/* Mobile: Show clickable avatars */}
            {isMobile && (
              <div className="flex items-center gap-1 cursor-pointer" onClick={handleShowReactions}>
                {/* Show first 5 user avatars with proper profile pictures */}
                <div className="flex -space-x-2">
                  {uniqueUsers.slice(0, 5).map((userId, index) => {
                    const profile = userProfiles[userId];
                    return (
                      <Avatar
                        key={userId}
                        className="w-6 h-6 border-2 border-background"
                        style={{ zIndex: 5 - index }}
                      >
                        <AvatarImage src={profile?.avatar_url || ''} alt={profile?.display_name || 'User'} />
                        <AvatarFallback className="text-xs">
                          {profile?.display_name?.substring(0, 2).toUpperCase() || 
                           profile?.username?.substring(0, 2).toUpperCase() ||
                           userId.substring(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                    );
                  })}
                </div>
                
                {/* Show +X for additional reactions */}
                {uniqueUsers.length > 5 && (
                  <div className="w-6 h-6 rounded-full bg-muted border-2 border-background flex items-center justify-center text-xs font-medium">
                    +{uniqueUsers.length - 5}
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </div>

      {/* Mobile Modal for showing reactions */}
      <Dialog open={showReactionsModal} onOpenChange={setShowReactionsModal}>
        <DialogContent className="max-w-sm mx-auto">
          <DialogHeader>
            <DialogTitle>Reactions</DialogTitle>
          </DialogHeader>
          <div className="max-h-64 overflow-y-auto space-y-3">
            {Object.entries(groupedReactions).map(([emoji, reactionList]) => (
              <div key={emoji} className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="text-lg">{emoji}</span>
                  <span className="text-sm font-medium">{reactionList.length}</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {reactionList.map((reaction) => {
                    const profile = userProfiles[reaction.userId];
                    return (
                      <div key={reaction.id} className="flex items-center gap-2 bg-muted/50 rounded-full px-2 py-1">
                        <Avatar className="w-5 h-5">
                          <AvatarImage src={profile?.avatar_url || ''} alt={profile?.display_name || 'User'} />
                          <AvatarFallback className="text-xs">
                            {profile?.display_name?.substring(0, 2).toUpperCase() || 
                             profile?.username?.substring(0, 2).toUpperCase() ||
                             reaction.userId.substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-xs">
                          {profile?.display_name || profile?.username || `User ${reaction.userId.substring(0, 8)}`}
                        </span>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    </TooltipProvider>
  );
}