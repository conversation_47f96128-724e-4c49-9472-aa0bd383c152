
import { v4 as uuidv4 } from 'uuid';
import { MediaFile } from '@/components/MediaUploader';

export type ChannelMember = {
  address: string;
  joinedAt: Date;
  role: 'owner' | 'moderator' | 'member';
};

export type ChannelMessage = {
  id: string;
  userAddress: string;
  timestamp: Date;
  transcript: string;
  audioUrl: string;
  duration: number;
  channelId: string;
  parentId?: string;
  isReply?: boolean;
  replies?: ChannelMessage[];
  media?: MediaFile[];
};

export type Channel = {
  id: string;
  name: string;
  description: string;
  coverImageUrl?: string;
  isPrivate: boolean;
  createdBy: string;
  createdAt: Date;
  members: ChannelMember[];
  messages: ChannelMessage[];
  tags: string[];
  rules?: string;
};

export type ChannelInvite = {
  id: string;
  channelId: string;
  code: string;
  createdBy: string;
  createdAt: Date;
  expiresAt?: Date;
  maxUses?: number;
  uses: number;
};

export function createDefaultChannel(
  id: string,
  name: string,
  description: string,
  creatorAddress: string
): Channel {
  return {
    id,
    name,
    description,
    isPrivate: false,
    createdBy: creatorAddress,
    createdAt: new Date(),
    members: [
      {
        address: creatorAddress,
        joinedAt: new Date(),
        role: 'owner'
      }
    ],
    messages: [],
    tags: []
  };
}

export function generateInviteCode(): string {
  // Create a 10-character alphanumeric code
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let code = '';
  for (let i = 0; i < 10; i++) {
    code += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  
  // Format as XXXXX-XXXXX
  return `${code.substring(0, 5)}-${code.substring(5, 10)}`;
}

export function createChannelInvite(
  channelId: string,
  creatorAddress: string,
  maxUses?: number,
  expiresInDays?: number
): ChannelInvite {
  const invite: ChannelInvite = {
    id: uuidv4(),
    channelId,
    code: generateInviteCode(),
    createdBy: creatorAddress,
    createdAt: new Date(),
    uses: 0
  };
  
  if (maxUses !== undefined && maxUses > 0) {
    invite.maxUses = maxUses;
  }
  
  if (expiresInDays !== undefined && expiresInDays > 0) {
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + expiresInDays);
    invite.expiresAt = expiryDate;
  }
  
  return invite;
}
