import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current file path
const __filename = fileURLToPath(import.meta.url);
// Get the current directory
const __dirname = path.dirname(__filename);

// Directories to search
const directories = [
  'src/components',
  'src/contexts',
  'src/services',
  'src/utils',
  'src/hooks',
  'src/pages'
];

// Files to ignore
const ignoreFiles = [
  'ceramic-shim.ts',
  'ceramic-client.ts',
  'ceramic-client.js'
];

// Patterns to look for
const patterns = [
  'ceramic',
  'Ceramic',
  'CERAMIC',
  'TileDocument',
  'orbis',
  'Orbis'
];

// Function to search for patterns in a file
function searchFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');

    // Check if any pattern is found in the file
    const found = patterns.some(pattern => content.includes(pattern));

    if (found) {
      console.log(`Found Ceramic references in: ${filePath}`);
    }

    return found;
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error);
    return false;
  }
}

// Function to recursively search directories
function searchDirectory(dir) {
  const files = fs.readdirSync(dir);

  const results = [];

  for (const file of files) {
    const filePath = path.join(dir, file);

    // Skip ignored files
    if (ignoreFiles.includes(file)) {
      continue;
    }

    const stats = fs.statSync(filePath);

    if (stats.isDirectory()) {
      // Recursively search subdirectories
      results.push(...searchDirectory(filePath));
    } else if (stats.isFile() &&
      (file.endsWith('.js') || file.endsWith('.jsx') ||
        file.endsWith('.ts') || file.endsWith('.tsx'))) {
      // Search JavaScript/TypeScript files
      if (searchFile(filePath)) {
        results.push(filePath);
      }
    }
  }

  return results;
}

// Main function
function main() {
  console.log('Searching for Ceramic references...');

  const results = [];

  for (const dir of directories) {
    try {
      // Use path relative to project root (one level up from scripts directory)
      const dirPath = path.join(path.dirname(__dirname), dir);
      if (fs.existsSync(dirPath)) {
        results.push(...searchDirectory(dirPath));
      }
    } catch (error) {
      console.error(`Error searching directory ${dir}:`, error);
    }
  }

  console.log('\nFound Ceramic references in the following files:');
  results.forEach(file => console.log(`- ${file}`));
  console.log(`\nTotal files with Ceramic references: ${results.length}`);
}

// Run the script
main();
