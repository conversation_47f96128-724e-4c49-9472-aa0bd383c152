import { UserProfile } from '@/types/user-profile';

/**
 * Returns a default user profile with empty values
 */
export const getDefaultProfile = (): UserProfile => {
  return {
    address: '',
    walletAddress: '',
    displayName: 'Anonymous User',
    username: 'anonymous',
    bio: '',
    profileImageUrl: '',
    coverImageUrl: '',
    socialLinks: {
      twitter: '',
      github: '',
      website: ''
    },
    stats: {
      posts: 0,
      followers: 0,
      following: 0,
      likes: 0,
      tips: 0
    },
    verification: {
      isVerified: false,
      type: null,
      verifiedAt: null
    },
    createdAt: new Date(),
    updatedAt: new Date()
  };
};

/**
 * Generates a random username based on a prefix and random numbers
 */
export const generateRandomUsername = (prefix: string = 'user'): string => {
  const randomNum = Math.floor(Math.random() * 10000);
  return `${prefix}${randomNum}`;
};

/**
 * Generates a display name from a wallet address
 */
export const generateDisplayNameFromAddress = (address: string): string => {
  if (!address) return 'Anonymous User';
  
  // Take the first 4 and last 4 characters of the address
  const start = address.substring(0, 4);
  const end = address.substring(address.length - 4);
  
  return `${start}...${end}`;
};

/**
 * Creates a profile with some default values based on the address
 */
export const createDefaultProfile = (address: string): UserProfile => {
  const displayName = generateDisplayNameFromAddress(address);
  const username = generateRandomUsername('user');
  
  return {
    address: address,
    walletAddress: address,
    displayName,
    username,
    bio: '',
    profileImageUrl: '',
    coverImageUrl: '',
    socialLinks: {
      twitter: '',
      github: '',
      website: ''
    },
    stats: {
      posts: 0,
      followers: 0,
      following: 0,
      likes: 0,
      tips: 0
    },
    verification: {
      isVerified: false,
      type: null,
      verifiedAt: null
    },
    createdAt: new Date(),
    updatedAt: new Date()
  };
};

/**
 * Merges a partial profile update with an existing profile
 */
export const mergeProfileUpdate = (
  existingProfile: UserProfile, 
  update: Partial<UserProfile>
): UserProfile => {
  return {
    ...existingProfile,
    ...update,
    socialLinks: {
      ...existingProfile.socialLinks,
      ...update.socialLinks
    },
    stats: {
      ...existingProfile.stats,
      ...update.stats
    },
    verification: {
      ...existingProfile.verification,
      ...update.verification
    },
    updatedAt: new Date()
  };
};
