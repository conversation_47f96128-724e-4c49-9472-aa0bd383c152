
/**
 * Audio Storage Service
 * 
 * This service handles the storage and retrieval of audio recordings
 * using Supabase Storage.
 */

import { v4 as uuidv4 } from 'uuid';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/sonner';

const AUDIO_BUCKET = 'audio';

/**
 * Upload audio to Supabase storage with improved reliability
 * @param audioBlob The audio blob
 * @param userId The user ID
 * @returns URL of the uploaded audio
 */
export async function uploadAudio(audioBlob: Blob, userId: string): Promise<string> {
  try {
    console.log('Uploading audio to Supabase storage...');
    
    // Generate a unique file name
    const fileExtension = getExtensionFromBlob(audioBlob);
    const fileName = `${userId}/${uuidv4()}.${fileExtension}`;
    
    console.log(`Uploading file: ${fileName} (${audioBlob.size} bytes, type: ${audioBlob.type})`);
    
    // Retry logic for more reliable uploads
    let retries = 3;
    let uploadedUrl = null;
    
    while (retries > 0 && !uploadedUrl) {
      try {
        // Upload to Supabase with optimized settings for larger files
        const { data, error } = await supabase.storage
          .from(AUDIO_BUCKET)
          .upload(fileName, audioBlob, {
            contentType: audioBlob.type,
            cacheControl: '3600',
            upsert: true,
            duplex: 'half' // Improve upload for large files
          });

        if (error) {
          console.error('Error uploading audio to Supabase:', error);
          retries--;
          if (retries > 0) {
            console.log(`Retrying upload... (${retries} attempts left)`);
            // Wait before retrying
            await new Promise(resolve => setTimeout(resolve, 1000));
          } else {
            throw error;
          }
        } else {
          // Get the public URL
          const { data: publicUrlData } = supabase.storage
            .from(AUDIO_BUCKET)
            .getPublicUrl(fileName);

          uploadedUrl = publicUrlData.publicUrl;
          console.log('Audio successfully uploaded to Supabase:', uploadedUrl);
          break;
        }
      } catch (retryError) {
        console.error(`Upload attempt failed (${retries} left):`, retryError);
        retries--;
        if (retries === 0) {
          throw retryError;
        }
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    if (!uploadedUrl) {
      throw new Error('Failed to upload audio after multiple attempts');
    }
    
    return uploadedUrl;
  } catch (error) {
    console.error('Error uploading audio:', error);
    toast.error('Failed to upload audio. Please try again.');
    throw new Error('Failed to upload audio to Supabase storage');
  }
}

/**
 * Get audio from URL
 * @param audioUrl The audio URL
 * @returns The audio blob
 */
export async function getAudioFromUrl(audioUrl: string): Promise<Blob> {
  try {
    console.log('Getting audio from URL:', audioUrl);
    
    // Check if the URL is from Supabase
    if (audioUrl.includes('storage.googleapis.com') || audioUrl.includes('supabase')) {
      // Extract file path from URL
      const filePathMatch = audioUrl.match(/\/storage\/v1\/object\/public\/audio\/(.*)/);
      
      if (filePathMatch && filePathMatch[1]) {
        const filePath = decodeURIComponent(filePathMatch[1]);
        
        // Try to fetch from Supabase
        const { data, error } = await supabase.storage
          .from(AUDIO_BUCKET)
          .download(filePath);
          
        if (error) {
          console.error('Error downloading audio from Supabase:', error);
          throw error;
        }
        
        console.log('Successfully downloaded audio from Supabase storage');
        return data;
      }
    }
    
    // If not a Supabase URL or extraction failed, try to fetch directly
    console.log('Fetching audio directly from URL:', audioUrl);
    const response = await fetch(audioUrl, {
      headers: {
        'Accept': 'audio/*',
        'Cache-Control': 'no-cache'
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch audio: ${response.status} ${response.statusText}`);
    }
    
    return await response.blob();
  } catch (error) {
    console.error('Error getting audio from URL:', error);
    throw error;
  }
}

/**
 * Get the correct file extension from a blob
 * @param blob The audio blob
 * @returns The file extension
 */
function getExtensionFromBlob(blob: Blob): string {
  switch (blob.type) {
    case 'audio/webm':
      return 'webm';
    case 'audio/mp4':
      return 'm4a';
    case 'audio/mpeg':
      return 'mp3';
    case 'audio/wav':
      return 'wav';
    case 'audio/ogg':
      return 'ogg';
    default:
      return 'webm';
  }
}

/**
 * Ensure audio buckets exist in Supabase
 * This should be called on app initialization
 */
export async function ensureAudioBuckets(): Promise<void> {
  try {
    // Check if audio bucket exists
    const { data: buckets, error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.error('Error checking buckets:', error);
      return;
    }
    
    // Create audio bucket if it doesn't exist
    if (!buckets?.find(b => b.name === AUDIO_BUCKET)) {
      try {
        const { error: createError } = await supabase.storage.createBucket(AUDIO_BUCKET, {
          public: true,
          fileSizeLimit: 50000000, // 50MB
          allowedMimeTypes: ['audio/webm', 'audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/ogg']
        });
        
        if (createError) {
          console.error('Error creating audio bucket:', createError);
        } else {
          console.log('Audio bucket created successfully');
        }
      } catch (bucketError) {
        console.error('Error creating audio bucket:', bucketError);
      }
    }
  } catch (error) {
    console.error('Error ensuring audio buckets:', error);
  }
}

export default {
  uploadAudio,
  getAudioFromUrl,
  ensureAudioBuckets
};
