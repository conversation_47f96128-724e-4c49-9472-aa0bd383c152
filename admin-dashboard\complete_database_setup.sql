-- COMPREHENSIVE DATABASE SETUP SCRIPT
-- This script sets up all necessary tables, functions, and RLS policies for the admin dashboard

-- =============================================
-- STEP 1: DISABLE RLS TEMPORARILY
-- =============================================
ALTER TABLE IF EXISTS admin_profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS admin_settings DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS content_reports DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS verification DISABLE ROW LEVEL SECURITY;

-- =============================================
-- STEP 2: CREATE OR UPDATE ADMIN_PROFILES TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS admin_profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL UNIQUE,
  role TEXT NOT NULL CHECK (role IN ('admin', 'super_admin')),
  display_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_login TIMESTAMP WITH TIME ZONE
);

-- Create a trigger to update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger on admin_profiles
DROP TRIGGER IF EXISTS update_admin_profiles_updated_at ON admin_profiles;
CREATE TRIGGER update_admin_profiles_updated_at
BEFORE UPDATE ON admin_profiles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- STEP 3: CREATE ADMIN_SETTINGS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS admin_settings (
  id TEXT PRIMARY KEY,
  settings JSONB NOT NULL DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default settings if they don't exist
INSERT INTO admin_settings (id, settings)
VALUES ('global', '{
  "session_timeout_hours": 24,
  "max_login_attempts": 5,
  "require_2fa_for_super_admin": false,
  "password_expiry_days": 90,
  "audit_log_retention_days": 365
}'::jsonb)
ON CONFLICT (id) DO NOTHING;

-- =============================================
-- STEP 4: CREATE VERIFICATION TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS verification (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  verification_type TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create a trigger on verification
DROP TRIGGER IF EXISTS update_verification_updated_at ON verification;
CREATE TRIGGER update_verification_updated_at
BEFORE UPDATE ON verification
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- STEP 5: CREATE CONTENT_REPORTS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS content_reports (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  reporter_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  reported_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  content_id TEXT NOT NULL,
  content_type TEXT NOT NULL,
  reason TEXT NOT NULL,
  details TEXT,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'dismissed', 'removed')),
  reported_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  resolved_at TIMESTAMP WITH TIME ZONE,
  resolved_by UUID REFERENCES admin_profiles(id) ON DELETE SET NULL,
  resolution_notes TEXT
);

-- =============================================
-- STEP 6: CREATE AUDIT_LOGS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  admin_id UUID REFERENCES admin_profiles(id),
  action TEXT NOT NULL,
  entity_type TEXT NOT NULL,
  entity_id TEXT,
  details JSONB,
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- STEP 7: ADD LAST_LOGIN COLUMN TO PROFILES IF MISSING
-- =============================================
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'last_login'
  ) THEN
    ALTER TABLE profiles ADD COLUMN last_login TIMESTAMP WITH TIME ZONE;
  END IF;
END $$;

-- =============================================
-- STEP 8: CREATE ADMIN FUNCTIONS
-- =============================================

-- Function to create the first super admin
CREATE OR REPLACE FUNCTION create_first_super_admin(
  p_user_id UUID,
  p_email TEXT,
  p_display_name TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  v_count INTEGER;
  v_admin_id UUID;
BEGIN
  -- Check if the user exists in auth.users
  SELECT COUNT(*) INTO v_count FROM auth.users WHERE id = p_user_id;
  
  IF v_count = 0 THEN
    RAISE EXCEPTION 'User with ID % does not exist in auth.users', p_user_id;
  END IF;
  
  -- Check if there are any existing admins
  SELECT COUNT(*) INTO v_count FROM admin_profiles;
  
  -- Insert the new admin
  INSERT INTO admin_profiles (
    id,
    email,
    role,
    display_name,
    created_at,
    updated_at,
    last_login
  ) VALUES (
    p_user_id,
    p_email,
    'super_admin',
    COALESCE(p_display_name, p_email),
    NOW(),
    NOW(),
    NOW()
  )
  ON CONFLICT (id) DO UPDATE SET
    role = 'super_admin',
    updated_at = NOW(),
    last_login = NOW()
  RETURNING id INTO v_admin_id;
  
  RETURN v_admin_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log admin actions
CREATE OR REPLACE FUNCTION log_admin_action(
  p_action TEXT,
  p_entity_type TEXT,
  p_entity_id TEXT,
  p_details JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  v_log_id UUID;
  v_ip_address TEXT;
  v_user_agent TEXT;
BEGIN
  -- Get client info from request headers
  v_ip_address := current_setting('request.headers', true)::json->'x-forwarded-for';
  v_user_agent := current_setting('request.headers', true)::json->'user-agent';
  
  -- Insert audit log
  INSERT INTO audit_logs (
    admin_id,
    action,
    entity_type,
    entity_id,
    details,
    ip_address,
    user_agent
  ) VALUES (
    auth.uid(),
    p_action,
    p_entity_type,
    p_entity_id,
    p_details,
    v_ip_address,
    v_user_agent
  ) RETURNING id INTO v_log_id;
  
  RETURN v_log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get audit logs with admin details
CREATE OR REPLACE FUNCTION get_audit_logs(
  p_limit INTEGER DEFAULT 100,
  p_offset INTEGER DEFAULT 0,
  p_action TEXT DEFAULT NULL,
  p_entity_type TEXT DEFAULT NULL,
  p_admin_id UUID DEFAULT NULL,
  p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  admin_id UUID,
  admin_email TEXT,
  admin_role TEXT,
  action TEXT,
  entity_type TEXT,
  entity_id TEXT,
  details JSONB,
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    al.id,
    al.admin_id,
    ap.email AS admin_email,
    ap.role AS admin_role,
    al.action,
    al.entity_type,
    al.entity_id,
    al.details,
    al.ip_address,
    al.user_agent,
    al.created_at
  FROM
    audit_logs al
  LEFT JOIN
    admin_profiles ap ON al.admin_id = ap.id
  WHERE
    (p_action IS NULL OR al.action = p_action) AND
    (p_entity_type IS NULL OR al.entity_type = p_entity_type) AND
    (p_admin_id IS NULL OR al.admin_id = p_admin_id) AND
    (p_start_date IS NULL OR al.created_at >= p_start_date) AND
    (p_end_date IS NULL OR al.created_at <= p_end_date)
  ORDER BY
    al.created_at DESC
  LIMIT
    p_limit
  OFFSET
    p_offset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- STEP 9: CREATE HELPER FUNCTIONS
-- =============================================

-- Function to check if a user is an admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM admin_profiles 
    WHERE id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if a user is a super admin
CREATE OR REPLACE FUNCTION is_super_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM admin_profiles 
    WHERE id = auth.uid() 
    AND role = 'super_admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- STEP 10: SET UP RLS POLICIES
-- =============================================

-- Enable RLS on all tables
ALTER TABLE admin_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE verification ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- RLS policies for admin_profiles
CREATE POLICY "Admins can view all admin profiles"
ON admin_profiles FOR SELECT
USING (is_admin());

CREATE POLICY "Admins can update their own profile"
ON admin_profiles FOR UPDATE
USING (auth.uid() = id)
WITH CHECK (
  auth.uid() = id AND
  (
    (SELECT role FROM admin_profiles WHERE id = auth.uid()) = role
    OR
    is_super_admin()
  )
);

CREATE POLICY "Super admins can update any profile"
ON admin_profiles FOR UPDATE
USING (is_super_admin());

CREATE POLICY "Super admins can delete profiles"
ON admin_profiles FOR DELETE
USING (is_super_admin() AND id != auth.uid());

CREATE POLICY "Super admins can insert profiles"
ON admin_profiles FOR INSERT
WITH CHECK (is_super_admin());

-- RLS policies for admin_settings
CREATE POLICY "Admins can view settings"
ON admin_settings FOR SELECT
USING (is_admin());

CREATE POLICY "Super admins can update settings"
ON admin_settings FOR UPDATE
USING (is_super_admin());

-- RLS policies for content_reports
CREATE POLICY "Admins can view content reports"
ON content_reports FOR SELECT
USING (is_admin());

CREATE POLICY "Admins can update content reports"
ON content_reports FOR UPDATE
USING (is_admin());

-- RLS policies for verification
CREATE POLICY "Admins can view verification"
ON verification FOR SELECT
USING (is_admin());

CREATE POLICY "Admins can update verification"
ON verification FOR UPDATE
USING (is_admin());

CREATE POLICY "Admins can insert verification"
ON verification FOR INSERT
WITH CHECK (is_admin());

CREATE POLICY "Admins can delete verification"
ON verification FOR DELETE
USING (is_admin());

-- RLS policies for audit_logs
CREATE POLICY "Admins can read audit logs"
ON audit_logs FOR SELECT
USING (is_admin());

CREATE POLICY "Super admins can delete audit logs"
ON audit_logs FOR DELETE
USING (is_super_admin());

-- =============================================
-- STEP 11: INSTRUCTIONS FOR FIRST ADMIN
-- =============================================
-- After running this script, create your first super admin by running:
-- SELECT create_first_super_admin('your-user-id', 'your-email', 'Your Name');
-- Replace 'your-user-id' with your Supabase user ID (the UUID from auth.users)
