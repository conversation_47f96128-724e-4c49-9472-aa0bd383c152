import { supabase } from '@/integrations/supabase/client';

export interface VoiceChat {
  id: string;
  name: string;
  description?: string;
  type: 'direct' | 'group' | 'wave' | 'space';
  creator_id: string;
  is_private: boolean;
  emotional_tone: 'calm' | 'excited' | 'focused' | 'creative' | 'mysterious';
  settings: any;
  avatar_url?: string;
  invite_code?: string;
  max_participants: number;
  created_at: string;
  updated_at: string;
  participants?: VoiceChatParticipant[];
  last_message?: VoiceChatMessage;
  unread_count?: number;
  participant_count?: number;
  admin_count?: number;
}

export interface VoiceChatParticipant {
  id: string;
  chat_id: string;
  profile_id: string;
  role: 'admin' | 'moderator' | 'member';
  joined_at: string;
  added_by?: string;
  last_seen: string;
  is_muted: boolean;
  is_banned: boolean;
  left_at?: string;
  profile?: {
    display_name?: string;
    username?: string;
    avatar_url?: string;
  };
}

export interface GroupInvite {
  id: string;
  chat_id: string;
  invite_code: string;
  created_by: string;
  max_uses?: number;
  current_uses: number;
  expires_at: string;
  is_active: boolean;
  created_at: string;
}

export interface GroupNotification {
  id: string;
  chat_id: string;
  user_id: string;
  type: 'added_to_group' | 'removed_from_group' | 'promoted' | 'demoted' | 'invite_received';
  message: string;
  data: any;
  is_read: boolean;
  created_at: string;
}

export interface VoiceChatMessage {
  id: string;
  chat_id: string;
  sender_id: string;
  message_type: 'text' | 'voice' | 'emotion' | 'reaction' | 'system';
  content?: string;
  voice_url?: string;
  voice_duration?: number;
  voice_transcript?: string;
  emotion_data?: any;
  reply_to_id?: string;
  is_edited: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  sender_profile?: {
    display_name?: string;
    username?: string;
    avatar_url?: string;
  };
}

export interface EmotionalWave {
  id: string;
  sender_id: string;
  chat_id: string;
  emotion: string;
  intensity: number;
  color: string;
  wave_data: any;
  created_at: string;
  sender_profile?: {
    display_name?: string;
    username?: string;
  };
}

export const voiceChatService = {
  // Create a new chat with enhanced group features
  async createChat(data: {
    name: string;
    description?: string;
    type: 'direct' | 'group' | 'wave' | 'space';
    creator_id: string;
    is_private?: boolean;
    participants?: string[];
  }): Promise<VoiceChat | null> {
    try {
      // Generate invite code for groups
      const inviteCode = data.type === 'group' ? this.generateInviteCode() : null;

      // Create the chat
      const { data: chatData, error: chatError } = await supabase
        .from('voice_chats')
        .insert({
          name: data.name,
          description: data.description,
          type: data.type,
          creator_id: data.creator_id,
          is_private: data.is_private || false,
          invite_code: inviteCode,
          max_participants: data.type === 'group' ? 100 : (data.type === 'space' ? 1000 : 2),
        })
        .select()
        .single();

      if (chatError) {
        console.error('Error creating chat:', chatError);
        return null;
      }

      // Add creator as admin
      await supabase
        .from('voice_chat_participants')
        .insert({
          chat_id: chatData.id,
          profile_id: data.creator_id,
          role: 'admin',
        });

      // Add other participants if provided
      if (data.participants && data.participants.length > 0) {
        // For now, we'll treat participant emails as profile IDs
        // In a real app, you'd look up users by email first
        const participantInserts = data.participants
          .filter(id => id && id !== data.creator_id) // Avoid duplicates
          .map(profileId => ({
            chat_id: chatData.id,
            profile_id: profileId,
            role: 'member',
          }));

        if (participantInserts.length > 0) {
          const { error: participantError } = await supabase
            .from('voice_chat_participants')
            .insert(participantInserts);

          if (participantError) {
            console.error('Error adding participants:', participantError);
            // Don't fail the chat creation, just log the error
          }
        }
      }

      return chatData;
    } catch (error) {
      console.error('Error creating chat:', error);
      return null;
    }
  },

  // Get user's chats
  async getUserChats(userId: string): Promise<VoiceChat[]> {
    try {
      const { data, error } = await supabase
        .from('voice_chats')
        .select(`
          *,
          voice_chat_participants!inner(profile_id),
          voice_chat_messages(
            id,
            content,
            voice_transcript,
            message_type,
            created_at,
            sender_id,
            sender_profile:profiles!voice_chat_messages_sender_id_fkey(display_name, username)
          )
        `)
        .eq('voice_chat_participants.profile_id', userId)
        .order('updated_at', { ascending: false });

      if (error) {
        console.error('Error fetching chats:', error);
        return [];
      }

      // Process the data to get last message and unread count
      const processedChats = data.map(chat => {
        const messages = chat.voice_chat_messages || [];
        const lastMessage = messages.length > 0 ? messages[messages.length - 1] : null;
        
        return {
          ...chat,
          last_message: lastMessage,
          unread_count: 0, // TODO: Implement unread count logic
        };
      });

      return processedChats;
    } catch (error) {
      console.error('Error fetching user chats:', error);
      return [];
    }
  },

  // Get chat messages
  async getChatMessages(chatId: string, limit: number = 50): Promise<VoiceChatMessage[]> {
    try {
      const { data, error } = await supabase
        .from('voice_chat_messages')
        .select(`
          *,
          sender_profile:profiles!voice_chat_messages_sender_id_fkey(display_name, username, avatar_url)
        `)
        .eq('chat_id', chatId)
        .eq('is_deleted', false)
        .order('created_at', { ascending: true })
        .limit(limit);

      if (error) {
        console.error('Error fetching messages:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching messages:', error);
      return [];
    }
  },

  // Send a text message
  async sendTextMessage(chatId: string, senderId: string, content: string): Promise<VoiceChatMessage | null> {
    try {
      const { data, error } = await supabase
        .from('voice_chat_messages')
        .insert({
          chat_id: chatId,
          sender_id: senderId,
          message_type: 'text',
          content: content,
        })
        .select(`
          *,
          sender_profile:profiles!voice_chat_messages_sender_id_fkey(display_name, username, avatar_url)
        `)
        .single();

      if (error) {
        console.error('Error sending message:', error);
        return null;
      }

      // Update chat's updated_at
      await supabase
        .from('voice_chats')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', chatId);

      return data;
    } catch (error) {
      console.error('Error sending message:', error);
      return null;
    }
  },

  // Send a voice message
  async sendVoiceMessage(
    chatId: string, 
    senderId: string, 
    voiceUrl: string, 
    duration: number, 
    transcript?: string
  ): Promise<VoiceChatMessage | null> {
    try {
      const { data, error } = await supabase
        .from('voice_chat_messages')
        .insert({
          chat_id: chatId,
          sender_id: senderId,
          message_type: 'voice',
          voice_url: voiceUrl,
          voice_duration: duration,
          voice_transcript: transcript,
        })
        .select(`
          *,
          sender_profile:profiles!voice_chat_messages_sender_id_fkey(display_name, username, avatar_url)
        `)
        .single();

      if (error) {
        console.error('Error sending voice message:', error);
        return null;
      }

      // Update chat's updated_at
      await supabase
        .from('voice_chats')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', chatId);

      return data;
    } catch (error) {
      console.error('Error sending voice message:', error);
      return null;
    }
  },

  // Send an emotional wave
  async sendEmotionalWave(
    chatId: string, 
    senderId: string, 
    emotion: string, 
    intensity: number = 50
  ): Promise<EmotionalWave | null> {
    try {
      // Generate a color based on emotion
      const emotionColors: { [key: string]: string } = {
        '💙': '#3b82f6',
        '🔥': '#ef4444', 
        '⚡': '#eab308',
        '🌟': '#a855f7',
        '🌊': '#06b6d4',
      };

      const color = emotionColors[emotion] || '#6b7280';

      const { data, error } = await supabase
        .from('emotional_waves')
        .insert({
          sender_id: senderId,
          chat_id: chatId,
          emotion: emotion,
          intensity: intensity,
          color: color,
          wave_data: { timestamp: Date.now() },
        })
        .select(`
          *,
          sender_profile:profiles!emotional_waves_sender_id_fkey(display_name, username)
        `)
        .single();

      if (error) {
        console.error('Error sending emotional wave:', error);
        return null;
      }

      // Also create a message for the wave
      await this.sendTextMessage(chatId, senderId, `${emotion} Sent an emotional wave`);

      return data;
    } catch (error) {
      console.error('Error sending emotional wave:', error);
      return null;
    }
  },

  // Get emotional waves for a chat
  async getEmotionalWaves(chatId: string, limit: number = 20): Promise<EmotionalWave[]> {
    try {
      const { data, error } = await supabase
        .from('emotional_waves')
        .select(`
          *,
          sender_profile:profiles!emotional_waves_sender_id_fkey(display_name, username)
        `)
        .eq('chat_id', chatId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching emotional waves:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching emotional waves:', error);
      return [];
    }
  },

  // Join a chat
  async joinChat(chatId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('voice_chat_participants')
        .insert({
          chat_id: chatId,
          profile_id: userId,
          role: 'member',
        });

      if (error) {
        console.error('Error joining chat:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error joining chat:', error);
      return false;
    }
  },

  // Leave a chat
  async leaveChat(chatId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('voice_chat_participants')
        .delete()
        .eq('chat_id', chatId)
        .eq('profile_id', userId);

      if (error) {
        console.error('Error leaving chat:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error leaving chat:', error);
      return false;
    }
  },

  // Get chat participants
  async getChatParticipants(chatId: string): Promise<VoiceChatParticipant[]> {
    try {
      const { data, error } = await supabase
        .from('voice_chat_participants')
        .select(`
          *,
          profile:profiles!voice_chat_participants_profile_id_fkey(display_name, username, avatar_url)
        `)
        .eq('chat_id', chatId);

      if (error) {
        console.error('Error fetching participants:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching participants:', error);
      return [];
    }
  },

  // Delete message for everyone
  async deleteMessageForEveryone(messageId: string, userId: string): Promise<boolean> {
    try {
      // Check if user is the sender or admin
      const { data: message, error: messageError } = await supabase
        .from('voice_chat_messages')
        .select('sender_id, chat_id')
        .eq('id', messageId)
        .single();

      if (messageError || !message) {
        console.error('Error fetching message:', messageError);
        return false;
      }

      // Check if user is sender or admin of the chat
      const { data: participant, error: participantError } = await supabase
        .from('voice_chat_participants')
        .select('role')
        .eq('chat_id', message.chat_id)
        .eq('profile_id', userId)
        .single();

      const isAuthorized = message.sender_id === userId || participant?.role === 'admin';

      if (!isAuthorized) {
        console.error('User not authorized to delete message');
        return false;
      }

      const { error } = await supabase
        .from('voice_chat_messages')
        .update({
          deleted_for_everyone: true,
          deleted_at: new Date().toISOString(),
          content: '[Message deleted]',
          voice_url: null,
          voice_transcript: null
        })
        .eq('id', messageId);

      if (error) {
        console.error('Error deleting message for everyone:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error deleting message for everyone:', error);
      return false;
    }
  },

  // Delete message for specific user only
  async deleteMessageForMe(messageId: string, userId: string): Promise<boolean> {
    try {
      // Get current deleted_for_users array
      const { data: message, error: fetchError } = await supabase
        .from('voice_chat_messages')
        .select('deleted_for_users')
        .eq('id', messageId)
        .single();

      if (fetchError) {
        console.error('Error fetching message:', fetchError);
        return false;
      }

      const deletedForUsers = message.deleted_for_users || [];
      if (!deletedForUsers.includes(userId)) {
        deletedForUsers.push(userId);
      }

      const { error } = await supabase
        .from('voice_chat_messages')
        .update({
          deleted_for_users: deletedForUsers
        })
        .eq('id', messageId);

      if (error) {
        console.error('Error deleting message for user:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error deleting message for user:', error);
      return false;
    }
  },

  // Get filtered messages (excluding deleted ones for current user)
  async getChatMessagesFiltered(chatId: string, userId: string, limit: number = 50): Promise<VoiceChatMessage[]> {
    try {
      const { data, error } = await supabase
        .from('voice_chat_messages')
        .select(`
          *,
          sender_profile:profiles!voice_chat_messages_sender_id_fkey(display_name, username, avatar_url)
        `)
        .eq('chat_id', chatId)
        .eq('deleted_for_everyone', false)
        .order('created_at', { ascending: true })
        .limit(limit);

      if (error) {
        console.error('Error fetching messages:', error);
        return [];
      }

      // Filter out messages deleted for this specific user
      const filteredMessages = (data || []).filter(message => {
        const deletedForUsers = message.deleted_for_users || [];
        return !deletedForUsers.includes(userId);
      });

      return filteredMessages;
    } catch (error) {
      console.error('Error fetching filtered messages:', error);
      return [];
    }
  },

  // =============================================
  // GROUP MANAGEMENT FUNCTIONS
  // =============================================

  // Generate invite code
  generateInviteCode(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  },

  // Get group details with participants
  async getGroupDetails(chatId: string): Promise<VoiceChat | null> {
    try {
      const { data, error } = await supabase
        .from('voice_chats')
        .select(`
          *,
          voice_chat_participants!inner(
            id,
            profile_id,
            role,
            joined_at,
            added_by,
            last_seen,
            is_muted,
            is_banned,
            left_at,
            profile:profiles!voice_chat_participants_profile_id_fkey(
              display_name,
              username,
              avatar_url
            )
          )
        `)
        .eq('id', chatId)
        .eq('voice_chat_participants.left_at', null)
        .single();

      if (error) {
        console.error('Error fetching group details:', error);
        return null;
      }

      return {
        ...data,
        participant_count: data.voice_chat_participants?.length || 0,
        admin_count: data.voice_chat_participants?.filter((p: any) => p.role === 'admin').length || 0,
      };
    } catch (error) {
      console.error('Error fetching group details:', error);
      return null;
    }
  },

  // Add user to group
  async addUserToGroup(chatId: string, userId: string, addedBy: string, role: 'member' | 'moderator' | 'admin' = 'member'): Promise<boolean> {
    try {
      // Check if user is already in group
      const { data: existing } = await supabase
        .from('voice_chat_participants')
        .select('id')
        .eq('chat_id', chatId)
        .eq('profile_id', userId)
        .eq('left_at', null)
        .single();

      if (existing) {
        console.log('User already in group');
        return false;
      }

      // Add user to group
      const { error: addError } = await supabase
        .from('voice_chat_participants')
        .insert({
          chat_id: chatId,
          profile_id: userId,
          role,
          added_by: addedBy,
        });

      if (addError) {
        console.error('Error adding user to group:', addError);
        return false;
      }

      // Create system message
      await this.createSystemMessage(chatId, addedBy, 'user_added', {
        added_user_id: userId,
        added_by: addedBy,
      });

      // Create notification for added user
      await this.createGroupNotification(chatId, userId, 'added_to_group', `You were added to the group`, {
        added_by: addedBy,
        chat_id: chatId,
      });

      return true;
    } catch (error) {
      console.error('Error adding user to group:', error);
      return false;
    }
  },

  // Remove user from group
  async removeUserFromGroup(chatId: string, userId: string, removedBy: string): Promise<boolean> {
    try {
      // Update participant record to mark as left
      const { error } = await supabase
        .from('voice_chat_participants')
        .update({ left_at: new Date().toISOString() })
        .eq('chat_id', chatId)
        .eq('profile_id', userId);

      if (error) {
        console.error('Error removing user from group:', error);
        return false;
      }

      // Create system message
      await this.createSystemMessage(chatId, removedBy, 'user_removed', {
        removed_user_id: userId,
        removed_by: removedBy,
      });

      // Create notification for removed user
      await this.createGroupNotification(chatId, userId, 'removed_from_group', `You were removed from the group`, {
        removed_by: removedBy,
        chat_id: chatId,
      });

      return true;
    } catch (error) {
      console.error('Error removing user from group:', error);
      return false;
    }
  },

  // User leaves group
  async leaveGroup(chatId: string, userId: string): Promise<boolean> {
    try {
      // Update participant record to mark as left
      const { error } = await supabase
        .from('voice_chat_participants')
        .update({ left_at: new Date().toISOString() })
        .eq('chat_id', chatId)
        .eq('profile_id', userId);

      if (error) {
        console.error('Error leaving group:', error);
        return false;
      }

      // Create system message
      await this.createSystemMessage(chatId, userId, 'user_left', {
        left_user_id: userId,
      });

      return true;
    } catch (error) {
      console.error('Error leaving group:', error);
      return false;
    }
  },

  // Promote user to admin/moderator
  async promoteUser(chatId: string, userId: string, newRole: 'admin' | 'moderator', promotedBy: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('voice_chat_participants')
        .update({ role: newRole })
        .eq('chat_id', chatId)
        .eq('profile_id', userId);

      if (error) {
        console.error('Error promoting user:', error);
        return false;
      }

      // Create system message
      await this.createSystemMessage(chatId, promotedBy, 'user_promoted', {
        promoted_user_id: userId,
        new_role: newRole,
        promoted_by: promotedBy,
      });

      // Create notification
      await this.createGroupNotification(chatId, userId, 'promoted', `You were promoted to ${newRole}`, {
        new_role: newRole,
        promoted_by: promotedBy,
        chat_id: chatId,
      });

      return true;
    } catch (error) {
      console.error('Error promoting user:', error);
      return false;
    }
  },

  // Create invite link
  async createInviteLink(chatId: string, createdBy: string, maxUses?: number, expiresInDays: number = 7): Promise<GroupInvite | null> {
    try {
      const inviteCode = this.generateInviteCode();
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + expiresInDays);

      const { data, error } = await supabase
        .from('group_invites')
        .insert({
          chat_id: chatId,
          invite_code: inviteCode,
          created_by: createdBy,
          max_uses: maxUses,
          expires_at: expiresAt.toISOString(),
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating invite link:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error creating invite link:', error);
      return null;
    }
  },

  // Join group via invite code
  async joinGroupViaInvite(inviteCode: string, userId: string): Promise<{ success: boolean; chatId?: string; error?: string }> {
    try {
      // Get invite details
      const { data: invite, error: inviteError } = await supabase
        .from('group_invites')
        .select(`
          *,
          voice_chats(name, type, is_private)
        `)
        .eq('invite_code', inviteCode)
        .eq('is_active', true)
        .single();

      if (inviteError || !invite) {
        return { success: false, error: 'Invalid or expired invite code' };
      }

      // Check if invite is expired
      if (new Date(invite.expires_at) < new Date()) {
        return { success: false, error: 'Invite code has expired' };
      }

      // Check if invite has reached max uses
      if (invite.max_uses && invite.current_uses >= invite.max_uses) {
        return { success: false, error: 'Invite code has reached maximum uses' };
      }

      // Add user to group
      const added = await this.addUserToGroup(invite.chat_id, userId, invite.created_by);

      if (!added) {
        return { success: false, error: 'Failed to join group' };
      }

      // Update invite usage count
      await supabase
        .from('group_invites')
        .update({ current_uses: invite.current_uses + 1 })
        .eq('id', invite.id);

      return { success: true, chatId: invite.chat_id };
    } catch (error) {
      console.error('Error joining group via invite:', error);
      return { success: false, error: 'Failed to join group' };
    }
  },

  // Get group invites
  async getGroupInvites(chatId: string): Promise<GroupInvite[]> {
    try {
      const { data, error } = await supabase
        .from('group_invites')
        .select('*')
        .eq('chat_id', chatId)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching group invites:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching group invites:', error);
      return [];
    }
  },

  // Create system message
  async createSystemMessage(chatId: string, senderId: string, type: string, metadata: any): Promise<void> {
    try {
      await supabase
        .from('voice_chat_messages')
        .insert({
          chat_id: chatId,
          sender_id: senderId,
          message_type: 'system',
          system_message_type: type,
          metadata,
          content: this.generateSystemMessageText(type, metadata),
        });
    } catch (error) {
      console.error('Error creating system message:', error);
    }
  },

  // Generate system message text
  generateSystemMessageText(type: string, metadata: any): string {
    switch (type) {
      case 'user_added':
        return `User was added to the group`;
      case 'user_removed':
        return `User was removed from the group`;
      case 'user_left':
        return `User left the group`;
      case 'user_promoted':
        return `User was promoted to ${metadata.new_role}`;
      case 'group_created':
        return `Group was created`;
      default:
        return `Group activity`;
    }
  },

  // Create group notification
  async createGroupNotification(chatId: string, userId: string, type: string, message: string, data: any): Promise<void> {
    try {
      await supabase
        .from('group_notifications')
        .insert({
          chat_id: chatId,
          user_id: userId,
          type,
          message,
          data,
        });
    } catch (error) {
      console.error('Error creating group notification:', error);
    }
  },

  // Get user's group notifications
  async getUserGroupNotifications(userId: string): Promise<GroupNotification[]> {
    try {
      const { data, error } = await supabase
        .from('group_notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) {
        console.error('Error fetching group notifications:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching group notifications:', error);
      return [];
    }
  },

  // Mark notification as read
  async markNotificationAsRead(notificationId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('group_notifications')
        .update({ is_read: true })
        .eq('id', notificationId);

      return !error;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      return false;
    }
  },

  // Delete entire chat/group
  async deleteChat(chatId: string, userId: string): Promise<boolean> {
    try {
      // Check if user has permission to delete (admin or creator)
      const { data: chat } = await supabase
        .from('voice_chats')
        .select('creator_id, voice_chat_participants!inner(role)')
        .eq('id', chatId)
        .eq('voice_chat_participants.profile_id', userId)
        .single();

      if (!chat || (chat.creator_id !== userId && !chat.voice_chat_participants.some((p: any) => p.role === 'admin'))) {
        console.error('User does not have permission to delete this chat');
        return false;
      }

      // Delete the chat (cascade will handle participants and messages)
      const { error } = await supabase
        .from('voice_chats')
        .delete()
        .eq('id', chatId);

      if (error) {
        console.error('Error deleting chat:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error deleting chat:', error);
      return false;
    }
  },

  // Clear all chats for a user (for testing)
  async clearAllUserChats(userId: string): Promise<boolean> {
    try {
      // Get all chats where user is a participant
      const { data: userChats } = await supabase
        .from('voice_chat_participants')
        .select('chat_id')
        .eq('profile_id', userId);

      if (!userChats || userChats.length === 0) {
        return true; // No chats to clear
      }

      const chatIds = userChats.map(uc => uc.chat_id);

      // Delete all chats
      const { error } = await supabase
        .from('voice_chats')
        .delete()
        .in('id', chatIds);

      if (error) {
        console.error('Error clearing chats:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error clearing chats:', error);
      return false;
    }
  },
};
