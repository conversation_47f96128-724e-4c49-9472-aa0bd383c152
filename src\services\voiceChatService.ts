import { supabase } from '@/integrations/supabase/client';

export interface VoiceChat {
  id: string;
  name: string;
  description?: string;
  type: 'direct' | 'group' | 'wave' | 'space';
  creator_id: string;
  is_private: boolean;
  emotional_tone: 'calm' | 'excited' | 'focused' | 'creative' | 'mysterious';
  settings: any;
  created_at: string;
  updated_at: string;
  participants?: VoiceChatParticipant[];
  last_message?: VoiceChatMessage;
  unread_count?: number;
}

export interface VoiceChatParticipant {
  id: string;
  chat_id: string;
  profile_id: string;
  role: 'admin' | 'moderator' | 'member';
  joined_at: string;
  last_seen: string;
  is_muted: boolean;
  profile?: {
    display_name?: string;
    username?: string;
    avatar_url?: string;
  };
}

export interface VoiceChatMessage {
  id: string;
  chat_id: string;
  sender_id: string;
  message_type: 'text' | 'voice' | 'emotion' | 'reaction' | 'system';
  content?: string;
  voice_url?: string;
  voice_duration?: number;
  voice_transcript?: string;
  emotion_data?: any;
  reply_to_id?: string;
  is_edited: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  sender_profile?: {
    display_name?: string;
    username?: string;
    avatar_url?: string;
  };
}

export interface EmotionalWave {
  id: string;
  sender_id: string;
  chat_id: string;
  emotion: string;
  intensity: number;
  color: string;
  wave_data: any;
  created_at: string;
  sender_profile?: {
    display_name?: string;
    username?: string;
  };
}

export const voiceChatService = {
  // Create a new chat
  async createChat(data: {
    name: string;
    description?: string;
    type: 'direct' | 'group' | 'wave' | 'space';
    creator_id: string;
    is_private?: boolean;
    participants?: string[];
  }): Promise<VoiceChat | null> {
    try {
      // Create the chat
      const { data: chatData, error: chatError } = await supabase
        .from('voice_chats')
        .insert({
          name: data.name,
          description: data.description,
          type: data.type,
          creator_id: data.creator_id,
          is_private: data.is_private || false,
        })
        .select()
        .single();

      if (chatError) {
        console.error('Error creating chat:', chatError);
        return null;
      }

      // Add creator as admin
      await supabase
        .from('voice_chat_participants')
        .insert({
          chat_id: chatData.id,
          profile_id: data.creator_id,
          role: 'admin',
        });

      // Add other participants if provided
      if (data.participants && data.participants.length > 0) {
        // For now, we'll treat participant emails as profile IDs
        // In a real app, you'd look up users by email first
        const participantInserts = data.participants
          .filter(id => id && id !== data.creator_id) // Avoid duplicates
          .map(profileId => ({
            chat_id: chatData.id,
            profile_id: profileId,
            role: 'member',
          }));

        if (participantInserts.length > 0) {
          const { error: participantError } = await supabase
            .from('voice_chat_participants')
            .insert(participantInserts);

          if (participantError) {
            console.error('Error adding participants:', participantError);
            // Don't fail the chat creation, just log the error
          }
        }
      }

      return chatData;
    } catch (error) {
      console.error('Error creating chat:', error);
      return null;
    }
  },

  // Get user's chats
  async getUserChats(userId: string): Promise<VoiceChat[]> {
    try {
      const { data, error } = await supabase
        .from('voice_chats')
        .select(`
          *,
          voice_chat_participants!inner(profile_id),
          voice_chat_messages(
            id,
            content,
            voice_transcript,
            message_type,
            created_at,
            sender_id,
            sender_profile:profiles!voice_chat_messages_sender_id_fkey(display_name, username)
          )
        `)
        .eq('voice_chat_participants.profile_id', userId)
        .order('updated_at', { ascending: false });

      if (error) {
        console.error('Error fetching chats:', error);
        return [];
      }

      // Process the data to get last message and unread count
      const processedChats = data.map(chat => {
        const messages = chat.voice_chat_messages || [];
        const lastMessage = messages.length > 0 ? messages[messages.length - 1] : null;
        
        return {
          ...chat,
          last_message: lastMessage,
          unread_count: 0, // TODO: Implement unread count logic
        };
      });

      return processedChats;
    } catch (error) {
      console.error('Error fetching user chats:', error);
      return [];
    }
  },

  // Get chat messages
  async getChatMessages(chatId: string, limit: number = 50): Promise<VoiceChatMessage[]> {
    try {
      const { data, error } = await supabase
        .from('voice_chat_messages')
        .select(`
          *,
          sender_profile:profiles!voice_chat_messages_sender_id_fkey(display_name, username, avatar_url)
        `)
        .eq('chat_id', chatId)
        .eq('is_deleted', false)
        .order('created_at', { ascending: true })
        .limit(limit);

      if (error) {
        console.error('Error fetching messages:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching messages:', error);
      return [];
    }
  },

  // Send a text message
  async sendTextMessage(chatId: string, senderId: string, content: string): Promise<VoiceChatMessage | null> {
    try {
      const { data, error } = await supabase
        .from('voice_chat_messages')
        .insert({
          chat_id: chatId,
          sender_id: senderId,
          message_type: 'text',
          content: content,
        })
        .select(`
          *,
          sender_profile:profiles!voice_chat_messages_sender_id_fkey(display_name, username, avatar_url)
        `)
        .single();

      if (error) {
        console.error('Error sending message:', error);
        return null;
      }

      // Update chat's updated_at
      await supabase
        .from('voice_chats')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', chatId);

      return data;
    } catch (error) {
      console.error('Error sending message:', error);
      return null;
    }
  },

  // Send a voice message
  async sendVoiceMessage(
    chatId: string, 
    senderId: string, 
    voiceUrl: string, 
    duration: number, 
    transcript?: string
  ): Promise<VoiceChatMessage | null> {
    try {
      const { data, error } = await supabase
        .from('voice_chat_messages')
        .insert({
          chat_id: chatId,
          sender_id: senderId,
          message_type: 'voice',
          voice_url: voiceUrl,
          voice_duration: duration,
          voice_transcript: transcript,
        })
        .select(`
          *,
          sender_profile:profiles!voice_chat_messages_sender_id_fkey(display_name, username, avatar_url)
        `)
        .single();

      if (error) {
        console.error('Error sending voice message:', error);
        return null;
      }

      // Update chat's updated_at
      await supabase
        .from('voice_chats')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', chatId);

      return data;
    } catch (error) {
      console.error('Error sending voice message:', error);
      return null;
    }
  },

  // Send an emotional wave
  async sendEmotionalWave(
    chatId: string, 
    senderId: string, 
    emotion: string, 
    intensity: number = 50
  ): Promise<EmotionalWave | null> {
    try {
      // Generate a color based on emotion
      const emotionColors: { [key: string]: string } = {
        '💙': '#3b82f6',
        '🔥': '#ef4444', 
        '⚡': '#eab308',
        '🌟': '#a855f7',
        '🌊': '#06b6d4',
      };

      const color = emotionColors[emotion] || '#6b7280';

      const { data, error } = await supabase
        .from('emotional_waves')
        .insert({
          sender_id: senderId,
          chat_id: chatId,
          emotion: emotion,
          intensity: intensity,
          color: color,
          wave_data: { timestamp: Date.now() },
        })
        .select(`
          *,
          sender_profile:profiles!emotional_waves_sender_id_fkey(display_name, username)
        `)
        .single();

      if (error) {
        console.error('Error sending emotional wave:', error);
        return null;
      }

      // Also create a message for the wave
      await this.sendTextMessage(chatId, senderId, `${emotion} Sent an emotional wave`);

      return data;
    } catch (error) {
      console.error('Error sending emotional wave:', error);
      return null;
    }
  },

  // Get emotional waves for a chat
  async getEmotionalWaves(chatId: string, limit: number = 20): Promise<EmotionalWave[]> {
    try {
      const { data, error } = await supabase
        .from('emotional_waves')
        .select(`
          *,
          sender_profile:profiles!emotional_waves_sender_id_fkey(display_name, username)
        `)
        .eq('chat_id', chatId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching emotional waves:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching emotional waves:', error);
      return [];
    }
  },

  // Join a chat
  async joinChat(chatId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('voice_chat_participants')
        .insert({
          chat_id: chatId,
          profile_id: userId,
          role: 'member',
        });

      if (error) {
        console.error('Error joining chat:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error joining chat:', error);
      return false;
    }
  },

  // Leave a chat
  async leaveChat(chatId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('voice_chat_participants')
        .delete()
        .eq('chat_id', chatId)
        .eq('profile_id', userId);

      if (error) {
        console.error('Error leaving chat:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error leaving chat:', error);
      return false;
    }
  },

  // Get chat participants
  async getChatParticipants(chatId: string): Promise<VoiceChatParticipant[]> {
    try {
      const { data, error } = await supabase
        .from('voice_chat_participants')
        .select(`
          *,
          profile:profiles!voice_chat_participants_profile_id_fkey(display_name, username, avatar_url)
        `)
        .eq('chat_id', chatId);

      if (error) {
        console.error('Error fetching participants:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching participants:', error);
      return [];
    }
  },

  // Delete message for everyone
  async deleteMessageForEveryone(messageId: string, userId: string): Promise<boolean> {
    try {
      // Check if user is the sender or admin
      const { data: message, error: messageError } = await supabase
        .from('voice_chat_messages')
        .select('sender_id, chat_id')
        .eq('id', messageId)
        .single();

      if (messageError || !message) {
        console.error('Error fetching message:', messageError);
        return false;
      }

      // Check if user is sender or admin of the chat
      const { data: participant, error: participantError } = await supabase
        .from('voice_chat_participants')
        .select('role')
        .eq('chat_id', message.chat_id)
        .eq('profile_id', userId)
        .single();

      const isAuthorized = message.sender_id === userId || participant?.role === 'admin';

      if (!isAuthorized) {
        console.error('User not authorized to delete message');
        return false;
      }

      const { error } = await supabase
        .from('voice_chat_messages')
        .update({
          deleted_for_everyone: true,
          deleted_at: new Date().toISOString(),
          content: '[Message deleted]',
          voice_url: null,
          voice_transcript: null
        })
        .eq('id', messageId);

      if (error) {
        console.error('Error deleting message for everyone:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error deleting message for everyone:', error);
      return false;
    }
  },

  // Delete message for specific user only
  async deleteMessageForMe(messageId: string, userId: string): Promise<boolean> {
    try {
      // Get current deleted_for_users array
      const { data: message, error: fetchError } = await supabase
        .from('voice_chat_messages')
        .select('deleted_for_users')
        .eq('id', messageId)
        .single();

      if (fetchError) {
        console.error('Error fetching message:', fetchError);
        return false;
      }

      const deletedForUsers = message.deleted_for_users || [];
      if (!deletedForUsers.includes(userId)) {
        deletedForUsers.push(userId);
      }

      const { error } = await supabase
        .from('voice_chat_messages')
        .update({
          deleted_for_users: deletedForUsers
        })
        .eq('id', messageId);

      if (error) {
        console.error('Error deleting message for user:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error deleting message for user:', error);
      return false;
    }
  },

  // Get filtered messages (excluding deleted ones for current user)
  async getChatMessagesFiltered(chatId: string, userId: string, limit: number = 50): Promise<VoiceChatMessage[]> {
    try {
      const { data, error } = await supabase
        .from('voice_chat_messages')
        .select(`
          *,
          sender_profile:profiles!voice_chat_messages_sender_id_fkey(display_name, username, avatar_url)
        `)
        .eq('chat_id', chatId)
        .eq('deleted_for_everyone', false)
        .order('created_at', { ascending: true })
        .limit(limit);

      if (error) {
        console.error('Error fetching messages:', error);
        return [];
      }

      // Filter out messages deleted for this specific user
      const filteredMessages = (data || []).filter(message => {
        const deletedForUsers = message.deleted_for_users || [];
        return !deletedForUsers.includes(userId);
      });

      return filteredMessages;
    } catch (error) {
      console.error('Error fetching filtered messages:', error);
      return [];
    }
  },
};
