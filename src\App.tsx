
import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom';
import { Toaster } from "@/components/ui/sonner";
import AppContent from './components/AppContent';

import databaseService from './services/databaseService';
import { supabase } from './integrations/supabase/client';
import { AuthProvider } from './contexts/AuthContext';
import { ProfileProvider } from './contexts/SimpleProfileContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { WalletProvider } from './contexts/WalletContext';
import { SettingsProvider } from './contexts/SettingsContext';
import JournalProvider from './contexts/JournalContext';
import { ContextProviders } from '@/components/ContextProviders';
import { checkAndFixDuplicateUsernames } from '@/utils/profileSyncUtils';
import PWAInstallPrompt from '@/components/PWAInstallPrompt';
import { initializePWA } from '@/utils/pwa';
import { initializePWAAudio } from '@/utils/pwaAudio';

function App() {
  // Initialize database buckets and check authentication on app load
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize storage buckets
        await databaseService.initializeStorageBuckets();

        // Initialize PWA functionality
        await initializePWA();

        // Initialize PWA audio fixes
        initializePWAAudio();

        // Check and fix duplicate usernames in the database
        console.log('Checking for duplicate usernames in the database...');
        const result = await checkAndFixDuplicateUsernames();
        if (result) {
          console.log('Successfully checked and fixed duplicate usernames');
        } else {
          console.error('Failed to check and fix duplicate usernames');
        }

        // Check if we have a session
        const { data: { session } } = await supabase.auth.getSession();

        if (session) {
          console.log('User is authenticated:', session.user);
          localStorage.setItem('connectedAccount', session.user.id);
          setConnectedAccount(session.user.id);
        } else {
          console.log('No authenticated session found');
          setConnectedAccount('');
        }
      } catch (error) {
        console.error('Error initializing app:', error);
      }
    };

    initializeApp();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        if (event === 'SIGNED_IN' && session) {
          console.log('User signed in:', session.user);
          localStorage.setItem('connectedAccount', session.user.id);
          setConnectedAccount(session.user.id);
        } else if (event === 'SIGNED_OUT') {
          console.log('User signed out');
          localStorage.removeItem('connectedAccount');
          setConnectedAccount('');
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // State for connected account
  const [connectedAccount, setConnectedAccount] = useState<string>('');

  // Initialize connectedAccount from current session on app startup
  useEffect(() => {
    const initializeConnectedAccount = async () => {
      try {
        // Check if we have a session
        const { data: { session } } = await supabase.auth.getSession();

        if (session?.user) {
          console.log('Initializing connectedAccount from session:', session.user.id);
          setConnectedAccount(session.user.id);
        } else {
          // Check localStorage as fallback
          const storedAccount = localStorage.getItem('connectedAccount');
          if (storedAccount) {
            console.log('Initializing connectedAccount from localStorage:', storedAccount);
            setConnectedAccount(storedAccount);
          }
        }
      } catch (error) {
        console.error('Error initializing connectedAccount:', error);
      }
    };

    initializeConnectedAccount();
  }, []);

  // Initialize userContext in the window object
  useEffect(() => {
    if (connectedAccount) {
      // Set the userContext in the window object for global access
      // connectedAccount is now consistently the user's UUID
      window.userContext = {
        walletAddress: connectedAccount, // This is actually the UUID now
        userId: connectedAccount
      };
    }
  }, [connectedAccount]);

  return (
    <>
      <ThemeProvider>
        <AuthProvider>
          <WalletProvider>
            <ProfileProvider connectedAccount={connectedAccount}>
              <SettingsProvider>
                <JournalProvider userAddress={connectedAccount}>
                  <ContextProviders userAddress={connectedAccount}>
                    <BrowserRouter>
                      <AppContent
                        connectedAccount={connectedAccount}
                        setConnectedAccount={setConnectedAccount}
                      />
                    </BrowserRouter>
                  </ContextProviders>
                </JournalProvider>
              </SettingsProvider>
            </ProfileProvider>
          </WalletProvider>
        </AuthProvider>
      </ThemeProvider>
      <Toaster position="bottom-center" />
      <PWAInstallPrompt />
    </>
  );
}

export default App;
