import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ArrowRight, ArrowLeft, Mic, MessageCircle, Users, Lock } from 'lucide-react';

const Welcome: React.FC = () => {
  const [currentScreen, setCurrentScreen] = useState(0);
  const navigate = useNavigate();

  const screens = [
    {
      title: "Welcome to Audra",
      description: "The next generation voice-based social platform for Web3 communities.",
      icon: <Mic size={64} className="text-voicechain-purple mb-6" />,
      image: "/welcome-1.png" // Replace with actual image path
    },
    {
      title: "Share Your Voice",
      description: "Record voice messages, add media, and connect with others through the power of voice.",
      icon: <MessageCircle size={64} className="text-voicechain-purple mb-6" />,
      image: "/welcome-2.png" // Replace with actual image path
    },
    {
      title: "Join Communities",
      description: "Discover and participate in voice channels focused on your favorite Web3 topics.",
      icon: <Users size={64} className="text-voicechain-purple mb-6" />,
      image: "/welcome-3.png" // Replace with actual image path
    },
    {
      title: "Secure & Decentralized",
      description: "Your voice, your data, your control. Built on blockchain technology for privacy and ownership.",
      icon: <Lock size={64} className="text-voicechain-purple mb-6" />,
      image: "/welcome-4.png" // Replace with actual image path
    }
  ];

  const handleNext = () => {
    if (currentScreen < screens.length - 1) {
      setCurrentScreen(currentScreen + 1);
    } else {
      navigate('/signup');
    }
  };

  const handlePrevious = () => {
    if (currentScreen > 0) {
      setCurrentScreen(currentScreen - 1);
    }
  };

  const handleSkip = () => {
    navigate('/signup');
  };

  const currentScreenData = screens[currentScreen];
  const isLastScreen = currentScreen === screens.length - 1;

  return (
    <div className="min-h-screen flex flex-col items-center bg-voicechain-dark text-foreground">
      <header className="p-4 w-full flex justify-center">
        <h1 className="text-2xl font-bold bg-gradient-to-r from-voicechain-purple to-voicechain-accent bg-clip-text text-transparent">
          Audra
        </h1>
      </header>

      <main className="flex-1 flex flex-col items-center justify-center p-6 text-center">
        <div className="max-w-md w-full mx-auto">
          {/* Icon */}
          <div className="flex justify-center">
            {currentScreenData.icon}
          </div>

          {/* Image placeholder */}
          <div className="w-full h-48 bg-voicechain-purple/10 rounded-xl mb-8 flex items-center justify-center overflow-hidden">
            {currentScreenData.image ? (
              <img
                src={currentScreenData.image}
                alt={currentScreenData.title}
                className="w-full h-full object-cover"
              />
            ) : (
              <span className="text-muted-foreground">Welcome Image</span>
            )}
          </div>

          <h2 className="text-2xl font-bold mb-3">{currentScreenData.title}</h2>
          <p className="mb-8 text-muted-foreground">{currentScreenData.description}</p>

          <div className="flex justify-center space-x-2 mb-8">
            {screens.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full ${index === currentScreen ? 'bg-voicechain-purple' : 'bg-muted'}`}
              />
            ))}
          </div>

          <div className="flex justify-between items-center">
            {currentScreen > 0 ? (
              <Button variant="ghost" onClick={handlePrevious}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
            ) : (
              <div />
            )}

            {!isLastScreen && (
              <Button variant="ghost" onClick={handleSkip}>
                Skip
              </Button>
            )}

            <Button
              className="bg-voicechain-purple hover:bg-voicechain-accent"
              onClick={handleNext}
            >
              {isLastScreen ? 'Get Started' : 'Next'}
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Welcome;
