
import React, { useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { User, Heart, MessageCircle, DollarSign, Bell, AtSign } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useNotifications, Notification } from '@/contexts/NotificationContext';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import { Button } from '@/components/ui/button';

const Notifications = () => {
  const { notifications, markAsRead, markAllAsRead } = useNotifications();
  const { getProfileByAddress } = useProfiles();

  // Mark all notifications as read when the page is loaded
  useEffect(() => {
    // We don't mark all as read automatically to allow the user to see which ones are new
    // This would be a good place to add a "Mark all as read" button
  }, []);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'like':
        return <Heart size={16} className="text-red-500" />;
      case 'reply':
        return <MessageCircle size={16} className="text-blue-500" />;
      case 'tip':
        return <DollarSign size={16} className="text-green-500" />;
      case 'summon':
        return <AtSign size={16} className="text-voicechain-purple" />;
      default:
        return <Bell size={16} />;
    }
  };

  const getNotificationText = (notification: Notification) => {
    // Get the profile of the user who triggered the notification
    const userProfile = getProfileByAddress(notification.from_address);
    const displayName = userProfile?.displayName || `${notification.from_address.substring(0, 6)}...${notification.from_address.substring(notification.from_address.length - 4)}`;

    switch (notification.type) {
      case 'like':
        return `${displayName} liked your voice message`;
      case 'reply':
        return `${displayName} replied to your voice message`;
      case 'tip':
        return `${displayName} tipped you ${notification.data?.tipAmount || ''}`;
      case 'mention':
        return `${displayName} mentioned you in a voice message`;
      case 'summon':
        return `${displayName} summoned you for an opinion`;
      default:
        return 'New notification';
    }
  };

  // Handle clicking on a notification
  const handleNotificationClick = (id: string) => {
    // Mark the notification as read
    markAsRead(id);

    // In a real app, we would navigate to the relevant message
    // For now, we just mark it as read
  };

  return (
    <div className="container mx-auto flex-1 px-4 py-6 max-w-3xl">
      <div className="sticky top-[60px] bg-voicechain-dark/95 backdrop-blur-md py-2 z-10">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold mb-2">Notifications</h1>
          {notifications.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => markAllAsRead()}
              className="text-xs"
            >
              Mark all as read
            </Button>
          )}
        </div>
        <Separator className="bg-border/50" />
      </div>

      <div className="mt-6 space-y-2">
        {notifications.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-10 text-center">
            <Bell size={40} className="text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-1">No notifications yet</h3>
            <p className="text-sm text-muted-foreground">
              When someone reacts, replies, or tips your voice messages, you'll see it here.
            </p>
          </div>
        ) : (
          notifications.map((notification) => {
            // Get the profile of the user who triggered the notification
            const userProfile = getProfileByAddress(notification.from_address);

            return (
              <div
                key={notification.id}
                className={`flex items-center p-3 rounded-lg cursor-pointer hover:bg-secondary/50 transition-colors ${notification.read ? 'bg-secondary/30' : 'bg-voicechain-purple/10 border-l-2 border-voicechain-purple'
                  }`}
                onClick={() => handleNotificationClick(notification.id)}
              >
                <Avatar className="h-10 w-10 mr-3">
                  <AvatarImage src={userProfile?.profileImageUrl} alt={userProfile?.displayName} />
                  <AvatarFallback className="bg-voicechain-accent/20 text-voicechain-accent text-xs">
                    {userProfile?.displayName?.charAt(0) || '?'}
                  </AvatarFallback>
                </Avatar>

                <div className="flex-1">
                  <p className={`text-sm ${notification.read ? 'text-muted-foreground' : 'text-foreground'}`}>
                    {getNotificationText(notification)}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {notification.created_at && typeof notification.created_at === 'string'
                      ? formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })
                      : 'recently'}
                  </p>
                </div>

                <div className="ml-2 p-2 bg-secondary rounded-full">
                  {getNotificationIcon(notification.type)}
                </div>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
};

export default Notifications;
