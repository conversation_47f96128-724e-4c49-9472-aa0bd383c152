/**
 * IPFS Audio Service
 * Handles storing and retrieving audio recordings using IPFS
 * This ensures audio recordings persist across devices and page refreshes
 */

import { NFTStorage } from 'nft.storage';
import { storeAudioInIndexedDB, getAudioFromIndexedDB } from '@/utils/audioStorageHelper';
import { v4 as uuidv4 } from 'uuid';
import { toast } from '@/components/ui/sonner';

// NFT.Storage API key
const NFT_STORAGE_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.Wd-qVgbLGzxbXdyNSTQgVdN9K_NW9m3Q8TaMn8Wnl-I';

// Create NFT.Storage client
const nftStorage = new NFTStorage({ token: NFT_STORAGE_API_KEY });

// In-memory cache for audio blobs
const audioCache: Record<string, Blob> = {};

/**
 * Upload audio to IPFS
 * @param audioBlob The audio blob to upload
 * @param userId The user ID (wallet address)
 * @returns The IPFS URI
 */
export async function uploadAudioToIPFS(audioBlob: Blob, userId: string): Promise<string> {
  try {
    console.log('Uploading audio to Supabase storage');

    // Import the supabase storage service
    const { uploadAudio } = await import('@/services/supabaseStorageService');

    // Upload directly to Supabase storage
    const supabaseUrl = await uploadAudio(audioBlob, userId);
    console.log('Audio uploaded to Supabase storage:', supabaseUrl);

    // Return the Supabase URL immediately
    return supabaseUrl;
  } catch (supabaseError) {
    console.error('Error uploading audio to Supabase:', supabaseError);

    // Fall back to IndexedDB only as a last resort
    try {
      console.warn('Falling back to IndexedDB storage (temporary)');
      const recordingId = `recording_${Date.now()}_${uuidv4()}`;
      await storeAudioInIndexedDB(recordingId, audioBlob, userId);

      // Add to in-memory cache
      audioCache[recordingId] = audioBlob;

      // Create an IndexedDB URL
      const indexedDBUrl = `indexeddb://${recordingId}`;

      // Show warning to user that this is temporary storage
      console.warn('Using temporary storage. Voice message may not persist after refresh.');

      return indexedDBUrl;
    } catch (indexedDBError) {
      console.error('Error storing audio in IndexedDB:', indexedDBError);
      throw new Error('Failed to store audio recording');
    }
  }
}

/**
 * Upload audio to IPFS in the background
 * @param audioBlob The audio blob to upload
 * @param fileName The file name
 * @param recordingId The recording ID in IndexedDB
 * @param userId The user ID (wallet address)
 */
async function uploadToIPFSBackground(
  audioBlob: Blob,
  fileName: string,
  recordingId: string,
  userId: string
): Promise<void> {
  try {
    console.log(`Starting background upload to IPFS for ${fileName}`);

    // Upload to IPFS
    const cid = await nftStorage.storeBlob(audioBlob);
    const ipfsUri = `ipfs://${cid}`;
    const ipfsGatewayUrl = `https://nftstorage.link/ipfs/${cid}`;

    console.log(`Audio uploaded to IPFS: ${ipfsUri}`);

    // Update the IndexedDB record with the IPFS URI
    await storeAudioInIndexedDB(recordingId, audioBlob, userId, {
      fileName,
      timestamp: Date.now(),
      ipfsUploading: false,
      ipfsUri,
      ipfsGatewayUrl
    });

    // Store the mapping in localStorage for future reference
    try {
      const mappings = JSON.parse(localStorage.getItem('ipfs_audio_mappings') || '{}');
      mappings[recordingId] = {
        ipfsUri,
        ipfsGatewayUrl,
        timestamp: Date.now()
      };
      localStorage.setItem('ipfs_audio_mappings', JSON.stringify(mappings));
    } catch (localStorageError) {
      console.warn('Error storing IPFS mapping in localStorage:', localStorageError);
    }

    console.log(`Background upload to IPFS completed for ${fileName}`);
  } catch (error) {
    console.error('Error in background upload to IPFS:', error);

    // Update the IndexedDB record to indicate upload failure
    try {
      await storeAudioInIndexedDB(recordingId, audioBlob, userId, {
        fileName,
        timestamp: Date.now(),
        ipfsUploading: false,
        ipfsUploadFailed: true
      });
    } catch (updateError) {
      console.error('Error updating IndexedDB record:', updateError);
    }
  }
}

/**
 * Get a playable URL for an audio recording
 * @param audioUrl The audio URL (can be IPFS URI, gateway URL, or IndexedDB URL)
 * @returns A playable URL
 */
export async function getPlayableAudioUrl(audioUrl: string): Promise<string> {
  try {
    console.log(`Getting playable URL for ${audioUrl}`);

    // If it's already a blob URL, return it
    if (audioUrl.startsWith('blob:')) {
      console.log('URL is already a blob URL');
      return audioUrl;
    }

    // If it's an IndexedDB URL, get the blob from IndexedDB
    if (audioUrl.startsWith('indexeddb://')) {
      const recordingId = audioUrl.replace('indexeddb://', '');

      // Check in-memory cache first
      if (audioCache[recordingId]) {
        console.log('Found audio in memory cache');
        return URL.createObjectURL(audioCache[recordingId]);
      }

      // Try to get from IndexedDB
      try {
        const audioData = await getAudioFromIndexedDB(recordingId);
        if (audioData && audioData.blob) {
          console.log('Found audio in IndexedDB');

          // Add to in-memory cache
          audioCache[recordingId] = audioData.blob;

          return URL.createObjectURL(audioData.blob);
        }
      } catch (indexedDBError) {
        console.error('Error getting audio from IndexedDB:', indexedDBError);
      }

      // Check if we have an IPFS mapping for this recording
      try {
        const mappings = JSON.parse(localStorage.getItem('ipfs_audio_mappings') || '{}');
        if (mappings[recordingId] && mappings[recordingId].ipfsGatewayUrl) {
          console.log('Found IPFS mapping, using gateway URL');
          return mappings[recordingId].ipfsGatewayUrl;
        }
      } catch (localStorageError) {
        console.warn('Error getting IPFS mapping from localStorage:', localStorageError);
      }
    }

    // If it's an IPFS URI, convert to gateway URL
    if (audioUrl.startsWith('ipfs://')) {
      const cid = audioUrl.replace('ipfs://', '');
      return `https://nftstorage.link/ipfs/${cid}`;
    }

    // Otherwise, return the URL as is
    return audioUrl;
  } catch (error) {
    console.error('Error getting playable audio URL:', error);
    return audioUrl;
  }
}

/**
 * Get the file extension from a blob
 * @param blob The blob
 * @returns The file extension
 */
function getExtensionFromBlob(blob: Blob): string {
  const type = blob.type;
  switch (type) {
    case 'audio/webm':
      return 'webm';
    case 'audio/mp3':
    case 'audio/mpeg':
      return 'mp3';
    case 'audio/wav':
      return 'wav';
    case 'audio/ogg':
      return 'ogg';
    default:
      return 'webm';
  }
}
