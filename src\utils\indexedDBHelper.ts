/**
 * IndexedDB Helper Functions
 * Provides utilities for storing and retrieving data from IndexedDB
 */

const DB_NAME = 'AudraStorage';
const DB_VERSION = 1;
const IMAGES_STORE = 'images';
const AUDIO_STORE = 'audio';
const PROFILES_STORE = 'profiles';

/**
 * Open the IndexedDB database
 * @returns Promise that resolves to the database instance
 */
export const openDatabase = (): Promise<IDBDatabase> => {
  return new Promise((resolve, reject) => {
    try {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onupgradeneeded = (event) => {
        const db = request.result;
        
        // Create object stores if they don't exist
        if (!db.objectStoreNames.contains(IMAGES_STORE)) {
          db.createObjectStore(IMAGES_STORE, { keyPath: 'id' });
        }
        
        if (!db.objectStoreNames.contains(AUDIO_STORE)) {
          db.createObjectStore(AUDIO_STORE, { keyPath: 'id' });
        }
        
        if (!db.objectStoreNames.contains(PROFILES_STORE)) {
          db.createObjectStore(PROFILES_STORE, { keyPath: 'address' });
        }
      };

      request.onsuccess = () => {
        resolve(request.result);
      };

      request.onerror = (event) => {
        console.error('Error opening IndexedDB:', event);
        reject(new Error('Failed to open IndexedDB'));
      };
    } catch (error) {
      console.error('Error accessing IndexedDB:', error);
      reject(error);
    }
  });
};

/**
 * Store data in IndexedDB
 * @param storeName The name of the object store
 * @param data The data to store
 * @returns Promise that resolves when the data is stored
 */
export const storeInIndexedDB = async (storeName: string, data: any): Promise<void> => {
  return new Promise(async (resolve, reject) => {
    try {
      const db = await openDatabase();
      const transaction = db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      
      const request = store.put(data);
      
      request.onsuccess = () => {
        console.log(`Data stored in ${storeName}:`, data.id || data.address);
        resolve();
      };
      
      request.onerror = (event) => {
        console.error(`Error storing data in ${storeName}:`, event);
        reject(new Error(`Failed to store data in ${storeName}`));
      };
      
      transaction.oncomplete = () => {
        db.close();
      };
    } catch (error) {
      console.error(`Error in storeInIndexedDB for ${storeName}:`, error);
      reject(error);
    }
  });
};

/**
 * Get data from IndexedDB
 * @param storeName The name of the object store
 * @param id The ID of the data to retrieve
 * @returns Promise that resolves to the retrieved data
 */
export const getFromIndexedDB = async (storeName: string, id: string): Promise<any> => {
  return new Promise(async (resolve, reject) => {
    try {
      const db = await openDatabase();
      const transaction = db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      
      const request = store.get(id);
      
      request.onsuccess = () => {
        if (request.result) {
          console.log(`Data retrieved from ${storeName}:`, id);
          resolve(request.result);
        } else {
          console.log(`No data found in ${storeName} for ID:`, id);
          resolve(null);
        }
      };
      
      request.onerror = (event) => {
        console.error(`Error retrieving data from ${storeName}:`, event);
        reject(new Error(`Failed to retrieve data from ${storeName}`));
      };
      
      transaction.oncomplete = () => {
        db.close();
      };
    } catch (error) {
      console.error(`Error in getFromIndexedDB for ${storeName}:`, error);
      reject(error);
    }
  });
};

/**
 * Store an image in IndexedDB
 * @param key The key to store the image under
 * @param dataUrl The data URL of the image
 * @returns Promise that resolves when the image is stored
 */
export const storeImageInIndexedDB = async (key: string, dataUrl: string): Promise<void> => {
  return storeInIndexedDB(IMAGES_STORE, {
    id: key,
    dataUrl,
    timestamp: Date.now()
  });
};

/**
 * Get an image from IndexedDB
 * @param key The key of the image to retrieve
 * @returns Promise that resolves to the data URL of the image
 */
export const getImageFromIndexedDB = async (key: string): Promise<string | null> => {
  const data = await getFromIndexedDB(IMAGES_STORE, key);
  return data ? data.dataUrl : null;
};

/**
 * Get an IndexedDB URL for an image
 * @param key The key of the image
 * @returns Promise that resolves to the IndexedDB URL
 */
export const getIndexedDBUrl = async (key: string): Promise<string | null> => {
  const dataUrl = await getImageFromIndexedDB(key);
  if (dataUrl) {
    return `indexeddb://${key}`;
  }
  return null;
};

/**
 * Delete data from IndexedDB
 * @param storeName The name of the object store
 * @param id The ID of the data to delete
 * @returns Promise that resolves when the data is deleted
 */
export const deleteFromIndexedDB = async (storeName: string, id: string): Promise<void> => {
  return new Promise(async (resolve, reject) => {
    try {
      const db = await openDatabase();
      const transaction = db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      
      const request = store.delete(id);
      
      request.onsuccess = () => {
        console.log(`Data deleted from ${storeName}:`, id);
        resolve();
      };
      
      request.onerror = (event) => {
        console.error(`Error deleting data from ${storeName}:`, event);
        reject(new Error(`Failed to delete data from ${storeName}`));
      };
      
      transaction.oncomplete = () => {
        db.close();
      };
    } catch (error) {
      console.error(`Error in deleteFromIndexedDB for ${storeName}:`, error);
      reject(error);
    }
  });
};
