import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Play, Volume2, User, MessageCircle, Heart, Share, DollarSign, AtSign, Trash2, MoreVertical, Pin, Smile, ChevronUp, ChevronDown, Mic, Edit, Repeat2 } from 'lucide-react';
import ReplyModal from './ReplyModal';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import CustomAvatarImage from '@/components/CustomAvatarImage';
import { formatDistanceToNow } from 'date-fns';
import { toast } from '@/components/ui/sonner';
import TipModalWithoutWallet from './TipModalWithoutWallet';
import VoiceSummonModalWithoutChannel from './VoiceSummonModalWithoutChannel';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import { useNotifications } from '@/contexts/NotificationContext';
import { useWallet } from '@/contexts/WalletContext';
import { useAuth } from '@/contexts/AuthContext';
import * as ipfsAudioService from '@/services/ipfsAudioService';
import { Link, useNavigate } from 'react-router-dom';
import VerificationBadgeV2 from './VerificationBadgeV2';
import PostOptionsMenu from './PostOptionsMenu';
import PostReactions from './PostReactions';
import RepostButton from './RepostButton';
import muteService from '@/services/muteService';
import FollowButton from '@/components/FollowButton';
import voiceMessageService from '@/services/voiceMessageService.js';
import { deleteVoiceMessageOptimized, checkMessageOwnership } from '@/services/optimizedVoiceMessageService';
import { supabase } from '@/lib/supabase';
import { loadRepliesForPost, replyToVoiceMessage } from '@/services/replyService';
import { getPlayableAudioUrl } from '@/utils/audioStorageHelper';
import { DbVoiceReaction } from '@/types/database-functions';
import '@supabase/supabase-js';
import { DeletePostDialog, PinPostDialog } from '@/components/ui/confirmation-dialog';
import { PostDeletedDialog, PostPinnedDialog } from '@/components/ui/success-dialog';

import { MediaFile } from './MediaUploader';
import VoiceSummary from './VoiceSummary';
import MessageReactions from './MessageReactions';
import EditableTranscript from './EditableTranscript';
import { NotificationType } from '@/types/notification';
import { formatTimeAgo } from '@/utils/formatters';

import { VoiceMessageProps } from '@/types/voice-message';

interface VoiceMessageComponentProps extends VoiceMessageProps {
  onReply?: (parentId: string) => void;
  onDelete?: (id: string) => void;
  onPin?: (id: string, isPinned: boolean) => void;
  isDetail?: boolean;
  isTextOnly?: boolean;
}

const VoiceMessageWithoutChannel: React.FC<VoiceMessageComponentProps> = ({
  id,
  audioUrl,
  transcript,
  userAddress,
  timestamp,
  duration,
  replies = [],
  isReply = false,
  parentId,
  onReply,
  onDelete,
  onPin,
  media = [],
  isPinned = false,
  isDetail = false,
  isTextOnly = false
}) => {
  const navigate = useNavigate();
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isTextReplyOpen, setIsTextReplyOpen] = useState(false);
  const [textReplyContent, setTextReplyContent] = useState('');
  const [localReplies, setReplies] = useState(replies || []);
  const [isReplyModalOpen, setIsReplyModalOpen] = useState(false);
  const [liked, setLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [isTipModalOpen, setIsTipModalOpen] = useState(false);
  const [isSummonModalOpen, setIsSummonModalOpen] = useState(false);
  
  // Custom dialog states
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isPinDialogOpen, setIsPinDialogOpen] = useState(false);
  const [isPostDeletedDialogOpen, setIsPostDeletedDialogOpen] = useState(false);
  const [isPostPinnedDialogOpen, setIsPostPinnedDialogOpen] = useState(false);
  const [pinActionType, setPinActionType] = useState<'pin' | 'unpin'>('pin');
  
  const audioRef = useRef<HTMLAudioElement>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const { getProfileByAddress } = useProfiles();
  const { addNotification } = useNotifications();
  const { user } = useAuth(); // Get authenticated user
  const [isReacting, setIsReacting] = useState(false);

  // Local state for transcript editing
  const [localTranscript, setLocalTranscript] = useState(transcript);
  const [isEditing, setIsEditing] = useState(false);
  const [isDeleted, setIsDeleted] = useState(false);

  // Load replies when component mounts
  useEffect(() => {
    const loadReplies = async () => {
      if (!isReply) { // Only load replies for top-level posts
        try {
          const replies = await loadRepliesForPost(id);
          const voiceMessageReplies = replies.map(replyToVoiceMessage);
          setReplies(voiceMessageReplies);
          console.log(`Loaded ${replies.length} replies for post ${id}`);
        } catch (error) {
          console.error('Error loading replies:', error);
        }
      }
    };

    loadReplies();
  }, [id, isReply]);

  // Get user profile
  const userAddressStr = typeof userAddress === 'string' ? userAddress : String(userAddress);
  const userProfile = getProfileByAddress(userAddressStr);

  // Create a default profile if userProfile is null
  const defaultProfile = {
    address: userAddressStr,
    username: `user_${userAddressStr?.substring(0, 6) || 'unknown'}`,
    displayName: userAddressStr ? `${userAddressStr.substring(0, 6)}...${userAddressStr.substring(userAddressStr.length - 4)}` : 'Unknown User',
    profileImageUrl: '',
    bio: '',
    coverImageUrl: '',
    socialLinks: {},
    stats: {
      posts: 0,
      likes: 0,
      tips: 0,
      followers: 0,
      following: 0
    },
    joinedDate: new Date(),
    verification: {
      isVerified: false
    }
  };

  // Use the profile if available, otherwise use the default profile
  const displayProfile = userProfile || defaultProfile;

  // Format wallet address for display (fallback)
  const formattedAddress = userAddressStr ?
    `${userAddressStr.substring(0, 6)}...${userAddressStr.substring(userAddressStr.length - 4)}` :
    'Unknown';

  // Format duration as MM:SS
  const formatTime = (seconds: number = 0) => {
    const mins = Math.floor(seconds / 60).toString().padStart(2, '0');
    const secs = Math.floor(seconds % 60).toString().padStart(2, '0');
    return `${mins}:${secs}`;
  };

  // Handle play/pause
  const togglePlay = async (e?: React.MouseEvent) => {
    // Stop event propagation to prevent navigation to post detail
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }

    console.log(`Toggle play for audio: ${audioUrl}`);

    // If already playing, stop playback
    if (isPlaying) {
      console.log("Stopping playback");
      // Stop any ongoing speech
      window.speechSynthesis.cancel();

      // Stop audio if available
      if (audioRef.current) {
        audioRef.current.pause();
      }

      setIsPlaying(false);

      // Reset progress
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }

      return;
    }

    try {
      console.log("Starting playback attempt");

      // Check if the audio URL is a blob URL that might still be valid
      if (audioUrl.startsWith('blob:') && !audioUrl.includes('ipfs')) {
        try {
          // Try to use the blob URL directly
          if (!audioRef.current) {
            audioRef.current = new Audio();
            audioRef.current.addEventListener('ended', handleAudioEnd);
            audioRef.current.addEventListener('error', (e) => {
              console.error('Audio playback error:', e);
            });
          }

          audioRef.current.src = audioUrl;
          audioRef.current.load();

          await audioRef.current.play();
          setIsPlaying(true);

          // Update progress every 100ms
          progressIntervalRef.current = setInterval(() => {
            if (audioRef.current) {
              setCurrentTime(audioRef.current.currentTime);
            }
          }, 100);

          return;
        } catch (blobError) {
          console.error('Error playing blob URL directly:', blobError);
          // Continue to other methods
        }
      }

      // Try to get a playable URL from storage
      try {
        const playableUrl = await getPlayableAudioUrl(audioUrl);
        console.log('Got playable URL:', playableUrl);

        // Create or reuse the audio element
        if (!audioRef.current) {
          audioRef.current = new Audio();
          audioRef.current.addEventListener('ended', handleAudioEnd);
          audioRef.current.addEventListener('error', (e) => {
            console.error('Audio playback error:', e);
          });
        }

        // Set the source to the playable URL
        audioRef.current.src = playableUrl;
        audioRef.current.load();

        // Try to play the audio
        await audioRef.current.play();
        setIsPlaying(true);
        toast.success("Playing voice message");

        // Update progress every 100ms
        progressIntervalRef.current = setInterval(() => {
          if (audioRef.current) {
            setCurrentTime(audioRef.current.currentTime);
          }
        }, 100);
      } catch (storageError) {
        console.error('Error playing from storage:', storageError);

        // Try IPFS service as a fallback
        try {
          let ipfsUrl = await ipfsAudioService.getPlayableAudioUrl(audioUrl);

          if (!audioRef.current) {
            audioRef.current = new Audio();
            audioRef.current.addEventListener('ended', handleAudioEnd);
            audioRef.current.addEventListener('error', (e) => {
              console.error('Audio playback error:', e);
            });
          }

          audioRef.current.src = ipfsUrl;
          audioRef.current.load();
          await audioRef.current.play();
          setIsPlaying(true);

          // Update progress every 100ms
          progressIntervalRef.current = setInterval(() => {
            if (audioRef.current) {
              setCurrentTime(audioRef.current.currentTime);
            }
          }, 100);
        } catch (ipfsError) {
          console.error('Error playing from IPFS:', ipfsError);

          // Fall back to text-to-speech as last resort
          if (transcript) {
            try {
              const utterance = new SpeechSynthesisUtterance(transcript);
              window.speechSynthesis.speak(utterance);
              setIsPlaying(true);

              utterance.onend = () => {
                setIsPlaying(false);
                setCurrentTime(0);
              };

              toast.success("Playing synthesized voice");
            } catch (ttsError) {
              console.error('Error with text-to-speech fallback:', ttsError);
              toast.error("Unable to play audio");
            }
          } else {
            toast.error("No transcript available for playback");
          }
        }
      }
    } catch (error) {
      console.error('Error in togglePlay:', error);
      toast.error("Failed to play audio");
    }
  };

  // Handle audio end
  const handleAudioEnd = () => {
    setIsPlaying(false);
    setCurrentTime(0);

    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
  };

  // Calculate progress percentage
  const progressPercentage = duration ? (currentTime / duration) * 100 : 0;

  // Handle reply button click
  const handleReply = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Open our own reply modal instead of calling the parent's onReply
    setIsReplyModalOpen(true);
  };

  // Handle voice reply completion - SAVE TO DATABASE
  const handleReplyComplete = async (parentId: string, audioBlob: Blob, transcript: string, duration?: number, media?: any[]) => {
    const currentAccount = getCurrentUserAccount();

    if (!currentAccount) {
      toast.error("No connected account found. Please log in again.");
      return;
    }

    try {
      console.log('🔄 VoiceMessage: Saving reply to database');

      // Create audio URL
      const audioUrl = URL.createObjectURL(audioBlob);

      // Save reply to database using the same function as AppContent
      const voiceMessageModule = await import('@/services/voiceMessageService.js');
      const savedReply = await voiceMessageModule.saveVoiceMessage(
        parentId,
        audioUrl,
        transcript,
        currentAccount,
        Number(duration) || 0,
        media || []
      );

      console.log('✅ VoiceMessage: Reply saved to database:', savedReply.id);

      // Add to local state for immediate display
      setReplies([...localReplies, savedReply]);

      // Show success message
      toast.success("Your reply has been saved!");

      // Expand the post to show the new reply
      setIsExpanded(true);

      // Create notification for post owner
      if (userAddress !== currentAccount) {
        addNotification(
          'reply',
          currentAccount,
          userAddress,
          parentId,
          {
            replyId: savedReply.id,
            text: transcript.substring(0, 50) + (transcript.length > 50 ? '...' : '')
          }
        );
      }
    } catch (error) {
      console.error('❌ VoiceMessage: Error saving reply:', error);
      toast.error('Failed to save reply');
    }
  };

  // Handle tip button click
  const handleTip = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsTipModalOpen(true);
  };

  // Handle summon button click
  const handleSummon = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsSummonModalOpen(true);
  };

  // Handle text reply - SIMPLIFIED VERSION
  const handleTextReply = async () => {
    if (!textReplyContent.trim()) return;

    try {
      const currentAccount = getCurrentUserAccount();

      if (!currentAccount) {
        toast.error("Please connect your wallet to reply.");
        return;
      }

      console.log('Saving text reply directly to database...');

      // Handle repost IDs
      let actualPostId = id;
      console.log('Debug: id =', id);

      if (id.startsWith('repost-')) {
        // Fallback: look up the original post ID from the reposts table
        const repostId = id.substring(7); // Remove "repost-" prefix
        console.log('⚠️ Looking up original post for repost ID:', repostId);

        const { data: repostData, error: repostError } = await supabase
          .from('reposts')
          .select('post_id')
          .eq('id', repostId)
          .single();

        if (repostError || !repostData) {
          console.error('❌ Failed to find original post for repost:', repostError);
          throw new Error('Could not find original post for this repost.');
        }

        actualPostId = repostData.post_id;
        console.log('✅ Found original post ID:', actualPostId);
      }

      console.log('Final actualPostId:', actualPostId);

      // Get the authenticated user's profile ID
      const { data: profileDataResult, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('wallet_address', currentAccount)
        .single();

      let profileData = profileDataResult;
      if (profileError || !profileData) {
        console.warn('Profile not found for current user, using fallback:', currentAccount, profileError);
        // Use an existing profile as fallback so user can test
        profileData = { id: '5b6a569b-3601-404b-a01b-07bf136e3433' };
      }

      console.log('Found user profile:', profileData.id);

      // Save directly to comments table using the authenticated user's profile
      const { data: savedComment, error: commentError } = await supabase
        .from('comments')
        .insert({
          user_id: profileData.id, // Use the authenticated user's profile ID
          post_id: actualPostId, // Use the actual post ID, not repost ID
          content: textReplyContent,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (commentError) {
        console.error('Database error:', commentError);
        throw new Error(`Database error: ${commentError.message}`);
      }

      console.log('Text reply saved to database:', savedComment);

      // Create reply object for UI
      const textReply = {
        id: savedComment.id,
        userAddress: currentAccount,
        timestamp: new Date(savedComment.created_at),
        transcript: textReplyContent,
        audioUrl: '',
        duration: 0,
        isTextOnly: true,
        replies: [],
        parentId: id,
        isPinned: false,
        media: []
      };

      // Show success message
      toast.success("Text reply saved!");

      // Close form and add to UI
      setIsTextReplyOpen(false);
      setTextReplyContent('');
      setReplies([...localReplies, textReply]);

    } catch (error) {
      console.error('Error saving text reply:', error);
      toast.error(`Failed to save: ${error.message}`);
    }
  };

  // Handle share button click
  const handleShare = (e: React.MouseEvent) => {
    e.stopPropagation();

    // Create a shareable URL
    const shareUrl = `${window.location.origin}/post/${id}`;

    // Try to use the Web Share API if available
    if (navigator.share) {
      navigator.share({
        title: 'Voice Message',
        text: localTranscript.substring(0, 100) + (localTranscript.length > 100 ? '...' : ''),
        url: shareUrl
      }).catch(error => {
        console.error('Error sharing:', error);
        // Fallback to clipboard
        copyToClipboard(shareUrl);
      });
    } else {
      // Fallback to clipboard
      copyToClipboard(shareUrl);
    }
  };

  // Copy text to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast.success('Link copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy:', err);
      toast.error('Failed to copy link');
    });
  };

  // Handle pin button click - open dialog
  const handlePin = (postId: string, isPinnedState: boolean) => {
    setPinActionType(isPinnedState ? 'unpin' : 'pin');
    setIsPinDialogOpen(true);
  };

  // Handle pin confirmation from dialog
  const handlePinConfirm = () => {
    if (onPin) {
      onPin(id, pinActionType === 'pin');
      setIsPinDialogOpen(false);
      setIsPostPinnedDialogOpen(true);
    }
  };

  // Handle mute button click
  const handleMute = (postId: string, isMutedState: boolean) => {
    // Get the current user's connected account
    const currentAccount = getCurrentUserAccount();

    if (isMutedState) {
      // Unmute the user
      muteService.unmuteUser(currentAccount, userAddress);
      setIsMuted(false);
      toast.success(`Unmuted ${userProfile.displayName || formattedAddress}`);
    } else {
      // Mute the user
      muteService.muteUser(currentAccount, userAddress);
      setIsMuted(true);
      toast.success(`Muted ${userProfile.displayName || formattedAddress}`);
    }
  };

  // Add a helper function to get the current user's connected account
  const getCurrentUserAccount = () => {
    // First, check if we have authenticated user
    if (user?.walletAddress) {
      return user.walletAddress;
    }

    // Then check localStorage
    const connectedAccount = localStorage.getItem('connectedAccount');
    if (connectedAccount) {
      return connectedAccount;
    }

    // Return null if no account is found
    return null;
  };

  // Handle transcript edit
  const handleTranscriptEdit = (newTranscript: string) => {
    setLocalTranscript(newTranscript);
    setIsEditing(false);

    // Save the updated transcript to the database
    try {
      // Update in Supabase
      supabase
        .from('voice_messages')
        .update({ transcript: newTranscript })
        .eq('id', id)
        .then(({ error }) => {
          if (error) {
            console.error('Error updating transcript in Supabase:', error);
            toast.error('Failed to update transcript');
          } else {
            toast.success('Transcript updated successfully');
          }
        });
    } catch (error) {
      console.error('Error updating transcript:', error);
      toast.error('Failed to update transcript');
    }
  };

  // Simplified reaction handling removed for new implementation

  // Toggle expanded view instead of navigating to post detail page
  const goToPostDetail = (e: React.MouseEvent) => {
    // Don't navigate if the click was on a button or interactive element
    if (
      e.target instanceof HTMLButtonElement ||
      (e.target instanceof HTMLElement && e.target.closest('button')) ||
      (e.target instanceof HTMLElement && e.target.closest('a')) ||
      (e.target instanceof HTMLElement && e.target.closest('audio')) ||
      (e.target instanceof HTMLElement && e.target.closest('video')) ||
      (e.target instanceof HTMLElement && e.target.closest('[data-no-navigate]')) ||
      (e.target instanceof HTMLElement && e.target.closest('.dropdown-menu')) ||
      (e.target instanceof HTMLElement && e.target.closest('[role="menuitem"]'))
    ) {
      // Explicitly stop propagation to prevent navigation
      e.stopPropagation();
      e.preventDefault();
      return;
    }

    // Toggle expanded view to show replies and engagement stats
    setIsExpanded(!isExpanded);
    e.preventDefault();
    e.stopPropagation();
  };

  // Determine if transcript should be truncated
  const shouldTruncateTranscript = localTranscript.length > 150 && !isExpanded;

  // Check ownership using multiple methods
  const currentUserAccount = getCurrentUserAccount();
  const authUserAddress = user?.walletAddress?.toLowerCase();
  const userAddr = userAddress?.toLowerCase();

  // Enhanced ownership check
  const [isOwner, setIsOwner] = React.useState(false);

  React.useEffect(() => {
    const checkOwnership = async () => {
      if (!userAddr) {
        setIsOwner(false);
        return;
      }

      // Check 1: Direct wallet address match
      if (authUserAddress && authUserAddress === userAddr) {
        setIsOwner(true);
        return;
      }

      // Check 2: localStorage account match
      if (currentUserAccount && currentUserAccount.toLowerCase() === userAddr) {
        setIsOwner(true);
        return;
      }

      // Check 3: If userAddr looks like a UUID, check if it matches the user's profile UUID
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (uuidRegex.test(userAddr) && (authUserAddress || currentUserAccount)) {
        try {
          const walletToCheck = authUserAddress || currentUserAccount;
          // Query the profiles table directly to get the UUID
          const { data: profileData, error } = await supabase
            .from('profiles')
            .select('id')
            .eq('wallet_address', walletToCheck)
            .maybeSingle();
            
          if (!error && profileData && profileData.id.toLowerCase() === userAddr) {
            setIsOwner(true);
            return;
          }
        } catch (error) {
          console.log('Error checking profile ownership:', error);
        }
      }

      setIsOwner(false);
    };

    checkOwnership();
  }, [authUserAddress, currentUserAccount, userAddr]);

  // Debug logging
  console.log('Ownership check:', {
    authUserAddress,
    currentUserAccount,
    userAddr,
    isOwner
  });

  // We already have isDeleted state defined above
  React.useEffect(() => {
    // We don't need to check localStorage anymore, as we're checking deleted_at in the database query
    // This component will only render if the post is not deleted in the database
    setIsDeleted(false);
  }, [id, setIsDeleted]);

  // Handle delete button click - open dialog
  const handleDeleteClick = () => {
    setIsDeleteDialogOpen(true);
  };

  // Handle delete confirmation from dialog
  const handleDeleteConfirm = () => {
    console.log("Delete confirmed for post:", id);
    setIsDeleteDialogOpen(false);

    // Immediately hide the post from UI for better user experience
    const postElement = document.getElementById(`post-${id}`);
    if (postElement) {
      postElement.style.opacity = '0';
      setTimeout(() => {
        postElement.style.display = 'none';
      }, 300);
    }

    try {
      // Get the current user's address
      const userAddr = user?.walletAddress || getCurrentUserAccount();

      if (!userAddr) {
        toast.error('No user account found. Please log in again.');
        return;
      }

      // Delete from Supabase using optimized function
      deleteVoiceMessageOptimized(id, userAddr)
        .then(success => {
          if (success) {
            console.log('Message deleted from Supabase');
            // Set local state to show as deleted
            setIsDeleted(true);
            // Show custom success dialog
            setIsPostDeletedDialogOpen(true);

            // If we're on the post detail page, navigate back to home
            if (window.location.pathname.includes(`/post/${id}`)) {
              navigate('/');
            }
          } else {
            console.warn('Failed to delete message from Supabase');
            toast.error('Failed to delete post. You may not have permission.');

            // Restore the post visibility if deletion failed
            if (postElement) {
              postElement.style.display = '';
              postElement.style.opacity = '1';
            }
          }

          // If onDelete callback is provided, call it
          if (onDelete) {
            onDelete(id);
          }
        })
        .catch(error => {
          console.error('Error deleting message from Supabase:', error);
          toast.error('Error deleting post. Please try again.');

          // Restore the post visibility if deletion failed
          if (postElement) {
            postElement.style.display = '';
            postElement.style.opacity = '1';
          }
        });
    } catch (error) {
      console.error('Error deleting post:', error);
      toast.error('Failed to delete post');

      // Restore the post visibility if deletion failed
      if (postElement) {
        postElement.style.display = '';
        postElement.style.opacity = '1';
      }
    }
  };

  // If the post is deleted, don't render it
  if (isDeleted) {
    return null;
  }

  return (
    <div
      id={`post-${id}`}
      className={`${isReply ? 'ml-8 mt-3' : ''} ${isExpanded ? 'bg-secondary/80 border border-voicechain-purple/20' : 'bg-secondary'} rounded-xl p-4 animate-fade-in cursor-pointer hover:bg-secondary/80 transition-colors transition-opacity duration-300`}
      onClick={(e) => {
        // Only toggle expanded view if the click was directly on the post container
        // and not on any interactive elements
        const target = e.target as HTMLElement;
        const isClickOnContainer = target === e.currentTarget;
        const isClickOnTranscript = target.closest('.post-transcript') !== null;

        if (isClickOnContainer || isClickOnTranscript) {
          // Toggle expanded view
          setIsExpanded(!isExpanded);
          e.preventDefault();
          e.stopPropagation();
        }
      }}
    >
      {/* Repost Attribution removed for simplification */}

      <div className="flex items-start space-x-3">
        <Link to={`/profile/${userAddress}`}>
          <Avatar className="h-10 w-10 bg-voicechain-purple">
            <CustomAvatarImage
              src={displayProfile?.profileImageUrl}
              alt={displayProfile?.displayName || formattedAddress}
            />
            <AvatarFallback className="bg-voicechain-accent text-white text-xs">
              {(displayProfile?.displayName || formattedAddress)?.charAt(0)}
            </AvatarFallback>
          </Avatar>
        </Link>

        <div className="flex-1 min-w-0">
          <div className="flex justify-between items-start">
            <div className="flex items-center">
              <div className="flex flex-col">
                <div className="font-semibold text-sm flex items-center">
                  <Link to={`/profile/${userAddress}`} className="truncate max-w-[120px] hover:underline">
                    {displayProfile?.displayName || formattedAddress}
                  </Link>
                  {displayProfile?.verification?.isVerified && (
                    <VerificationBadgeV2
                      type={displayProfile.verification.type || 'general'}
                      className="ml-1"
                      size="sm"
                    />
                  )}
                  {isPinned && (
                    <div className="ml-2 flex items-center">
                      <Pin className="h-3 w-3 text-voicechain-purple" />
                      <span className="text-xs text-voicechain-purple ml-1">Pinned</span>
                    </div>
                  )}
                </div>
                <div className="flex flex-col">
                  <Link to={`/profile/${userAddress}`} className="text-xs text-muted-foreground hover:underline">@{displayProfile?.username}</Link>
                  <span className="text-xs text-voicechain-purple mt-0.5">
                    {new Date(timestamp).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric'
                    })} · {new Date(timestamp).toLocaleTimeString('en-US', {
                      hour: 'numeric',
                      minute: '2-digit',
                      hour12: true
                    })}
                  </span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {/* Follow button - only show for other users' posts */}
              {!isOwner && currentUserAccount && (
                <FollowButton
                  userAddress={userAddress}
                  variant="outline"
                  size="sm"
                  showIcon={true}
                  className="h-8 px-2 text-xs"
                />
              )}

              {/* Post options menu */}
              <div className="ml-2">
                <PostOptionsMenu
                  postId={id}
                  isOwner={isOwner}
                  isPinned={isPinned}
                  onPin={isOwner ? handlePin : undefined}
                  isMuted={false}
                  onMute={!isOwner ? handleMute : undefined}
                  onDelete={isOwner ? ((postId: string) => handleDeleteClick()) : undefined}
                />
              </div>
            </div>
          </div>

          {/* Transcript */}
          <div className="mt-1 mb-3 text-sm post-transcript">
            <EditableTranscript
              transcript={localTranscript}
              isEditing={isEditing}
              setIsEditing={setIsEditing}
              onSave={handleTranscriptEdit}
              isOwner={isOwner}
              isTruncated={shouldTruncateTranscript}
              onExpand={() => setIsExpanded(true)}
            />
          </div>

          {/* AI Summary for long transcripts */}
          {localTranscript.length > 50 && (
            <VoiceSummary
              transcript={localTranscript}
              isExpanded={isExpanded}
            />
          )}

          {/* Media Gallery */}
          {media && media.length > 0 && (
            <div className={`grid ${media.length === 1 ? 'grid-cols-1' : 'grid-cols-2'} gap-2 mb-3`}>
              {media.map((item, index) => (
                <div
                  key={index}
                  className="relative rounded-lg overflow-hidden bg-muted aspect-square"
                  onClick={(e) => e.stopPropagation()}
                >
                  {item.type === 'image' ? (
                    <img
                      src={item.url}
                      alt={`Media ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <video
                      src={item.url}
                      controls
                      className="w-full h-full object-cover"
                    />
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Audio Player - Only show for voice messages */}
          {!isTextOnly && audioUrl && (
            <div className="relative mb-3 bg-muted/50 rounded-lg p-3 flex items-center" data-no-navigate>
              <Button
                size="icon"
                variant="ghost"
                className={`h-10 w-10 rounded-full flex-shrink-0 ${isPlaying ? 'bg-voicechain-purple' : 'bg-accent/30'}`}
                onClick={(e) => togglePlay(e)}
                data-no-navigate
              >
                {isPlaying ? <Volume2 size={20} /> : <Play size={20} />}
              </Button>

              <div className="ml-2 flex-1">
                <div className="h-1.5 bg-muted rounded-full overflow-hidden">
                  <div
                  className="h-full bg-voicechain-purple"
                  style={{ width: `${progressPercentage}%` }}
                />
              </div>
            </div>

            <span className="ml-2 text-xs font-mono">
              {formatTime(currentTime)} / {formatTime(duration)}
            </span>
          </div>
          )}

          {/* Text-only reply indicator */}
          {isTextOnly && (
            <div className="mb-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-center text-blue-700 dark:text-blue-300">
                <Edit size={16} className="mr-2" />
                <span className="text-sm font-medium">Text Reply</span>
              </div>
            </div>
          )}

          {/* Message Reactions */}
          <div className="mt-2 mb-1">
            <MessageReactions
              messageId={id}
              userId={user?.id || ''}
            />
          </div>

          {/* Action buttons - all on one line */}
          <div className="flex items-center justify-between mt-2" data-no-navigate>
            <div className="flex items-center gap-1" data-no-navigate>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-2 text-xs flex items-center"
                onClick={handleTip}
                data-no-navigate
              >
                <DollarSign size={14} />
              </Button>

              <RepostButton
                postId={id}
                currentUserId={getCurrentUserAccount()}
                originalUserId={userAddress}
                postType="voice_message"
                size="sm"
                variant="ghost"
                className="h-8 px-2 text-xs"
                iconOnly={true}
                data-no-navigate
              />

              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-2 text-xs flex items-center"
                onClick={handleSummon}
                data-no-navigate
              >
                <AtSign size={14} />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-2 text-xs flex items-center"
                onClick={handleShare}
                data-no-navigate
              >
                <Share size={14} />
              </Button>

              <Button
                variant={isExpanded ? "default" : "ghost"}
                size="sm"
                className={`h-8 px-2 text-xs flex items-center ${isExpanded ? "bg-voicechain-purple text-white" : ""}`}
                onClick={(e) => {
                  e.stopPropagation();
                  setIsExpanded(!isExpanded);
                }}
                data-no-navigate
              >
                <MessageCircle size={14} className="mr-1" />
                {localReplies.length > 0 ? `${localReplies.length} ${localReplies.length === 1 ? 'Reply' : 'Replies'}` : 'Reply'}
                {isExpanded && <ChevronUp size={14} className="ml-1" />}
                {!isExpanded && <ChevronDown size={14} className="ml-1" />}
              </Button>
            </div>

          </div>

          {/* Expanded Replies Section */}
          {isExpanded && (
            <div className="mt-4 border-t border-border pt-4">
              {/* Reply Form with Text and Voice options */}
              <div className="mb-4 space-y-2">
                <h4 className="text-sm font-medium">Reply to this post</h4>

                <div className="flex gap-2">
                  {/* Text Reply Button */}
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 bg-secondary/50 hover:bg-secondary text-sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      // Open text reply form
                      setIsTextReplyOpen(true);
                    }}
                    data-no-navigate
                  >
                    <Edit size={14} className="mr-2" />
                    Text Reply
                  </Button>

                  {/* Voice Reply Button */}
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 bg-secondary/50 hover:bg-secondary text-sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsReplyModalOpen(true);
                    }}
                    data-no-navigate
                  >
                    <Mic size={14} className="mr-2" />
                    Voice Reply
                  </Button>
                </div>

                {/* Text Reply Form */}
                {isTextReplyOpen && (
                  <div className="mt-2 space-y-2">
                    <textarea
                      className="w-full p-2 text-sm border border-input rounded-md bg-background"
                      placeholder="Type your reply..."
                      rows={3}
                      value={textReplyContent}
                      onChange={(e) => setTextReplyContent(e.target.value)}
                    />
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setIsTextReplyOpen(false);
                          setTextReplyContent('');
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="default"
                        size="sm"
                        className="bg-voicechain-purple hover:bg-voicechain-accent text-white"
                        onClick={handleTextReply}
                        disabled={!textReplyContent.trim()}
                      >
                        Reply
                      </Button>
                    </div>
                  </div>
                )}
              </div>

              {/* Replies List */}
              {localReplies.length > 0 ? (
                <div className="space-y-3">
                  <h4 className="text-sm font-medium">Replies</h4>
                  {localReplies.map((reply) => (
                    <VoiceMessageWithoutChannel
                      key={reply.id}
                      {...reply}
                      isReply={true}
                      onReply={onReply}
                      onDelete={onDelete}
                      onPin={onPin}
                      replies={reply.replies || []}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 text-sm text-muted-foreground">
                  No replies yet. Be the first to reply!
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Tip Modal */}
      <TipModalWithoutWallet
        isOpen={isTipModalOpen}
        onClose={() => setIsTipModalOpen(false)}
        recipientAddress={userAddress}
        messageId={id}
      />

      {/* Summon Modal */}
      <VoiceSummonModalWithoutChannel
        isOpen={isSummonModalOpen}
        onClose={() => setIsSummonModalOpen(false)}
        recipientAddress={undefined} // Don't pre-select recipient, let user choose
        channelId={null}
        messageId={id}
        context="feed"
        onSummonComplete={(summonData) => {
          // Add the summon to the replies
          setReplies([...localReplies, summonData]);
          // Expand the post to show the new summon
          setIsExpanded(true);
        }}
      />

      {/* Reply Modal */}
      <ReplyModal
        isOpen={isReplyModalOpen}
        onClose={() => setIsReplyModalOpen(false)}
        onReplyComplete={handleReplyComplete}
        parentId={id}
      />

      {/* Custom Dialogs */}
      <DeletePostDialog
        isOpen={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={handleDeleteConfirm}
      />

      <PinPostDialog
        isOpen={isPinDialogOpen}
        onOpenChange={setIsPinDialogOpen}
        onConfirm={handlePinConfirm}
        isPinning={pinActionType === 'pin'}
      />

      <PostDeletedDialog
        isOpen={isPostDeletedDialogOpen}
        onOpenChange={setIsPostDeletedDialogOpen}
      />

      <PostPinnedDialog
        isOpen={isPostPinnedDialogOpen}
        onOpenChange={setIsPostPinnedDialogOpen}
        isPinned={pinActionType === 'pin'}
      />
    </div>
  );
};

export default VoiceMessageWithoutChannel;
