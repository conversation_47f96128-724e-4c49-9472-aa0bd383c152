import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogTitle, DialogHeader, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

const TestDialog: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div>
      <Button 
        onClick={() => {
          console.log('Test button clicked');
          setIsOpen(true);
          console.log('isOpen set to true');
        }}
      >
        Open Test Dialog
      </Button>

      <Dialog 
        open={isOpen} 
        onOpenChange={(open) => {
          console.log('Dialog onOpenChange triggered, new open state:', open);
          setIsOpen(open);
        }}
      >
        <DialogContent className="sm:max-w-md p-6">
          <DialogHeader className="p-0">
            <div className="flex justify-between items-center w-full mb-4">
              <DialogTitle className="text-xl font-semibold">Test Dialog</DialogTitle>
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={() => {
                  console.log('Close button clicked');
                  setIsOpen(false);
                }} 
                type="button"
              >
                <X size={20} />
              </Button>
            </div>
          </DialogHeader>
          <div className="py-4">
            This is a test dialog to verify that the Dialog component is working correctly.
          </div>
          <DialogFooter>
            <Button 
              onClick={() => {
                console.log('Close button in footer clicked');
                setIsOpen(false);
              }}
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TestDialog;
