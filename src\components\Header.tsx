
import React from 'react';
import WalletConnect from './WalletConnect';
import { LogOut } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

interface HeaderProps {
  onWalletConnect: (account: string) => void;
  connectedAccount: string;
}

const Header: React.FC<HeaderProps> = ({ onWalletConnect, connectedAccount }) => {
  const isMobile = useIsMobile();
  const { isAuthenticated, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <header className="sticky top-0 z-20 backdrop-blur-md bg-voicechain-dark/80 dark:bg-voicechain-dark/80 light:bg-white/80 border-b border-border/50">
      {/* Safe area spacer for status bar - fallback to 44px for iOS, 24px for Android */}
      <div className="h-safe-top bg-voicechain-dark/80 dark:bg-voicechain-dark/80 light:bg-white/80"
           style={{
             height: 'env(safe-area-inset-top, 44px)',
             minHeight: '24px'
           }}></div>

      {/* Header content */}
      <div className="px-4 py-3">
        <div className="container mx-auto flex justify-between items-center">
        <div className="flex items-center">
          <h1 className="text-2xl font-bold bg-gradient-to-r from-voicechain-purple to-voicechain-accent bg-clip-text text-transparent">
            Audra
          </h1>
        </div>

        <div className="ml-auto flex items-center gap-2">
          {isAuthenticated && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLogout}
              className="flex items-center gap-1 text-sm"
            >
              <LogOut size={16} />
              <span className="hidden sm:inline">Logout</span>
            </Button>
          )}
          {!isAuthenticated && !connectedAccount && <WalletConnect onConnect={onWalletConnect} />}
        </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
