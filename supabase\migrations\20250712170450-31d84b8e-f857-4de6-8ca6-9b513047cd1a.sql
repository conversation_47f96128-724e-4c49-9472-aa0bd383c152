-- Universal profile setup trigger for all users
-- This ensures ANY authenticated user gets a profile automatically

-- First, let's create a function that ensures any user has a profile
CREATE OR REPLACE FUNCTION public.ensure_user_profile()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_id_text TEXT;
  unique_username TEXT;
  username_suffix INTEGER := 0;
BEGIN
  -- Convert UUID to text for consistency
  user_id_text := NEW.id::text;
  
  -- Generate base username from user ID
  unique_username := 'user_' || substr(replace(user_id_text, '-', ''), 1, 8);
  
  -- Make sure username is unique
  WHILE EXISTS(SELECT 1 FROM profiles WHERE username = unique_username) LOOP
    username_suffix := username_suffix + 1;
    unique_username := 'user_' || substr(replace(user_id_text, '-', ''), 1, 8) || '_' || username_suffix;
  END LOOP;
  
  -- Insert profile for the new user
  INSERT INTO profiles (
    id,
    wallet_address,
    username,
    display_name,
    bio,
    avatar_url,
    cover_image_url,
    social_links,
    created_at,
    updated_at
  ) VALUES (
    user_id_text,
    user_id_text,
    unique_username,
    'User',
    'Welcome to my profile!',
    '',
    '',
    '{}',
    NOW(),
    NOW()
  )
  ON CONFLICT (id) DO NOTHING; -- Don't overwrite existing profiles
  
  RETURN NEW;
END;
$$;

-- Create trigger to automatically create profiles for new users
DROP TRIGGER IF EXISTS create_profile_on_signup ON auth.users;
CREATE TRIGGER create_profile_on_signup
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.ensure_user_profile();

-- Also create a function to handle existing users without profiles
CREATE OR REPLACE FUNCTION public.get_or_create_user_profile(p_user_id TEXT)
RETURNS TABLE(
  id TEXT,
  wallet_address TEXT,
  username TEXT,
  display_name TEXT,
  bio TEXT,
  avatar_url TEXT,
  cover_image_url TEXT,
  social_links JSONB,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  unique_username TEXT;
  username_suffix INTEGER := 0;
BEGIN
  -- Check if profile exists
  IF EXISTS(SELECT 1 FROM profiles WHERE profiles.id = p_user_id) THEN
    -- Return existing profile
    RETURN QUERY
    SELECT 
      p.id,
      p.wallet_address,
      p.username,
      p.display_name,
      p.bio,
      p.avatar_url,
      p.cover_image_url,
      p.social_links,
      p.created_at,
      p.updated_at
    FROM profiles p
    WHERE p.id = p_user_id;
  ELSE
    -- Generate unique username
    unique_username := 'user_' || substr(replace(p_user_id, '-', ''), 1, 8);
    
    WHILE EXISTS(SELECT 1 FROM profiles WHERE username = unique_username) LOOP
      username_suffix := username_suffix + 1;
      unique_username := 'user_' || substr(replace(p_user_id, '-', ''), 1, 8) || '_' || username_suffix;
    END LOOP;
    
    -- Create new profile
    INSERT INTO profiles (
      id,
      wallet_address,
      username,
      display_name,
      bio,
      avatar_url,
      cover_image_url,
      social_links,
      created_at,
      updated_at
    ) VALUES (
      p_user_id,
      p_user_id,
      unique_username,
      'User',
      'Welcome to my profile!',
      '',
      '',
      '{}',
      NOW(),
      NOW()
    );
    
    -- Return the newly created profile
    RETURN QUERY
    SELECT 
      p.id,
      p.wallet_address,
      p.username,
      p.display_name,
      p.bio,
      p.avatar_url,
      p.cover_image_url,
      p.social_links,
      p.created_at,
      p.updated_at
    FROM profiles p
    WHERE p.id = p_user_id;
  END IF;
END;
$$;