import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Shield, UserPlus, Trash2, AlertTriangle, Loader2 } from 'lucide-react';
import { toast } from '@/components/ui/toast';
import { supabase } from '@/services/supabase';
import { useAuth } from '@/contexts/AuthContext';

interface AdminUser {
  id: string;
  email: string;
  role: 'admin' | 'super_admin';
  created_at: string;
}

const AdminManagement: React.FC = () => {
  const { user } = useAuth();
  const [admins, setAdmins] = useState<AdminUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [newAdminEmail, setNewAdminEmail] = useState('');
  const [newAdminRole, setNewAdminRole] = useState<'admin' | 'super_admin'>('admin');
  const [isAddingAdmin, setIsAddingAdmin] = useState(false);
  const [processingId, setProcessingId] = useState<string | null>(null);

  useEffect(() => {
    fetchAdmins();
  }, []);

  const fetchAdmins = async () => {
    try {
      setIsLoading(true);

      // Fetch admin profiles from Supabase
      const { data, error } = await supabase
        .from('admin_profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      setAdmins(data as AdminUser[]);
    } catch (error) {
      console.error('Error fetching admins:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch admin users',
        duration: 3000
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddAdmin = async () => {
    if (!newAdminEmail) {
      toast({
        title: 'Error',
        description: 'Please enter an email address',
        duration: 3000
      });
      return;
    }

    try {
      setIsAddingAdmin(true);

      // First, check if the user exists in Supabase Auth
      // Note: In a real implementation, you would use the admin API to get user by email
      // For now, we'll use a workaround to get the user
      const { data: userData, error: userError } = await supabase.auth.admin.getUserById(newAdminEmail);

      if (userError) {
        throw userError;
      }

      if (!userData || !userData.user) {
        // User doesn't exist, we need to invite them
        toast({
          title: 'User Not Found',
          description: 'This user does not exist in the system. Please invite them first.',
          duration: 5000
        });
        return;
      }

      // Check if user is already an admin
      const { data: existingAdmin, error: checkError } = await supabase
        .from('admin_profiles')
        .select('*')
        .eq('id', userData.user.id)
        .single();

      if (existingAdmin) {
        toast({
          title: 'Error',
          description: 'This user is already an admin',
          duration: 3000
        });
        return;
      }

      // Add user to admin_profiles
      const { error: insertError } = await supabase
        .from('admin_profiles')
        .insert({
          id: userData.user.id,
          email: newAdminEmail,
          role: newAdminRole,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (insertError) {
        throw insertError;
      }

      toast({
        title: 'Admin Added',
        description: `${newAdminEmail} has been added as an ${newAdminRole}`,
        duration: 3000
      });

      // Reset form and refresh list
      setNewAdminEmail('');
      setNewAdminRole('admin');
      fetchAdmins();
    } catch (error) {
      console.error('Error adding admin:', error);
      toast({
        title: 'Error',
        description: 'Failed to add admin user',
        duration: 3000
      });
    } finally {
      setIsAddingAdmin(false);
    }
  };

  const handleRemoveAdmin = async (adminId: string) => {
    // Don't allow removing yourself
    if (adminId === user?.id) {
      toast({
        title: 'Error',
        description: 'You cannot remove yourself as an admin',
        duration: 3000
      });
      return;
    }

    try {
      setProcessingId(adminId);

      // Remove from admin_profiles
      const { error } = await supabase
        .from('admin_profiles')
        .delete()
        .eq('id', adminId);

      if (error) {
        throw error;
      }

      toast({
        title: 'Admin Removed',
        description: 'The admin user has been removed',
        duration: 3000
      });

      // Refresh list
      fetchAdmins();
    } catch (error) {
      console.error('Error removing admin:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove admin user',
        duration: 3000
      });
    } finally {
      setProcessingId(null);
    }
  };

  const handleUpdateRole = async (adminId: string, newRole: 'admin' | 'super_admin') => {
    try {
      setProcessingId(adminId);

      // Update role in admin_profiles
      const { error } = await supabase
        .from('admin_profiles')
        .update({
          role: newRole,
          updated_at: new Date().toISOString()
        })
        .eq('id', adminId);

      if (error) {
        throw error;
      }

      toast({
        title: 'Role Updated',
        description: `Admin role has been updated to ${newRole}`,
        duration: 3000
      });

      // Refresh list
      fetchAdmins();
    } catch (error) {
      console.error('Error updating admin role:', error);
      toast({
        title: 'Error',
        description: 'Failed to update admin role',
        duration: 3000
      });
    } finally {
      setProcessingId(null);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Admin Management</CardTitle>
          <CardDescription>
            Manage admin users and their permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          {user?.role === 'super_admin' && (
            <div className="space-y-6 mb-8">
              <h3 className="text-lg font-medium">Add New Admin</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="admin-email">Email Address</Label>
                  <Input
                    id="admin-email"
                    placeholder="Enter email address"
                    value={newAdminEmail}
                    onChange={(e) => setNewAdminEmail(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="admin-role">Role</Label>
                  <Select
                    value={newAdminRole}
                    onValueChange={(value) => setNewAdminRole(value as 'admin' | 'super_admin')}
                  >
                    <SelectTrigger id="admin-role">
                      <SelectValue placeholder="Select role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="admin">Admin</SelectItem>
                      <SelectItem value="super_admin">Super Admin</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <Button
                onClick={handleAddAdmin}
                disabled={isAddingAdmin || !newAdminEmail}
                className="mt-2"
              >
                {isAddingAdmin ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Adding...
                  </>
                ) : (
                  <>
                    <UserPlus className="mr-2 h-4 w-4" />
                    Add Admin
                  </>
                )}
              </Button>
              <Separator className="my-6" />
            </div>
          )}

          <h3 className="text-lg font-medium mb-4">Admin Users</h3>

          {isLoading ? (
            <div className="text-center py-8">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
              <p>Loading admin users...</p>
            </div>
          ) : admins.length === 0 ? (
            <div className="text-center py-8">
              <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-medium">No admin users found</h3>
              <p className="text-muted-foreground">
                Add your first admin user to get started
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {admins.map((admin) => (
                <Card key={admin.id} className="overflow-hidden">
                  <div className="p-4">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between">
                      <div className="flex items-center mb-4 sm:mb-0">
                        <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                          <Shield className="h-5 w-5 text-purple-600" />
                        </div>
                        <div>
                          <h3 className="font-medium">{admin.email}</h3>
                          <div className="flex items-center mt-1">
                            <Badge className={admin.role === 'super_admin' ? 'bg-purple-600' : 'bg-blue-600'}>
                              {admin.role === 'super_admin' ? 'Super Admin' : 'Admin'}
                            </Badge>
                            {admin.id === user?.id && (
                              <Badge variant="outline" className="ml-2">
                                You
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>

                      {user?.role === 'super_admin' && (
                        <div className="flex items-center space-x-2">
                          {admin.id !== user?.id && (
                            <>
                              <Select
                                value={admin.role}
                                onValueChange={(value) => handleUpdateRole(admin.id, value as 'admin' | 'super_admin')}
                                disabled={processingId === admin.id}
                              >
                                <SelectTrigger className="w-[140px]">
                                  <SelectValue placeholder="Select role" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="admin">Admin</SelectItem>
                                  <SelectItem value="super_admin">Super Admin</SelectItem>
                                </SelectContent>
                              </Select>

                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => handleRemoveAdmin(admin.id)}
                                disabled={processingId === admin.id}
                              >
                                {processingId === admin.id ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <Trash2 className="h-4 w-4" />
                                )}
                              </Button>
                            </>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminManagement;
