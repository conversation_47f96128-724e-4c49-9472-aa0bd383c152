
import { useState, useRef, useCallback } from 'react';

interface UseRecordOptions {
  onDataAvailable?: (data: Blob) => void;
  mimeType?: string;
}

interface UseRecordReturn {
  isRecording: boolean;
  recordingTime: number;
  startRecording: () => Promise<void>;
  stopRecording: () => Promise<Blob>;
  recordedBlob: Blob | null;
}

export const useRecord = (options: UseRecordOptions = {}): UseRecordReturn => {
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [recordedBlob, setRecordedBlob] = useState<Blob | null>(null);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const startRecording = useCallback(async () => {
    try {
      audioChunksRef.current = [];
      setRecordedBlob(null);

      // Request audio with specific constraints for better quality
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100,
          channelCount: 1
        }
      });

      streamRef.current = stream;

      // Log audio tracks to debug
      const audioTracks = stream.getAudioTracks();
      console.log("Audio tracks:", audioTracks);
      console.log("Audio track settings:", audioTracks[0]?.getSettings());

      // Try to use a more compatible audio format
      let mimeType = options.mimeType || 'audio/webm;codecs=opus';
      
      // Check if the browser supports this format
      if (!MediaRecorder.isTypeSupported(mimeType)) {
        // Fallback to default
        console.log(`${mimeType} not supported, trying audio/webm`);
        mimeType = 'audio/webm';
        
        if (!MediaRecorder.isTypeSupported(mimeType)) {
          console.log(`${mimeType} not supported either, using default format`);
          mimeType = '';
        }
      }
      
      console.log("Using audio format:", mimeType || "default");

      const recorderOptions: MediaRecorderOptions = mimeType ? { mimeType } : {};
      mediaRecorderRef.current = new MediaRecorder(stream, recorderOptions);

      // Make sure we're getting data
      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
          console.log(`Received audio chunk: ${event.data.size} bytes, total chunks: ${audioChunksRef.current.length}`);
          
          // Call onDataAvailable if provided
          if (options.onDataAvailable) {
            options.onDataAvailable(event.data);
          }
        } else {
          console.warn("Received empty data chunk");
        }
      };

      // Start recording with smaller chunks for better reliability
      mediaRecorderRef.current.start(500); // 500ms chunks
      console.log("MediaRecorder started with timeslice:", 500);
      setIsRecording(true);

      // Start recording timer
      setRecordingTime(0);
      timerRef.current = setInterval(() => {
        setRecordingTime(prevTime => prevTime + 1);
      }, 1000);

    } catch (error) {
      console.error('Error accessing microphone:', error);
      throw error;
    }
  }, [options]);

  const stopRecording = useCallback(async (): Promise<Blob> => {
    return new Promise((resolve, reject) => {
      if (!mediaRecorderRef.current || !isRecording) {
        reject(new Error('Not recording'));
        return;
      }

      // Set up the onstop handler to resolve the promise
      mediaRecorderRef.current.onstop = () => {
        // Stop all tracks
        if (streamRef.current) {
          streamRef.current.getTracks().forEach(track => {
            track.stop();
            console.log(`Track ${track.kind} stopped`);
          });
          streamRef.current = null;
        }
        
        // Check if we have any audio data
        if (audioChunksRef.current.length === 0) {
          reject(new Error('No audio data recorded'));
          return;
        }
        
        // Get the correct MIME type
        let mimeType = mediaRecorderRef.current!.mimeType;
        if (!mimeType) {
          mimeType = 'audio/webm';
        }
        
        // Create the final audio blob
        const audioBlob = new Blob(audioChunksRef.current, { type: mimeType });
        console.log("Created audio blob:", audioBlob.size, "bytes, type:", audioBlob.type);
        
        if (audioBlob.size === 0) {
          reject(new Error('Created an empty audio blob'));
          return;
        }
        
        setRecordedBlob(audioBlob);
        resolve(audioBlob);
      };

      // Add error handler
      mediaRecorderRef.current.onerror = (event) => {
        reject(new Error('MediaRecorder error: ' + event.toString()));
      };

      try {
        // Request a final data chunk before stopping
        if (mediaRecorderRef.current.state === 'recording') {
          mediaRecorderRef.current.requestData();
        }
        
        // Stop the media recorder after a small delay to ensure the final chunk is processed
        setTimeout(() => {
          try {
            if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
              mediaRecorderRef.current.stop();
              console.log('MediaRecorder stopped');
            }
          } catch (stopError) {
            console.error('Error stopping MediaRecorder:', stopError);
            reject(stopError);
          }
        }, 100);
      } catch (error) {
        console.error('Error in stopRecording:', error);
        reject(error);
      }

      // Clear timer
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }

      setIsRecording(false);
    });
  }, [isRecording]);

  return {
    isRecording,
    recordingTime,
    startRecording,
    stopRecording,
    recordedBlob
  };
};

export default useRecord;
