-- This SQL script fixes the infinite recursion issue in the RLS policies

-- First, let's disable the problematic policies
ALTER TABLE channel_members DISABLE ROW LEVEL SECURITY;

-- Create a new policy for channel_members that doesn't cause recursion
CREATE OR REPLACE FUNCTION channel_member_policy() RETURNS boolean AS $$
BEGIN
  -- Simple policy that doesn't reference other tables with policies
  RETURN true;
END;
$$ LANGUAGE plpgsql;

-- Re-enable R<PERSON> with the new policy
ALTER TABLE channel_members ENABLE ROW LEVEL SECURITY;
CREATE POLICY channel_members_policy ON channel_members USING (channel_member_policy());

-- Create a function to get user messages without using problematic joins
CREATE OR REPLACE FUNCTION get_user_messages(user_address TEXT)
RETURNS TABLE (
  id UUID,
  profile_id TEXT,
  audio_url TEXT,
  transcript TEXT,
  created_at TIMESTAMPTZ,
  audio_duration INTEGER,
  parent_id UUID,
  is_pinned BOOLEAN,
  deleted_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    vm.id,
    vm.profile_id,
    vm.audio_url,
    vm.transcript,
    vm.created_at,
    vm.audio_duration,
    vm.parent_id,
    vm.is_pinned,
    vm.deleted_at
  FROM voice_messages vm
  WHERE vm.profile_id = user_address
  AND vm.deleted_at IS NULL
  ORDER BY vm.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get a user's pinned message
CREATE OR REPLACE FUNCTION get_pinned_message(user_address TEXT)
RETURNS TABLE (
  id UUID,
  profile_id TEXT,
  audio_url TEXT,
  transcript TEXT,
  created_at TIMESTAMPTZ,
  audio_duration INTEGER,
  parent_id UUID,
  is_pinned BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    vm.id,
    vm.profile_id,
    vm.audio_url,
    vm.transcript,
    vm.created_at,
    vm.audio_duration,
    vm.parent_id,
    vm.is_pinned
  FROM voice_messages vm
  WHERE vm.profile_id = user_address
  AND vm.is_pinned = true
  AND vm.deleted_at IS NULL
  LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Refresh the schema cache
SELECT pg_notify('pgrst', 'reload schema');
