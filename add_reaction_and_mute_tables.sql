-- Create post_reactions table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.post_reactions (
  id UUID PRIMARY KEY,
  post_id TEXT NOT NULL,
  user_id TEXT NOT NULL,
  reaction_type TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(post_id, user_id, reaction_type)
);

-- Create user_mutes table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.user_mutes (
  id UUID PRIMARY KEY,
  user_id TEXT NOT NULL,
  target_id TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('user', 'post')),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(user_id, target_id, type)
);

-- Enable RLS on post_reactions table
ALTER TABLE public.post_reactions ENABLE ROW LEVEL SECURITY;

-- Enable RLS on user_mutes table
ALTER TABLE public.user_mutes ENABLE ROW LEVEL SECURITY;

-- Create policies for post_reactions table

-- 1. Allow users to view all reactions
CREATE POLICY "Users can view all reactions"
ON public.post_reactions
FOR SELECT
USING (true);

-- 2. Allow users to insert their own reactions
CREATE POLICY "Users can insert their own reactions"
ON public.post_reactions
FOR INSERT
WITH CHECK (user_id = auth.uid());

-- 3. Allow users to update their own reactions
CREATE POLICY "Users can update their own reactions"
ON public.post_reactions
FOR UPDATE
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

-- 4. Allow users to delete their own reactions
CREATE POLICY "Users can delete their own reactions"
ON public.post_reactions
FOR DELETE
USING (user_id = auth.uid());

-- Create policies for user_mutes table

-- 1. Allow users to view their own mutes
CREATE POLICY "Users can view their own mutes"
ON public.user_mutes
FOR SELECT
USING (user_id = auth.uid());

-- 2. Allow users to insert their own mutes
CREATE POLICY "Users can insert their own mutes"
ON public.user_mutes
FOR INSERT
WITH CHECK (user_id = auth.uid());

-- 3. Allow users to delete their own mutes
CREATE POLICY "Users can delete their own mutes"
ON public.user_mutes
FOR DELETE
USING (user_id = auth.uid());

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS post_reactions_post_id_idx ON public.post_reactions(post_id);
CREATE INDEX IF NOT EXISTS post_reactions_user_id_idx ON public.post_reactions(user_id);
CREATE INDEX IF NOT EXISTS user_mutes_user_id_idx ON public.user_mutes(user_id);
CREATE INDEX IF NOT EXISTS user_mutes_target_id_idx ON public.user_mutes(target_id);

-- Create function to get reactions for a post
CREATE OR REPLACE FUNCTION get_post_reactions(post_id_param TEXT)
RETURNS TABLE (
  id UUID,
  post_id TEXT,
  user_id TEXT,
  reaction_type TEXT,
  created_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT pr.id, pr.post_id, pr.user_id, pr.reaction_type, pr.created_at
  FROM public.post_reactions pr
  WHERE pr.post_id = post_id_param;
END;
$$ LANGUAGE plpgsql;

-- Create function to get muted users for a user
CREATE OR REPLACE FUNCTION get_muted_users(user_id_param TEXT)
RETURNS TABLE (
  id UUID,
  target_id TEXT,
  created_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT um.id, um.target_id, um.created_at
  FROM public.user_mutes um
  WHERE um.user_id = user_id_param
  AND um.type = 'user';
END;
$$ LANGUAGE plpgsql;

-- Create function to get muted posts for a user
CREATE OR REPLACE FUNCTION get_muted_posts(user_id_param TEXT)
RETURNS TABLE (
  id UUID,
  target_id TEXT,
  created_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT um.id, um.target_id, um.created_at
  FROM public.user_mutes um
  WHERE um.user_id = user_id_param
  AND um.type = 'post';
END;
$$ LANGUAGE plpgsql;
