import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface NotificationSettings {
  mentions: boolean;
  replies: boolean;
  likes: boolean;
  tips: boolean;
  channelInvites: boolean;
  newFollowers: boolean;
  systemAnnouncements: boolean;
  emailNotifications: boolean;
  pushNotifications: boolean;
}

interface PrivacySettings {
  showOnlineStatus: boolean;
  allowDirectMessages: boolean;
  allowMentions: boolean;
  showListeningActivity: boolean;
  allowProfileViews: boolean;
  allowSearchDiscovery: boolean;
}

interface AudioSettings {
  autoPlayVoiceMessages: boolean;
  highQualityAudio: boolean;
  reduceBackground: boolean;
  enhanceSpeech: boolean;
  defaultPlaybackSpeed: number;
  defaultRecordingQuality: 'low' | 'medium' | 'high';
}

interface AccessibilitySettings {
  reduceAnimations: boolean;
  highContrastMode: boolean;
  largerText: boolean;
  screenReaderOptimized: boolean;
}

interface LanguageSettings {
  appLanguage: string;
  voiceTranscriptionLanguage: string;
}

interface WalletSettings {
  defaultTipAmount: string;
  showBalanceInHeader: boolean;
  confirmBeforeTipping: boolean;
  defaultGasSettings: 'low' | 'medium' | 'high' | 'custom';
}

interface SettingsContextType {
  notifications: NotificationSettings;
  privacy: PrivacySettings;
  audio: AudioSettings;
  accessibility: AccessibilitySettings;
  language: LanguageSettings;
  wallet: WalletSettings;
  
  updateNotificationSettings: (settings: Partial<NotificationSettings>) => void;
  updatePrivacySettings: (settings: Partial<PrivacySettings>) => void;
  updateAudioSettings: (settings: Partial<AudioSettings>) => void;
  updateAccessibilitySettings: (settings: Partial<AccessibilitySettings>) => void;
  updateLanguageSettings: (settings: Partial<LanguageSettings>) => void;
  updateWalletSettings: (settings: Partial<WalletSettings>) => void;
  resetSettings: () => void;
}

const defaultNotificationSettings: NotificationSettings = {
  mentions: true,
  replies: true,
  likes: true,
  tips: true,
  channelInvites: true,
  newFollowers: true,
  systemAnnouncements: true,
  emailNotifications: false,
  pushNotifications: true,
};

const defaultPrivacySettings: PrivacySettings = {
  showOnlineStatus: true,
  allowDirectMessages: true,
  allowMentions: true,
  showListeningActivity: true,
  allowProfileViews: true,
  allowSearchDiscovery: true,
};

const defaultAudioSettings: AudioSettings = {
  autoPlayVoiceMessages: true,
  highQualityAudio: true,
  reduceBackground: true,
  enhanceSpeech: true,
  defaultPlaybackSpeed: 1.0,
  defaultRecordingQuality: 'high',
};

const defaultAccessibilitySettings: AccessibilitySettings = {
  reduceAnimations: false,
  highContrastMode: false,
  largerText: false,
  screenReaderOptimized: false,
};

const defaultLanguageSettings: LanguageSettings = {
  appLanguage: 'en',
  voiceTranscriptionLanguage: 'en-US',
};

const defaultWalletSettings: WalletSettings = {
  defaultTipAmount: '0.01',
  showBalanceInHeader: true,
  confirmBeforeTipping: true,
  defaultGasSettings: 'medium',
};

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

interface SettingsProviderProps {
  children: ReactNode;
}

export const SettingsProvider: React.FC<SettingsProviderProps> = ({ children }) => {
  // Initialize state with default values or values from localStorage
  const [notifications, setNotifications] = useState<NotificationSettings>(() => {
    const saved = localStorage.getItem('voicechain-notification-settings');
    return saved ? JSON.parse(saved) : defaultNotificationSettings;
  });
  
  const [privacy, setPrivacy] = useState<PrivacySettings>(() => {
    const saved = localStorage.getItem('voicechain-privacy-settings');
    return saved ? JSON.parse(saved) : defaultPrivacySettings;
  });
  
  const [audio, setAudio] = useState<AudioSettings>(() => {
    const saved = localStorage.getItem('voicechain-audio-settings');
    return saved ? JSON.parse(saved) : defaultAudioSettings;
  });
  
  const [accessibility, setAccessibility] = useState<AccessibilitySettings>(() => {
    const saved = localStorage.getItem('voicechain-accessibility-settings');
    return saved ? JSON.parse(saved) : defaultAccessibilitySettings;
  });
  
  const [language, setLanguage] = useState<LanguageSettings>(() => {
    const saved = localStorage.getItem('voicechain-language-settings');
    return saved ? JSON.parse(saved) : defaultLanguageSettings;
  });
  
  const [wallet, setWallet] = useState<WalletSettings>(() => {
    const saved = localStorage.getItem('voicechain-wallet-settings');
    return saved ? JSON.parse(saved) : defaultWalletSettings;
  });
  
  // Save settings to localStorage when they change
  useEffect(() => {
    localStorage.setItem('voicechain-notification-settings', JSON.stringify(notifications));
  }, [notifications]);
  
  useEffect(() => {
    localStorage.setItem('voicechain-privacy-settings', JSON.stringify(privacy));
  }, [privacy]);
  
  useEffect(() => {
    localStorage.setItem('voicechain-audio-settings', JSON.stringify(audio));
  }, [audio]);
  
  useEffect(() => {
    localStorage.setItem('voicechain-accessibility-settings', JSON.stringify(accessibility));
  }, [accessibility]);
  
  useEffect(() => {
    localStorage.setItem('voicechain-language-settings', JSON.stringify(language));
  }, [language]);
  
  useEffect(() => {
    localStorage.setItem('voicechain-wallet-settings', JSON.stringify(wallet));
  }, [wallet]);
  
  // Update functions
  const updateNotificationSettings = (settings: Partial<NotificationSettings>) => {
    setNotifications(prev => ({ ...prev, ...settings }));
  };
  
  const updatePrivacySettings = (settings: Partial<PrivacySettings>) => {
    setPrivacy(prev => ({ ...prev, ...settings }));
  };
  
  const updateAudioSettings = (settings: Partial<AudioSettings>) => {
    setAudio(prev => ({ ...prev, ...settings }));
  };
  
  const updateAccessibilitySettings = (settings: Partial<AccessibilitySettings>) => {
    setAccessibility(prev => ({ ...prev, ...settings }));
  };
  
  const updateLanguageSettings = (settings: Partial<LanguageSettings>) => {
    setLanguage(prev => ({ ...prev, ...settings }));
  };
  
  const updateWalletSettings = (settings: Partial<WalletSettings>) => {
    setWallet(prev => ({ ...prev, ...settings }));
  };
  
  // Reset all settings to defaults
  const resetSettings = () => {
    setNotifications(defaultNotificationSettings);
    setPrivacy(defaultPrivacySettings);
    setAudio(defaultAudioSettings);
    setAccessibility(defaultAccessibilitySettings);
    setLanguage(defaultLanguageSettings);
    setWallet(defaultWalletSettings);
  };
  
  return (
    <SettingsContext.Provider
      value={{
        notifications,
        privacy,
        audio,
        accessibility,
        language,
        wallet,
        updateNotificationSettings,
        updatePrivacySettings,
        updateAudioSettings,
        updateAccessibilitySettings,
        updateLanguageSettings,
        updateWalletSettings,
        resetSettings,
      }}
    >
      {children}
    </SettingsContext.Provider>
  );
};
