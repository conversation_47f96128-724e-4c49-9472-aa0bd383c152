-- Create admin_profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS admin_profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL UNIQUE,
  role TEXT NOT NULL CHECK (role IN ('admin', 'super_admin')),
  display_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_login TIMESTAMP WITH TIME ZONE
);

-- Create a trigger to update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger on admin_profiles
DROP TRIGGER IF EXISTS update_admin_profiles_updated_at ON admin_profiles;
CREATE TRIGGER update_admin_profiles_updated_at
BEFORE UPDATE ON admin_profiles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create a function to create the first super admin
CREATE OR REPLACE FUNCTION create_first_super_admin(
  p_user_id UUID,
  p_email TEXT,
  p_display_name TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  v_count INTEGER;
  v_admin_id UUID;
BEGIN
  -- Check if the user exists in auth.users
  SELECT COUNT(*) INTO v_count FROM auth.users WHERE id = p_user_id;

  IF v_count = 0 THEN
    RAISE EXCEPTION 'User with ID % does not exist in auth.users', p_user_id;
  END IF;

  -- Check if there are any existing admins
  SELECT COUNT(*) INTO v_count FROM admin_profiles;

  IF v_count > 0 THEN
    -- Only allow creating more admins if the caller is a super_admin
    IF NOT EXISTS (
      SELECT 1 FROM admin_profiles
      WHERE id = auth.uid()
      AND role = 'super_admin'
    ) THEN
      RAISE EXCEPTION 'Only super admins can create new admin accounts';
    END IF;
  END IF;

  -- Insert the new admin
  INSERT INTO admin_profiles (
    id,
    email,
    role,
    display_name,
    created_at,
    updated_at,
    last_login
  ) VALUES (
    p_user_id,
    p_email,
    'super_admin',
    COALESCE(p_display_name, p_email),
    NOW(),
    NOW(),
    NOW()
  )
  ON CONFLICT (id) DO UPDATE SET
    role = 'super_admin',
    updated_at = NOW(),
    last_login = NOW()
  RETURNING id INTO v_admin_id;

  RETURN v_admin_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to update admin last login
CREATE OR REPLACE FUNCTION update_admin_last_login(
  p_admin_id UUID
)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE admin_profiles
  SET last_login = NOW()
  WHERE id = p_admin_id;

  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to create a new admin
CREATE OR REPLACE FUNCTION create_admin(
  p_user_id UUID,
  p_email TEXT,
  p_role TEXT,
  p_display_name TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  v_count INTEGER;
  v_admin_id UUID;
BEGIN
  -- Check if the user exists in auth.users
  SELECT COUNT(*) INTO v_count FROM auth.users WHERE id = p_user_id;

  IF v_count = 0 THEN
    RAISE EXCEPTION 'User with ID % does not exist in auth.users', p_user_id;
  END IF;

  -- Check if the caller is a super_admin
  IF NOT EXISTS (
    SELECT 1 FROM admin_profiles
    WHERE id = auth.uid()
    AND role = 'super_admin'
  ) THEN
    RAISE EXCEPTION 'Only super admins can create new admin accounts';
  END IF;

  -- Validate role
  IF p_role NOT IN ('admin', 'super_admin') THEN
    RAISE EXCEPTION 'Invalid role: %. Must be "admin" or "super_admin"', p_role;
  END IF;

  -- Insert the new admin
  INSERT INTO admin_profiles (
    id,
    email,
    role,
    display_name,
    created_at,
    updated_at
  ) VALUES (
    p_user_id,
    p_email,
    p_role,
    COALESCE(p_display_name, p_email),
    NOW(),
    NOW()
  )
  ON CONFLICT (id) DO UPDATE SET
    role = p_role,
    email = p_email,
    display_name = COALESCE(p_display_name, p_email),
    updated_at = NOW()
  RETURNING id INTO v_admin_id;

  RETURN v_admin_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Instructions:
-- 1. Run this script in the Supabase SQL editor
-- 2. This will create the admin_profiles table if it doesn't exist
-- 3. It will also create functions for managing admin profiles
-- 4. After running this script, run the fix_rls_policies.sql script to set up proper RLS policies
-- 5. To create the first super admin, run:
--    SELECT create_first_super_admin('your-user-id', 'your-email', 'Your Name');
--    Replace 'your-user-id' with the UUID of your Supabase user
