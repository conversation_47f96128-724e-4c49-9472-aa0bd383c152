-- Create function to get voice reactions for a message
CREATE OR R<PERSON>LACE FUNCTION get_message_voice_reactions(message_id_param TEXT)
RETURNS TABLE (
  id UUID,
  voice_message_id TEXT,
  profile_id TEXT,
  emoji TEXT,
  created_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    vr.id::UUID,
    vr.voice_message_id,
    vr.profile_id,
    vr.emoji,
    vr.created_at
  FROM 
    public.voice_reactions vr
  WHERE 
    vr.voice_message_id = message_id_param
  ORDER BY 
    vr.created_at ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
