import { supabase } from '@/integrations/supabase/client';
import { VoiceMessageProps } from '@/types/voice-message';

/**
 * Optimized voice message service with better performance
 */

// Get all voice messages with optimized queries
export async function getAllVoiceMessagesOptimized(): Promise<VoiceMessageProps[]> {
  try {
    console.log('🚀 Loading voice messages with optimized queries...');
    
    // Single query to get all messages with their media in one go
    const { data: messages, error } = await supabase
      .from('voice_messages')
      .select(`
        *,
        voice_message_media (
          id,
          url,
          type
        )
      `)
      .is('deleted_at', null)
      .order('created_at', { ascending: false })
      .limit(50); // Limit initial load for better performance

    if (error) {
      console.error('Error getting voice messages:', error);
      throw error;
    }

    if (!messages || messages.length === 0) {
      console.log('No voice messages found');
      return [];
    }

    console.log(`✅ Loaded ${messages.length} voice messages`);

    // Convert to VoiceMessageProps format
    const voiceMessages: VoiceMessageProps[] = messages.map((message) => ({
      id: message.id,
      userAddress: message.profile_id, // Using profile_id as userAddress
      audioUrl: message.audio_url,
      transcript: message.transcript || '',
      timestamp: new Date(message.created_at).getTime(),
      audioDuration: message.audio_duration || 0,
      media: message.voice_message_media?.map((media: any) => ({
        id: media.id,
        url: media.url,
        type: media.type as 'image' | 'video'
      })) || [],
      replies: [], // Load replies separately when needed
      isPinned: message.is_pinned || false,
      parentId: message.parent_id || undefined
    }));

    return voiceMessages;
  } catch (error) {
    console.error('Error in getAllVoiceMessagesOptimized:', error);
    return [];
  }
}

// Get user voice messages with optimized queries
export async function getUserVoiceMessagesOptimized(userAddress: string): Promise<VoiceMessageProps[]> {
  try {
    console.log(`🚀 Loading user messages for ${userAddress}...`);
    
    // Get user's profile ID first
    const { data: profile } = await supabase
      .from('profiles')
      .select('id')
      .eq('wallet_address', userAddress.toLowerCase())
      .single();

    if (!profile) {
      console.log('No profile found for user');
      return [];
    }

    // Single query to get user's messages with media
    const { data: messages, error } = await supabase
      .from('voice_messages')
      .select(`
        *,
        voice_message_media (
          id,
          url,
          type
        )
      `)
      .eq('profile_id', profile.id)
      .is('deleted_at', null)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error getting user voice messages:', error);
      throw error;
    }

    if (!messages || messages.length === 0) {
      return [];
    }

    console.log(`✅ Loaded ${messages.length} user messages`);

    // Convert to VoiceMessageProps format
    const voiceMessages: VoiceMessageProps[] = messages.map((message) => ({
      id: message.id,
      userAddress: userAddress,
      audioUrl: message.audio_url,
      transcript: message.transcript || '',
      timestamp: new Date(message.created_at).getTime(),
      audioDuration: message.audio_duration || 0,
      media: message.voice_message_media?.map((media: any) => ({
        id: media.id,
        url: media.url,
        type: media.type as 'image' | 'video'
      })) || [],
      replies: [],
      isPinned: message.is_pinned || false,
      parentId: message.parent_id || undefined
    }));

    return voiceMessages;
  } catch (error) {
    console.error('Error in getUserVoiceMessagesOptimized:', error);
    return [];
  }
}

// Load replies for a specific message (lazy loading)
export async function loadRepliesOptimized(messageId: string): Promise<VoiceMessageProps[]> {
  try {
    const { data: replies, error } = await supabase
      .from('voice_messages')
      .select(`
        *,
        voice_message_media (
          id,
          url,
          type
        )
      `)
      .eq('parent_id', messageId)
      .is('deleted_at', null)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Error loading replies:', error);
      return [];
    }

    if (!replies || replies.length === 0) {
      return [];
    }

    // Convert to VoiceMessageProps format
    const replyMessages: VoiceMessageProps[] = replies.map((reply) => ({
      id: reply.id,
      userAddress: reply.profile_id,
      audioUrl: reply.audio_url,
      transcript: reply.transcript || '',
      timestamp: new Date(reply.created_at).getTime(),
      audioDuration: reply.audio_duration || 0,
      media: reply.voice_message_media?.map((media: any) => ({
        id: media.id,
        url: media.url,
        type: media.type as 'image' | 'video'
      })) || [],
      replies: [],
      isPinned: reply.is_pinned || false,
      parentId: reply.parent_id
    }));

    return replyMessages;
  } catch (error) {
    console.error('Error in loadRepliesOptimized:', error);
    return [];
  }
}

// Delete voice message with better error handling
export async function deleteVoiceMessageOptimized(messageId: string, userAddress: string): Promise<boolean> {
  try {
    console.log(`🗑️ Deleting message ${messageId} by user ${userAddress}`);

    if (!messageId || !userAddress) {
      console.error('Invalid inputs for delete');
      return false;
    }

    // Get user's profile ID
    const { data: profile } = await supabase
      .from('profiles')
      .select('id')
      .eq('wallet_address', userAddress.toLowerCase())
      .single();

    if (!profile) {
      console.error('User profile not found');
      return false;
    }

    // Check if user owns the message
    const { data: message, error: messageError } = await supabase
      .from('voice_messages')
      .select('profile_id, deleted_at')
      .eq('id', messageId)
      .single();

    if (messageError || !message) {
      console.error('Message not found:', messageError);
      return false;
    }

    if (message.deleted_at) {
      console.log('Message already deleted');
      return true;
    }

    if (message.profile_id !== profile.id) {
      console.error('User does not own this message');
      return false;
    }

    // Soft delete the message
    const { error: deleteError } = await supabase
      .from('voice_messages')
      .update({ deleted_at: new Date().toISOString() })
      .eq('id', messageId);

    if (deleteError) {
      console.error('Error deleting message:', deleteError);
      return false;
    }

    console.log('✅ Message deleted successfully');
    return true;
  } catch (error) {
    console.error('Error in deleteVoiceMessageOptimized:', error);
    return false;
  }
}

// Check if user owns a message
export async function checkMessageOwnership(messageId: string, userAddress: string): Promise<boolean> {
  try {
    // Get user's profile ID
    const { data: profile } = await supabase
      .from('profiles')
      .select('id')
      .eq('wallet_address', userAddress.toLowerCase())
      .single();

    if (!profile) {
      return false;
    }

    // Check message ownership
    const { data: message } = await supabase
      .from('voice_messages')
      .select('profile_id')
      .eq('id', messageId)
      .single();

    return message?.profile_id === profile.id;
  } catch (error) {
    console.error('Error checking message ownership:', error);
    return false;
  }
}

// Pin/Unpin a message
export async function togglePinMessageOptimized(messageId: string, userAddress: string, currentlyPinned: boolean): Promise<boolean> {
  try {
    console.log(`${currentlyPinned ? 'Unpinning' : 'Pinning'} message ${messageId} for user ${userAddress}`);

    // Get user's profile ID
    const { data: profile } = await supabase
      .from('profiles')
      .select('id')
      .eq('wallet_address', userAddress.toLowerCase())
      .single();

    if (!profile) {
      console.error('User profile not found');
      return false;
    }

    // Check if user owns the message
    const { data: message, error: messageError } = await supabase
      .from('voice_messages')
      .select('profile_id, is_pinned')
      .eq('id', messageId)
      .single();

    if (messageError || !message) {
      console.error('Message not found:', messageError);
      return false;
    }

    if (message.profile_id !== profile.id) {
      console.error('User does not own this message');
      return false;
    }

    // If pinning (currentlyPinned = false, so we want to pin it)
    if (!currentlyPinned) {
      // First unpin all other messages by this user
      const { error: unpinError } = await supabase
        .from('voice_messages')
        .update({ is_pinned: false })
        .eq('profile_id', profile.id)
        .eq('is_pinned', true);

      if (unpinError) {
        console.error('Error unpinning other messages:', unpinError);
        // Continue anyway, this is not critical
      }
    }

    // Toggle the pin status
    const newPinStatus = !currentlyPinned;
    const { error: updateError } = await supabase
      .from('voice_messages')
      .update({ is_pinned: newPinStatus })
      .eq('id', messageId);

    if (updateError) {
      console.error('Error updating pin status:', updateError);
      return false;
    }

    console.log(`✅ Message ${newPinStatus ? 'pinned' : 'unpinned'} successfully`);
    return true;
  } catch (error) {
    console.error('Error in togglePinMessageOptimized:', error);
    return false;
  }
}
