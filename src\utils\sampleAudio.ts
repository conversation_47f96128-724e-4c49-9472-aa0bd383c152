/**
 * Sample Audio Generator
 *
 * This utility generates sample audio URLs for testing purposes.
 * It creates an AudioContext and generates a simple tone that can be played in the browser.
 */

// Create a simple beep sound using a pre-generated audio file
export function createBeepSound(duration: number = 2, frequency: number = 440): Promise<string> {
  // Instead of trying to generate audio on the fly, let's use a static audio file
  // This is more reliable across browsers
  return Promise.resolve('/audio/beep.mp3');
}

// Create a sample speech audio
export async function createSampleSpeechAudio(text: string): Promise<string> {
  // For now, we'll just return a static audio file
  // In a real implementation, this would use a text-to-speech API
  return '/audio/beep.mp3';
}
