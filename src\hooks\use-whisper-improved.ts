import { useState, useCallback } from 'react';
import { getStorageService } from '@/services/storageServiceFactory';

interface WhisperOptions {
  apiKey?: string;
  language?: string;
  onTranscriptionProgress?: (text: string) => void;
  onTranscriptionComplete?: (text: string) => void;
}

interface WhisperResult {
  transcribing: boolean;
  transcript: string;
  startTranscription: (audioBlob: Blob) => Promise<string>;
  stopTranscription: () => void;
}

/**
 * A hook for using speech-to-text transcription with Web Speech API fallback
 */
export const useWhisper = (options: WhisperOptions = {}): WhisperResult => {
  const [transcribing, setTranscribing] = useState(false);
  const [transcript, setTranscript] = useState('');

  // Function to transcribe audio using Web Speech API
  const transcribeWithWebSpeech = async (audioBlob: Blob): Promise<string> => {
    return new Promise((resolve) => {
      // Check if browser supports SpeechRecognition
      if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        console.error('Speech recognition not supported in this browser');
        resolve("Click here to add your message text");
        return;
      }

      try {
        // Create a direct URL for the audio blob
        const audioURL = URL.createObjectURL(audioBlob);
        
        // Create an audio element to analyze the audio
        const audioElement = new Audio(audioURL);
        
        // Wait for the audio to load metadata
        audioElement.addEventListener('loadedmetadata', () => {
          console.log(`Audio duration: ${audioElement.duration} seconds`);
          
          // If the audio is too short, return a message
          if (audioElement.duration < 0.5) {
            console.warn('Audio is too short for transcription');
            resolve("Your recording was too short. Click here to add your message text.");
            return;
          }
          
          // Try to use the browser's built-in speech recognition
          // @ts-ignore - TypeScript doesn't know about SpeechRecognition
          const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
          const recognition = new SpeechRecognition();

          // Configure recognition
          recognition.lang = 'en-US';
          recognition.continuous = true;
          recognition.interimResults = true;
          recognition.maxAlternatives = 3;

          let finalTranscript = '';
          let interimTranscript = '';

          // Handle recognition results
          recognition.onresult = (event: any) => {
            interimTranscript = '';

            for (let i = event.resultIndex; i < event.results.length; i++) {
              const transcript = event.results[i][0].transcript;

              if (event.results[i].isFinal) {
                finalTranscript += transcript + ' ';

                // Call progress callback if provided
                if (options.onTranscriptionProgress) {
                  options.onTranscriptionProgress(finalTranscript.trim());
                }
              } else {
                interimTranscript += transcript;
              }
            }

            // Log progress for debugging
            console.log('Transcription progress:', finalTranscript + interimTranscript);
          };

          // Handle recognition end
          recognition.onend = () => {
            if (finalTranscript.trim()) {
              // Store the transcript in localStorage to prevent it from being lost
              try {
                const transcriptsKey = 'voice_transcripts';
                const storedTranscripts = localStorage.getItem(transcriptsKey);
                const transcripts = storedTranscripts ? JSON.parse(storedTranscripts) : {};

                // Generate a unique ID for this transcript
                const transcriptId = `transcript_${Date.now()}`;
                transcripts[transcriptId] = finalTranscript.trim();

                // Store with a 24-hour expiration
                localStorage.setItem(transcriptsKey, JSON.stringify(transcripts));
                localStorage.setItem(`${transcriptId}_timestamp`, Date.now().toString());

                console.log('Stored transcript in localStorage with ID:', transcriptId);
              } catch (storageError) {
                console.error('Error storing transcript in localStorage:', storageError);
              }

              resolve(finalTranscript.trim());
            } else {
              console.log('No transcription detected, using fallback');
              resolve("Click here to add your message text");
            }
          };

          // Handle recognition errors
          recognition.onerror = (event: any) => {
            console.error('Speech recognition error:', event.error);
            // Don't reject, use fallback text instead
            resolve("Click here to add your message text");
          };

          // Try multiple approaches to transcribe the audio
          
          // Approach 1: Use AudioContext to decode and play the audio
          try {
            // Convert the blob to an ArrayBuffer
            const reader = new FileReader();
            reader.onload = function() {
              try {
                const arrayBuffer = reader.result as ArrayBuffer;
                
                // Create an AudioContext
                const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
                
                // Decode the audio data
                audioContext.decodeAudioData(arrayBuffer, function(buffer) {
                  console.log('Audio decoded successfully, length:', buffer.duration);
                  
                  // Create a source node from the buffer
                  const source = audioContext.createBufferSource();
                  source.buffer = buffer;
                  
                  // Connect to the audio context destination
                  source.connect(audioContext.destination);
                  
                  // Start recognition before playing
                  console.log('Starting speech recognition with AudioContext...');
                  recognition.start();
                  
                  // Play the audio
                  source.start(0);
                  
                  // Stop recognition when audio ends
                  source.onended = function() {
                    console.log('Audio playback ended, stopping recognition');
                    recognition.stop();
                  };
                  
                  // Set a timeout to stop recognition after the audio duration plus a small buffer
                  setTimeout(() => {
                    recognition.stop();
                  }, (buffer.duration * 1000) + 1000);
                }, function(e) {
                  console.error('Error decoding audio data:', e);
                  // Try approach 2 if this fails
                  tryApproach2();
                });
              } catch (contextError) {
                console.error('Error with AudioContext:', contextError);
                // Try approach 2 if this fails
                tryApproach2();
              }
            };
            
            reader.onerror = function() {
              console.error('Error reading blob as ArrayBuffer');
              // Try approach 2 if this fails
              tryApproach2();
            };
            
            reader.readAsArrayBuffer(audioBlob);
          } catch (audioError) {
            console.error('Error in approach 1:', audioError);
            // Try approach 2 if this fails
            tryApproach2();
          }
          
          // Approach 2: Use the Audio element directly
          function tryApproach2() {
            try {
              console.log('Trying approach 2: Audio element');
              
              // Create a new audio element
              const audio = new Audio(audioURL);
              
              // Set volume to low to avoid feedback but still trigger recognition
              audio.volume = 0.1;
              
              // Start recognition when audio starts playing
              audio.onplay = () => {
                console.log('Starting speech recognition with Audio element...');
                try {
                  recognition.start();
                } catch (e) {
                  console.error('Error starting recognition in approach 2:', e);
                }
              };
              
              // Stop recognition when audio ends
              audio.onended = () => {
                console.log('Audio playback ended, stopping recognition');
                try {
                  recognition.stop();
                } catch (e) {
                  console.error('Error stopping recognition in approach 2:', e);
                }
              };
              
              // Start playing audio to trigger recognition
              audio.play().catch(err => {
                console.error('Error playing audio in approach 2:', err);
                // Try approach 3 if this fails
                tryApproach3();
              });
            } catch (audioError) {
              console.error('Error in approach 2:', audioError);
              // Try approach 3 if this fails
              tryApproach3();
            }
          }
          
          // Approach 3: Use direct recognition
          function tryApproach3() {
            try {
              console.log('Trying approach 3: Direct recognition');
              
              // Start recognition directly
              console.log('Starting speech recognition directly...');
              recognition.start();
              
              // Set a timeout to stop recognition after a reasonable time
              setTimeout(() => {
                try {
                  recognition.stop();
                } catch (e) {
                  console.error('Error stopping recognition in approach 3:', e);
                }
              }, 10000); // 10 seconds should be enough for most voice messages
            } catch (directError) {
              console.error('Error in approach 3:', directError);
              resolve("Click here to add your message text");
            }
          }
        });
        
        // Handle errors loading the audio
        audioElement.addEventListener('error', (e) => {
          console.error('Error loading audio for transcription:', e);
          resolve("Click here to add your message text");
        });
        
      } catch (error) {
        console.error('Error in Web Speech API:', error);
        resolve("Click here to add your message text");
      }
    });
  };

  const startTranscription = useCallback(async (audioBlob: Blob): Promise<string> => {
    try {
      setTranscribing(true);
      setTranscript('');

      let text = '';

      // Try to use the transcription service
      try {
        // Upload audio using our storage service
        const userId = localStorage.getItem('connectedAccount') || 'anonymous';

        // Get the storage service
        const storageService = getStorageService();

        // Upload the audio for transcription
        const audioUrl = await storageService.uploadForTranscription(audioBlob, userId);

        console.log('Audio uploaded for transcription:', audioUrl);

        // Call transcription service via edge function
        // Add this part when you implement the edge function
        // For now, fall back to Web Speech API
        throw new Error('Transcription service not implemented yet');
      } catch (serverError) {
        console.warn('Server transcription failed, falling back to Web Speech API:', serverError);

        // Fall back to Web Speech API
        text = await transcribeWithWebSpeech(audioBlob);
      }

      setTranscript(text);

      if (options.onTranscriptionComplete) {
        options.onTranscriptionComplete(text);
      }

      setTranscribing(false);
      return text;
    } catch (error) {
      console.error('Error in transcription:', error);
      const fallbackText = "Click here to add your message text";
      setTranscript(fallbackText);
      setTranscribing(false);
      return fallbackText;
    }
  }, [options]);

  const stopTranscription = useCallback(() => {
    setTranscribing(false);
  }, []);

  return {
    transcribing,
    transcript,
    startTranscription,
    stopTranscription
  };
};

export default useWhisper;
