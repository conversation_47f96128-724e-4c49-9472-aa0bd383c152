import React, { useState, useCallback, useEffect } from 'react';
import { Browser<PERSON>outer, Routes, Route } from "react-router-dom";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import Index from "@/pages/Index";
import NotFound from "@/pages/NotFound";
import Channels from "@/pages/Channels";
import Notifications from "@/pages/Notifications";
import Profile from "@/pages/Profile";
import Settings from "@/pages/Settings";
import ChainVoiceMoments from "@/pages/ChainVoiceMoments";
import VoiceJournals from "@/pages/VoiceJournals";
import PostDetail from "@/pages/PostDetail";
import Analytics from "@/pages/Analytics";
import Wallet from "@/pages/Wallet";
import Login from "@/pages/Login";
import Signup from "@/pages/Signup";
import ForgotPassword from "@/pages/ForgotPassword";
import Welcome from "@/pages/Welcome";
import AdminVerification from "@/pages/AdminVerification";
import Layout from "@/components/Layout";
import ReplyModal from "@/components/ReplyModal";
import ProtectedRoute from "@/components/ProtectedRoute";
import { VoiceMessageProps } from "@/components/VoiceMessage";
import { MediaFile } from "@/components/MediaUploader";
import { useNotifications } from '@/contexts/NotificationContext';
import { useAuth } from '@/contexts/AuthContext';

interface AppContentProps {
  connectedAccount: string;
  setConnectedAccount: (address: string) => void;
}

const AppContent: React.FC<AppContentProps> = ({
  connectedAccount,
  setConnectedAccount
}) => {
  // Initialize messages from localStorage if available
  const [messages, setMessages] = useState<VoiceMessageProps[]>(() => {
    const savedMessages = localStorage.getItem('audra_voice_messages');
    if (savedMessages) {
      try {
        // Parse the saved messages and convert string dates back to Date objects
        const parsedMessages = JSON.parse(savedMessages);
        return parsedMessages.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp),
          // Also convert timestamps in replies
          replies: (msg.replies || []).map((reply: any) => ({
            ...reply,
            timestamp: new Date(reply.timestamp)
          }))
        }));
      } catch (error) {
        console.error('Error loading saved messages:', error);
        return [];
      }
    }
    return [];
  });

  const [isReplyModalOpen, setIsReplyModalOpen] = useState(false);
  const [replyingToId, setReplyingToId] = useState<string | null>(null);
  const { addNotification } = useNotifications();

  // Save messages to localStorage whenever they change
  useEffect(() => {
    if (messages.length > 0) {
      localStorage.setItem('audra_voice_messages', JSON.stringify(messages));
    }
  }, [messages]);

  // Handle new voice recordings
  const handleRecordingComplete = async (audioBlob: Blob, transcript: string, duration?: number, media?: MediaFile[]) => {
    if (!connectedAccount) {
      return;
    }

    try {
      // Import the audio storage service
      const audioStorageService = (await import('@/services/audioStorageService')).default;

      // Ensure the audio bucket exists
      await audioStorageService.ensureAudioBucketExists();

      // Upload the audio blob to Supabase Storage
      const audioUrl = await audioStorageService.uploadAudio(audioBlob, connectedAccount);

      console.log('Audio uploaded successfully:', audioUrl);

      // If duration is provided directly, use it
      if (duration && isFinite(duration)) {
        console.log(`Using provided duration: ${duration}s`);
        console.log(`Media files attached: ${media?.length || 0}`);

        // Create a new message with the provided duration and media
        const newMessage: VoiceMessageProps = {
          id: Date.now().toString(),
          audioUrl,
          transcript,
          userAddress: connectedAccount,
          timestamp: new Date(),
          duration: duration,
          replies: [],
          media: media || [] // Ensure media is always an array
        };

        // Add to messages
        setMessages(prev => [newMessage, ...prev]);

        // Save to database if possible
        try {
          const voiceMessageService = (await import('@/services/voiceMessageService')).default;
          await voiceMessageService.createVoiceMessage(
            connectedAccount,
            audioUrl,
            transcript,
            duration,
            undefined, // channelId
            undefined, // parentId
            media
          );
        } catch (dbError) {
          console.error('Error saving message to database:', dbError);
          // Continue with local state update despite the error
        }

        return;
      }
    } catch (error) {
      console.error('Error processing audio:', error);
      // Fall back to blob URL if upload fails
      const tempAudioUrl = URL.createObjectURL(audioBlob);

      // Create a new message with the blob URL
      const newMessage: VoiceMessageProps = {
        id: Date.now().toString(),
        audioUrl: tempAudioUrl,
        transcript,
        userAddress: connectedAccount,
        timestamp: new Date(),
        duration: duration || 0,
        replies: [],
        media: media || [] // Ensure media is always an array
      };

      // Add to messages
      setMessages(prev => [newMessage, ...prev]);
      return;
    }

    // If no duration provided, calculate it
    console.log('Duration not provided, calculating from audio element');

    try {
      // Import the audio storage service
      const audioStorageService = (await import('@/services/audioStorageService')).default;

      // Ensure the audio bucket exists
      await audioStorageService.ensureAudioBucketExists();

      // Upload the audio blob to Supabase Storage
      const persistentAudioUrl = await audioStorageService.uploadAudio(audioBlob, connectedAccount);

      console.log('Audio uploaded successfully:', persistentAudioUrl);

      // Get accurate audio duration
      const audio = new Audio(persistentAudioUrl);

      // Create a promise to get the duration
      const getDuration = () => {
        return new Promise<number>((resolve) => {
          audio.addEventListener('loadedmetadata', () => {
            if (audio.duration !== Infinity) {
              resolve(audio.duration);
            } else {
              // Fallback if duration is Infinity
              audio.currentTime = 24 * 60 * 60; // Seek to 24 hours
              audio.addEventListener('timeupdate', function getDurationFromTimeUpdate() {
                if (audio.currentTime > 0) {
                  resolve(audio.duration);
                  audio.removeEventListener('timeupdate', getDurationFromTimeUpdate);
                }
              });
            }
          });
        });
      };

      // Get the duration and then create the message
      getDuration().then(async (actualDuration) => {
        console.log(`Calculated duration: ${actualDuration}s`);
        console.log(`Media files attached: ${media?.length || 0}`);

        // Create a new message with accurate duration
        const newMessage: VoiceMessageProps = {
          id: Date.now().toString(),
          audioUrl: persistentAudioUrl,
          transcript,
          userAddress: connectedAccount,
          timestamp: new Date(),
          duration: actualDuration,
          replies: [],
          media: media || [] // Ensure media is always an array
        };

        // Add to messages
        setMessages(prev => [newMessage, ...prev]);

        // Save to database if possible
        try {
          const voiceMessageService = (await import('@/services/voiceMessageService')).default;
          await voiceMessageService.createVoiceMessage(
            connectedAccount,
            persistentAudioUrl,
            transcript,
            actualDuration,
            undefined, // channelId
            undefined, // parentId
            media
          );
        } catch (dbError) {
          console.error('Error saving message to database:', dbError);
          // Continue with local state update despite the error
        }
      });
    } catch (error) {
      console.error('Error processing audio with persistent URL:', error);
      // Fall back to blob URL if upload fails
      const tempAudioUrl = URL.createObjectURL(audioBlob);

      // Create a new message with the blob URL
      const newMessage: VoiceMessageProps = {
        id: Date.now().toString(),
        audioUrl: tempAudioUrl,
        transcript,
        userAddress: connectedAccount,
        timestamp: new Date(),
        duration: 30, // Default duration
        replies: [],
        media: media || [] // Ensure media is always an array
      };

      // Add to messages
      setMessages(prev => [newMessage, ...prev]);
    }
  };

  // Handle reply button click
  const handleReply = useCallback((parentId: string) => {
    setReplyingToId(parentId);
    setIsReplyModalOpen(true);
  }, []);

  // Find the parent message by ID
  const findMessageById = useCallback((id: string): VoiceMessageProps | null => {
    for (const message of messages) {
      if (message.id === id) {
        return message;
      }
    }
    return null;
  }, [messages]);

  // Handle reply recording complete
  const handleReplyComplete = useCallback(async (parentId: string, audioBlob: Blob, transcript: string, duration?: number, media?: MediaFile[]) => {
    if (!connectedAccount || !parentId) {
      return;
    }

    try {
      // Import the audio storage service
      const audioStorageService = (await import('@/services/audioStorageService')).default;

      // Ensure the audio bucket exists
      await audioStorageService.ensureAudioBucketExists();

      // Upload the audio blob to Supabase Storage
      const audioUrl = await audioStorageService.uploadAudio(audioBlob, connectedAccount);

      console.log('Reply audio uploaded successfully:', audioUrl);

      // If duration is provided directly, use it
      if (duration && isFinite(duration)) {
        console.log(`Using provided duration for reply: ${duration}s`);
        console.log(`Media files attached to reply: ${media?.length || 0}`);

        const replyId = Date.now().toString();

        // Create a new reply message with the provided duration and media
        const newReply: VoiceMessageProps = {
          id: replyId,
          audioUrl,
          transcript,
          userAddress: connectedAccount,
          timestamp: new Date(),
          duration: duration,
          parentId,
          isReply: true,
          media: media || [] // Ensure media is always an array
        };

        // Find the parent message to get the owner's address
        const parentMessage = findMessageById(parentId);

        if (parentMessage && parentMessage.userAddress !== connectedAccount) {
          // Create a notification for the parent message owner
          addNotification(
            'reply',
            connectedAccount,
            parentMessage.userAddress,
            parentId,
            {
              replyId,
              text: transcript.substring(0, 50) + (transcript.length > 50 ? '...' : '')
            }
          );
        }

        // Add the reply to the parent message
        setMessages(prev => {
          return prev.map(message => {
            if (message.id === parentId) {
              return {
                ...message,
                replies: [...(message.replies || []), newReply]
              };
            }
            return message;
          });
        });

        return;
      }

      // If no duration provided, calculate it
      console.log('Duration not provided for reply, calculating from audio element');

      // Get accurate audio duration
      const audio = new Audio(audioUrl);

      // Create a promise to get the duration
      const getDuration = () => {
        return new Promise<number>((resolve) => {
          audio.addEventListener('loadedmetadata', () => {
            if (audio.duration !== Infinity) {
              resolve(audio.duration);
            } else {
              // Fallback if duration is Infinity
              audio.currentTime = 24 * 60 * 60; // Seek to 24 hours
              audio.addEventListener('timeupdate', function getDurationFromTimeUpdate() {
                if (audio.currentTime > 0) {
                  resolve(audio.duration);
                  audio.removeEventListener('timeupdate', getDurationFromTimeUpdate);
                }
              });
            }
          });
        });
      };

      // Get the duration and then create the reply
      getDuration().then(actualDuration => {
        const replyId = Date.now().toString();

        console.log(`Calculated duration for reply: ${actualDuration}s`);
        console.log(`Media files attached to reply: ${media?.length || 0}`);

        // Create a new reply message with accurate duration
        const newReply: VoiceMessageProps = {
          id: replyId,
          audioUrl,
          transcript,
          userAddress: connectedAccount,
          timestamp: new Date(),
          duration: actualDuration,
          parentId,
          isReply: true,
          media: media || [] // Ensure media is always an array
        };

        // Find the parent message to get the owner's address
        const parentMessage = findMessageById(parentId);

        if (parentMessage && parentMessage.userAddress !== connectedAccount) {
          // Create a notification for the parent message owner
          addNotification(
            'reply',
            connectedAccount,
            parentMessage.userAddress,
            parentId,
            {
              replyId,
              text: transcript.substring(0, 50) + (transcript.length > 50 ? '...' : '')
            }
          );
        }

        // Add the reply to the parent message
        setMessages(prev => {
          return prev.map(message => {
            if (message.id === parentId) {
              return {
                ...message,
                replies: [...(message.replies || []), newReply]
              };
            }
            return message;
          });
        });
      });
    } catch (error) {
      console.error('Error processing reply audio:', error);
      // Fall back to blob URL if upload fails
      const tempAudioUrl = URL.createObjectURL(audioBlob);

      const replyId = Date.now().toString();

      // Create a new reply with the blob URL
      const newReply: VoiceMessageProps = {
        id: replyId,
        audioUrl: tempAudioUrl,
        transcript,
        userAddress: connectedAccount,
        timestamp: new Date(),
        duration: duration || 30, // Default duration
        parentId,
        isReply: true,
        media: media || [] // Ensure media is always an array
      };

      // Add the reply to the parent message
      setMessages(prev => {
        return prev.map(message => {
          if (message.id === parentId) {
            return {
              ...message,
              replies: [...(message.replies || []), newReply]
            };
          }
          return message;
        });
      });
    }
  }, [connectedAccount, findMessageById, addNotification]);

  // Get authentication state
  const { user } = useAuth();

  // Use user address from auth context if available
  const userAddress = user?.walletAddress || connectedAccount;

  // Update connected account when user logs in
  useEffect(() => {
    if (user?.walletAddress) {
      setConnectedAccount(user.walletAddress);
    }
  }, [user, setConnectedAccount]);

  return (
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          {/* Public Routes */}
          <Route path="/welcome" element={<Welcome />} />
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<Signup />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />

          {/* Protected Routes */}
          <Route path="/" element={
            <ProtectedRoute>
              <Layout
                connectedAccount={userAddress}
                onWalletConnect={setConnectedAccount}
                onRecordingComplete={handleRecordingComplete}
              >
                <Index
                  connectedAccount={userAddress}
                  messages={messages}
                  onReply={handleReply}
                />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/channels" element={
            <ProtectedRoute>
              <Layout connectedAccount={userAddress} onWalletConnect={setConnectedAccount}>
                <Channels />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/notifications" element={
            <ProtectedRoute>
              <Layout connectedAccount={userAddress} onWalletConnect={setConnectedAccount}>
                <Notifications />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/profile" element={
            <ProtectedRoute>
              <Layout connectedAccount={userAddress} onWalletConnect={setConnectedAccount}>
                <Profile userAddress={userAddress} />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/settings" element={
            <ProtectedRoute>
              <Layout connectedAccount={userAddress} onWalletConnect={setConnectedAccount}>
                <Settings userAddress={userAddress} />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/chain-voice" element={
            <ProtectedRoute>
              <Layout connectedAccount={userAddress} onWalletConnect={setConnectedAccount}>
                <ChainVoiceMoments />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/chain-voice/:filter/:value" element={
            <ProtectedRoute>
              <Layout connectedAccount={userAddress} onWalletConnect={setConnectedAccount}>
                <ChainVoiceMoments />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/journals" element={
            <ProtectedRoute>
              <Layout connectedAccount={userAddress} onWalletConnect={setConnectedAccount}>
                <VoiceJournals userAddress={userAddress} />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/post/:id" element={
            <ProtectedRoute>
              <Layout connectedAccount={userAddress} onWalletConnect={setConnectedAccount}>
                <PostDetail
                  messages={messages}
                  onReply={handleReply}
                  connectedAccount={userAddress}
                />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/analytics" element={
            <ProtectedRoute>
              <Layout connectedAccount={userAddress} onWalletConnect={setConnectedAccount}>
                <Analytics
                  messages={messages}
                  userAddress={userAddress}
                />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/wallet" element={
            <ProtectedRoute>
              <Layout connectedAccount={userAddress} onWalletConnect={setConnectedAccount}>
                <Wallet />
              </Layout>
            </ProtectedRoute>
          } />

          {/* Admin Routes */}
          <Route path="/admin/verification" element={
            <ProtectedRoute>
              <Layout connectedAccount={userAddress} onWalletConnect={setConnectedAccount}>
                <AdminVerification />
              </Layout>
            </ProtectedRoute>
          } />

          {/* Catch-all Route */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>

      {/* Reply Modal */}
      {replyingToId && (
        <ReplyModal
          isOpen={isReplyModalOpen}
          onClose={() => setIsReplyModalOpen(false)}
          onReplyComplete={handleReplyComplete}
          parentId={replyingToId}
        />
      )}
    </TooltipProvider>
  );
};

export default AppContent;