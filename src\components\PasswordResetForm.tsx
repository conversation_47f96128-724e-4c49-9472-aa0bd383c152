import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { toast } from '@/components/ui/sonner';
import { Loader2, KeyRound, Mail, ArrowLeft } from 'lucide-react';

// Helper function to get query parameters
const useQuery = () => {
  return new URLSearchParams(useLocation().search);
};

const PasswordResetForm: React.FC = () => {
  const navigate = useNavigate();
  const query = useQuery();
  const token = query.get('token');
  const { requestPasswordReset, resetPassword, isLoading } = useAuth();
  
  // If token is present, show reset password form, otherwise show request form
  const [isResetForm, setIsResetForm] = useState(!!token);
  
  const [requestData, setRequestData] = useState({
    email: ''
  });
  
  const [resetData, setResetData] = useState({
    token: token || '',
    password: '',
    confirmPassword: ''
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSuccess, setIsSuccess] = useState(false);
  
  // Handle input change for request form
  const handleRequestChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setRequestData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user types
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };
  
  // Handle input change for reset form
  const handleResetChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setResetData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user types
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };
  
  // Validate request form
  const validateRequestForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!requestData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(requestData.email)) {
      newErrors.email = 'Email is invalid';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Validate reset form
  const validateResetForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!resetData.token) {
      newErrors.token = 'Token is required';
    }
    
    if (!resetData.password) {
      newErrors.password = 'Password is required';
    } else if (resetData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
    }
    
    if (!resetData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (resetData.password !== resetData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Handle request form submission
  const handleRequestSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateRequestForm()) {
      return;
    }
    
    try {
      await requestPasswordReset({
        email: requestData.email
      });
      
      setIsSuccess(true);
    } catch (error) {
      console.error('Password reset request error:', error);
    }
  };
  
  // Handle reset form submission
  const handleResetSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateResetForm()) {
      return;
    }
    
    try {
      await resetPassword({
        token: resetData.token,
        password: resetData.password,
        confirmPassword: resetData.confirmPassword
      });
      
      setIsSuccess(true);
      
      // Navigate to login page after 3 seconds
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (error) {
      console.error('Password reset error:', error);
    }
  };
  
  // Render request form
  const renderRequestForm = () => (
    <>
      <CardHeader>
        <CardTitle>Reset Password</CardTitle>
        <CardDescription>
          Enter your email to receive password reset instructions
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {isSuccess ? (
          <div className="text-center py-4">
            <Mail className="h-12 w-12 text-voicechain-purple mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Check Your Email</h3>
            <p className="text-sm text-muted-foreground mb-4">
              We've sent password reset instructions to your email address.
            </p>
            <p className="text-xs text-muted-foreground">
              If you don't see the email, check your spam folder.
            </p>
          </div>
        ) : (
          <form onSubmit={handleRequestSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="Enter your email"
                value={requestData.email}
                onChange={handleRequestChange}
                className={errors.email ? 'border-red-500' : ''}
              />
              {errors.email && (
                <p className="text-xs text-red-500">{errors.email}</p>
              )}
            </div>
            
            <Button 
              type="submit" 
              className="w-full bg-voicechain-purple hover:bg-voicechain-accent"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : (
                'Send Reset Instructions'
              )}
            </Button>
          </form>
        )}
      </CardContent>
    </>
  );
  
  // Render reset form
  const renderResetForm = () => (
    <>
      <CardHeader>
        <CardTitle>Create New Password</CardTitle>
        <CardDescription>
          Enter your new password
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {isSuccess ? (
          <div className="text-center py-4">
            <KeyRound className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Password Reset Successful</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Your password has been reset successfully.
            </p>
            <p className="text-xs text-muted-foreground">
              You will be redirected to the login page shortly.
            </p>
          </div>
        ) : (
          <form onSubmit={handleResetSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="token">Reset Token</Label>
              <Input
                id="token"
                name="token"
                placeholder="Enter your reset token"
                value={resetData.token}
                onChange={handleResetChange}
                className={errors.token ? 'border-red-500' : ''}
                readOnly={!!token}
              />
              {errors.token && (
                <p className="text-xs text-red-500">{errors.token}</p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="password">New Password</Label>
              <Input
                id="password"
                name="password"
                type="password"
                placeholder="Enter your new password"
                value={resetData.password}
                onChange={handleResetChange}
                className={errors.password ? 'border-red-500' : ''}
              />
              {errors.password && (
                <p className="text-xs text-red-500">{errors.password}</p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                placeholder="Confirm your new password"
                value={resetData.confirmPassword}
                onChange={handleResetChange}
                className={errors.confirmPassword ? 'border-red-500' : ''}
              />
              {errors.confirmPassword && (
                <p className="text-xs text-red-500">{errors.confirmPassword}</p>
              )}
            </div>
            
            <Button 
              type="submit" 
              className="w-full bg-voicechain-purple hover:bg-voicechain-accent"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Resetting...
                </>
              ) : (
                'Reset Password'
              )}
            </Button>
          </form>
        )}
      </CardContent>
    </>
  );
  
  return (
    <Card className="w-full max-w-md mx-auto">
      {isResetForm ? renderResetForm() : renderRequestForm()}
      
      <CardFooter>
        <div className="w-full">
          <Button 
            variant="ghost" 
            size="sm" 
            className="flex items-center text-xs"
            onClick={() => navigate('/login')}
          >
            <ArrowLeft className="h-3 w-3 mr-1" />
            Back to Login
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

export default PasswordResetForm;
