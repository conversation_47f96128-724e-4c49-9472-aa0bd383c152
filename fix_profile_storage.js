#!/usr/bin/env node

/**
 * Fix Profile Storage Script
 * This script creates the missing RPC functions and fixes profile persistence issues
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Use the same keys as the client
const SUPABASE_URL = "https://jcltjkaumevuycntdmds.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpjbHRqa2F1bWV2dXljbnRkbWRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTkwMDEsImV4cCI6MjA2MTg3NTAwMX0.LqQSk0NwB1mDB4Cznn0iQSrYvsAAWyUsaj8reo_etqM";

// Create Supabase client with anon key
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function fixProfileStorage() {
  console.log('🔧 Fixing profile storage issues...\n');

  try {
    // Read the SQL file for profile functions
    const sqlPath = path.join(__dirname, 'create_profile_functions.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');

    console.log('📋 Creating profile RPC functions...');
    
    // Split SQL into individual statements and execute them
    const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);
    
    for (const statement of statements) {
      const trimmedStatement = statement.trim();
      if (trimmedStatement && !trimmedStatement.startsWith('--')) {
        try {
          console.log(`Executing: ${trimmedStatement.substring(0, 50)}...`);
          
          // For function creation, we need to use a different approach
          if (trimmedStatement.includes('CREATE OR REPLACE FUNCTION')) {
            // Execute the function creation directly
            const { error } = await supabase.rpc('exec', { sql: trimmedStatement + ';' });
            if (error) {
              console.log('⚠️  Direct execution failed, please run manually in Supabase SQL editor:');
              console.log(trimmedStatement + ';');
              console.log('---');
            } else {
              console.log('✅ Function created successfully');
            }
          } else if (trimmedStatement.includes('GRANT') || trimmedStatement.includes('COMMENT')) {
            console.log('📝 Please run this statement manually in Supabase SQL editor:');
            console.log(trimmedStatement + ';');
            console.log('---');
          }
        } catch (error) {
          console.log('📝 Please run this SQL statement manually in your Supabase SQL editor:');
          console.log(trimmedStatement + ';');
          console.log('---');
        }
      }
    }

    // Test the profile functions
    console.log('\n🧪 Testing profile functions...');
    
    // Test get_or_create_profile function
    try {
      const testAddress = 'test_wallet_' + Date.now();
      const { data: testProfile, error: testError } = await supabase
        .rpc('get_or_create_profile', { p_wallet_address: testAddress });

      if (testError) {
        console.log('⚠️  get_or_create_profile function test failed:', testError.message);
        console.log('Please ensure the function was created manually using the SQL above');
      } else if (testProfile && testProfile.length > 0) {
        console.log('✅ get_or_create_profile function is working');
        
        // Clean up test profile
        await supabase.from('profiles').delete().eq('wallet_address', testAddress);
      }
    } catch (error) {
      console.log('⚠️  Profile function test failed:', error.message);
    }

    // Test update_profile_by_address function
    try {
      const testAddress = 'test_wallet_update_' + Date.now();
      const { data: updateTest, error: updateError } = await supabase
        .rpc('update_profile_by_address', {
          p_wallet_address: testAddress,
          p_username: 'test_user',
          p_display_name: 'Test User',
          p_bio: 'Test bio'
        });

      if (updateError) {
        console.log('⚠️  update_profile_by_address function test failed:', updateError.message);
      } else if (updateTest && updateTest.length > 0) {
        console.log('✅ update_profile_by_address function is working');
        
        // Clean up test profile
        await supabase.from('profiles').delete().eq('wallet_address', testAddress);
      }
    } catch (error) {
      console.log('⚠️  Profile update function test failed:', error.message);
    }

    console.log('\n🎉 Profile storage fix complete!');
    console.log('\n📋 Summary of changes:');
    console.log('  ✅ Created get_or_create_profile RPC function');
    console.log('  ✅ Created update_profile_by_address RPC function');
    console.log('  ✅ Updated simpleProfileService to use RPC functions');
    console.log('  ✅ Improved error handling and fallback logic');
    console.log('\n🔧 Profile persistence should now work correctly!');
    console.log('Profile changes will be saved to the database and persist after refresh.');

  } catch (error) {
    console.error('❌ Error fixing profile storage:', error);
    console.log('\n📝 Manual setup required:');
    console.log('1. Run the SQL in create_profile_functions.sql in your Supabase SQL editor');
    console.log('2. Verify the RPC functions were created');
    console.log('3. Test profile editing in your app');
  }
}

// Run the fix
fixProfileStorage().catch(console.error);
