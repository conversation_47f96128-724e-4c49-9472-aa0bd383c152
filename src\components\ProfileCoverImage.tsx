import React, { useState, useEffect } from 'react';

interface ProfileCoverImageProps {
  src: string;
  alt?: string;
  className?: string;
  style?: React.CSSProperties;
}

const ProfileCoverImage: React.FC<ProfileCoverImageProps> = ({
  src,
  alt,
  className = '',
  style = {}
}) => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<boolean>(false);

  useEffect(() => {
    // Reset state when src changes
    setIsLoading(true);
    setError(false);

    // Create an image object to check if the image loads correctly
    if (src) {
      const img = new Image();
      img.onload = () => {
        setIsLoading(false);
      };
      img.onerror = () => {
        console.error('Error loading cover image:', src);
        setError(true);
        setIsLoading(false);
      };
      img.src = src;
    } else {
      setIsLoading(false);
    }
  }, [src]);

  // Combine the provided style with our background image style
  const combinedStyle = {
    ...style,
    backgroundImage: !error && src ? `url(${src})` : undefined,
    opacity: isLoading ? 0.7 : 1,
    transition: 'opacity 0.3s ease'
  };

  return (
    <div
      className={className}
      style={combinedStyle}
    >
      {(!src || error) && (
        <div className="w-full h-full flex items-center justify-center bg-secondary/50">
          {alt && <span className="text-muted-foreground">{alt}</span>}
        </div>
      )}
    </div>
  );
};

export default ProfileCoverImage;
