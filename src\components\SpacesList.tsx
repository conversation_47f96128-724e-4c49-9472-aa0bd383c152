import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, Users, Mic, Calendar, Clock, Play } from 'lucide-react';
import { spaceService, type LiveStream } from '@/services/spaceService';
import { SpaceCreationModal } from './SpaceCreationModal';
import { formatDistanceToNow } from 'date-fns';

interface SpacesListProps {
  channelId?: string;
  currentUserId?: string;
  onJoinSpace: (spaceId: string) => void;
}

export const SpacesList: React.FC<SpacesListProps> = ({
  channelId,
  currentUserId,
  onJoinSpace,
}) => {
  const [spaces, setSpaces] = useState<LiveStream[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    loadSpaces();
  }, [channelId]);

  const loadSpaces = async () => {
    try {
      setIsLoading(true);
      console.log('🚀 SpacesList: Loading spaces for channelId:', channelId);
      const liveSpaces = await spaceService.getLiveStreams(channelId);
      console.log('🏠 SpacesList: Loaded spaces:', liveSpaces);
      setSpaces(liveSpaces);
    } catch (error) {
      console.error('❌ SpacesList: Error loading spaces:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSpaceCreated = () => {
    loadSpaces();
  };

  const formatScheduledTime = (scheduledStart: string) => {
    const date = new Date(scheduledStart);
    const now = new Date();
    
    if (date < now) {
      return 'Starting soon';
    }
    
    return `in ${formatDistanceToNow(date)}`;
  };

  const getStatusBadge = (space: LiveStream) => {
    switch (space.status) {
      case 'live':
        return (
          <Badge variant="destructive" className="animate-pulse">
            🔴 LIVE
          </Badge>
        );
      case 'scheduled':
        return (
          <Badge variant="secondary">
            <Calendar className="h-3 w-3 mr-1" />
            Scheduled
          </Badge>
        );
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-muted rounded w-3/4" />
              <div className="h-3 bg-muted rounded w-1/2" />
            </CardHeader>
            <CardContent>
              <div className="h-3 bg-muted rounded w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  console.log('🏢 SpacesList: Rendering with', spaces.length, 'spaces, currentUserId:', currentUserId);

  return (
    <div className="space-y-4">
      <div className="bg-purple-100 border border-purple-300 p-2 rounded mb-2">
        <p className="text-xs text-purple-800">
          SpacesList Debug: {spaces.length} spaces loaded, currentUserId: {currentUserId || 'none'}, channelId: {channelId || 'none'}
        </p>
      </div>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">Voice Spaces</h2>
          <p className="text-sm text-muted-foreground">
            Join live conversations or create your own
          </p>
        </div>
        
        {currentUserId && (
          <Button onClick={() => setShowCreateModal(true)} className="gap-2">
            <Plus className="h-4 w-4" />
            Create Space
          </Button>
        )}
      </div>

      {/* Spaces List */}
      <div className="space-y-4">
        {spaces.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Mic className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No spaces yet</h3>
              <p className="text-muted-foreground text-center mb-4">
                Be the first to start a live conversation in this channel
              </p>
              {currentUserId && (
                <Button onClick={() => setShowCreateModal(true)} className="gap-2">
                  <Plus className="h-4 w-4" />
                  Create Space
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          spaces.map((space) => (
            <Card key={space.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <CardTitle className="text-base">{space.title}</CardTitle>
                      {getStatusBadge(space)}
                    </div>
                    
                    {space.description && (
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {space.description}
                      </p>
                    )}
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      <span>{space.participant_count}</span>
                    </div>
                    
                    <div className="flex items-center gap-1">
                      <Mic className="h-4 w-4" />
                      <span>{space.speaker_count}</span>
                    </div>
                    
                    {space.status === 'scheduled' && space.scheduled_start && (
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        <span>{formatScheduledTime(space.scheduled_start)}</span>
                      </div>
                    )}
                  </div>
                  
                  <Button
                    size="sm"
                    onClick={() => onJoinSpace(space.id)}
                    disabled={space.status !== 'live'}
                    className="gap-2"
                  >
                    {space.status === 'live' ? (
                      <>
                        <Play className="h-4 w-4" />
                        Join
                      </>
                    ) : (
                      <>
                        <Calendar className="h-4 w-4" />
                        Scheduled
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Create Space Modal */}
      {currentUserId && (
        <SpaceCreationModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSpaceCreated={handleSpaceCreated}
          channelId={channelId || ''}
          profileId={currentUserId}
        />
      )}
    </div>
  );
};