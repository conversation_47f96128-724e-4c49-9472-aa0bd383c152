
import React, { useState } from 'react';
import { Dialog, DialogContent, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Drawer, DrawerContent } from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { X, Copy, RefreshCw, Clock, Users } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { toast } from '@/components/ui/sonner';
import { useChannels } from '@/contexts/ChannelContext';
import { Channel, ChannelInvite } from '@/types/channel';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { formatDistanceToNow } from 'date-fns';

interface ChannelInviteModalProps {
  isOpen: boolean;
  onClose: () => void;
  channel: Channel;
}

const ChannelInviteModal: React.FC<ChannelInviteModalProps> = ({
  isOpen,
  onClose,
  channel
}) => {
  const isMobile = useIsMobile();
  const { createInvite, channelInvites } = useChannels();
  
  const [maxUses, setMaxUses] = useState<number | undefined>(undefined);
  const [expiresInDays, setExpiresInDays] = useState<number | undefined>(undefined);
  const [isGenerating, setIsGenerating] = useState(false);
  
  // Get active invites for this channel
  const activeInvites = channelInvites
    .filter(invite => invite.channelId === channel.id)
    .filter(invite => {
      // Filter out expired invites
      if (invite.expiresAt && invite.expiresAt < new Date()) {
        return false;
      }
      
      // Filter out invites that have reached max uses
      if (invite.maxUses && invite.uses >= invite.maxUses) {
        return false;
      }
      
      return true;
    });
  
  const handleCreateInvite = async () => {
    setIsGenerating(true);
    try {
      const newInvite = await createInvite(channel.id, maxUses, expiresInDays);
      if (newInvite) {
        toast('Invite created successfully!');
        
        // Reset form
        setMaxUses(undefined);
        setExpiresInDays(undefined);
      } else {
        toast('Failed to create invite. Please try again.');
      }
    } catch (error) {
      console.error('Error creating invite:', error);
      toast('An error occurred while creating the invite.');
    } finally {
      setIsGenerating(false);
    }
  };
  
  const handleCopyInvite = (inviteCode: string) => {
    const inviteLink = `${window.location.origin}/channels/join/${inviteCode}`;
    navigator.clipboard.writeText(inviteLink);
    toast('Invite link copied to clipboard!');
  };
  
  const formatInviteExpiry = (invite: ChannelInvite) => {
    if (!invite.expiresAt) {
      return 'Never expires';
    }
    
    return `Expires ${formatDistanceToNow(invite.expiresAt, { addSuffix: true })}`;
  };
  
  const formatInviteUses = (invite: ChannelInvite) => {
    if (!invite.maxUses) {
      return `${invite.uses} uses`;
    }
    
    return `${invite.uses}/${invite.maxUses} uses`;
  };
  
  const ModalContent = () => (
    <div className="flex flex-col py-6 px-4 space-y-6 max-h-[80vh] overflow-y-auto">
      <div className="flex justify-between items-center w-full">
        <DialogTitle className="text-xl font-semibold">Invite to #{channel.name}</DialogTitle>
        <Button variant="ghost" size="icon" onClick={onClose}>
          <X size={20} />
        </Button>
      </div>
      
      <DialogDescription className="text-sm text-muted-foreground">
        Create and manage invite links for this channel
      </DialogDescription>
      
      <div className="space-y-6">
        {/* Active Invites */}
        {activeInvites.length > 0 && (
          <div>
            <h3 className="text-sm font-medium mb-2">Active Invites</h3>
            <div className="space-y-2">
              {activeInvites.map(invite => (
                <div 
                  key={invite.id} 
                  className="flex items-center justify-between p-3 dark:bg-gray-800 bg-secondary rounded-lg"
                >
                  <div>
                    <p className="font-mono text-sm">{invite.code}</p>
                    <div className="flex items-center gap-4 mt-1">
                      <span className="text-xs text-muted-foreground flex items-center gap-1">
                        <Clock size={12} />
                        {formatInviteExpiry(invite)}
                      </span>
                      <span className="text-xs text-muted-foreground flex items-center gap-1">
                        <Users size={12} />
                        {formatInviteUses(invite)}
                      </span>
                    </div>
                  </div>
                  <Button 
                    variant="ghost" 
                    size="icon"
                    onClick={() => handleCopyInvite(invite.code)}
                  >
                    <Copy size={16} />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Create New Invite */}
        <div>
          <h3 className="text-sm font-medium mb-2">Create New Invite</h3>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="maxUses">Max Uses</Label>
              <Select
                value={maxUses?.toString() || "unlimited"}
                onValueChange={(value) => setMaxUses(value === "unlimited" ? undefined : parseInt(value, 10))}
              >
                <SelectTrigger className="dark:bg-gray-800 dark:border-gray-700 dark:text-white">
                  <SelectValue placeholder="Unlimited" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="unlimited">Unlimited</SelectItem>
                  <SelectItem value="1">1 use</SelectItem>
                  <SelectItem value="5">5 uses</SelectItem>
                  <SelectItem value="10">10 uses</SelectItem>
                  <SelectItem value="25">25 uses</SelectItem>
                  <SelectItem value="50">50 uses</SelectItem>
                  <SelectItem value="100">100 uses</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="expiresInDays">Expires After</Label>
              <Select
                value={expiresInDays?.toString() || "never"}
                onValueChange={(value) => setExpiresInDays(value === "never" ? undefined : parseInt(value, 10))}
              >
                <SelectTrigger className="dark:bg-gray-800 dark:border-gray-700 dark:text-white">
                  <SelectValue placeholder="Never" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="never">Never</SelectItem>
                  <SelectItem value="1">1 day</SelectItem>
                  <SelectItem value="7">7 days</SelectItem>
                  <SelectItem value="30">30 days</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <Button 
              onClick={handleCreateInvite}
              className="w-full bg-voicechain-purple hover:bg-voicechain-accent"
              disabled={isGenerating}
            >
              {isGenerating ? (
                <>
                  <RefreshCw size={16} className="mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <RefreshCw size={16} className="mr-2" />
                  Generate New Invite
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
  
  if (isMobile) {
    return (
      <Drawer open={isOpen} onOpenChange={open => !open && onClose()}>
        <DrawerContent className="max-h-[90vh] dark:bg-gray-900">
          <ModalContent />
        </DrawerContent>
      </Drawer>
    );
  }
  
  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-md dark:bg-gray-900">
        <ModalContent />
      </DialogContent>
    </Dialog>
  );
};

export default ChannelInviteModal;
