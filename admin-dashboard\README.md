# Audra Admin Dashboard

This is a separate admin dashboard for the Audra voice-based Web3 social platform. It provides administrative tools for managing users, verifications, and content moderation.

## Features

- User Management
  - View all users
  - Suspend/unsuspend users
  - Edit user profiles
  - Delete user accounts

- Verification Management
  - Review verification applications
  - Approve/reject verification requests
  - Assign verification badges
  - Revoke verification status

- Content Moderation
  - View reported content
  - Remove inappropriate content
  - Mute/unmute users
  - Block/unblock users

- Analytics
  - User growth metrics
  - Engagement statistics
  - Content popularity
  - Platform health monitoring

## Getting Started

1. Clone this repository
2. Install dependencies with `npm install`
3. Create a `.env` file with your Supabase credentials
4. Run the development server with `npm run dev`

## Authentication

The admin dashboard uses a separate authentication system from the main Audra application. Only authorized administrators can access the dashboard.

## Technology Stack

- React
- TypeScript
- Supabase
- Shadcn UI
- Vite

## Project Structure

```
admin-dashboard/
├── public/
├── src/
│   ├── components/
│   ├── contexts/
│   ├── hooks/
│   ├── pages/
│   ├── services/
│   ├── types/
│   ├── utils/
│   ├── App.tsx
│   └── main.tsx
├── .env
├── package.json
└── README.md
```

## Security

The admin dashboard implements strict security measures:
- Role-based access control
- Audit logging for all administrative actions
- Two-factor authentication for admin accounts
- IP-based access restrictions

## Development

This is a separate project from the main Audra application to ensure proper separation of concerns and enhanced security.
