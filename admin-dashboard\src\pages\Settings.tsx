import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/toast';
import { AlertCircle, Save, Key, Shield, Globe, Bell, Moon, Sun, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/services/supabase';

const Settings: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('general');
  const [isSaving, setIsSaving] = useState(false);

  // General settings
  const [siteName, setSiteName] = useState('Audra');
  const [siteDescription, setSiteDescription] = useState('Voice-based Web3 social platform');
  const [theme, setTheme] = useState('system');

  // API settings
  const [nftStorageApiKey, setNftStorageApiKey] = useState('fd56bf09.020c8783b870438d93f29d2655391154');
  const [heliusApiKey, setHeliusApiKey] = useState('5422bf21-7f7b-43b6-8a10-3a72b3d7bb3c');
  const [elevenLabsApiKey, setElevenLabsApiKey] = useState('***************************************************');

  // Security settings
  const [requireEmailVerification, setRequireEmailVerification] = useState(true);
  const [maxLoginAttempts, setMaxLoginAttempts] = useState(5);
  const [sessionTimeout, setSessionTimeout] = useState(24);

  // Notification settings
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [adminAlerts, setAdminAlerts] = useState(true);
  const [alertEmail, setAlertEmail] = useState('<EMAIL>');

  // Load settings from Supabase
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const { data, error } = await supabase
          .from('admin_settings')
          .select('*')
          .eq('id', 'global')
          .single();

        if (error && error.code !== 'PGRST116') { // PGRST116 means no rows returned
          console.error('Error loading settings:', error);
          return;
        }

        if (data) {
          // General settings
          if (data.site_name) setSiteName(data.site_name);
          if (data.site_description) setSiteDescription(data.site_description);
          if (data.theme) setTheme(data.theme);

          // API settings
          if (data.nft_storage_api_key) setNftStorageApiKey(data.nft_storage_api_key);
          if (data.helius_api_key) setHeliusApiKey(data.helius_api_key);
          if (data.eleven_labs_api_key) setElevenLabsApiKey(data.eleven_labs_api_key);

          // Security settings
          if (data.require_email_verification !== undefined)
            setRequireEmailVerification(data.require_email_verification);
          if (data.max_login_attempts)
            setMaxLoginAttempts(data.max_login_attempts);
          if (data.session_timeout)
            setSessionTimeout(data.session_timeout);

          // Notification settings
          if (data.email_notifications !== undefined)
            setEmailNotifications(data.email_notifications);
          if (data.admin_alerts !== undefined)
            setAdminAlerts(data.admin_alerts);
          if (data.alert_email)
            setAlertEmail(data.alert_email);
        }
      } catch (error) {
        console.error('Error loading settings:', error);
      }
    };

    loadSettings();
  }, []);

  const handleSaveSettings = async (section: string) => {
    try {
      setIsSaving(true);

      // Save settings to Supabase
      const settingsData = {
        updated_at: new Date().toISOString()
      };

      // Add the appropriate settings data based on the section
      if (section === 'General') {
        Object.assign(settingsData, {
          site_name: siteName,
          site_description: siteDescription,
          theme: theme
        });
      } else if (section === 'API') {
        // Only super_admins can update API keys
        if (user?.role !== 'super_admin') {
          toast({
            title: 'Permission Denied',
            description: 'Only super admins can update API keys.',
            duration: 3000
          });
          return;
        }

        Object.assign(settingsData, {
          nft_storage_api_key: nftStorageApiKey,
          helius_api_key: heliusApiKey,
          eleven_labs_api_key: elevenLabsApiKey
        });
      } else if (section === 'Security') {
        // Only super_admins can update security settings
        if (user?.role !== 'super_admin') {
          toast({
            title: 'Permission Denied',
            description: 'Only super admins can update security settings.',
            duration: 3000
          });
          return;
        }

        Object.assign(settingsData, {
          require_email_verification: requireEmailVerification,
          max_login_attempts: maxLoginAttempts,
          session_timeout: sessionTimeout
        });
      } else if (section === 'Notification') {
        Object.assign(settingsData, {
          email_notifications: emailNotifications,
          admin_alerts: adminAlerts,
          alert_email: alertEmail
        });
      }

      // Save to Supabase settings table
      const { error } = await supabase
        .from('admin_settings')
        .upsert({
          id: 'global', // Use a single global settings record
          ...settingsData
        });

      if (error) {
        throw error;
      }

      toast({
        title: 'Settings Saved',
        description: `${section} settings have been updated successfully.`,
        duration: 3000
      });
    } catch (error) {
      console.error(`Error saving ${section} settings:`, error);
      toast({
        title: 'Error',
        description: `Failed to save ${section} settings.`,
        duration: 3000
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Admin Settings</CardTitle>
          <CardDescription>
            Configure admin dashboard and platform settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="general" onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-4 mb-6">
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="api">API Keys</TabsTrigger>
              <TabsTrigger value="security">Security</TabsTrigger>
              <TabsTrigger value="notifications">Notifications</TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="site-name">Site Name</Label>
                  <Input
                    id="site-name"
                    value={siteName}
                    onChange={(e) => setSiteName(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="site-description">Site Description</Label>
                  <Textarea
                    id="site-description"
                    value={siteDescription}
                    onChange={(e) => setSiteDescription(e.target.value)}
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="theme">Theme</Label>
                  <Select value={theme} onValueChange={setTheme}>
                    <SelectTrigger id="theme">
                      <SelectValue placeholder="Select theme" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">
                        <div className="flex items-center">
                          <Sun className="h-4 w-4 mr-2" />
                          Light
                        </div>
                      </SelectItem>
                      <SelectItem value="dark">
                        <div className="flex items-center">
                          <Moon className="h-4 w-4 mr-2" />
                          Dark
                        </div>
                      </SelectItem>
                      <SelectItem value="system">
                        <div className="flex items-center">
                          <Globe className="h-4 w-4 mr-2" />
                          System
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  onClick={() => handleSaveSettings('General')}
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Changes
                    </>
                  )}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="api" className="space-y-6">
              <div className="space-y-4">
                <div className="p-4 border rounded-md bg-amber-50 dark:bg-amber-950/20 border-amber-200 dark:border-amber-800">
                  <div className="flex items-start">
                    <AlertCircle className="h-5 w-5 text-amber-600 dark:text-amber-400 mr-2 mt-0.5" />
                    <div>
                      <h4 className="text-sm font-medium text-amber-800 dark:text-amber-300">Important Security Notice</h4>
                      <p className="text-sm text-amber-700 dark:text-amber-400 mt-1">
                        API keys should be kept secure and not exposed in client-side code.
                        These keys are used for server-side operations only.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="nft-storage-key">NFT.Storage API Key</Label>
                  <div className="flex">
                    <Input
                      id="nft-storage-key"
                      value={nftStorageApiKey}
                      onChange={(e) => setNftStorageApiKey(e.target.value)}
                      type="password"
                      className="flex-1 rounded-r-none"
                    />
                    <Button variant="outline" className="rounded-l-none">
                      <Key className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground">Used for storing media files on IPFS</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="helius-key">Helius API Key</Label>
                  <div className="flex">
                    <Input
                      id="helius-key"
                      value={heliusApiKey}
                      onChange={(e) => setHeliusApiKey(e.target.value)}
                      type="password"
                      className="flex-1 rounded-r-none"
                    />
                    <Button variant="outline" className="rounded-l-none">
                      <Key className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground">Used for blockchain integration</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="elevenlabs-key">ElevenLabs API Key</Label>
                  <div className="flex">
                    <Input
                      id="elevenlabs-key"
                      value={elevenLabsApiKey}
                      onChange={(e) => setElevenLabsApiKey(e.target.value)}
                      type="password"
                      className="flex-1 rounded-r-none"
                    />
                    <Button variant="outline" className="rounded-l-none">
                      <Key className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground">Used for voice generation</p>
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  onClick={() => handleSaveSettings('API')}
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save API Keys
                    </>
                  )}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="security" className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-verification">Require Email Verification</Label>
                    <p className="text-sm text-muted-foreground">
                      Users must verify their email before accessing the platform
                    </p>
                  </div>
                  <Switch
                    id="email-verification"
                    checked={requireEmailVerification}
                    onCheckedChange={setRequireEmailVerification}
                  />
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label htmlFor="max-login-attempts">Maximum Login Attempts</Label>
                  <Input
                    id="max-login-attempts"
                    type="number"
                    min={1}
                    max={10}
                    value={maxLoginAttempts}
                    onChange={(e) => setMaxLoginAttempts(parseInt(e.target.value))}
                  />
                  <p className="text-sm text-muted-foreground">
                    Number of failed login attempts before account is temporarily locked
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="session-timeout">Session Timeout (hours)</Label>
                  <Input
                    id="session-timeout"
                    type="number"
                    min={1}
                    max={72}
                    value={sessionTimeout}
                    onChange={(e) => setSessionTimeout(parseInt(e.target.value))}
                  />
                  <p className="text-sm text-muted-foreground">
                    How long user sessions remain active before requiring re-authentication
                  </p>
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Two-Factor Authentication</h3>
                      <p className="text-sm text-muted-foreground">
                        Add an extra layer of security to your admin account
                      </p>
                    </div>
                    <Button variant="outline" onClick={() => window.location.href = '/two-factor-auth'}>
                      <Shield className="h-4 w-4 mr-2" />
                      Manage 2FA
                    </Button>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  onClick={() => handleSaveSettings('Security')}
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Shield className="mr-2 h-4 w-4" />
                      Save Security Settings
                    </>
                  )}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="notifications" className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-notifications">Email Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                      Send email notifications to users
                    </p>
                  </div>
                  <Switch
                    id="email-notifications"
                    checked={emailNotifications}
                    onCheckedChange={setEmailNotifications}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="admin-alerts">Admin Alerts</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive email alerts for important admin events
                    </p>
                  </div>
                  <Switch
                    id="admin-alerts"
                    checked={adminAlerts}
                    onCheckedChange={setAdminAlerts}
                  />
                </div>

                {adminAlerts && (
                  <div className="space-y-2">
                    <Label htmlFor="alert-email">Alert Email Address</Label>
                    <Input
                      id="alert-email"
                      type="email"
                      value={alertEmail}
                      onChange={(e) => setAlertEmail(e.target.value)}
                    />
                    <p className="text-sm text-muted-foreground">
                      Email address to receive admin alerts
                    </p>
                  </div>
                )}
              </div>

              <div className="flex justify-end">
                <Button
                  onClick={() => handleSaveSettings('Notification')}
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Bell className="mr-2 h-4 w-4" />
                      Save Notification Settings
                    </>
                  )}
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="border-t bg-muted/50 flex justify-between">
          <div className="text-sm text-muted-foreground">
            Logged in as <span className="font-medium">{user?.email}</span> ({user?.role})
          </div>
          <div className="text-sm text-muted-foreground">
            Last updated: {new Date().toLocaleDateString()}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default Settings;
