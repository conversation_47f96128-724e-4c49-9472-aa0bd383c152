import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Trash2, Pin, AlertTriangle } from "lucide-react";

interface ConfirmationDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  confirmText: string;
  cancelText?: string;
  onConfirm: () => void;
  variant?: 'destructive' | 'default';
  icon?: React.ReactNode;
}

export const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
  isOpen,
  onOpenChange,
  title,
  description,
  confirmText,
  cancelText = "Cancel",
  onConfirm,
  variant = 'default',
  icon
}) => {
  return (
    <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <div className="flex items-center gap-3 mb-2">
            {icon && (
              <div className={`p-2 rounded-full ${
                variant === 'destructive' 
                  ? 'bg-destructive/10 text-destructive' 
                  : 'bg-primary/10 text-primary'
              }`}>
                {icon}
              </div>
            )}
            <AlertDialogTitle className="text-lg font-semibold">
              {title}
            </AlertDialogTitle>
          </div>
          <AlertDialogDescription className="text-sm text-muted-foreground leading-relaxed">
            {description}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="gap-2">
          <AlertDialogCancel asChild>
            <Button variant="outline" className="flex-1">
              {cancelText}
            </Button>
          </AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button 
              variant={variant === 'destructive' ? 'destructive' : 'default'}
              className="flex-1"
              onClick={onConfirm}
            >
              {confirmText}
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

// Predefined dialogs for common actions
interface DeletePostDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
}

export const DeletePostDialog: React.FC<DeletePostDialogProps> = ({
  isOpen,
  onOpenChange,
  onConfirm
}) => {
  return (
    <ConfirmationDialog
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title="Delete Post"
      description="Are you sure you want to delete this post? This action cannot be undone and the post will be permanently removed from your profile."
      confirmText="Delete Post"
      onConfirm={onConfirm}
      variant="destructive"
      icon={<Trash2 size={20} />}
    />
  );
};

interface PinPostDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  isPinning: boolean;
}

export const PinPostDialog: React.FC<PinPostDialogProps> = ({
  isOpen,
  onOpenChange,
  onConfirm,
  isPinning
}) => {
  return (
    <ConfirmationDialog
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title={isPinning ? "Pin Post" : "Unpin Post"}
      description={
        isPinning 
          ? "This post will be pinned to the top of your profile. Only one post can be pinned at a time."
          : "This post will be removed from the top of your profile."
      }
      confirmText={isPinning ? "Pin Post" : "Unpin Post"}
      onConfirm={onConfirm}
      icon={<Pin size={20} />}
    />
  );
};