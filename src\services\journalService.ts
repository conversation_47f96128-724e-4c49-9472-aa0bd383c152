import { supabase } from '@/integrations/supabase/client';
import { JournalEntry } from '@/types/journal';
import { MediaFile } from '@/components/MediaUploader';

/**
 * Service for managing journal entries
 */
class JournalService {
  /**
   * Create a new journal entry
   */
  public async createJournal(
    userAddress: string,
    title: string,
    audioUrl: string,
    transcript: string,
    duration: number,
    privacyLevel: 'my_journal' | 'public' | 'locked_public' | 'private' | 'locked_private' = 'public',
    isLocked: boolean = false,
    unlockCondition?: {
      type: 'date' | 'password' | 'token' | 'tip';
      value: string;
      tipAmount?: number;
      tipCurrency?: string;
    },
    scheduledFor?: Date,
    media?: MediaFile[],
    summonedUsers?: string[],
    tipToUnlockAmount?: number,
    tipToUnlockCurrency?: string
  ): Promise<string> {
    try {
      // Generate a unique ID
      const id = crypto.randomUUID();

      // Normalize the user address to lowercase
      const normalizedAddress = userAddress.toLowerCase();

      // Determine if the journal should be published based on privacy level
      const isPublished = privacyLevel === 'public' && (!scheduledFor || new Date() >= scheduledFor);

      // Create the journal in Supabase
      const { error } = await supabase
        .from('journals')
        .insert({
          id,
          profile_id: userAddress,
          title,
          audio_url: audioUrl,
          transcript,
          audio_duration: duration,
          privacy_level: privacyLevel,
          is_locked: isLocked,
          unlock_condition: unlockCondition,
          scheduled_for: scheduledFor?.toISOString(),
          summoned_users: summonedUsers || [],
          tip_to_unlock_amount: tipToUnlockAmount,
          tip_to_unlock_currency: tipToUnlockCurrency || 'SOL',
          is_published: isPublished,
          repost_count: 0,
          reply_count: 0,
          reaction_count: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (error) {
        console.error('Error creating journal:', error);
        throw new Error('Failed to create journal');
      }

      // If there's media, store it in a separate table
      if (media && media.length > 0) {
        const mediaItems = media.map(item => ({
          id: crypto.randomUUID(),
          journal_id: id,
          url: item.url,
          type: item.type,
          created_at: new Date().toISOString()
        }));

        const { error: mediaError } = await supabase
          .from('journal_media')
          .insert(mediaItems);

        if (mediaError) {
          console.error('Error storing media for journal:', mediaError);
          // Continue anyway, the journal is created
        }
      }

      return id;
    } catch (error) {
      console.error('Error in createJournal:', error);
      throw error;
    }
  }

  /**
   * Get a journal by ID
   */
  public async getJournal(id: string): Promise<JournalEntry | null> {
    try {
      // Get the journal
      const { data: journalData, error } = await supabase
        .from('journals')
        .select('*')
        .eq('id', id)
        .single();

      if (error || !journalData) {
        console.error('Error getting journal:', error);
        return null;
      }

      // Get media for the journal
      const { data: mediaData } = await supabase
        .from('journal_media')
        .select('*')
        .eq('journal_id', id);

      // Convert to JournalEntry
      const journal: JournalEntry = {
        id: journalData.id,
        title: journalData.title,
        audioUrl: journalData.audio_url,
        transcript: journalData.transcript || '',
        userAddress: journalData.profile_id,
        createdAt: new Date(journalData.created_at),
        scheduledFor: journalData.scheduled_for ? new Date(journalData.scheduled_for) : undefined,
        isPublished: journalData.is_published || false,
        isPrivate: !journalData.is_published, // If not published, it's private
        isLocked: journalData.is_locked || false,
        isUnlocked: !journalData.is_locked, // If not locked, it's unlocked
        unlockCondition: journalData.unlock_condition,
        duration: journalData.audio_duration || 0,
        media: mediaData ? mediaData.map(item => ({
          id: item.id,
          url: item.url,
          type: item.type as 'image' | 'video'
        })) : []
      };

      return journal;
    } catch (error) {
      console.error('Error in getJournal:', error);
      return null;
    }
  }

  /**
   * Get journals for a specific user
   */
  public async getUserJournals(userAddress: string): Promise<JournalEntry[]> {
    try {
      // Normalize the user address to lowercase
      const normalizedAddress = userAddress.toLowerCase();
      
      // Get journals
      const { data, error } = await supabase
        .from('journals')
        .select('*')
        .eq('profile_id', normalizedAddress)
        .order('created_at', { ascending: false });
        
      if (error) {
        console.error('Error fetching user journals:', error);
        return [];
      }
      
      if (!data || data.length === 0) {
        return [];
      }
      
      // Convert to JournalEntry array
      const journals: JournalEntry[] = await Promise.all(data.map(async (journal) => {
        // Get media for each journal
        const { data: mediaData } = await supabase
          .from('journal_media')
          .select('id, url, type')
          .eq('journal_id', journal.id);
          
        return {
          id: journal.id,
          title: journal.title,
          audioUrl: journal.audio_url,
          transcript: journal.transcript || '',
          userAddress: journal.profile_id,
          createdAt: new Date(journal.created_at),
          scheduledFor: journal.scheduled_for ? new Date(journal.scheduled_for) : undefined,
          isPublished: journal.is_published || false,
          isPrivate: !journal.is_published, // If not published, it's private
          isLocked: journal.is_locked || false,
          isUnlocked: !journal.is_locked, // If not locked, it's unlocked
          unlockCondition: journal.unlock_condition,
          duration: journal.audio_duration || 0,
          media: mediaData ? mediaData.map(item => ({
            id: item.id,
            url: item.url,
            type: item.type as 'image' | 'video'
          })) : []
        };
      }));
      
      return journals;
    } catch (error) {
      console.error('Error in getUserJournals:', error);
      return [];
    }
  }

  /**
   * Get all public journals
   */
  public async getPublicJournals(): Promise<JournalEntry[]> {
    try {
      // Get published journals
      const { data, error } = await supabase
        .from('journals')
        .select('*')
        .eq('is_published', true)
        .order('created_at', { ascending: false });
        
      if (error) {
        console.error('Error fetching public journals:', error);
        return [];
      }
      
      if (!data || data.length === 0) {
        return [];
      }
      
      // Convert to JournalEntry array
      const journals: JournalEntry[] = await Promise.all(data.map(async (journal) => {
        // Get media for each journal
        const { data: mediaData } = await supabase
          .from('journal_media')
          .select('id, url, type')
          .eq('journal_id', journal.id);
          
        return {
          id: journal.id,
          title: journal.title,
          audioUrl: journal.audio_url,
          transcript: journal.transcript || '',
          userAddress: journal.profile_id,
          createdAt: new Date(journal.created_at),
          scheduledFor: journal.scheduled_for ? new Date(journal.scheduled_for) : undefined,
          isPublished: journal.is_published,
          isLocked: journal.is_locked,
          unlockCondition: journal.unlock_condition,
          duration: journal.audio_duration,
          media: mediaData ? mediaData.map(item => ({
            id: item.id,
            url: item.url,
            type: item.type as 'image' | 'video'
          })) : []
        };
      }));
      
      return journals;
    } catch (error) {
      console.error('Error in getPublicJournals:', error);
      return [];
    }
  }

  /**
   * Delete a journal
   */
  public async deleteJournal(id: string, userAddress: string): Promise<boolean> {
    try {
      // Normalize the user address to lowercase
      const normalizedAddress = userAddress.toLowerCase();

      // Check if the user is the owner of the journal
      const { data: journalData, error: checkError } = await supabase
        .from('journals')
        .select('profile_id')
        .eq('id', id)
        .single();

      if (checkError || !journalData) {
        console.error('Error checking journal ownership:', checkError);
        return false;
      }

      // Verify ownership
      const journalProfileId = journalData.profile_id.toLowerCase();
      if (journalProfileId !== normalizedAddress) {
        console.error(`User does not own this journal. Journal owner: ${journalProfileId}, User: ${normalizedAddress}`);
        return false;
      }

      // Delete the journal
      const { error } = await supabase
        .from('journals')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting journal:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in deleteJournal:', error);
      return false;
    }
  }

  // Get journals with privacy filtering
  public async getAccessibleJournals(
    userAddress: string,
    privacyFilter?: 'my_journal' | 'public' | 'locked_public' | 'private' | 'locked_private'
  ): Promise<JournalEntry[]> {
    try {
      // Try the new RPC function first
      const { data, error } = await supabase.rpc('get_accessible_journals', {
        p_user_id: userAddress,
        p_privacy_filter: privacyFilter
      });

      if (!error && data) {
        return data.map((journal: any) => this.convertDbJournalToJournalEntry(journal));
      }

      // Fallback to existing methods if RPC doesn't exist yet
      console.log('RPC function not available, using fallback methods');

      // Load user's own journals
      const userJournals = await this.getUserJournals(userAddress);

      // Load public journals
      const publicJournals = await this.getPublicJournals();

      // Combine and deduplicate journals
      const allJournals = [...userJournals];

      // Add public journals that aren't already in user's journals
      publicJournals.forEach(publicJournal => {
        if (!allJournals.find(j => j.id === publicJournal.id)) {
          allJournals.push(publicJournal);
        }
      });

      return allJournals;
    } catch (error) {
      console.error('Error in getAccessibleJournals:', error);

      // Final fallback - try to load journals using existing methods
      try {
        const userJournals = await this.getUserJournals(userAddress);
        const publicJournals = await this.getPublicJournals();

        const allJournals = [...userJournals];
        publicJournals.forEach(publicJournal => {
          if (!allJournals.find(j => j.id === publicJournal.id)) {
            allJournals.push(publicJournal);
          }
        });

        return allJournals;
      } catch (fallbackError) {
        console.error('Fallback also failed:', fallbackError);
        return [];
      }
    }
  }

  // Summon users to a private journal
  public async summonUsersToJournal(
    journalId: string,
    userIds: string[],
    summonedBy: string
  ): Promise<boolean> {
    try {
      // Add users to journal access
      const accessRecords = userIds.map(userId => ({
        journal_id: journalId,
        user_id: userId,
        access_type: 'summoned',
        granted_by: summonedBy
      }));

      const { error } = await supabase
        .from('journal_access')
        .insert(accessRecords);

      if (error) {
        console.error('Error summoning users:', error);
        return false;
      }

      // Update the journal's summoned_users array
      const { error: updateError } = await supabase
        .from('journals')
        .update({
          summoned_users: supabase.rpc('array_append', {
            array_col: 'summoned_users',
            new_elements: userIds
          })
        })
        .eq('id', journalId);

      return !updateError;
    } catch (error) {
      console.error('Error in summonUsersToJournal:', error);
      return false;
    }
  }

  // Tip to unlock a private journal
  public async tipToUnlockJournal(
    journalId: string,
    userId: string,
    tipAmount: number,
    tipCurrency: string = 'SOL'
  ): Promise<boolean> {
    try {
      // Add tip unlock access
      const { error } = await supabase
        .from('journal_access')
        .insert({
          journal_id: journalId,
          user_id: userId,
          access_type: 'tip_unlocked',
          tip_amount: tipAmount,
          tip_currency: tipCurrency
        });

      return !error;
    } catch (error) {
      console.error('Error in tipToUnlockJournal:', error);
      return false;
    }
  }

  // Repost a journal
  public async repostJournal(journalId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('journal_reposts')
        .insert({
          journal_id: journalId,
          user_id: userId
        });

      if (!error) {
        // Increment repost count
        await supabase.rpc('increment_journal_count', {
          journal_id: journalId,
          count_type: 'repost'
        });
      }

      return !error;
    } catch (error) {
      console.error('Error in repostJournal:', error);
      return false;
    }
  }

  // Add reply to journal
  public async addJournalReply(
    journalId: string,
    userId: string,
    content?: string,
    audioUrl?: string,
    audioDuration?: number
  ): Promise<string | null> {
    try {
      const { data, error } = await supabase
        .from('journal_replies')
        .insert({
          journal_id: journalId,
          user_id: userId,
          content,
          audio_url: audioUrl,
          audio_duration: audioDuration,
          is_voice_reply: !!audioUrl
        })
        .select()
        .single();

      if (!error) {
        // Increment reply count
        await supabase.rpc('increment_journal_count', {
          journal_id: journalId,
          count_type: 'reply'
        });

        return data.id;
      }

      return null;
    } catch (error) {
      console.error('Error in addJournalReply:', error);
      return null;
    }
  }

  // Add reaction to journal
  public async addJournalReaction(
    journalId: string,
    userId: string,
    reactionType: 'like' | 'love' | 'laugh' | 'wow' | 'sad' | 'angry'
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('journal_reactions')
        .upsert({
          journal_id: journalId,
          user_id: userId,
          reaction_type: reactionType
        });

      if (!error) {
        // Increment reaction count
        await supabase.rpc('increment_journal_count', {
          journal_id: journalId,
          count_type: 'reaction'
        });
      }

      return !error;
    } catch (error) {
      console.error('Error in addJournalReaction:', error);
      return false;
    }
  }

  // Helper method to convert database journal to JournalEntry
  private convertDbJournalToJournalEntry(dbJournal: any): JournalEntry {
    return {
      id: dbJournal.id,
      title: dbJournal.title,
      audioUrl: dbJournal.audio_url,
      transcript: dbJournal.transcript || '',
      userAddress: dbJournal.profile_id,
      createdAt: new Date(dbJournal.created_at),
      scheduledFor: dbJournal.scheduled_for ? new Date(dbJournal.scheduled_for) : undefined,
      privacyLevel: dbJournal.privacy_level || 'public',
      isLocked: dbJournal.is_locked || false,
      isUnlocked: !dbJournal.is_locked,
      isPublished: dbJournal.is_published || false,
      isPrivate: dbJournal.privacy_level === 'private' || dbJournal.privacy_level === 'locked_private',
      unlockCondition: dbJournal.unlock_condition,
      summonedUsers: dbJournal.summoned_users || [],
      tipToUnlockAmount: dbJournal.tip_to_unlock_amount,
      tipToUnlockCurrency: dbJournal.tip_to_unlock_currency,
      repostCount: dbJournal.repost_count || 0,
      replyCount: dbJournal.reply_count || 0,
      reactionCount: dbJournal.reaction_count || 0,
      duration: dbJournal.audio_duration || 0,
      media: [] // TODO: Load media from journal_media table
    };
  }
}

export const journalService = new JournalService();
export default journalService;
