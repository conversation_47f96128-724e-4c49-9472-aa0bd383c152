-- Check if voice_message_media table exists
DO $$
BEGIN
  IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'voice_message_media') THEN
    -- Create the table if it doesn't exist
    CREATE TABLE public.voice_message_media (
      id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
      voice_message_id TEXT NOT NULL,
      url TEXT NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('image', 'video')),
      created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
    );
  ELSE
    -- Make sure the columns have the correct types
    IF EXISTS (SELECT FROM information_schema.columns 
               WHERE table_schema = 'public' 
               AND table_name = 'voice_message_media' 
               AND column_name = 'id' 
               AND data_type = 'uuid') THEN
      -- Convert id column from UUID to TEXT
      ALTER TABLE public.voice_message_media 
      ALTER COLUMN id TYPE TEXT USING id::text;
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.columns 
               WHERE table_schema = 'public' 
               AND table_name = 'voice_message_media' 
               AND column_name = 'voice_message_id' 
               AND data_type = 'uuid') THEN
      -- Convert voice_message_id column from UUID to TEXT
      ALTER TABLE public.voice_message_media 
      ALTER COLUMN voice_message_id TYPE TEXT USING voice_message_id::text;
    END IF;
  END IF;
  
  -- Enable RLS on voice_message_media table
  ALTER TABLE public.voice_message_media ENABLE ROW LEVEL SECURITY;
  
  -- Create or replace policies for voice_message_media table
  
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Users can view all voice message media" ON public.voice_message_media;
  DROP POLICY IF EXISTS "Users can insert their own voice message media" ON public.voice_message_media;
  DROP POLICY IF EXISTS "Users can delete their own voice message media" ON public.voice_message_media;
  
  -- Create new policies
  CREATE POLICY "Users can view all voice message media"
  ON public.voice_message_media
  FOR SELECT
  USING (true);
  
  CREATE POLICY "Users can insert voice message media"
  ON public.voice_message_media
  FOR INSERT
  WITH CHECK (true);
  
  CREATE POLICY "Users can delete voice message media"
  ON public.voice_message_media
  FOR DELETE
  USING (true);
  
  -- Create indexes for better performance
  CREATE INDEX IF NOT EXISTS voice_message_media_voice_message_id_idx ON public.voice_message_media(voice_message_id);
END $$;
