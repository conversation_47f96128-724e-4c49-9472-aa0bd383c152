-- Create tip_balances table to track accumulated tip earnings
CREATE TABLE public.tip_balances (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  eth_balance DECIMAL(18,8) NOT NULL DEFAULT 0,
  sol_balance DECIMAL(18,8) NOT NULL DEFAULT 0,
  usdc_balance DECIMAL(18,6) NOT NULL DEFAULT 0,
  total_received_eth DECIMAL(18,8) NOT NULL DEFAULT 0,
  total_received_sol DECIMAL(18,8) NOT NULL DEFAULT 0,
  total_received_usdc DECIMAL(18,6) NOT NULL DEFAULT 0,
  total_sent_eth DECIMAL(18,8) NOT NULL DEFAULT 0,
  total_sent_sol DECIMAL(18,8) NOT NULL DEFAULT 0,
  total_sent_usdc DECIMAL(18,6) NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(user_id)
);

-- Create wallet_transactions table for tracking all wallet activity
CREATE TABLE public.wallet_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  tx_hash TEXT,
  transaction_type TEXT NOT NULL CHECK (transaction_type IN ('tip_send', 'tip_receive', 'deposit', 'withdrawal', 'funding')),
  amount DECIMAL(18,8) NOT NULL,
  currency TEXT NOT NULL CHECK (currency IN ('ETH', 'SOL', 'USDC')),
  blockchain TEXT NOT NULL CHECK (blockchain IN ('ethereum', 'solana')),
  from_address TEXT,
  to_address TEXT,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed')),
  source_type TEXT NOT NULL CHECK (source_type IN ('connected_wallet', 'tip_balance')),
  target_type TEXT NOT NULL CHECK (target_type IN ('connected_wallet', 'tip_balance')),
  message TEXT,
  related_message_id UUID,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.tip_balances ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.wallet_transactions ENABLE ROW LEVEL SECURITY;

-- Create policies for tip_balances
CREATE POLICY "Users can view their own tip balance" 
ON public.tip_balances 
FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own tip balance" 
ON public.tip_balances 
FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own tip balance" 
ON public.tip_balances 
FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Create policies for wallet_transactions
CREATE POLICY "Users can view their own transactions" 
ON public.wallet_transactions 
FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own transactions" 
ON public.wallet_transactions 
FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Create function to update tip balance when tips are received
CREATE OR REPLACE FUNCTION public.update_tip_balance_on_receive()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert or update tip balance record
  INSERT INTO public.tip_balances (
    user_id,
    eth_balance,
    sol_balance, 
    usdc_balance,
    total_received_eth,
    total_received_sol,
    total_received_usdc
  ) VALUES (
    NEW.receiver_id,
    CASE WHEN NEW.currency = 'ETH' THEN NEW.amount ELSE 0 END,
    CASE WHEN NEW.currency = 'SOL' THEN NEW.amount ELSE 0 END,
    CASE WHEN NEW.currency = 'USDC' THEN NEW.amount ELSE 0 END,
    CASE WHEN NEW.currency = 'ETH' THEN NEW.amount ELSE 0 END,
    CASE WHEN NEW.currency = 'SOL' THEN NEW.amount ELSE 0 END,
    CASE WHEN NEW.currency = 'USDC' THEN NEW.amount ELSE 0 END
  )
  ON CONFLICT (user_id) DO UPDATE SET
    eth_balance = tip_balances.eth_balance + CASE WHEN NEW.currency = 'ETH' THEN NEW.amount ELSE 0 END,
    sol_balance = tip_balances.sol_balance + CASE WHEN NEW.currency = 'SOL' THEN NEW.amount ELSE 0 END,
    usdc_balance = tip_balances.usdc_balance + CASE WHEN NEW.currency = 'USDC' THEN NEW.amount ELSE 0 END,
    total_received_eth = tip_balances.total_received_eth + CASE WHEN NEW.currency = 'ETH' THEN NEW.amount ELSE 0 END,
    total_received_sol = tip_balances.total_received_sol + CASE WHEN NEW.currency = 'SOL' THEN NEW.amount ELSE 0 END,
    total_received_usdc = tip_balances.total_received_usdc + CASE WHEN NEW.currency = 'USDC' THEN NEW.amount ELSE 0 END,
    updated_at = now();

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update tip balance when tips are received
CREATE TRIGGER update_tip_balance_on_tip_receive
  AFTER INSERT ON public.tips
  FOR EACH ROW
  EXECUTE FUNCTION public.update_tip_balance_on_receive();

-- Create function to get user tip balance
CREATE OR REPLACE FUNCTION public.get_user_tip_balance(p_user_id UUID)
RETURNS TABLE(
  eth_balance DECIMAL,
  sol_balance DECIMAL, 
  usdc_balance DECIMAL,
  total_received_eth DECIMAL,
  total_received_sol DECIMAL,
  total_received_usdc DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    tb.eth_balance,
    tb.sol_balance,
    tb.usdc_balance,
    tb.total_received_eth,
    tb.total_received_sol,
    tb.total_received_usdc
  FROM public.tip_balances tb
  WHERE tb.user_id = p_user_id;
  
  -- If no record exists, return zeros
  IF NOT FOUND THEN
    RETURN QUERY SELECT 0::DECIMAL, 0::DECIMAL, 0::DECIMAL, 0::DECIMAL, 0::DECIMAL, 0::DECIMAL;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to deduct from tip balance when sending tips
CREATE OR REPLACE FUNCTION public.deduct_tip_balance(
  p_user_id UUID,
  p_amount DECIMAL,
  p_currency TEXT
) RETURNS BOOLEAN AS $$
DECLARE
  current_balance DECIMAL;
BEGIN
  -- Get current balance for the currency
  SELECT 
    CASE 
      WHEN p_currency = 'ETH' THEN eth_balance
      WHEN p_currency = 'SOL' THEN sol_balance
      WHEN p_currency = 'USDC' THEN usdc_balance
    END
  INTO current_balance
  FROM public.tip_balances
  WHERE user_id = p_user_id;
  
  -- Check if user has sufficient balance
  IF current_balance IS NULL OR current_balance < p_amount THEN
    RETURN FALSE;
  END IF;
  
  -- Deduct the amount
  UPDATE public.tip_balances
  SET 
    eth_balance = CASE WHEN p_currency = 'ETH' THEN eth_balance - p_amount ELSE eth_balance END,
    sol_balance = CASE WHEN p_currency = 'SOL' THEN sol_balance - p_amount ELSE sol_balance END,
    usdc_balance = CASE WHEN p_currency = 'USDC' THEN usdc_balance - p_amount ELSE usdc_balance END,
    total_sent_eth = CASE WHEN p_currency = 'ETH' THEN total_sent_eth + p_amount ELSE total_sent_eth END,
    total_sent_sol = CASE WHEN p_currency = 'SOL' THEN total_sent_sol + p_amount ELSE total_sent_sol END,
    total_sent_usdc = CASE WHEN p_currency = 'USDC' THEN total_sent_usdc + p_amount ELSE total_sent_usdc END,
    updated_at = now()
  WHERE user_id = p_user_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;