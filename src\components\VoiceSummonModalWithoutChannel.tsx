
import React, { useState, useEffect, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import CustomAvatarImage from '@/components/CustomAvatarImage';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import { useNotifications } from '@/contexts/NotificationContext';
import AudioRecorder from './AudioRecorder';
import { toast } from '@/components/ui/sonner';
import { AtSign, User, Mic, Send, Play, Square } from 'lucide-react';
import { UserProfile } from '@/types/user-profile';

interface VoiceSummonModalWithoutChannelProps {
  isOpen: boolean;
  onClose: () => void;
  recipientAddress: string;
  channelId?: string | null;
  messageId?: string;
  context?: 'channel' | 'feed' | 'profile';
  onSummonComplete?: (summonData: any) => void;
}

const VoiceSummonModalWithoutChannel: React.FC<VoiceSummonModalWithoutChannelProps> = ({
  isOpen,
  onClose,
  recipientAddress,
  channelId,
  messageId,
  context = 'feed',
  onSummonComplete
}) => {
  const { getProfileByAddress, profiles } = useProfiles();
  const { addNotification } = useNotifications();
  const [isRecording, setIsRecording] = useState(false);
  const [recordingComplete, setRecordingComplete] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [transcript, setTranscript] = useState('');
  const [audioDuration, setAudioDuration] = useState(0);
  const [recipientProfile, setRecipientProfile] = useState<UserProfile | null>(null);
  const [tagInputValue, setTagInputValue] = useState('');
  const [suggestedUsers, setSuggestedUsers] = useState<UserProfile[]>([]);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);

  // Get recipient profile using useEffect
  useEffect(() => {
    if (recipientAddress) {
      const profile = getProfileByAddress(recipientAddress);
      setRecipientProfile(profile);
      if (profile) {
        setTagInputValue(`@${profile.username}`);
      }
    }
  }, [recipientAddress, getProfileByAddress]);

  // Handle audio playback
  useEffect(() => {
    return () => {
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  // Get context description
  const getContextDescription = () => {
    switch (context) {
      case 'channel':
        return channelId ? 'in this channel' : 'in this channel';
      case 'profile':
        return 'from their profile';
      case 'feed':
      default:
        return 'from the main feed';
    }
  };

  const handleRecordingStart = () => {
    setIsRecording(true);
    setRecordingComplete(false);
  };

  const handleRecordingComplete = (blob: Blob, text: string, duration?: number) => {
    setAudioBlob(blob);
    setTranscript(text);
    setAudioDuration(duration || 0);
    setIsRecording(false);
    setRecordingComplete(true);

    // Create object URL for audio preview
    if (blob) {
      const url = URL.createObjectURL(blob);
      setAudioUrl(url);
    }
  };

  const handleTagInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setTagInputValue(value);

    // If input starts with @ and has at least 2 characters after @, show suggestions
    if (value.startsWith('@') && value.length >= 2) {
      const searchTerm = value.substring(1).toLowerCase();
      const results = Object.values(profiles).filter(profile =>
        profile.username.toLowerCase().includes(searchTerm) ||
        profile.displayName.toLowerCase().includes(searchTerm)
      ).slice(0, 5);
      setSuggestedUsers(results);
    } else {
      setSuggestedUsers([]);
    }
  };

  const selectUser = (profile: UserProfile) => {
    setTagInputValue(`@${profile.username}`);
    setRecipientProfile(profile);
    setSuggestedUsers([]);
  };

  const toggleAudioPlayback = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleAudioEnded = () => {
    setIsPlaying(false);
  };

  const handleSummon = async () => {
    if (!audioBlob || !transcript) {
      toast.error('Please record a voice message first');
      return;
    }

    if (!recipientProfile) {
      toast.error('Please select a recipient');
      return;
    }

    const currentAccount = localStorage.getItem('connectedAccount');
    if (!currentAccount) {
      toast.error('You must be logged in to summon');
      return;
    }

    try {
      // Create object URL for the audio blob
      const audioUrl = URL.createObjectURL(audioBlob);

      // Save summon to database for persistence
      const voiceMessageModule = await import('@/services/voiceMessageService.js');
      const savedSummon = await voiceMessageModule.saveSummon(
        audioUrl,
        transcript,
        currentAccount,
        recipientProfile.address,
        audioDuration,
        [], // No media for now, can be enhanced later
        context || 'feed',
        messageId
      );

      console.log('Summon saved to database:', savedSummon);

      // Create a notification for the recipient
      try {
        await addNotification(
          'summon',
          currentAccount,
          recipientProfile.address,
          messageId || savedSummon.id,
          {
            text: transcript,
            summonQuestion: transcript,
            summonAudioUrl: audioUrl,
            summonDuration: audioDuration,
            channelId: channelId,
            context: context,
            messageId: messageId,
            summonId: savedSummon.id
          }
        );

        console.log('✅ Summon notification sent successfully');
        toast.success(`Voice summon sent to ${recipientProfile.displayName}`);
      } catch (notificationError) {
        console.error('❌ Error sending summon notification:', notificationError);
        toast.error('Failed to send summon notification');
      }

      // Create a summon object that can be added to replies
      const summonData = {
        ...savedSummon,
        transcript: `@${recipientProfile.username} ${transcript}`,
        isSummon: true,
        recipientProfile: recipientProfile
      };

      // Call the onSummonComplete callback if provided
      if (onSummonComplete) {
        onSummonComplete(summonData);
      }

      onClose();
    } catch (error) {
      console.error('Error sending summon:', error);
      toast.error('Failed to send summon. Please try again.');
    }
  };

  const resetModal = () => {
    setAudioBlob(null);
    setTranscript('');
    setRecordingComplete(false);
    setIsRecording(false);
    setAudioUrl(null);
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) {
        resetModal();
        onClose();
      }
    }}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AtSign size={18} className="text-voicechain-purple" />
            Summon User
          </DialogTitle>
          <DialogDescription>
            Summon a user to get their opinion on this post.
            They'll receive a notification and can respond with their voice.
          </DialogDescription>
        </DialogHeader>

        {/* Tag input field */}
        <div className="relative mt-2">
          <div className="flex items-center border border-input rounded-md px-3 py-2 focus-within:ring-2 focus-within:ring-voicechain-purple focus-within:border-voicechain-purple">
            <User className="mr-2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="@username"
              value={tagInputValue}
              onChange={handleTagInputChange}
              className="border-0 p-0 focus-visible:ring-0 focus-visible:ring-offset-0"
            />
          </div>

          {/* User suggestions dropdown */}
          {suggestedUsers.length > 0 && (
            <div className="absolute z-10 mt-1 w-full bg-background border border-border rounded-md shadow-lg max-h-60 overflow-auto">
              {suggestedUsers.map(profile => (
                <div
                  key={profile.address}
                  className="flex items-center px-3 py-2 hover:bg-secondary cursor-pointer"
                  onClick={() => selectUser(profile)}
                >
                  <Avatar className="h-6 w-6 mr-2">
                    <CustomAvatarImage src={profile.profileImageUrl} alt={profile.displayName} />
                    <AvatarFallback className="bg-voicechain-accent text-white text-xs">
                      {profile.displayName.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="text-sm font-medium">{profile.displayName}</p>
                    <p className="text-xs text-muted-foreground">@{profile.username}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {recipientProfile && (
          <div className="flex items-center space-x-4 py-4">
            <Avatar className="h-12 w-12">
              <CustomAvatarImage src={recipientProfile.profileImageUrl} alt={recipientProfile.displayName} />
              <AvatarFallback className="bg-voicechain-accent text-white">
                {recipientProfile.displayName.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <div>
              <h4 className="font-medium">{recipientProfile.displayName}</h4>
              <p className="text-sm text-muted-foreground">@{recipientProfile.username}</p>
            </div>
          </div>
        )}

        <div className="text-sm text-muted-foreground mb-4">
          Context: {getContextDescription()}
        </div>

        <div className="bg-secondary/50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <p className="text-sm font-medium">Record your question:</p>
            <Mic size={16} className="text-voicechain-purple" />
          </div>

          <div className="w-full">
            <AudioRecorder
              onRecordingStart={handleRecordingStart}
              onRecordingComplete={handleRecordingComplete}
              placeholder="What would you like to ask?"
            />
          </div>

          {recordingComplete && transcript && (
            <div className="mt-4 space-y-3">
              {/* Audio preview */}
              {audioUrl && (
                <div className="bg-background rounded-md p-3">
                  <div className="flex items-center">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 rounded-full"
                      onClick={toggleAudioPlayback}
                    >
                      {isPlaying ? (
                        <Square size={14} className="text-voicechain-purple" />
                      ) : (
                        <Play size={14} className="text-voicechain-purple" />
                      )}
                    </Button>
                    <div className="ml-2 text-sm">Preview your recording</div>
                    <audio
                      ref={audioRef}
                      src={audioUrl}
                      onEnded={handleAudioEnded}
                      className="hidden"
                    />
                  </div>
                </div>
              )}

              {/* Transcript */}
              <div className="bg-background rounded-md p-3">
                <p className="text-sm font-medium mb-1">Your question:</p>
                <p className="text-sm">{transcript}</p>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-between sm:justify-between">
          <Button
            variant="outline"
            onClick={() => {
              resetModal();
              onClose();
            }}
          >
            Cancel
          </Button>
          <Button
            disabled={!recordingComplete || isRecording || !recipientProfile}
            onClick={handleSummon}
            className="bg-voicechain-purple hover:bg-voicechain-accent"
          >
            <Send size={16} className="mr-2" />
            Summon
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default VoiceSummonModalWithoutChannel;
