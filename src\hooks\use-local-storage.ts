
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

/**
 * Custom hook for storing user settings in Supabase
 * @param key The settings key to use
 * @param initialValue The initial value to use if no value is found
 * @returns A tuple with the current value and a function to update it
 */
export function useUserSettings<T>(
  userId: string,
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => Promise<void>] {
  // State to store our value
  const [storedValue, setStoredValue] = useState<T>(initialValue);

  // Load the value from Supabase on mount
  useEffect(() => {
    const fetchSetting = async () => {
      if (!userId) return;
      
      try {
        const { data, error } = await supabase
          .from('user_settings')
          .select('value')
          .eq('user_id', userId)
          .eq('key', key)
          .maybeSingle();

        if (error) {
          console.error(`Error reading user setting "${key}":`, error);
          return;
        }

        if (data) {
          setStoredValue(data.value);
        }
      } catch (error) {
        console.error(`Error reading user setting "${key}":`, error);
      }
    };

    fetchSetting();
  }, [userId, key]);

  // Return a wrapped version of useState's setter function that
  // persists the new value to Supabase
  const setValue = async (value: T | ((val: T) => T)) => {
    try {
      if (!userId) return;
      
      // Allow value to be a function
      const valueToStore =
        value instanceof Function ? value(storedValue) : value;
      
      // Save state
      setStoredValue(valueToStore);
      
      // Save to Supabase
      const { error } = await supabase
        .from('user_settings')
        .upsert({ 
          user_id: userId,
          key,
          value: valueToStore,
          updated_at: new Date().toISOString()
        }, { 
          onConflict: 'user_id,key' 
        });

      if (error) {
        console.error(`Error setting user setting "${key}":`, error);
      }
    } catch (error) {
      console.error(`Error setting user setting "${key}":`, error);
    }
  };

  return [storedValue, setValue];
}

/**
 * This is a replacement for the old useLocalStorage hook
 * It uses Supabase instead of localStorage
 */
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => Promise<void>] {
  // State to store our value
  const [storedValue, setStoredValue] = useState<T>(initialValue);
  
  // Get the current user
  const [userId, setUserId] = useState<string | null>(null);
  
  useEffect(() => {
    const getCurrentUser = async () => {
      const { data } = await supabase.auth.getSession();
      if (data.session) {
        setUserId(data.session.user.id);
      }
    };
    
    getCurrentUser();
  }, []);
  
  // Load the value from Supabase on mount
  useEffect(() => {
    if (!userId) return;
    
    const fetchSetting = async () => {
      try {
        const { data, error } = await supabase
          .from('user_settings')
          .select('value')
          .eq('user_id', userId)
          .eq('key', key)
          .maybeSingle();

        if (error) {
          console.error(`Error reading user setting "${key}":`, error);
          return;
        }

        if (data) {
          setStoredValue(data.value);
        }
      } catch (error) {
        console.error(`Error reading user setting "${key}":`, error);
      }
    };

    fetchSetting();
  }, [userId, key]);

  // Return a wrapped version that persists the new value to Supabase
  const setValue = async (value: T | ((val: T) => T)) => {
    try {
      if (!userId) {
        console.warn('User not authenticated, cannot save setting');
        return;
      }
      
      // Allow value to be a function
      const valueToStore =
        value instanceof Function ? value(storedValue) : value;
      
      // Save state
      setStoredValue(valueToStore);
      
      // Save to Supabase
      const { error } = await supabase
        .from('user_settings')
        .upsert({ 
          user_id: userId,
          key,
          value: valueToStore,
          updated_at: new Date().toISOString()
        }, { 
          onConflict: 'user_id,key' 
        });

      if (error) {
        console.error(`Error setting user setting "${key}":`, error);
      }
    } catch (error) {
      console.error(`Error setting user setting "${key}":`, error);
    }
  };

  return [storedValue, setValue];
}

/**
 * A replacement for the old useSessionStorage hook
 * It uses the same Supabase backend as useLocalStorage but with a different prefix
 */
export function useSessionStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => Promise<void>] {
  return useLocalStorage<T>(`session_${key}`, initialValue);
}

/**
 * Replacement for the old getFromLocalStorage function
 * Now retrieves from Supabase instead
 */
export async function getFromLocalStorage<T>(key: string, defaultValue: T): Promise<T> {
  try {
    // Get the current user
    const { data: userData } = await supabase.auth.getSession();
    if (!userData.session) {
      return defaultValue;
    }
    
    const userId = userData.session.user.id;
    
    // Get the setting from Supabase
    const { data, error } = await supabase
      .from('user_settings')
      .select('value')
      .eq('user_id', userId)
      .eq('key', key)
      .maybeSingle();
    
    if (error) {
      console.error(`Error getting setting "${key}":`, error);
      return defaultValue;
    }
    
    return data?.value as T || defaultValue;
  } catch (error) {
    console.error(`Error in getFromLocalStorage for "${key}":`, error);
    return defaultValue;
  }
}

/**
 * Replacement for the old setInLocalStorage function
 * Now saves to Supabase instead
 */
export async function setInLocalStorage<T>(key: string, value: T): Promise<boolean> {
  try {
    // Get the current user
    const { data: userData } = await supabase.auth.getSession();
    if (!userData.session) {
      console.warn('User not authenticated, cannot save setting');
      return false;
    }
    
    const userId = userData.session.user.id;
    
    // Save to Supabase
    const { error } = await supabase
      .from('user_settings')
      .upsert({ 
        user_id: userId,
        key,
        value,
        updated_at: new Date().toISOString()
      }, { 
        onConflict: 'user_id,key' 
      });
    
    if (error) {
      console.error(`Error setting "${key}":`, error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error(`Error in setInLocalStorage for "${key}":`, error);
    return false;
  }
}
