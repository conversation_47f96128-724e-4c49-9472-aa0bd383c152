const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

// Create Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase URL or service key. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Function to run a SQL migration
async function runMigration(sql) {
  try {
    const { data, error } = await supabase.rpc('run_sql', { sql });
    
    if (error) {
      console.error('Error running migration:', error);
      return false;
    }
    
    console.log('Migration successful:', data);
    return true;
  } catch (error) {
    console.error('Error running migration:', error);
    return false;
  }
}

// Function to run all migrations in the migrations directory
async function runAllMigrations() {
  const migrationsDir = path.join(__dirname, '..', 'supabase', 'migrations');
  
  try {
    // Check if the migrations directory exists
    if (!fs.existsSync(migrationsDir)) {
      console.error('Migrations directory not found:', migrationsDir);
      return;
    }
    
    // Get all migration files
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort(); // Sort to ensure migrations run in order
    
    console.log(`Found ${migrationFiles.length} migration files`);
    
    // Run each migration
    for (const file of migrationFiles) {
      console.log(`Running migration: ${file}`);
      
      // Read the migration file
      const sql = fs.readFileSync(path.join(migrationsDir, file), 'utf8');
      
      // Run the migration
      const success = await runMigration(sql);
      
      if (!success) {
        console.error(`Migration failed: ${file}`);
        process.exit(1);
      }
      
      console.log(`Migration successful: ${file}`);
    }
    
    console.log('All migrations completed successfully');
  } catch (error) {
    console.error('Error running migrations:', error);
    process.exit(1);
  }
}

// Run all migrations
runAllMigrations();
