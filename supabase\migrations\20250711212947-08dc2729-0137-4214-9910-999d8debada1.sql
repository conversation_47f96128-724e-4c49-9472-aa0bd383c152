-- Create stories table for ephemeral voice content
CREATE TABLE public.voice_stories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  profile_id text NOT NULL,
  audio_url text NOT NULL,
  audio_duration numeric NOT NULL,
  transcript text,
  background_image_url text,
  background_color text DEFAULT '#8B5CF6',
  
  -- Story metadata
  story_type text DEFAULT 'voice' CHECK (story_type IN ('voice', 'voice_image', 'voice_poll')),
  title text,
  tags text[] DEFAULT '{}',
  
  -- Interactive elements
  poll_question text,
  poll_options jsonb,
  allow_replies boolean DEFAULT true,
  
  -- Privacy and visibility
  is_public boolean DEFAULT true,
  viewers_can_reply boolean DEFAULT true,
  
  -- Timestamps
  created_at timestamp with time zone DEFAULT now(),
  expires_at timestamp with time zone DEFAULT (now() + interval '24 hours'),
  
  -- Analytics
  view_count integer DEFAULT 0,
  reply_count integer DEFAULT 0
);

-- Create story views table to track who viewed what
CREATE TABLE public.story_views (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  story_id uuid REFERENCES public.voice_stories(id) ON DELETE CASCADE,
  viewer_id text NOT NULL,
  viewed_at timestamp with time zone DEFAULT now(),
  view_duration numeric DEFAULT 0,
  
  UNIQUE(story_id, viewer_id)
);

-- Create story replies table
CREATE TABLE public.story_replies (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  story_id uuid REFERENCES public.voice_stories(id) ON DELETE CASCADE,
  profile_id text NOT NULL,
  audio_url text NOT NULL,
  audio_duration numeric NOT NULL,
  transcript text,
  created_at timestamp with time zone DEFAULT now()
);

-- Create story highlights table for persistent stories
CREATE TABLE public.story_highlights (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  profile_id text NOT NULL,
  story_id uuid REFERENCES public.voice_stories(id) ON DELETE CASCADE,
  highlight_name text NOT NULL,
  highlight_description text,
  cover_image_url text,
  created_at timestamp with time zone DEFAULT now(),
  
  UNIQUE(profile_id, story_id)
);

-- Enable RLS
ALTER TABLE public.voice_stories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.story_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.story_replies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.story_highlights ENABLE ROW LEVEL SECURITY;

-- RLS Policies for voice_stories
CREATE POLICY "Anyone can view public stories" ON public.voice_stories
  FOR SELECT USING (is_public = true AND expires_at > now());

CREATE POLICY "Users can view their own stories" ON public.voice_stories
  FOR SELECT USING (profile_id = auth.uid()::text);

CREATE POLICY "Users can create their own stories" ON public.voice_stories
  FOR INSERT WITH CHECK (profile_id = auth.uid()::text);

CREATE POLICY "Users can update their own stories" ON public.voice_stories
  FOR UPDATE USING (profile_id = auth.uid()::text);

CREATE POLICY "Users can delete their own stories" ON public.voice_stories
  FOR DELETE USING (profile_id = auth.uid()::text);

-- RLS Policies for story_views
CREATE POLICY "Users can view their own story views" ON public.story_views
  FOR SELECT USING (viewer_id = auth.uid()::text);

CREATE POLICY "Users can insert their own story views" ON public.story_views
  FOR INSERT WITH CHECK (viewer_id = auth.uid()::text);

-- RLS Policies for story_replies
CREATE POLICY "Anyone can view story replies" ON public.story_replies
  FOR SELECT USING (true);

CREATE POLICY "Users can create their own story replies" ON public.story_replies
  FOR INSERT WITH CHECK (profile_id = auth.uid()::text);

CREATE POLICY "Users can delete their own story replies" ON public.story_replies
  FOR DELETE USING (profile_id = auth.uid()::text);

-- RLS Policies for story_highlights
CREATE POLICY "Anyone can view story highlights" ON public.story_highlights
  FOR SELECT USING (true);

CREATE POLICY "Users can manage their own story highlights" ON public.story_highlights
  FOR ALL USING (profile_id = auth.uid()::text);

-- Create functions for story management
CREATE OR REPLACE FUNCTION public.increment_story_view_count(story_id_param uuid, viewer_id_param text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Insert or update story view
  INSERT INTO public.story_views (story_id, viewer_id, viewed_at)
  VALUES (story_id_param, viewer_id_param, now())
  ON CONFLICT (story_id, viewer_id) 
  DO UPDATE SET viewed_at = now();
  
  -- Increment view count
  UPDATE public.voice_stories
  SET view_count = view_count + 1
  WHERE id = story_id_param;
END;
$$;

CREATE OR REPLACE FUNCTION public.get_active_stories(viewer_id_param text DEFAULT NULL)
RETURNS TABLE(
  id uuid,
  profile_id text,
  audio_url text,
  audio_duration numeric,
  transcript text,
  background_image_url text,
  background_color text,
  story_type text,
  title text,
  tags text[],
  poll_question text,
  poll_options jsonb,
  created_at timestamp with time zone,
  expires_at timestamp with time zone,
  view_count integer,
  reply_count integer,
  has_viewed boolean
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    vs.id,
    vs.profile_id,
    vs.audio_url,
    vs.audio_duration,
    vs.transcript,
    vs.background_image_url,
    vs.background_color,
    vs.story_type,
    vs.title,
    vs.tags,
    vs.poll_question,
    vs.poll_options,
    vs.created_at,
    vs.expires_at,
    vs.view_count,
    vs.reply_count,
    CASE 
      WHEN viewer_id_param IS NOT NULL AND sv.viewer_id IS NOT NULL THEN true
      ELSE false
    END as has_viewed
  FROM public.voice_stories vs
  LEFT JOIN public.story_views sv ON vs.id = sv.story_id AND sv.viewer_id = viewer_id_param
  WHERE vs.is_public = true 
    AND vs.expires_at > now()
  ORDER BY vs.created_at DESC;
END;
$$;

-- Create indexes for better performance
CREATE INDEX idx_voice_stories_profile_id ON public.voice_stories(profile_id);
CREATE INDEX idx_voice_stories_expires_at ON public.voice_stories(expires_at);
CREATE INDEX idx_voice_stories_created_at ON public.voice_stories(created_at);
CREATE INDEX idx_story_views_story_id ON public.story_views(story_id);
CREATE INDEX idx_story_replies_story_id ON public.story_replies(story_id);