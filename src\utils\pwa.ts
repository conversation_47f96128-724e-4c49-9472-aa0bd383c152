/**
 * PWA Service Worker Registration and Management
 */

// Check if service workers are supported
export const isServiceWorkerSupported = (): boolean => {
  return 'serviceWorker' in navigator;
};

// Register service worker
export const registerServiceWorker = async (): Promise<ServiceWorkerRegistration | null> => {
  if (!isServiceWorkerSupported()) {
    console.log('Service workers are not supported in this browser');
    return null;
  }

  try {
    console.log('🔧 Registering service worker...');
    const registration = await navigator.serviceWorker.register('/sw.js', {
      scope: '/',
      updateViaCache: 'none'
    });

    console.log('✅ Service worker registered successfully:', registration);

    // Handle service worker updates
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;
      if (newWorker) {
        console.log('🔄 New service worker found, installing...');
        
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            console.log('🆕 New service worker installed, update available');
            // You can show a notification to the user here
            showUpdateAvailableNotification();
          }
        });
      }
    });

    return registration;
  } catch (error) {
    console.error('❌ Service worker registration failed:', error);
    return null;
  }
};

// Show update available notification
const showUpdateAvailableNotification = (): void => {
  // Create a custom event that components can listen to
  const updateEvent = new CustomEvent('sw-update-available', {
    detail: { message: 'A new version of Audra is available!' }
  });
  window.dispatchEvent(updateEvent);
};

// Update service worker
export const updateServiceWorker = async (): Promise<void> => {
  if (!isServiceWorkerSupported()) return;

  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (registration && registration.waiting) {
      console.log('🔄 Updating service worker...');
      registration.waiting.postMessage({ type: 'SKIP_WAITING' });
      
      // Reload the page to activate the new service worker
      window.location.reload();
    }
  } catch (error) {
    console.error('❌ Service worker update failed:', error);
  }
};

// Unregister service worker (for development/debugging)
export const unregisterServiceWorker = async (): Promise<boolean> => {
  if (!isServiceWorkerSupported()) return false;

  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (registration) {
      const result = await registration.unregister();
      console.log('🗑️ Service worker unregistered:', result);
      return result;
    }
    return false;
  } catch (error) {
    console.error('❌ Service worker unregistration failed:', error);
    return false;
  }
};

// Check if app is running as PWA
export const isPWA = (): boolean => {
  const isStandaloneMode = window.matchMedia('(display-mode: standalone)').matches;
  const isIOSStandalone = (window.navigator as any).standalone === true;
  return isStandaloneMode || isIOSStandalone;
};

// Check if app can be installed
export const canInstallPWA = (): Promise<boolean> => {
  return new Promise((resolve) => {
    let canInstall = false;

    const handleBeforeInstallPrompt = () => {
      canInstall = true;
      resolve(true);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt, { once: true });

    // Timeout after 1 second
    setTimeout(() => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      resolve(canInstall);
    }, 1000);
  });
};

// Get PWA display mode
export const getPWADisplayMode = (): string => {
  if (isPWA()) {
    return 'standalone';
  }
  
  if (window.matchMedia('(display-mode: minimal-ui)').matches) {
    return 'minimal-ui';
  }
  
  if (window.matchMedia('(display-mode: fullscreen)').matches) {
    return 'fullscreen';
  }
  
  return 'browser';
};

// Add to home screen prompt for iOS
export const showIOSInstallPrompt = (): void => {
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
  const isInStandaloneMode = (window.navigator as any).standalone;

  if (isIOS && !isInStandaloneMode) {
    // Create and dispatch custom event for iOS install prompt
    const iosInstallEvent = new CustomEvent('ios-install-prompt', {
      detail: {
        message: 'To install Audra, tap the Share button and select "Add to Home Screen"'
      }
    });
    window.dispatchEvent(iosInstallEvent);
  }
};

// Cache management utilities
export const clearPWACache = async (): Promise<void> => {
  if ('caches' in window) {
    try {
      const cacheNames = await caches.keys();
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      );
      console.log('🧹 PWA cache cleared');
    } catch (error) {
      console.error('❌ Failed to clear PWA cache:', error);
    }
  }
};

// Get cache size
export const getCacheSize = async (): Promise<number> => {
  if (!('caches' in window)) return 0;

  try {
    const cacheNames = await caches.keys();
    let totalSize = 0;

    for (const cacheName of cacheNames) {
      const cache = await caches.open(cacheName);
      const requests = await cache.keys();
      
      for (const request of requests) {
        const response = await cache.match(request);
        if (response) {
          const blob = await response.blob();
          totalSize += blob.size;
        }
      }
    }

    return totalSize;
  } catch (error) {
    console.error('❌ Failed to calculate cache size:', error);
    return 0;
  }
};

// Format cache size for display
export const formatCacheSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// PWA analytics
export const trackPWAUsage = (): void => {
  const displayMode = getPWADisplayMode();
  const isInstalled = isPWA();
  
  // Track PWA usage (you can integrate with your analytics service)
  console.log('📊 PWA Analytics:', {
    displayMode,
    isInstalled,
    userAgent: navigator.userAgent,
    timestamp: new Date().toISOString()
  });
};

// Initialize PWA
export const initializePWA = async (): Promise<void> => {
  console.log('🚀 Initializing PWA...');
  
  // Register service worker
  await registerServiceWorker();
  
  // Track PWA usage
  trackPWAUsage();
  
  // Set up update listener
  window.addEventListener('sw-update-available', (event: any) => {
    console.log('🔄 Service worker update available:', event.detail.message);
  });
  
  console.log('✅ PWA initialization complete');
};
