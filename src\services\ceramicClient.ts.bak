/**
 * Ceramic client service for decentralized data storage
 * This service handles storing and retrieving metadata using Ceramic Network
 */

// Note: You'll need to install these dependencies:
// npm install @ceramicnetwork/http-client dids key-did-resolver

import { CeramicClient } from '@ceramicnetwork/http-client';
import { DID } from 'dids';
import { getResolver as keyDidResolver } from 'key-did-resolver';
import { Ed25519Provider } from 'key-did-provider-ed25519';
import { TileDocument } from '@ceramicnetwork/stream-tile';

// List of Ceramic endpoints to try
const ceramicEndpoints = [
  'https://ceramic.composedb.com',
  'https://gateway.ceramic.network',
  'https://ceramic-clay.3boxlabs.com',
  'https://dev.ceramic.network',
  'https://gateway-clay.ceramic.network',
  'https://ceramic.3boxlabs.com'
];

// Default endpoint to start with
const CERAMIC_API_URL = ceramicEndpoints[0];

/**
 * Try to connect to a Ceramic endpoint
 * @param endpoint The endpoint URL to try
 * @returns A promise that resolves to true if the connection was successful, false otherwise
 */
async function tryCeramicEndpoint(endpoint: string): Promise<boolean> {
  try {
    console.log(`Testing Ceramic endpoint: ${endpoint}`);

    // Try a simple fetch to see if the endpoint is reachable
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    const response = await fetch(`${endpoint}/api/v0/node/healthcheck`, {
      signal: controller.signal,
      method: 'GET',
      headers: { 'Accept': 'application/json' }
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      console.log(`✅ Successfully connected to Ceramic endpoint: ${endpoint}`);
      return true;
    } else {
      console.warn(`⚠️ Ceramic endpoint ${endpoint} returned status: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Failed to connect to Ceramic endpoint ${endpoint}:`, error);
    return false;
  }
}

/**
 * Find a working Ceramic endpoint
 * @returns A promise that resolves to a working endpoint URL or null if none found
 */
async function findWorkingCeramicEndpoint(): Promise<string | null> {
  for (const endpoint of ceramicEndpoints) {
    const isWorking = await tryCeramicEndpoint(endpoint);
    if (isWorking) {
      return endpoint;
    }
  }
  return null;
}

// Create a temporary Ceramic client with the default endpoint
// We'll update this with a working endpoint if we find one
export const ceramic = new CeramicClient(CERAMIC_API_URL) as any;

// Log the Ceramic client to check available methods
console.log('Initial Ceramic client methods:', Object.keys(ceramic));

// Initialize Ceramic with a working endpoint
(async () => {
  try {
    console.log('Searching for a working Ceramic endpoint...');

    // Try to find a working endpoint
    const workingEndpoint = await findWorkingCeramicEndpoint();

    if (workingEndpoint) {
      console.log(`Found working Ceramic endpoint: ${workingEndpoint}`);

      // Update the Ceramic client with the working endpoint
      if (workingEndpoint !== CERAMIC_API_URL) {
        console.log(`Switching Ceramic client to endpoint: ${workingEndpoint}`);
        ceramic._apiUrl = workingEndpoint;
      }

      // Try to authenticate the DID to make sure everything is working
      try {
        console.log('Testing DID authentication with the working endpoint...');
        const seed = new Uint8Array(32);
        window.crypto.getRandomValues(seed);

        const provider = new Ed25519Provider(seed);
        const did = new DID({
          provider,
          resolver: { ...keyDidResolver() }
        });

        await did.authenticate();
        console.log('✅ DID authentication successful with the working endpoint');
      } catch (authError) {
        console.warn('⚠️ DID authentication failed with the working endpoint:', authError);
      }
    } else {
      console.error('❌ Could not find any working Ceramic endpoint');
      console.error('This will cause profile creation to fail');
      console.error('Please check your network connection and try again later');

      // Try a direct DNS lookup to diagnose the issue
      console.log('Attempting direct DNS lookup to diagnose the issue...');
      try {
        // This won't actually work in the browser, but it's a hint for the user
        console.log('Try running "nslookup gateway.ceramic.network" in your terminal');
        console.log('Or visit https://gateway.ceramic.network in your browser');
      } catch (e) {
        // Ignore errors
      }
    }
  } catch (error) {
    console.error('Error initializing Ceramic:', error);
  }
})();

// Patch the Ceramic client to avoid process.env usage
// This is a workaround for the "process is not defined" error
if (ceramic.constructor.prototype._getDidOptions) {
  const originalGetDidOptions = ceramic.constructor.prototype._getDidOptions;
  ceramic.constructor.prototype._getDidOptions = function () {
    try {
      return originalGetDidOptions.call(this);
    } catch (e) {
      // If there's an error accessing process.env, return default options
      console.warn('Using default DID options due to process.env access error');
      return {
        skipMigration: false,
        syncTimeoutSeconds: 30
      };
    }
  };
}

/**
 * Authenticate with Ceramic using a DID
 * @returns The authenticated Ceramic client
 */
export async function authenticateDID(): Promise<CeramicClient> {
  try {
    // First, make sure we have a working endpoint
    console.log('Finding a working Ceramic endpoint before authentication...');
    const workingEndpoint = await findWorkingCeramicEndpoint();

    if (!workingEndpoint) {
      throw new Error('No working Ceramic endpoint found. Cannot authenticate DID.');
    }

    // Update the Ceramic client with the working endpoint
    if (workingEndpoint !== ceramic._apiUrl) {
      console.log(`Switching to working endpoint for authentication: ${workingEndpoint}`);
      ceramic._apiUrl = workingEndpoint;
    }

    console.log('Starting DID authentication with Ceramic endpoint:', ceramic._apiUrl);

    // Generate a random seed for the DID
    const seed = new Uint8Array(32);
    window.crypto.getRandomValues(seed);
    console.log('Generated random seed for DID');

    // Create a DID provider with the seed
    const provider = new Ed25519Provider(seed);
    console.log('Created Ed25519Provider for DID');

    // Create a DID instance with the provider and resolver
    const did = new DID({
      provider,
      resolver: {
        ...keyDidResolver()
      }
    });
    console.log('Created DID instance');

    // Authenticate the DID
    console.log('Attempting to authenticate DID...');
    await did.authenticate();
    console.log('DID authenticated successfully');

    // Set the DID on the Ceramic client
    ceramic.did = did;
    console.log('Set authenticated DID on Ceramic client');

    console.log('Successfully authenticated DID with Ceramic');
    return ceramic;
  } catch (error) {
    console.error('Error authenticating DID with Ceramic:', error);

    // Provide more detailed error information
    if (error instanceof TypeError && error.message.includes('fetch')) {
      console.error('Network error: Unable to connect to Ceramic endpoint. This could be due to:');
      console.error('1. DNS resolution issues');
      console.error('2. Network connectivity problems');
      console.error('3. Firewall or network restrictions');
      console.error('4. The Ceramic service might be down');
      console.error('Please check your network connection and try again later.');
    }

    throw error;
  }
}

/**
 * Create a voice post document in Ceramic
 * @param metadata The metadata for the voice post
 * @returns The Ceramic document ID
 */
export async function publishVoicePost(metadata: VoicePostMetadata): Promise<string> {
  try {
    // Ensure we have an authenticated DID
    if (!ceramic.did) {
      throw new Error('No authenticated DID. Call authenticateDID() first.');
    }

    // Create a new document with the metadata using TileDocument
    const doc = await TileDocument.create(
      ceramic,
      metadata,
      {
        controllers: [ceramic.did?.id || ''],
        family: 'voicepost'
      }
    );

    // Return the document ID
    return doc.id.toString();
  } catch (error) {
    console.error('Error publishing voice post to Ceramic:', error);
    throw error;
  }
}

/**
 * Get a voice post document from Ceramic
 * @param documentId The Ceramic document ID
 * @returns The voice post metadata
 */
export async function getVoicePost(documentId: string): Promise<VoicePostMetadata> {
  try {
    // Load the document using TileDocument
    const doc = await TileDocument.load(ceramic, documentId);

    // Return the document content
    return doc.content as VoicePostMetadata;
  } catch (error) {
    console.error('Error getting voice post from Ceramic:', error);
    throw error;
  }
}

/**
 * Update a voice post document in Ceramic
 * @param documentId The Ceramic document ID
 * @param metadata The updated metadata
 * @returns The updated document ID (should be the same)
 */
export async function updateVoicePost(
  documentId: string,
  metadata: VoicePostMetadata
): Promise<string> {
  try {
    // Ensure we have an authenticated DID
    if (!ceramic.did) {
      throw new Error('No authenticated DID. Call authenticateDID() first.');
    }

    // Load the document using TileDocument
    const doc = await TileDocument.load(ceramic, documentId);

    // Update the document content
    await doc.update(metadata);

    // Return the document ID
    return doc.id.toString();
  } catch (error) {
    console.error('Error updating voice post in Ceramic:', error);
    throw error;
  }
}

/**
 * Interface for voice post metadata
 */
export interface VoicePostMetadata {
  audio: string; // IPFS URI for the audio file
  transcript?: string; // Optional transcript
  encrypted?: boolean; // Whether the audio is encrypted
  createdAt: string; // ISO timestamp
  updatedAt?: string; // ISO timestamp for updates
  author: string; // DID or wallet address of the author
  accessControlConditions?: any[]; // Lit Protocol access control conditions if encrypted
  media?: MediaItem[]; // Optional attached media
  parentId?: string; // Optional parent post ID for replies
  channelId?: string; // Optional channel ID
  isPinned?: boolean; // Whether the post is pinned
  duration?: number; // Audio duration in seconds
}

/**
 * Interface for media items
 */
export interface MediaItem {
  id: string;
  url: string; // IPFS URI
  type: 'image' | 'video';
}
