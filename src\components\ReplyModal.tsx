
import React from 'react';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { Drawer, DrawerContent } from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';
import AudioRecorder from './AudioRecorder';
import { MediaFile } from './MediaUploader';
import { useIsMobile } from '@/hooks/use-mobile';

interface ReplyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onReplyComplete: (parentId: string, audioBlob: Blob, transcript: string, duration?: number, media?: MediaFile[]) => void;
  parentId: string;
}

const ReplyModal: React.FC<ReplyModalProps> = ({
  isOpen,
  onClose,
  onReplyComplete,
  parentId
}) => {
  const isMobile = useIsMobile();

  const handleRecordingComplete = (audioBlob: Blob, transcript: string, duration?: number, media?: MediaFile[]) => {
    onReplyComplete(parentId, audioBlob, transcript, duration, media);
    onClose();
  };

  const ModalContent = () => (
    <div className="flex flex-col items-center justify-center py-6 px-4 space-y-6 relative">
      <div className="flex justify-between items-center w-full sticky top-0 bg-background z-10 pb-2">
        {isMobile ? (
          <h2 className="text-xl font-semibold">Record a reply</h2>
        ) : (
          <DialogTitle className="text-xl font-semibold">Record a reply</DialogTitle>
        )}
        <Button variant="ghost" size="icon" onClick={onClose}>
          <X size={20} />
        </Button>
      </div>

      <div className="w-full max-w-md">
        <AudioRecorder
          onRecordingComplete={handleRecordingComplete}
          onCancel={onClose}
          maxMediaFiles={4}
        />
      </div>
    </div>
  );

  if (isMobile) {
    return (
      <Drawer open={isOpen} onOpenChange={open => !open && onClose()}>
        <DrawerContent className="max-h-[85vh] overflow-y-auto">
          <ModalContent />
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
        <DialogTitle className="sr-only">Record a reply</DialogTitle>
        <ModalContent />
      </DialogContent>
    </Dialog>
  );
};

export default ReplyModal;
