# Mobile App Deployment Guide

## Building and Publishing Your App

### 1. Testing on Physical Devices

Before publishing, test your app on real devices:

#### For iOS:
```bash
# Connect your device and run
npx react-native run-ios --device "Your iPhone Name"
```

#### For Android:
```bash
# Connect your device and run
npx react-native run-android
```

### 2. Preparing for App Store Submission (iOS)

1. **Create an Apple Developer Account**
   - Sign up at [developer.apple.com](https://developer.apple.com)
   - Pay the annual fee ($99)

2. **Configure App in App Store Connect**
   - Log in to [App Store Connect](https://appstoreconnect.apple.com)
   - Create a new app entry
   - Fill in all required metadata (app name, description, screenshots, etc.)

3. **Configure Xcode Project**
   - Open `ios/AudraMobile.xcworkspace` in Xcode
   - Set up signing certificates and provisioning profiles
   - Configure app icons and launch screens
   - Set the correct bundle identifier

4. **Build for Distribution**
   ```bash
   # In the project root
   cd ios
   xcodebuild -workspace AudraMobile.xcworkspace -scheme AudraMobile -configuration Release -archivePath ./build/AudraMobile.xcarchive archive
   ```

5. **Upload to App Store**
   - Use Xcode's Organizer to upload the archive
   - Or use Application Loader

### 3. Preparing for Google Play Store Submission (Android)

1. **Create a Google Play Developer Account**
   - Sign up at [play.google.com/console](https://play.google.com/console)
   - Pay the one-time fee ($25)

2. **Configure App in Google Play Console**
   - Create a new app
   - Fill in all required metadata (app name, description, screenshots, etc.)

3. **Generate a Signed APK/AAB**
   - Create a keystore file:
     ```bash
     keytool -genkeypair -v -storetype PKCS12 -keystore audra-key.keystore -alias audra-alias -keyalg RSA -keysize 2048 -validity 10000
     ```
   - Add signing config to `android/app/build.gradle`:
     ```gradle
     signingConfigs {
         release {
             storeFile file('audra-key.keystore')
             storePassword 'your-store-password'
             keyAlias 'audra-alias'
             keyPassword 'your-key-password'
         }
     }
     buildTypes {
         release {
             signingConfig signingConfigs.release
             // ...
         }
     }
     ```

4. **Build the Release Version**
   ```bash
   # For APK
   cd android && ./gradlew assembleRelease
   
   # For AAB (recommended)
   cd android && ./gradlew bundleRelease
   ```

5. **Upload to Google Play**
   - Go to your app in Google Play Console
   - Navigate to "Production" > "Create new release"
   - Upload your AAB file
   - Complete the release form and submit

### 4. Using CI/CD for Mobile Deployment

Consider setting up CI/CD for automated builds:

#### Using GitHub Actions:

Create `.github/workflows/mobile-build.yml`:

```yaml
name: Mobile App Build

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  android-build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Set up JDK
        uses: actions/setup-java@v2
        with:
          distribution: 'adopt'
          java-version: '11'
          
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build Android Release
        run: |
          cd android
          ./gradlew bundleRelease
          
      - name: Upload Artifact
        uses: actions/upload-artifact@v2
        with:
          name: app-release.aab
          path: android/app/build/outputs/bundle/release/app-release.aab
          
  ios-build:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Install CocoaPods
        run: |
          cd ios
          pod install
          
      - name: Build iOS
        run: |
          cd ios
          xcodebuild -workspace AudraMobile.xcworkspace -scheme AudraMobile -configuration Release -sdk iphoneos build CODE_SIGN_IDENTITY="" CODE_SIGNING_REQUIRED=NO
```

### 5. App Updates

For seamless updates without going through the app stores:

1. **Consider CodePush (AppCenter)**
   - Install the package:
     ```bash
     npm install react-native-code-push
     ```
   - Wrap your app with CodePush:
     ```typescript
     import codePush from 'react-native-code-push';
     
     const codePushOptions = {
       checkFrequency: codePush.CheckFrequency.ON_APP_START,
     };
     
     export default codePush(codePushOptions)(App);
     ```

2. **Deploy Updates**
   ```bash
   npx appcenter codepush release-react -a {owner}/{app} -d Production
   ```

### 6. Analytics and Crash Reporting

Implement mobile-specific analytics:

1. **Firebase Analytics and Crashlytics**
   - Install the packages:
     ```bash
     npm install @react-native-firebase/app @react-native-firebase/analytics @react-native-firebase/crashlytics
     ```
   - Initialize in your app:
     ```typescript
     import analytics from '@react-native-firebase/analytics';
     import crashlytics from '@react-native-firebase/crashlytics';
     
     // Log an event
     analytics().logEvent('user_action', {
       id: 'user123',
       action: 'voice_recording'
     });
     
     // Log a crash
     try {
       // Your code
     } catch (error) {
       crashlytics().recordError(error);
     }
     ```
