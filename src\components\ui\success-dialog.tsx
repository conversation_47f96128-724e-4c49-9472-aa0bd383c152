import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle2, Pin, Trash2, Mic } from "lucide-react";

interface SuccessDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  icon?: React.ReactNode;
  autoClose?: boolean;
  autoCloseDelay?: number;
}

export const SuccessDialog: React.FC<SuccessDialogProps> = ({
  isOpen,
  onOpenChange,
  title,
  description,
  icon,
  autoClose = true,
  autoCloseDelay = 2000
}) => {
  React.useEffect(() => {
    if (isOpen && autoClose) {
      const timer = setTimeout(() => {
        onOpenChange(false);
      }, autoCloseDelay);
      return () => clearTimeout(timer);
    }
  }, [isOpen, autoClose, autoCloseDelay, onOpenChange]);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 rounded-full bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400">
              {icon || <CheckCircle2 size={20} />}
            </div>
            <DialogTitle className="text-lg font-semibold text-green-700 dark:text-green-400">
              {title}
            </DialogTitle>
          </div>
          <DialogDescription className="text-sm text-muted-foreground leading-relaxed">
            {description}
          </DialogDescription>
        </DialogHeader>
        <div className="flex justify-end mt-4">
          <Button 
            onClick={() => onOpenChange(false)}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            Got it
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Predefined success dialogs
interface PostDeletedDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export const PostDeletedDialog: React.FC<PostDeletedDialogProps> = ({
  isOpen,
  onOpenChange
}) => {
  return (
    <SuccessDialog
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title="Post Deleted"
      description="Your post has been successfully deleted and removed from your profile."
      icon={<Trash2 size={20} />}
    />
  );
};

interface PostPinnedDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  isPinned: boolean;
}

export const PostPinnedDialog: React.FC<PostPinnedDialogProps> = ({
  isOpen,
  onOpenChange,
  isPinned
}) => {
  return (
    <SuccessDialog
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title={isPinned ? "Post Pinned" : "Post Unpinned"}
      description={
        isPinned 
          ? "Your post has been pinned to the top of your profile for everyone to see."
          : "Your post has been unpinned and moved back to your regular feed."
      }
      icon={<Pin size={20} />}
    />
  );
};

interface PostCreatedDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export const PostCreatedDialog: React.FC<PostCreatedDialogProps> = ({
  isOpen,
  onOpenChange
}) => {
  return (
    <SuccessDialog
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title="Post Created"
      description="Your voice message has been successfully posted and is now live on your profile."
      icon={<Mic size={20} />}
    />
  );
};