
import React, { useState, useEffect } from 'react';
import { AvatarImage } from '@/components/ui/avatar';

interface CustomAvatarImageProps {
  src: string | null | undefined;
  alt?: string;
  className?: string;
}

const CustomAvatarImage: React.FC<CustomAvatarImageProps> = ({ src, alt, className }) => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<boolean>(false);

  useEffect(() => {
    // Reset state when src changes
    setIsLoading(true);
    setError(false);

    // If src is null or undefined, set error and stop loading
    if (!src) {
      setError(true);
      setIsLoading(false);
      return;
    }

    // Create an image object to check if the image loads correctly
    const img = new Image();
    img.onload = () => {
      setIsLoading(false);
    };
    img.onerror = () => {
      console.error('Error loading image:', src);
      setError(true);
      setIsLoading(false);
    };
    img.src = src;
  }, [src]);

  if (error || !src) {
    // Return null to trigger the fallback
    return null;
  }

  return (
    <AvatarImage
      src={src}
      alt={alt || 'User avatar'}
      className={className}
      style={{ opacity: isLoading ? 0.5 : 1 }}
    />
  );
};

export default CustomAvatarImage;
