-- Fix Media Persistence - UUID/TEXT Column Type Mismatch
-- This script fixes the incompatible column types between voice_messages and voice_message_media tables

-- Step 1: Drop the foreign key constraint temporarily
ALTER TABLE voice_message_media 
DROP CONSTRAINT IF EXISTS voice_message_media_voice_message_id_fkey;

-- Step 2: Convert voice_message_media columns to TEXT
ALTER TABLE voice_message_media 
ALTER COLUMN id TYPE TEXT USING id::text;

ALTER TABLE voice_message_media 
ALTER COLUMN voice_message_id TYPE TEXT USING voice_message_id::text;

-- Step 3: Update the default value for id column
ALTER TABLE voice_message_media 
ALTER COLUMN id SET DEFAULT gen_random_uuid()::text;

-- Step 4: Check if voice_messages table needs to be converted too
-- First, let's see what type voice_messages.id currently is
DO $$
DECLARE
    voice_messages_id_type TEXT;
BEGIN
    SELECT data_type INTO voice_messages_id_type
    FROM information_schema.columns 
    WHERE table_name = 'voice_messages' 
    AND column_name = 'id' 
    AND table_schema = 'public';
    
    RAISE NOTICE 'voice_messages.id column type: %', voice_messages_id_type;
    
    -- If voice_messages.id is UUID, convert it to TEXT
    IF voice_messages_id_type = 'uuid' THEN
        RAISE NOTICE 'Converting voice_messages.id from UUID to TEXT...';
        
        -- Convert voice_messages.id to TEXT
        ALTER TABLE voice_messages 
        ALTER COLUMN id TYPE TEXT USING id::text;
        
        -- Update default value
        ALTER TABLE voice_messages 
        ALTER COLUMN id SET DEFAULT gen_random_uuid()::text;
        
        -- Also convert parent_id if it exists and is UUID
        IF EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'voice_messages' 
                   AND column_name = 'parent_id' 
                   AND table_schema = 'public'
                   AND data_type = 'uuid') THEN
            ALTER TABLE voice_messages 
            ALTER COLUMN parent_id TYPE TEXT USING parent_id::text;
        END IF;
        
        RAISE NOTICE 'voice_messages table converted to TEXT successfully';
    ELSE
        RAISE NOTICE 'voice_messages.id is already TEXT type';
    END IF;
END $$;

-- Step 5: Recreate the foreign key constraint with correct types
ALTER TABLE voice_message_media 
ADD CONSTRAINT voice_message_media_voice_message_id_fkey 
FOREIGN KEY (voice_message_id) REFERENCES voice_messages(id) ON DELETE CASCADE;

-- Step 6: Create indexes for better performance
CREATE INDEX IF NOT EXISTS voice_message_media_voice_message_id_idx 
ON voice_message_media(voice_message_id);

CREATE INDEX IF NOT EXISTS voice_messages_id_idx 
ON voice_messages(id);

-- Step 7: Ensure RLS policies are in place
ALTER TABLE voice_message_media ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Allow all operations on voice_message_media" ON voice_message_media;
DROP POLICY IF EXISTS "Users can view all voice message media" ON voice_message_media;
DROP POLICY IF EXISTS "Users can insert voice message media" ON voice_message_media;
DROP POLICY IF EXISTS "Users can delete voice message media" ON voice_message_media;

-- Create permissive RLS policies
CREATE POLICY "Allow all operations on voice_message_media" 
ON voice_message_media 
FOR ALL 
USING (true) 
WITH CHECK (true);

-- Step 8: Grant necessary permissions
GRANT ALL ON voice_message_media TO anon;
GRANT ALL ON voice_message_media TO authenticated;

-- Step 9: Test the fix with a sample insert
DO $$
DECLARE
    test_message_id TEXT := 'test_' || extract(epoch from now())::text;
    test_media_id TEXT := 'media_' || extract(epoch from now())::text;
BEGIN
    -- Insert test voice message
    INSERT INTO voice_messages (
        id, 
        profile_id, 
        audio_url, 
        transcript, 
        audio_duration, 
        created_at
    ) VALUES (
        test_message_id,
        'test_profile',
        'https://example.com/test.mp3',
        'Test message for media fix',
        10,
        NOW()
    );
    
    -- Insert test media
    INSERT INTO voice_message_media (
        id,
        voice_message_id,
        url,
        type,
        created_at
    ) VALUES (
        test_media_id,
        test_message_id,
        'https://example.com/test-image.jpg',
        'image',
        NOW()
    );
    
    RAISE NOTICE 'Test insert successful - media persistence should now work!';
    
    -- Clean up test data
    DELETE FROM voice_message_media WHERE id = test_media_id;
    DELETE FROM voice_messages WHERE id = test_message_id;
    
    RAISE NOTICE 'Test data cleaned up';
    
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Test insert failed: %', SQLERRM;
END $$;

-- Step 10: Show final table structures
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name IN ('voice_messages', 'voice_message_media')
AND table_schema = 'public'
ORDER BY table_name, ordinal_position;

-- Success message
SELECT 'Media persistence UUID/TEXT mismatch fixed successfully!' as status;
