-- Create content_reports table
CREATE TABLE IF NOT EXISTS content_reports (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  content_id TEXT NOT NULL,
  content_type TEXT NOT NULL CHECK (content_type IN ('voice_message', 'comment', 'profile')),
  reason TEXT NOT NULL,
  details TEXT,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'dismissed', 'removed')),
  reported_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  resolved_at TIMESTAMP WITH TIME ZONE,
  reporter_id UUID REFERENCES profiles(id),
  reported_user_id UUID REFERENCES profiles(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies
ALTER TABLE content_reports ENABLE ROW LEVEL SECURITY;

-- Allow any authenticated user to create a report
CREATE POLICY "Users can create reports" 
ON content_reports FOR INSERT 
TO authenticated
WITH CHECK (auth.uid() = reporter_id);

-- Only allow admins to read all reports
CREATE POLICY "Admins can read all reports" 
ON content_reports FOR SELECT 
USING (auth.uid() IN (SELECT id FROM admin_profiles));

-- Allow users to read their own reports
CREATE POLICY "Users can read their own reports" 
ON content_reports FOR SELECT 
USING (auth.uid() = reporter_id);

-- Only allow admins to update reports
CREATE POLICY "Admins can update reports" 
ON content_reports FOR UPDATE 
USING (auth.uid() IN (SELECT id FROM admin_profiles));

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS content_reports_content_id_idx ON content_reports(content_id);
CREATE INDEX IF NOT EXISTS content_reports_reporter_id_idx ON content_reports(reporter_id);
CREATE INDEX IF NOT EXISTS content_reports_reported_user_id_idx ON content_reports(reported_user_id);
CREATE INDEX IF NOT EXISTS content_reports_status_idx ON content_reports(status);

-- Add columns to voice_messages table for moderation
ALTER TABLE IF EXISTS voice_messages 
ADD COLUMN IF NOT EXISTS is_removed BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS removed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS removed_reason TEXT;

-- Add columns to comments table for moderation
ALTER TABLE IF EXISTS comments 
ADD COLUMN IF NOT EXISTS is_removed BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS removed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS removed_reason TEXT;

-- Add columns to profiles table for moderation
ALTER TABLE IF EXISTS profiles 
ADD COLUMN IF NOT EXISTS is_flagged BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS flagged_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS flag_reason TEXT;

-- Create function to report content
CREATE OR REPLACE FUNCTION report_content(
  p_content_id TEXT,
  p_content_type TEXT,
  p_reason TEXT,
  p_details TEXT,
  p_reported_user_id UUID
)
RETURNS UUID AS $$
DECLARE
  v_report_id UUID;
BEGIN
  INSERT INTO content_reports (
    content_id,
    content_type,
    reason,
    details,
    reporter_id,
    reported_user_id
  ) VALUES (
    p_content_id,
    p_content_type,
    p_reason,
    p_details,
    auth.uid(),
    p_reported_user_id
  ) RETURNING id INTO v_report_id;
  
  RETURN v_report_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Instructions:
-- 1. Run this script in the Supabase SQL editor
-- 2. This will set up the content_reports table and add moderation columns to existing tables
-- 3. Users can report content using the report_content function
-- 4. Admins can view and moderate reported content through the admin dashboard
