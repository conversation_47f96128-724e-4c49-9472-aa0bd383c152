/**
 * Orbis shim
 * This file replaces the Orbis SDK with a disabled version
 * to prevent errors when Orbis/Ceramic is not available
 */

// Polyfills for Node.js modules used by Orbis SDK
// These are still needed for other libraries
import process from '../shims/process-browser';
import { <PERSON>uffer } from 'buffer';

// Make sure global is defined
(window as any).global = window;

// Add process to window
(window as any).process = process;

// Add Buffer to window
(window as any).Buffer = Buffer;

// Replace the Orbis SDK with our disabled version
// This is done by adding a module alias in the window object
// that will be used by the dynamic import() function
if (typeof window !== 'undefined') {
    // Create a moduleAlias object if it doesn't exist
    if (!(window as any).moduleAlias) {
        (window as any).moduleAlias = {};
    }

    // Add an alias for the Orbis SDK
    (window as any).moduleAlias['@orbisclub/orbis-sdk'] = '../services/disabledOrbisClient';
}

// Add console message to confirm the shim is loaded
console.log('Orbis disabled - using stub implementation');
