
export type NotificationType = 'like' | 'reply' | 'tip' | 'follow' | 'summon' | 'repost' | 'reaction';

export interface Notification {
  id: string;
  type: NotificationType;
  from_address: string;  // Using snake_case to match Supabase
  to_address: string;    // Using snake_case to match Supabase
  message_id?: string;
  data?: any;
  read: boolean;
  created_at: string;    // Using snake_case to match Supabase
}
