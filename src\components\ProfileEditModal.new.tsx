import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>T<PERSON>le, <PERSON><PERSON>Header, DialogFooter } from '@/components/ui/dialog';
import { Drawer, DrawerContent, DrawerHeader, DrawerFooter } from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { X, Upload, Twitter, Globe, Github } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { toast } from '@/components/ui/sonner';
import { UserProfile, UserProfileUpdate } from '@/types/user-profile';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { supabase } from '@/integrations/supabase/client';
import { ScrollArea } from '@/components/ui/scroll-area';

interface ProfileEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  profile: UserProfile;
  onSave: (update: UserProfileUpdate) => void;
}

const ProfileEditModal: React.FC<ProfileEditModalProps> = ({
  isOpen,
  onClose,
  profile,
  onSave
}) => {
  const isMobile = useIsMobile();
  const [formData, setFormData] = useState<UserProfileUpdate & {
    profileImageFile?: File;
    coverImageFile?: File;
  }>({
    username: profile.username,
    displayName: profile.displayName,
    bio: profile.bio || '',
    profileImageUrl: profile.profileImageUrl || '',
    coverImageUrl: profile.coverImageUrl || '',
    socialLinks: {
      twitter: profile.socialLinks?.twitter || '',
      github: profile.socialLinks?.github || '',
      website: profile.socialLinks?.website || ''
    }
  });
  
  const [isUploading, setIsUploading] = useState(false);
  
  // Refs for file inputs
  const profileImageInputRef = useRef<HTMLInputElement>(null);
  const coverImageInputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    if (name.startsWith('social.')) {
      const socialType = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        socialLinks: {
          ...prev.socialLinks || {},
          [socialType]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleProfileImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // Create a preview
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setFormData(prev => ({
            ...prev,
            profileImageUrl: event.target?.result as string,
            profileImageFile: file
          }));
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleCoverImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // Create a preview
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setFormData(prev => ({
            ...prev,
            coverImageUrl: event.target?.result as string,
            coverImageFile: file
          }));
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const uploadImageToSupabase = async (file: File, path: string): Promise<string> => {
    try {
      // Upload image to storage
      const { data, error } = await supabase.storage
        .from('profiles')
        .upload(`${profile.address}/${path}`, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (error) {
        throw new Error(error.message);
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('profiles')
        .getPublicUrl(data.path);

      return urlData.publicUrl;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    }
  };

  const handleSubmit = async () => {
    try {
      setIsUploading(true);
      toast.loading('Updating profile...');

      // Validate the form data
      if (!formData.displayName || formData.displayName.trim() === '') {
        toast.dismiss();
        toast.error('Display name is required');
        setIsUploading(false);
        return;
      }

      if (!formData.username || formData.username.trim() === '') {
        toast.dismiss();
        toast.error('Username is required');
        setIsUploading(false);
        return;
      }

      // Create a deep copy of the form data to avoid reference issues
      const update: UserProfileUpdate = {
        displayName: formData.displayName.trim(),
        username: formData.username.trim(),
        bio: formData.bio?.trim() || '',
        socialLinks: { ...formData.socialLinks },
        profileImageUrl: formData.profileImageUrl,
        coverImageUrl: formData.coverImageUrl
      };

      // Upload profile image if changed
      if (formData.profileImageFile) {
        try {
          const profileImageUrl = await uploadImageToSupabase(formData.profileImageFile, `profile-${Date.now()}`);
          update.profileImageUrl = profileImageUrl;
        } catch (error) {
          console.error('Error uploading profile image:', error);
          toast.dismiss();
          toast.error('Failed to upload profile image');
          setIsUploading(false);
          return;
        }
      }

      // Upload cover image if changed
      if (formData.coverImageFile) {
        try {
          const coverImageUrl = await uploadImageToSupabase(formData.coverImageFile, `cover-${Date.now()}`);
          update.coverImageUrl = coverImageUrl;
        } catch (error) {
          console.error('Error uploading cover image:', error);
          toast.dismiss();
          toast.error('Failed to upload cover image');
          setIsUploading(false);
          return;
        }
      }

      console.log("ProfileEditModal - Updating profile with:", update);

      // Call the onSave function with the update data
      await onSave(update);

      // Dismiss loading toast
      toast.dismiss();
      toast.success('Profile updated successfully!');

      // Close the modal
      onClose();
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.dismiss();
      toast.error("Failed to update profile");
    } finally {
      setIsUploading(false);
    }
  };

  // Function to handle removing profile image
  const removeProfileImage = () => {
    setFormData(prev => ({
      ...prev,
      profileImageUrl: '',
      profileImageFile: undefined
    }));
  };

  // Function to handle removing cover image
  const removeCoverImage = () => {
    setFormData(prev => ({
      ...prev,
      coverImageUrl: '',
      coverImageFile: undefined
    }));
  };

  const ModalContent = () => (
    <>
      <div className="flex justify-between items-center w-full mb-4">
        <h2 className="text-xl font-semibold">Edit Profile</h2>
        <Button variant="ghost" size="icon" onClick={onClose} type="button">
          <X size={20} />
        </Button>
      </div>

      <ScrollArea className="h-[60vh] pr-4">
        <div className="space-y-6">
          {/* Cover Image */}
          <div className="relative">
            <div
              className="w-full h-32 bg-secondary rounded-lg overflow-hidden flex items-center justify-center"
              style={{
                backgroundImage: formData.coverImageUrl ? `url(${formData.coverImageUrl})` : undefined,
                backgroundSize: 'cover',
                backgroundPosition: 'center'
              }}
            >
              {!formData.coverImageUrl && <span className="text-muted-foreground">Add Cover Image</span>}
            </div>
            <div className="absolute bottom-2 right-2 flex space-x-2">
              {formData.coverImageUrl && (
                <Button
                  variant="destructive"
                  size="icon"
                  className="h-8 w-8 rounded-full"
                  onClick={removeCoverImage}
                  type="button"
                >
                  <X size={14} />
                </Button>
              )}
              <Button
                variant="secondary"
                size="icon"
                className="h-8 w-8 rounded-full"
                onClick={() => coverImageInputRef.current?.click()}
                type="button"
              >
                <Upload size={14} />
              </Button>
              <input
                ref={coverImageInputRef}
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleCoverImageChange}
              />
            </div>
          </div>

          {/* Profile Image */}
          <div className="flex justify-center -mt-12">
            <div className="relative">
              <Avatar className="h-24 w-24 border-4 border-background">
                <AvatarImage src={formData.profileImageUrl} alt="Profile" />
                <AvatarFallback className="bg-voicechain-purple text-white text-xl">
                  {formData.displayName?.charAt(0) || profile.displayName.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div className="absolute -bottom-2 -right-2 flex space-x-1">
                {formData.profileImageUrl && (
                  <Button
                    variant="destructive"
                    size="icon"
                    className="h-7 w-7 rounded-full"
                    onClick={removeProfileImage}
                    type="button"
                  >
                    <X size={12} />
                  </Button>
                )}
                <Button
                  variant="secondary"
                  size="icon"
                  className="h-7 w-7 rounded-full"
                  onClick={() => profileImageInputRef.current?.click()}
                  type="button"
                >
                  <Upload size={12} />
                </Button>
                <input
                  ref={profileImageInputRef}
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleProfileImageChange}
                />
              </div>
            </div>
          </div>

          {/* Form Fields */}
          <div className="space-y-4 mt-4">
            <div>
              <Label htmlFor="displayName">Display Name</Label>
              <Input
                id="displayName"
                name="displayName"
                value={formData.displayName}
                onChange={handleInputChange}
                placeholder="Your display name"
              />
            </div>

            <div>
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                placeholder="username"
              />
            </div>

            <div>
              <Label htmlFor="bio">Bio</Label>
              <Textarea
                id="bio"
                name="bio"
                value={formData.bio}
                onChange={handleInputChange}
                placeholder="Tell us about yourself"
                className="resize-none h-24"
              />
            </div>

            <div>
              <Label className="flex items-center gap-2" htmlFor="social.twitter">
                <Twitter size={16} className="text-[#1DA1F2]" />
                Twitter
              </Label>
              <Input
                id="social.twitter"
                name="social.twitter"
                value={formData.socialLinks?.twitter || ''}
                onChange={handleInputChange}
                placeholder="@username"
              />
            </div>

            <div>
              <Label className="flex items-center gap-2" htmlFor="social.github">
                <Github size={16} />
                GitHub
              </Label>
              <Input
                id="social.github"
                name="social.github"
                value={formData.socialLinks?.github || ''}
                onChange={handleInputChange}
                placeholder="username"
              />
            </div>

            <div>
              <Label className="flex items-center gap-2" htmlFor="social.website">
                <Globe size={16} className="text-blue-500" />
                Website
              </Label>
              <Input
                id="social.website"
                name="social.website"
                value={formData.socialLinks?.website || ''}
                onChange={handleInputChange}
                placeholder="https://yourwebsite.com"
              />
            </div>
          </div>
        </div>
      </ScrollArea>

      <div className="mt-6">
        <Button
          onClick={handleSubmit}
          className="w-full bg-voicechain-purple hover:bg-voicechain-accent"
          disabled={isUploading}
          type="button"
        >
          {isUploading ? 'Saving...' : 'Save Profile'}
        </Button>
      </div>
    </>
  );

  if (isMobile) {
    return (
      <Drawer open={isOpen} onOpenChange={open => !open && onClose()}>
        <DrawerContent className="px-4 py-6">
          <ModalContent />
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-md p-6">
        <ModalContent />
      </DialogContent>
    </Dialog>
  );
};

export default ProfileEditModal;
