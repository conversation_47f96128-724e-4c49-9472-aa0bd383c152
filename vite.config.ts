import { defineConfig, Plugin } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// Removed Ceramic fix as we're not using Ceramic anymore

// Custom plugin to fix Orbis SDK
function orbisFix(): Plugin {
  return {
    name: 'orbis-sdk-fix',
    transform(code, id) {
      // Target the Orbis SDK files
      if (id.includes('@orbisclub/orbis-sdk')) {
        // Replace Node.js specific imports with browser-compatible ones
        let newCode = code;

        // Fix stream imports
        if (code.includes("require('stream')")) {
          newCode = newCode.replace(
            /require\('stream'\)/g,
            "require('stream-browserify')"
          );
        }

        // Fix crypto imports
        if (code.includes("require('crypto')")) {
          newCode = newCode.replace(
            /require\('crypto'\)/g,
            "require('crypto-browserify')"
          );
        }

        // Fix process references
        if (code.includes('process.env')) {
          newCode = newCode.replace(
            /process\.env/g,
            '({NODE_ENV:"production",BROWSER:true})'
          );
        }

        return newCode;
      }
      return code;
    }
  };
}

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [
    react(),
    mode === 'development' && componentTagger(),
    orbisFix(), // Add our custom Orbis fix plugin
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      // Polyfills for Node.js built-ins - use absolute paths to avoid duplication
      buffer: path.resolve(__dirname, 'node_modules/buffer'),
      process: path.resolve(__dirname, 'node_modules/process'),
      'process/browser': path.resolve(__dirname, 'node_modules/process/browser.js'),
      stream: path.resolve(__dirname, 'node_modules/stream-browserify'),
      util: path.resolve(__dirname, 'node_modules/util'),
      events: path.resolve(__dirname, 'node_modules/events'),
      crypto: path.resolve(__dirname, 'node_modules/crypto-browserify'),
    },
  },
  optimizeDeps: {
    esbuildOptions: {
      // Node.js global to browser globalThis
      define: {
        global: 'globalThis',
      },
    },
    include: [
      'buffer',
      'process',
      'process/browser',
      'stream-browserify',
      'events',
      'util',
      'crypto-browserify',
      '@orbisclub/orbis-sdk'
    ],
  },
  define: {
    // This replaces process.env with a static object during build time
    'process.env': {
      NODE_ENV: JSON.stringify(mode),
      DEBUG: JSON.stringify(process.env.VITE_DEBUG || ''),
      CERAMIC_API_URL: JSON.stringify('https://ceramic-clay.3boxlabs.com'),
      DID_PRIVATE_KEY: JSON.stringify(process.env.VITE_DID_PRIVATE_KEY || ''),
      BROWSER: JSON.stringify('true'),
      // Add any other environment variables needed by Ceramic
    },
    // Add global process object
    'process': {
      env: {
        NODE_ENV: JSON.stringify(mode),
        DEBUG: JSON.stringify(process.env.VITE_DEBUG || ''),
        CERAMIC_API_URL: JSON.stringify('https://ceramic-clay.3boxlabs.com'),
        DID_PRIVATE_KEY: JSON.stringify(process.env.VITE_DID_PRIVATE_KEY || ''),
        BROWSER: JSON.stringify('true'),
      },
      browser: true,
      version: JSON.stringify('16.0.0'),
      platform: JSON.stringify('browser')
    }
  },
}));
