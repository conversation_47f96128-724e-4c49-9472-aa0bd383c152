/**
 * Thirdweb Storage Service
 * This service handles uploading files to IPFS via Thirdweb Storage
 */

import { ThirdwebStorage } from "@thirdweb-dev/storage";
import { THIRDWEB_CLIENT_ID } from '@/utils/env';

// Initialize the Thirdweb Storage SDK
const storage = new ThirdwebStorage({
  // You can provide a clientId from the Thirdweb dashboard for better rate limits
  clientId: THIRDWEB_CLIENT_ID || "",
  // Add required headers
  clientOptions: {
    clientId: THIRDWEB_CLIENT_ID || "",
    secretKey: "",
    headers: {
      "x-client-id": THIRDWEB_CLIENT_ID || "",
    }
  }
});

/**
 * Upload a file to IPFS via Thirdweb Storage
 * @param file The file to upload
 * @returns IPFS URI in the format ipfs://{CID}
 */
export async function uploadToIPFS(file: File): Promise<string> {
  try {
    console.log(`Uploading file to IPFS via Thirdweb: ${file.name} (${file.size} bytes, type: ${file.type})`);

    // Upload the file to IPFS
    console.log('Starting upload to Thirdweb Storage...');
    const uri = await storage.upload(file);

    console.log(`Successfully uploaded to IPFS with URI: ${uri}`);

    return uri;
  } catch (error) {
    console.error('Error uploading to IPFS via Thirdweb Storage:', error);
    throw error;
  }
}

/**
 * Upload a blob to IPFS via Thirdweb Storage
 * @param blob The blob to upload
 * @param fileName The name to give the file
 * @returns IPFS URI in the format ipfs://{CID}
 */
export async function uploadBlobToIPFS(blob: Blob, fileName: string): Promise<string> {
  try {
    console.log(`Converting blob to File object: ${fileName} (${blob.size} bytes, type: ${blob.type})`);

    // Validate inputs
    if (!blob) {
      throw new Error('No blob provided for upload');
    }

    if (!fileName) {
      fileName = `file_${Date.now()}`;
      console.warn(`No filename provided, using generated name: ${fileName}`);
    }

    // Convert blob to File object
    const file = new File([blob], fileName, { type: blob.type });

    // Upload to IPFS
    console.log(`Uploading blob as file: ${fileName}`);
    return await uploadToIPFS(file);
  } catch (error) {
    console.error('Error uploading blob to IPFS via Thirdweb Storage:', error);

    // Add fallback behavior
    console.warn('Falling back to blob URL for storage');
    const blobUrl = URL.createObjectURL(blob);
    console.warn(`Created temporary blob URL: ${blobUrl}`);
    console.warn('Note: This URL will not persist across page refreshes');

    throw error;
  }
}

/**
 * Upload multiple files to IPFS via Thirdweb Storage
 * @param files Array of files to upload
 * @returns Array of IPFS URIs
 */
export async function uploadBatchToIPFS(files: File[]): Promise<string[]> {
  try {
    console.log(`Uploading batch of ${files.length} files to IPFS via Thirdweb`);

    // Upload each file individually
    const uris = await Promise.all(files.map(file => uploadToIPFS(file)));

    console.log(`Successfully uploaded ${uris.length} files to IPFS`);
    return uris;
  } catch (error) {
    console.error('Error uploading batch to IPFS via Thirdweb Storage:', error);
    throw error;
  }
}

/**
 * Download data from an IPFS URI
 * @param uri The IPFS URI to download from
 * @returns The downloaded data
 */
export async function downloadFromIPFS(uri: string): Promise<Blob> {
  try {
    console.log(`Downloading from IPFS URI: ${uri}`);

    // Download the data
    const data = await storage.download(uri);

    console.log(`Successfully downloaded data from IPFS`);
    return data;
  } catch (error) {
    console.error('Error downloading from IPFS via Thirdweb Storage:', error);
    throw error;
  }
}

/**
 * Get a gateway URL for an IPFS URI
 * @param uri The IPFS URI
 * @returns HTTP URL for accessing the content
 */
export function getGatewayUrl(uri: string): string {
  return storage.resolveScheme(uri);
}

/**
 * Check if a string is an IPFS URI
 * @param uri The URI to check
 * @returns True if the URI is an IPFS URI
 */
export function isIPFSUri(uri: string): boolean {
  return uri.startsWith('ipfs://');
}

/**
 * Convert an IPFS URI to a gateway URL if needed
 * @param uri The URI to convert
 * @returns HTTP URL for accessing the content
 */
export function resolveIPFSUri(uri: string): string {
  if (isIPFSUri(uri)) {
    return getGatewayUrl(uri);
  }
  return uri;
}
