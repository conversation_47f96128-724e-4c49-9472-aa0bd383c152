
-- Drop existing voice_reactions table if it exists
DROP TABLE IF EXISTS public.voice_reactions;

-- Create voice_reactions table with correct data types
CREATE TABLE public.voice_reactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  voice_message_id TEXT NOT NULL, -- Changed from UUID to TEXT to match voice_messages.id
  profile_id TEXT NOT NULL,
  emoji TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Enable RLS on voice_reactions table
ALTER TABLE public.voice_reactions ENABLE ROW LEVEL SECURITY;

-- Create policies for voice_reactions table
CREATE POLICY "Users can view all voice reactions"
ON public.voice_reactions
FOR SELECT
USING (true);

CREATE POLICY "Users can insert their own voice reactions"
ON public.voice_reactions
FOR INSERT
WITH CHECK (profile_id = auth.uid());

CREATE POLICY "Users can delete their own voice reactions"
ON public.voice_reactions
FOR DELETE
USING (profile_id = auth.uid());

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS voice_reactions_voice_message_id_idx ON public.voice_reactions(voice_message_id);
CREATE INDEX IF NOT EXISTS voice_reactions_profile_id_idx ON public.voice_reactions(profile_id);
CREATE INDEX IF NOT EXISTS voice_reactions_emoji_idx ON public.voice_reactions(emoji);
