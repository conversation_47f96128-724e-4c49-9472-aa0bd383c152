import { ethers } from 'ethers';
import { WalletData } from './walletService';
import * as web3 from '@solana/web3.js';
import { HELIUS_API_KEY } from '@/utils/env';

// Constants
const WALLET_STORAGE_PREFIX = 'audra_wallet_';
const DEFAULT_NETWORK = 'base';
const DEFAULT_SOLANA_NETWORK = 'mainnet-beta';

// RPC configuration with fallbacks and retry logic
const RPC_CONFIG = {
  base: {
    urls: [
      'https://mainnet.base.org',
      'https://base-mainnet.public.blastapi.io',
      'https://1rpc.io/base'
    ],
    currentIndex: 0,
    maxRetries: 3,
    retryDelay: 1000, // Start with 1 second delay
  },
  'base-goerli': {
    urls: [
      'https://goerli.base.org',
      'https://base-goerli.public.blastapi.io'
    ],
    currentIndex: 0,
    maxRetries: 3,
    retryDelay: 1000,
  }
};

// Solana RPC configuration with Helius API - ensure we're using the correct key format
const SOLANA_RPC_CONFIG = {
  'mainnet-beta': {
    urls: [
      // Primary Helius API endpoint
      `https://mainnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}`,
      // Fallbacks if needed
      `https://api.mainnet.solana.com`,
      `https://solana-mainnet.g.alchemy.com/v2/demo`,
      `https://solana-api.projectserum.com`
    ],
    currentIndex: 0,
    maxRetries: 3,
    retryDelay: 1000,
  },
  'devnet': {
    urls: [
      `https://devnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}`,
      `https://api.devnet.solana.com`
    ],
    currentIndex: 0,
    maxRetries: 3,
    retryDelay: 1000,
  }
};

// Store active connection objects
let solanaConnections = new Map<string, web3.Connection>();

/**
 * WalletManager - A centralized system for managing wallet addresses
 * 
 * This ensures that the same user always gets the same wallet address
 * regardless of device or browser session.
 * Now supporting both Ethereum (Base) and Solana networks.
 */
class WalletManager {
  private static instance: WalletManager;
  private walletCache: Map<string, WalletData> = new Map();
  private providers: Map<string, ethers.providers.JsonRpcProvider> = new Map();

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  /**
   * Get the singleton instance of WalletManager
   */
  public static getInstance(): WalletManager {
    if (!WalletManager.instance) {
      WalletManager.instance = new WalletManager();
    }
    return WalletManager.instance;
  }

  /**
   * Get a JSON RPC provider with retry and fallback logic
   */
  private getProvider(network: 'base' | 'base-goerli' = DEFAULT_NETWORK): ethers.providers.JsonRpcProvider {
    const cacheKey = `provider-${network}`;
    
    if (this.providers.has(cacheKey)) {
      return this.providers.get(cacheKey)!;
    }
    
    // Create a new provider with the first URL
    const config = RPC_CONFIG[network];
    const url = config.urls[config.currentIndex];
    const provider = new ethers.providers.JsonRpcProvider(url);
    
    // Add custom retry logic for 429 errors
    const originalFetch = provider.fetch.bind(provider);
    provider.fetch = async (url: string, json: string) => {
      let retries = 0;
      let lastError;
      
      while (retries <= config.maxRetries) {
        try {
          return await originalFetch(url, json);
        } catch (error: any) {
          lastError = error;
          
          // Check if it's a 429 error
          if (error?.status === 429 || error?.statusCode === 429 || 
              (typeof error?.message === 'string' && 
               error.message.includes('429'))) {
            
            console.log(`RPC rate limited (429). Retry ${retries + 1}/${config.maxRetries + 1}`);
            
            // Try next URL if available
            if (retries < config.urls.length - 1) {
              config.currentIndex = (config.currentIndex + 1) % config.urls.length;
              const newUrl = config.urls[config.currentIndex];
              console.log(`Switching to alternative RPC: ${newUrl}`);
              
              // Reset the provider with the new URL
              const newProvider = new ethers.providers.JsonRpcProvider(newUrl);
              this.providers.set(cacheKey, newProvider);
              return newProvider.fetch(url, json);
            }
            
            // Otherwise, implement exponential backoff
            const delay = config.retryDelay * Math.pow(2, retries);
            console.log(`Waiting ${delay}ms before retry...`);
            await new Promise(resolve => setTimeout(resolve, delay));
          } else {
            // For other errors, just throw
            throw error;
          }
          
          retries++;
        }
      }
      
      // If we've exhausted all retries
      throw lastError;
    };
    
    this.providers.set(cacheKey, provider);
    return provider;
  }

  /**
   * Get a Solana connection with retry and fallback logic
   * Now using Helius API by default
   */
  public getSolanaConnection(network: string = DEFAULT_SOLANA_NETWORK): web3.Connection {
    const cacheKey = `solana-${network}`;
    
    if (solanaConnections.has(cacheKey)) {
      return solanaConnections.get(cacheKey)!;
    }
    
    // Get the configuration for this network
    const config = SOLANA_RPC_CONFIG[network as keyof typeof SOLANA_RPC_CONFIG] || SOLANA_RPC_CONFIG['mainnet-beta'];
    const url = config.urls[config.currentIndex];

    console.log(`Creating new Solana connection with RPC: ${url}`);
    
    // Create a connection with appropriate options
    const connection = new web3.Connection(url, {
      commitment: 'confirmed',
      disableRetryOnRateLimit: true,
      confirmTransactionInitialTimeout: 60000
    });
    
    // Add custom error handler to the connection to detect API key issues
    const originalFetch = connection.rpcRequest;
    connection.rpcRequest = async function(method, params) {
      try {
        return await originalFetch.call(connection, method, params);
      } catch (error: any) {
        // Check if the error is due to API key issues (401 or 403)
        if (error?.message?.includes('401') || error?.message?.includes('403') || 
            error?.message?.includes('api key not found')) {
          console.warn(`Solana RPC error with URL ${url}: API key issue detected`);
          // Switch to next URL
          config.currentIndex = (config.currentIndex + 1) % config.urls.length;
          const newUrl = config.urls[config.currentIndex];
          console.log(`Switching to alternative Solana RPC: ${newUrl}`);
          
          // Create a new connection with the new URL
          const newConnection = new web3.Connection(newUrl, {
            commitment: 'confirmed',
            disableRetryOnRateLimit: true,
            confirmTransactionInitialTimeout: 60000
          });
          
          // Update cache
          solanaConnections.set(cacheKey, newConnection);
          
          // Retry the request with the new connection
          return newConnection.rpcRequest(method, params);
        }
        throw error;
      }
    };
    
    // Store in cache
    solanaConnections.set(cacheKey, connection);
    return connection;
  }

  /**
   * Switch to the next Solana RPC URL if the current one is failing
   */
  public switchSolanaRpcUrl(network: string = DEFAULT_SOLANA_NETWORK): web3.Connection {
    const config = SOLANA_RPC_CONFIG[network as keyof typeof SOLANA_RPC_CONFIG] || SOLANA_RPC_CONFIG['mainnet-beta'];
    const cacheKey = `solana-${network}`;
    
    // Move to the next URL in the list
    config.currentIndex = (config.currentIndex + 1) % config.urls.length;
    const newUrl = config.urls[config.currentIndex];
    console.log(`Switching to alternative Solana RPC: ${newUrl}`);
    
    // Create a new connection
    const connection = new web3.Connection(newUrl, {
      commitment: 'confirmed',
      disableRetryOnRateLimit: true,
      confirmTransactionInitialTimeout: 60000
    });
    
    // Update cache
    solanaConnections.set(cacheKey, connection);
    
    return connection;
  }

  /**
   * Get a wallet for a user ID
   * This will always return the same wallet for the same user ID
   * For the owner, it will return the verified owner wallet
   */
  public getWallet(userId: string): WalletData {
    // Check if this wallet is in our cache first
    if (this.walletCache.has(userId)) {
      return this.walletCache.get(userId)!;
    }

    // Check if this wallet is in local storage
    const storedWallet = this.getStoredWallet(userId);
    if (storedWallet) {
      this.walletCache.set(userId, storedWallet);
      return storedWallet;
    }

    // Special case for the owner - ONLY match exact owner ID
    const OWNER_ID = '2cd24c86-d3c5-4406-a92c-1f0892495e0a';
    const OWNER_WALLET_ADDRESS = '******************************************';

    // Check if this is the owner's account by user ID (exact match only)
    if (userId === OWNER_ID) {
      console.log('Owner account detected by exact user ID');

      // Create a wallet with the owner's address
      const ownerWallet: WalletData = {
        address: OWNER_WALLET_ADDRESS,
        privateKey: '', // We don't have the private key for security reasons
        balance: '0.0',
        network: DEFAULT_NETWORK as 'base' | 'base-goerli',
        hasBackup: true,
        tokenBalances: {
          USDC: '0.0'
        },
        // Add Solana wallet info for owner
        solana: {
          address: '', // Owner's Solana address would be added here
          network: DEFAULT_SOLANA_NETWORK,
          balance: '0.0',
          tokenBalances: {
            USDC: '0.0'
          }
        }
      };

      // Store it for future use
      this.storeWallet(userId, ownerWallet);
      this.walletCache.set(userId, ownerWallet);

      return ownerWallet;
    }

    // For non-owner users, create a deterministic wallet based on user ID
    // This ensures the same wallet address across all devices for the same user
    const wallet = this.createDeterministicWallet(userId);

    // Store it for future use
    this.storeWallet(userId, wallet);
    this.walletCache.set(userId, wallet);

    console.log(`Deterministic wallets created for user ${userId}: ${wallet.address} (ETH) and ${wallet.solana?.address} (SOL)`);

    return wallet;
  }

  /**
   * Create a deterministic wallet from a user ID
   * This ensures the same user always gets the same wallet
   * Now creates both Ethereum and Solana wallets
   */
  private createDeterministicWallet(userId: string): WalletData {
    try {
      // Create a deterministic private key from the user ID for Ethereum
      const privateKey = this.generateDeterministicPrivateKey(userId);
      const ethWallet = new ethers.Wallet(privateKey);
      
      // Generate a Solana keypair from the same user ID
      const solanaKeypair = this.generateSolanaKeypair(userId);
      const solanaAddress = solanaKeypair.publicKey.toString();

      // Convert the secret key to a storable string format
      const secretKeyArray = Array.from(solanaKeypair.secretKey);
      
      return {
        address: ethWallet.address,
        privateKey: ethWallet.privateKey,
        balance: '0.0',
        network: DEFAULT_NETWORK as 'base' | 'base-goerli',
        hasBackup: false,
        tokenBalances: {
          USDC: '0.0'
        },
        // Add Solana wallet data
        solana: {
          address: solanaAddress,
          secretKey: secretKeyArray.toString(),
          publicKey: solanaAddress,
          network: DEFAULT_SOLANA_NETWORK,
          balance: '0.0',
          tokenBalances: {
            USDC: '0.0'
          }
        }
      };
    } catch (error) {
      console.error('Error creating deterministic wallet:', error);
      throw new Error('Failed to create wallet');
    }
  }

  /**
   * Generate a deterministic private key from a user ID for Ethereum
   */
  private generateDeterministicPrivateKey(userId: string): string {
    // We need to ensure the private key is valid for Ethereum
    // It must be 32 bytes (64 hex characters) without 0x prefix

    // First, create a hash of the user ID
    const hash = ethers.utils.id(userId); // keccak256 hash

    // The hash is already 32 bytes, so we can use it directly
    return hash;
  }

  /**
   * Generate a deterministic Solana keypair from a user ID
   */
  private generateSolanaKeypair(userId: string): web3.Keypair {
    try {
      // Generate a seed from the user ID
      const hash = ethers.utils.id(userId); // keccak256 hash
      
      // Create a Uint8Array from the hash (removing 0x prefix)
      const seedHex = hash.slice(2); // Remove '0x' prefix
      
      // Convert hex to byte array manually without using Buffer
      const seedBytes = new Uint8Array(32);
      for (let i = 0; i < 32; i++) {
        const byteHex = seedHex.slice(i * 2, i * 2 + 2);
        seedBytes[i] = parseInt(byteHex, 16);
      }
      
      // Create a keypair from the seed
      return web3.Keypair.fromSeed(seedBytes);
    } catch (error) {
      console.error('Error generating Solana keypair:', error);
      throw new Error('Failed to generate Solana keypair');
    }
  }

  /**
   * Store a wallet in local storage
   */
  private storeWallet(userId: string, wallet: WalletData): void {
    try {
      localStorage.setItem(
        `${WALLET_STORAGE_PREFIX}${userId}`,
        JSON.stringify(wallet)
      );
    } catch (error) {
      console.error('Error storing wallet:', error);
    }
  }

  /**
   * Get a stored wallet from local storage
   */
  private getStoredWallet(userId: string): WalletData | null {
    try {
      const storedWallet = localStorage.getItem(`${WALLET_STORAGE_PREFIX}${userId}`);
      if (storedWallet) {
        return JSON.parse(storedWallet);
      }
      return null;
    } catch (error) {
      console.error('Error getting stored wallet:', error);
      return null;
    }
  }

  /**
   * Clear all stored wallets (for testing purposes)
   */
  public clearAllWallets(): void {
    this.walletCache.clear();
    solanaConnections.clear();

    // Clear all wallet items from local storage
    Object.keys(localStorage)
      .filter(key => key.startsWith(WALLET_STORAGE_PREFIX))
      .forEach(key => localStorage.removeItem(key));
  }

  /**
   * Update a wallet's data in cache and storage
   */
  public updateWallet(userId: string, walletData: Partial<WalletData>): WalletData {
    // Get the current wallet
    let wallet = this.getWallet(userId);
    
    // Update the wallet data
    wallet = { ...wallet, ...walletData };
    
    // Store updated wallet
    this.storeWallet(userId, wallet);
    this.walletCache.set(userId, wallet);
    
    return wallet;
  }

  /**
   * Update Solana wallet data specifically
   */
  public updateSolanaWallet(userId: string, solanaData: Partial<WalletData['solana']>): WalletData {
    // Get the current wallet
    let wallet = this.getWallet(userId);
    
    // Update the Solana wallet data
    if (wallet.solana) {
      wallet.solana = { ...wallet.solana, ...solanaData };
    } else {
      wallet.solana = solanaData as WalletData['solana'];
    }
    
    // Store updated wallet
    this.storeWallet(userId, wallet);
    this.walletCache.set(userId, wallet);
    
    return wallet;
  }
}

// Export the singleton instance
export const walletManager = WalletManager.getInstance();

export default walletManager;
