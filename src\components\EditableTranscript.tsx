import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Edit, Check, X } from 'lucide-react';
import { toast } from '@/components/ui/sonner';

interface EditableTranscriptProps {
  transcript: string;
  onSave: (newTranscript: string) => void;
  className?: string;
  isEditing?: boolean;
  setIsEditing?: React.Dispatch<React.SetStateAction<boolean>>;
  isOwner?: boolean;
  isTruncated?: boolean;
  onExpand?: () => void;
}

/**
 * EditableTranscript component
 * Allows users to view and edit transcriptions
 */
const EditableTranscript: React.FC<EditableTranscriptProps> = ({
  transcript,
  onSave,
  className = '',
  isEditing: externalIsEditing,
  setIsEditing: externalSetIsEditing,
  isOwner = true,
  isTruncated = false,
  onExpand
}) => {
  // Use external state if provided, otherwise use internal state
  const [internalIsEditing, setInternalIsEditing] = useState(false);
  const isEditing = externalIsEditing !== undefined ? externalIsEditing : internalIsEditing;
  const setIsEditing = externalSetIsEditing || setInternalIsEditing;

  const [editedTranscript, setEditedTranscript] = useState(transcript);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Update local state when transcript prop changes
  useEffect(() => {
    setEditedTranscript(transcript);
  }, [transcript]);

  // Focus the textarea when editing starts
  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus();
      // Place cursor at the end
      const length = textareaRef.current.value.length;
      textareaRef.current.setSelectionRange(length, length);
    }
  }, [isEditing]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    setEditedTranscript(transcript);
    setIsEditing(false);
  };

  const handleSave = () => {
    if (editedTranscript.trim() === '') {
      toast.error('Transcript cannot be empty');
      return;
    }

    onSave(editedTranscript);
    setIsEditing(false);
    toast.success('Transcript updated');
  };

  // Auto-resize textarea based on content
  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditedTranscript(e.target.value);

    // Auto-resize
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Save on Ctrl+Enter or Cmd+Enter
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      e.preventDefault();
      handleSave();
    }

    // Cancel on Escape
    if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  return (
    <div className={`relative ${className}`}>
      {isEditing ? (
        <div className="space-y-2">
          <Textarea
            ref={textareaRef}
            value={editedTranscript}
            onChange={handleTextareaChange}
            onKeyDown={handleKeyDown}
            className="min-h-[80px] resize-none text-sm"
            placeholder="Edit transcript..."
          />
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
              className="h-8 px-2 text-xs"
            >
              <X className="mr-1 h-3 w-3" />
              Cancel
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={handleSave}
              className="h-8 px-2 text-xs bg-voicechain-purple hover:bg-voicechain-accent"
            >
              <Check className="mr-1 h-3 w-3" />
              Save
            </Button>
          </div>
        </div>
      ) : (
        <div
          className="group relative p-2 border border-dashed border-transparent hover:border-voicechain-purple/30 rounded-md cursor-pointer transition-all"
          onClick={(e) => {
            e.stopPropagation();
            if (isOwner) handleEdit();
          }}
          data-no-navigate="true"
        >
          <p className="text-sm text-muted-foreground whitespace-pre-wrap break-words">
            {isTruncated ? (
              <>
                {transcript.substring(0, 150)}...
                {onExpand && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onExpand();
                    }}
                    className="ml-1 text-voicechain-purple hover:underline text-xs font-medium"
                    data-no-navigate="true"
                  >
                    View more
                  </button>
                )}
              </>
            ) : (
              <>
                {transcript || 'No transcript available'}
                {transcript && transcript.length > 150 && onExpand && (
                  <div className="mt-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onExpand();
                      }}
                      className="text-voicechain-purple hover:underline text-xs font-medium"
                      data-no-navigate="true"
                    >
                      View less
                    </button>
                  </div>
                )}
              </>
            )}
          </p>
          {isOwner && (
            <div className="absolute top-1 right-1 flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity bg-background/80 px-1 py-0.5 rounded text-xs text-voicechain-purple">
              <Edit className="h-3 w-3" />
              <span>Edit</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default EditableTranscript;
