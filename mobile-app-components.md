# Key Components for Audra Mobile App

## Audio Recording Component

Create `src/components/AudioRecorder.tsx`:

```typescript
import React, { useState, useEffect } from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  Platform,
  PermissionsAndroid,
} from 'react-native';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import RNFS from 'react-native-fs';
import { check, request, PERMISSIONS, RESULTS } from 'react-native-permissions';

const audioRecorderPlayer = new AudioRecorderPlayer();

interface AudioRecorderProps {
  onRecordingComplete: (uri: string, duration: number) => void;
}

const AudioRecorder: React.FC<AudioRecorderProps> = ({ onRecordingComplete }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [recordTime, setRecordTime] = useState('00:00');
  const [recordedUri, setRecordedUri] = useState<string | null>(null);
  const [recordedDuration, setRecordedDuration] = useState(0);

  useEffect(() => {
    // Request permissions when component mounts
    requestPermissions();

    // Clean up when component unmounts
    return () => {
      if (isRecording) {
        audioRecorderPlayer.stopRecorder();
      }
      if (isPlaying) {
        audioRecorderPlayer.stopPlayer();
      }
    };
  }, []);

  const requestPermissions = async () => {
    if (Platform.OS === 'android') {
      try {
        const grants = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        ]);

        if (
          grants['android.permission.WRITE_EXTERNAL_STORAGE'] === PermissionsAndroid.RESULTS.GRANTED &&
          grants['android.permission.READ_EXTERNAL_STORAGE'] === PermissionsAndroid.RESULTS.GRANTED &&
          grants['android.permission.RECORD_AUDIO'] === PermissionsAndroid.RESULTS.GRANTED
        ) {
          console.log('All permissions granted');
        } else {
          console.log('Some permissions denied');
        }
      } catch (err) {
        console.warn(err);
      }
    } else if (Platform.OS === 'ios') {
      const microphoneStatus = await check(PERMISSIONS.IOS.MICROPHONE);
      
      if (microphoneStatus !== RESULTS.GRANTED) {
        const result = await request(PERMISSIONS.IOS.MICROPHONE);
        if (result !== RESULTS.GRANTED) {
          console.log('Microphone permission denied');
        }
      }
    }
  };

  const onStartRecord = async () => {
    const path = Platform.OS === 'ios' 
      ? `${RNFS.DocumentDirectoryPath}/recording.m4a`
      : `${RNFS.ExternalDirectoryPath}/recording.mp3`;
    
    try {
      await audioRecorderPlayer.startRecorder(path);
      audioRecorderPlayer.addRecordBackListener((e) => {
        setRecordTime(audioRecorderPlayer.mmssss(Math.floor(e.currentPosition)));
        setRecordedDuration(e.currentPosition);
      });
      setIsRecording(true);
    } catch (error) {
      console.error('Failed to start recording', error);
    }
  };

  const onStopRecord = async () => {
    try {
      const result = await audioRecorderPlayer.stopRecorder();
      audioRecorderPlayer.removeRecordBackListener();
      setIsRecording(false);
      setRecordedUri(result);
    } catch (error) {
      console.error('Failed to stop recording', error);
    }
  };

  const onStartPlay = async () => {
    if (!recordedUri) return;
    
    try {
      await audioRecorderPlayer.startPlayer(recordedUri);
      audioRecorderPlayer.addPlayBackListener((e) => {
        if (e.currentPosition === e.duration) {
          audioRecorderPlayer.stopPlayer();
          setIsPlaying(false);
        }
      });
      setIsPlaying(true);
    } catch (error) {
      console.error('Failed to play recording', error);
    }
  };

  const onStopPlay = async () => {
    try {
      await audioRecorderPlayer.stopPlayer();
      audioRecorderPlayer.removePlayBackListener();
      setIsPlaying(false);
    } catch (error) {
      console.error('Failed to stop playing', error);
    }
  };

  const handleSubmit = () => {
    if (recordedUri && recordedDuration) {
      onRecordingComplete(recordedUri, recordedDuration);
      // Reset state after submission
      setRecordedUri(null);
      setRecordTime('00:00');
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.recordingInfo}>
        <Text style={styles.recordingTime}>{recordTime}</Text>
      </View>
      
      <View style={styles.controls}>
        {!isRecording && !recordedUri && (
          <TouchableOpacity 
            style={[styles.button, styles.recordButton]} 
            onPress={onStartRecord}
          >
            <Text style={styles.buttonText}>Record</Text>
          </TouchableOpacity>
        )}
        
        {isRecording && (
          <TouchableOpacity 
            style={[styles.button, styles.stopButton]} 
            onPress={onStopRecord}
          >
            <Text style={styles.buttonText}>Stop</Text>
          </TouchableOpacity>
        )}
        
        {recordedUri && !isPlaying && (
          <TouchableOpacity 
            style={[styles.button, styles.playButton]} 
            onPress={onStartPlay}
          >
            <Text style={styles.buttonText}>Play</Text>
          </TouchableOpacity>
        )}
        
        {isPlaying && (
          <TouchableOpacity 
            style={[styles.button, styles.stopButton]} 
            onPress={onStopPlay}
          >
            <Text style={styles.buttonText}>Stop</Text>
          </TouchableOpacity>
        )}
        
        {recordedUri && (
          <TouchableOpacity 
            style={[styles.button, styles.submitButton]} 
            onPress={handleSubmit}
          >
            <Text style={styles.buttonText}>Submit</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    marginVertical: 8,
  },
  recordingInfo: {
    alignItems: 'center',
    marginBottom: 16,
  },
  recordingTime: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  recordButton: {
    backgroundColor: '#FF4136',
  },
  stopButton: {
    backgroundColor: '#0074D9',
  },
  playButton: {
    backgroundColor: '#2ECC40',
  },
  submitButton: {
    backgroundColor: '#7928CA',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
  },
});

export default AudioRecorder;
```
