-- Create the missing get_accessible_journals function
CREATE OR R<PERSON><PERSON>CE FUNCTION public.get_accessible_journals(user_id_param text)
RETURNS TABLE(
  id uuid,
  profile_id text,
  title text,
  transcript text,
  audio_url text,
  audio_duration integer,
  is_locked boolean,
  is_published boolean,
  scheduled_for timestamp with time zone,
  created_at timestamp with time zone,
  updated_at timestamp with time zone,
  unlock_condition jsonb
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    j.id,
    j.profile_id,
    j.title,
    j.transcript,
    j.audio_url,
    j.audio_duration,
    j.is_locked,
    j.is_published,
    j.scheduled_for,
    j.created_at,
    j.updated_at,
    j.unlock_condition
  FROM 
    public.journals j
  WHERE 
    (j.profile_id = user_id_param OR j.is_published = true)
    AND (j.scheduled_for IS NULL OR j.scheduled_for <= NOW())
  ORDER BY 
    j.created_at DESC;
END;
$$;