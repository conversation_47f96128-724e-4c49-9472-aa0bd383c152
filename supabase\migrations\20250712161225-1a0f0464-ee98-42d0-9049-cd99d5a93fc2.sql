-- Ensure the profiles storage bucket exists and has proper policies
DO $$ 
BEGIN 
  -- Insert the bucket if it doesn't exist
  INSERT INTO storage.buckets (id, name, public, avif_autodetection, file_size_limit, allowed_mime_types)
  VALUES ('profiles', 'profiles', true, false, 5242880, '{"image/png","image/jpeg","image/gif","image/webp"}')
  ON CONFLICT (id) DO NOTHING;
END $$;

-- Create policies for the profiles bucket if they don't exist
DO $$
BEGIN
  -- Policy for viewing profile images (public)
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'storage' 
    AND tablename = 'objects' 
    AND policyname = 'Public profile images are viewable'
  ) THEN
    CREATE POLICY "Public profile images are viewable"
    ON storage.objects FOR SELECT
    USING (bucket_id = 'profiles');
  END IF;

  -- Policy for uploading profile images (authenticated users only)
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'storage' 
    AND tablename = 'objects' 
    AND policyname = 'Users can upload their own profile images'
  ) THEN
    CREATE POLICY "Users can upload their own profile images"
    ON storage.objects FOR INSERT
    WITH CHECK (bucket_id = 'profiles' AND auth.uid() IS NOT NULL);
  END IF;

  -- Policy for updating profile images
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'storage' 
    AND tablename = 'objects' 
    AND policyname = 'Users can update their own profile images'
  ) THEN
    CREATE POLICY "Users can update their own profile images"
    ON storage.objects FOR UPDATE
    USING (bucket_id = 'profiles' AND auth.uid() IS NOT NULL);
  END IF;
END $$;