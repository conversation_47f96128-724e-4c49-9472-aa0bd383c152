-- Create missing database functions for journals and profiles

-- Function to get accessible journals for a user
CREATE OR REPLACE FUNCTION public.get_accessible_journals(user_id_param text)
RETURNS TABLE(
  id uuid,
  profile_id text,
  title text,
  transcript text,
  audio_url text,
  audio_duration integer,
  is_locked boolean,
  is_published boolean,
  scheduled_for timestamp with time zone,
  created_at timestamp with time zone,
  updated_at timestamp with time zone,
  unlock_condition jsonb
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    j.id,
    j.profile_id,
    j.title,
    j.transcript,
    j.audio_url,
    j.audio_duration,
    j.is_locked,
    j.is_published,
    j.scheduled_for,
    j.created_at,
    j.updated_at,
    j.unlock_condition
  FROM 
    public.journals j
  WHERE 
    j.profile_id = user_id_param
    AND j.is_published = true
  ORDER BY 
    j.created_at DESC;
END;
$$;

-- Function to get or create a profile
CREATE OR REPLACE FUNCTION public.get_or_create_profile(p_wallet_address text)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_profile_id UUID;
  v_result JSONB;
  v_wallet_address TEXT;
  v_is_uuid BOOLEAN;
BEGIN
  -- Normalize the address
  v_wallet_address := LOWER(p_wallet_address);

  -- Check if the input looks like a UUID
  BEGIN
    v_profile_id := v_wallet_address::UUID;
    v_is_uuid := TRUE;
  EXCEPTION WHEN others THEN
    v_is_uuid := FALSE;
  END;

  -- Check if profile exists by wallet address or ID
  IF v_is_uuid THEN
    -- If it might be a UUID, check both id and wallet_address
    SELECT to_jsonb(profiles.*) INTO v_result
    FROM profiles
    WHERE id = v_wallet_address::UUID OR LOWER(wallet_address) = v_wallet_address
    LIMIT 1;
  ELSE
    -- If definitely not a UUID, just check wallet_address
    SELECT to_jsonb(profiles.*) INTO v_result
    FROM profiles
    WHERE LOWER(wallet_address) = v_wallet_address
    LIMIT 1;
  END IF;

  -- If profile exists, return it
  IF v_result IS NOT NULL THEN
    RETURN v_result;
  END IF;

  -- If profile doesn't exist, create it
  v_profile_id := gen_random_uuid();

  INSERT INTO profiles (
    id,
    wallet_address,
    display_name,
    username,
    created_at,
    updated_at
  ) VALUES (
    v_profile_id,
    v_wallet_address,
    'User ' || substring(v_wallet_address, 1, 6),
    'user_' || substring(v_wallet_address, 1, 8),
    NOW(),
    NOW()
  )
  RETURNING to_jsonb(profiles.*) INTO v_result;

  -- Ensure we have profile_stats record for this user
  INSERT INTO profile_stats (profile_id)
  VALUES (v_profile_id)
  ON CONFLICT (profile_id) DO NOTHING;

  RETURN v_result;
END;
$$;