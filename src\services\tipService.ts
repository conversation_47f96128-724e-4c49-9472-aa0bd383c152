import { supabase } from '@/integrations/supabase/client';
import { v4 as uuidv4 } from 'uuid';

/**
 * Send a tip to a user
 */
export async function sendTip(
  fromUserId: string,
  toUserId: string,
  messageId: string | null,
  amount: string,
  currency: string = 'SOL'
): Promise<{ success: boolean; tipId?: string }> {
  try {
    // Record the tip transaction
    // This would typically involve blockchain interaction
    // For now, we'll just create a record in the database
    
    const tipId = uuidv4();
    const { error: tipError } = await supabase
      .from('tips')
      .insert({
        id: tipId,
        from_user_id: fromUserId,
        to_user_id: toUserId,
        message_id: messageId,
        amount,
        currency,
        created_at: new Date().toISOString()
      });
      
    if (tipError) {
      console.error('Error recording tip:', tipError);
      return { success: false };
    }
    
    // Create notification for the recipient
    const { error: notificationError } = await supabase
      .from('notifications')
      .insert({
        id: uuidv4(),
        type: 'tip',
        from_address: fromUserId,
        to_address: toUserId,
        message_id: messageId,
        data: { tipAmount: amount, currency },
        read: false,
        created_at: new Date().toISOString()
      });
      
    if (notificationError) {
      console.error('Error creating tip notification:', notificationError);
    }
    
    return { success: true, tipId };
  } catch (error) {
    console.error('Error in sendTip:', error);
    return { success: false };
  }
}

/**
 * Send a tip for a journal entry
 */
export async function sendJournalTip(
  fromUserId: string,
  journalId: string,
  journalOwnerId: string,
  amount: string,
  currency: string = 'SOL'
): Promise<{ success: boolean; tipId?: string }> {
  try {
    const tipId = uuidv4();
    
    // Record the journal tip transaction
    const { error: tipError } = await supabase
      .from('journal_tips')
      .insert({
        id: tipId,
        from_user_id: fromUserId,
        to_user_id: journalOwnerId,
        journal_id: journalId,
        amount: parseFloat(amount),
        currency,
        created_at: new Date().toISOString()
      });
      
    if (tipError) {
      console.error('Error recording journal tip:', tipError);
      return { success: false };
    }
    
    // Create notification for the journal owner
    const { error: notificationError } = await supabase
      .from('notifications')
      .insert({
        id: uuidv4(),
        type: 'journal_tip',
        from_address: fromUserId,
        to_address: journalOwnerId,
        data: { 
          tipAmount: amount, 
          currency,
          journalId,
          journalTitle: 'Voice Journal' // You can pass this as a parameter if needed
        },
        read: false,
        created_at: new Date().toISOString()
      });
      
    if (notificationError) {
      console.error('Error creating journal tip notification:', notificationError);
    }
    
    return { success: true, tipId };
  } catch (error) {
    console.error('Error in sendJournalTip:', error);
    return { success: false };
  }
}

/**
 * Get tips for a specific journal
 */
export async function getJournalTips(journalId: string): Promise<any[]> {
  try {
    const { data, error } = await supabase
      .from('journal_tips')
      .select(`
        *,
        from_profile:profiles!journal_tips_from_user_id_fkey(id, display_name, username, avatar_url)
      `)
      .eq('journal_id', journalId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching journal tips:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getJournalTips:', error);
    return [];
  }
}

/**
 * Get total tip amount for a journal
 */
export async function getJournalTipTotal(journalId: string): Promise<{ total: number; tipsByCurrency: Record<string, number> }> {
  try {
    const { data, error } = await supabase
      .from('journal_tips')
      .select('amount, currency')
      .eq('journal_id', journalId);

    if (error) {
      console.error('Error fetching journal tip total:', error);
      return { total: 0, tipsByCurrency: {} };
    }

    // Group by currency and sum amounts
    const tipsByCurrency = (data || []).reduce((acc, tip) => {
      if (!acc[tip.currency]) {
        acc[tip.currency] = 0;
      }
      acc[tip.currency] += Number(tip.amount);
      return acc;
    }, {} as Record<string, number>);

    const total = Object.values(tipsByCurrency).reduce((sum, amount) => sum + amount, 0);

    return { total, tipsByCurrency };
  } catch (error) {
    console.error('Error in getJournalTipTotal:', error);
    return { total: 0, tipsByCurrency: {} };
  }
}