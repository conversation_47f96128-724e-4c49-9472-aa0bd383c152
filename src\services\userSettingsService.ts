
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/sonner';

/**
 * Service for managing user settings stored in Supabase
 */
export default class UserSettingsService {
  /**
   * Save a setting to Supabase
   * @param key The setting key
   * @param value The setting value
   * @param userId The user ID
   */
  static async saveSetting<T>(key: string, value: T, userId: string): Promise<boolean> {
    try {
      if (!userId) {
        console.error('Cannot save setting: No user ID provided');
        return false;
      }

      // Check if setting already exists
      const { data: existingData, error: fetchError } = await supabase
        .from('user_settings')
        .select('id')
        .eq('user_id', userId)
        .eq('key', key)
        .maybeSingle();

      if (fetchError) {
        console.error('Error checking existing setting:', fetchError);
        return false;
      }

      if (existingData) {
        // Update existing setting
        const { error: updateError } = await supabase
          .from('user_settings')
          .update({ value })
          .eq('id', existingData.id);

        if (updateError) {
          console.error('Error updating setting:', updateError);
          return false;
        }
      } else {
        // Insert new setting
        const { error: insertError } = await supabase
          .from('user_settings')
          .insert({
            key,
            value,
            user_id: userId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (insertError) {
          console.error('Error inserting setting:', insertError);
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Error saving setting:', error);
      toast.error('Failed to save setting');
      return false;
    }
  }

  /**
   * Get a setting from Supabase
   * @param key The setting key
   * @param userId The user ID
   * @param defaultValue The default value if setting doesn't exist
   */
  static async getSetting<T>(key: string, userId: string, defaultValue: T): Promise<T> {
    try {
      if (!userId) {
        console.error('Cannot get setting: No user ID provided');
        return defaultValue;
      }

      const { data, error } = await supabase
        .from('user_settings')
        .select('value')
        .eq('user_id', userId)
        .eq('key', key)
        .maybeSingle();

      if (error) {
        console.error('Error getting setting:', error);
        return defaultValue;
      }

      return data ? (data.value as T) : defaultValue;
    } catch (error) {
      console.error('Error getting setting:', error);
      return defaultValue;
    }
  }

  /**
   * Delete a setting from Supabase
   * @param key The setting key
   * @param userId The user ID
   */
  static async deleteSetting(key: string, userId: string): Promise<boolean> {
    try {
      if (!userId) {
        console.error('Cannot delete setting: No user ID provided');
        return false;
      }

      const { error } = await supabase
        .from('user_settings')
        .delete()
        .eq('user_id', userId)
        .eq('key', key);

      if (error) {
        console.error('Error deleting setting:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error deleting setting:', error);
      return false;
    }
  }

  /**
   * Get all settings for a user from Supabase
   * @param userId The user ID
   */
  static async getAllSettings(userId: string): Promise<Record<string, any>> {
    try {
      if (!userId) {
        console.error('Cannot get settings: No user ID provided');
        return {};
      }

      const { data, error } = await supabase
        .from('user_settings')
        .select('key, value')
        .eq('user_id', userId);

      if (error) {
        console.error('Error getting all settings:', error);
        return {};
      }

      // Convert array to object
      return data.reduce((acc, item) => {
        acc[item.key] = item.value;
        return acc;
      }, {} as Record<string, any>);
    } catch (error) {
      console.error('Error getting all settings:', error);
      return {};
    }
  }
}
