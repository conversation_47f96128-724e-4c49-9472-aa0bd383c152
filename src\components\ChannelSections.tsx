import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/lib/supabase';
import { 
  MessageCircle, 
  Radio, 
  Hash, 
  BookOpen, 
  Music, 
  Users,
  Plus,
  Settings,
  Eye,
  EyeOff
} from 'lucide-react';

interface ChannelSection {
  id: string;
  channel_id: string;
  name: string;
  description: string;
  section_type: 'voice_posts' | 'live_streams' | 'topics' | 'voice_journals' | 'drops_releases' | 'members_lounge';
  is_enabled: boolean;
  settings: any;
  created_at: string;
  updated_at: string;
}

interface ChannelSectionsProps {
  channelId: string;
  userAddress: string;
  isOwner: boolean;
  onSectionSelect: (sectionType: string, sectionId: string) => void;
}

const ChannelSections: React.FC<ChannelSectionsProps> = ({
  channelId,
  userAddress,
  isOwner,
  onSectionSelect
}) => {
  const [sections, setSections] = useState<ChannelSection[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedSection, setSelectedSection] = useState<string | null>(null);

  // Section icons and colors
  const sectionConfig = {
    voice_posts: {
      icon: MessageCircle,
      color: 'bg-blue-500',
      gradient: 'from-blue-500 to-blue-600',
      description: 'Share voice messages with the community'
    },
    live_streams: {
      icon: Radio,
      color: 'bg-red-500',
      gradient: 'from-red-500 to-red-600',
      description: 'Host live voice rooms and discussions'
    },
    topics: {
      icon: Hash,
      color: 'bg-green-500',
      gradient: 'from-green-500 to-green-600',
      description: 'Start structured voice-based discussions'
    },
    voice_journals: {
      icon: BookOpen,
      color: 'bg-purple-500',
      gradient: 'from-purple-500 to-purple-600',
      description: 'Personal voice entries and reflections'
    },
    drops_releases: {
      icon: Music,
      color: 'bg-orange-500',
      gradient: 'from-orange-500 to-orange-600',
      description: 'Creator content drops and releases'
    },
    members_lounge: {
      icon: Users,
      color: 'bg-pink-500',
      gradient: 'from-pink-500 to-pink-600',
      description: 'Private voice chat for channel members'
    }
  };

  useEffect(() => {
    loadSections();
  }, [channelId]);

  const loadSections = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('channel_sections')
        .select('*')
        .eq('channel_id', channelId)
        .order('section_type');

      if (error) {
        console.error('Error loading sections:', error);
        return;
      }

      setSections(data || []);
    } catch (error) {
      console.error('Error loading sections:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleSectionVisibility = async (sectionId: string, currentEnabled: boolean) => {
    if (!isOwner) return;

    try {
      const { error } = await supabase
        .from('channel_sections')
        .update({ is_enabled: !currentEnabled })
        .eq('id', sectionId);

      if (error) {
        console.error('Error updating section:', error);
        return;
      }

      // Update local state
      setSections(prev => prev.map(section => 
        section.id === sectionId 
          ? { ...section, is_enabled: !currentEnabled }
          : section
      ));
    } catch (error) {
      console.error('Error updating section:', error);
    }
  };

  const handleSectionClick = (section: ChannelSection) => {
    if (!section.is_enabled) return;
    
    setSelectedSection(section.id);
    onSectionSelect(section.section_type, section.id);
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-12 bg-secondary rounded mb-4"></div>
              <div className="h-4 bg-secondary rounded mb-2"></div>
              <div className="h-3 bg-secondary rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-foreground">Channel Sections</h2>
          <p className="text-muted-foreground">Organize your voice content by type</p>
        </div>
        {isOwner && (
          <Button variant="outline" size="sm">
            <Settings size={16} className="mr-2" />
            Manage Sections
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {sections.map((section) => {
          const config = sectionConfig[section.section_type];
          const Icon = config.icon;
          const isSelected = selectedSection === section.id;

          return (
            <Card
              key={section.id}
              className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                isSelected 
                  ? 'border-voicechain-purple shadow-lg' 
                  : section.is_enabled 
                    ? 'hover:border-border/80' 
                    : 'opacity-50'
              } ${!section.is_enabled ? 'cursor-not-allowed' : ''}`}
              onClick={() => handleSectionClick(section)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${config.gradient} flex items-center justify-center`}>
                    <Icon size={24} className="text-white" />
                  </div>
                  
                  {isOwner && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleSectionVisibility(section.id, section.is_enabled);
                      }}
                      className="h-8 w-8 p-0"
                    >
                      {section.is_enabled ? (
                        <Eye size={16} className="text-green-600" />
                      ) : (
                        <EyeOff size={16} className="text-muted-foreground" />
                      )}
                    </Button>
                  )}
                </div>
                
                <div>
                  <CardTitle className="text-lg">{section.name}</CardTitle>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant={section.is_enabled ? "secondary" : "outline"} className="text-xs">
                      {section.is_enabled ? 'Active' : 'Disabled'}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                <p className="text-sm text-muted-foreground mb-4">
                  {section.description}
                </p>
                
                {section.is_enabled && (
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-muted-foreground">
                      Click to explore
                    </span>
                    <Button size="sm" variant="ghost" className="h-8 px-2">
                      <Plus size={14} />
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {sections.filter(s => s.is_enabled).length === 0 && (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-secondary rounded-full flex items-center justify-center mx-auto mb-4">
            <Hash size={32} className="text-muted-foreground" />
          </div>
          <h3 className="text-lg font-semibold mb-2">No Active Sections</h3>
          <p className="text-muted-foreground mb-4">
            {isOwner 
              ? 'Enable sections to organize your channel content'
              : 'This channel has no active sections yet'
            }
          </p>
          {isOwner && (
            <Button>
              <Settings size={16} className="mr-2" />
              Enable Sections
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export default ChannelSections;
