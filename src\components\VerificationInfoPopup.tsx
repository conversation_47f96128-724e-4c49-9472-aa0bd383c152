import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Shield, Clock, CheckCircle, XCircle, ExternalLink } from 'lucide-react';
import { VerificationType } from './VerificationBadge';
import { getVerificationDescription, getDetailedVerificationDescription } from '@/utils/verification';
import { formatDistanceToNow } from 'date-fns';

interface VerificationInfoPopupProps {
  isOpen: boolean;
  onClose: () => void;
  status: 'pending' | 'verified' | 'none';
  type?: VerificationType;
  verifiedAt?: string;
  submittedAt?: string;
  applicationId?: string;
}

const VerificationInfoPopup: React.FC<VerificationInfoPopupProps> = ({
  isOpen,
  onClose,
  status,
  type,
  verifiedAt,
  submittedAt,
  applicationId
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            {status === 'verified' && (
              <>
                <Shield className="h-5 w-5 mr-2 text-voicechain-purple" />
                Verification Status
              </>
            )}
            {status === 'pending' && (
              <>
                <Clock className="h-5 w-5 mr-2 text-amber-500" />
                Verification Pending
              </>
            )}
            {status === 'none' && (
              <>
                <Shield className="h-5 w-5 mr-2 text-gray-400" />
                Not Verified
              </>
            )}
          </DialogTitle>
          <DialogDescription>
            {status === 'verified' && type && (
              <>{getDetailedVerificationDescription(type)}</>
            )}
            {status === 'pending' && (
              <>Your verification application is currently under review. We'll notify you once a decision has been made.</>
            )}
            {status === 'none' && (
              <>This account is not verified. Verification helps users identify authentic accounts and provides additional benefits.</>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 mt-2">
          {status === 'verified' && (
            <div className="bg-green-50 p-4 rounded-md flex items-start">
              <CheckCircle className="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-sm">Verified Account</h4>
                <p className="text-sm text-gray-600 mt-1">
                  This account has been verified by the Audra team.
                  {verifiedAt && (
                    <> Verified {formatDistanceToNow(new Date(verifiedAt), { addSuffix: true })}.</>
                  )}
                </p>
              </div>
            </div>
          )}

          {status === 'pending' && (
            <div className="bg-amber-50 p-4 rounded-md flex items-start">
              <Clock className="h-5 w-5 text-amber-500 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-sm">Application Under Review</h4>
                <p className="text-sm text-gray-600 mt-1">
                  Your verification application is being reviewed by our team.
                  {submittedAt && (
                    <> Submitted {formatDistanceToNow(new Date(submittedAt), { addSuffix: true })}.</>
                  )}
                </p>
                <p className="text-sm text-gray-600 mt-2">
                  The review process typically takes 1-3 business days. You'll be notified once a decision has been made.
                </p>
              </div>
            </div>
          )}

          {status === 'none' && (
            <div className="bg-gray-50 p-4 rounded-md flex items-start">
              <Shield className="h-5 w-5 text-gray-400 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-sm">Not Verified</h4>
                <p className="text-sm text-gray-600 mt-1">
                  This account has not applied for verification or has not been verified yet.
                </p>
                <p className="text-sm text-gray-600 mt-2">
                  Verification helps users identify authentic accounts and provides additional benefits.
                </p>
              </div>
            </div>
          )}

          {status === 'verified' && (
            <div className="bg-voicechain-purple/10 p-4 rounded-md">
              <h4 className="font-medium text-sm mb-2">Benefits of Verification</h4>
              <ul className="text-sm text-gray-600 space-y-1 list-disc pl-5">
                <li>Increased visibility and credibility</li>
                <li>Higher trust from other users</li>
                <li>Access to exclusive features</li>
                <li>Protection against impersonation</li>
              </ul>
            </div>
          )}

          {status === 'none' && (
            <div className="flex justify-end">
              <Button
                onClick={onClose}
                className="bg-voicechain-purple hover:bg-voicechain-accent"
              >
                <Shield className="h-4 w-4 mr-2" />
                Apply for Verification
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default VerificationInfoPopup;
