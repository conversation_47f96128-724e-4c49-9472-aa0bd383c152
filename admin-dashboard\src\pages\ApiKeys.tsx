import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '../components/ui/tabs';
import { <PERSON><PERSON>, Eye, EyeOff, RefreshCw, Save } from 'lucide-react';
import { toast } from '../components/ui/toast';

interface ApiKey {
  id: string;
  name: string;
  key: string;
  service: string;
  created_at: string;
  last_used?: string;
}

// Mock API keys for development
const mockApiKeys: ApiKey[] = [
  {
    id: '1',
    name: 'Helius API Key',
    key: '5422bf21-7f7b-43b6-8a10-3a72b3d7bb3c',
    service: 'helius',
    created_at: '2023-01-15T00:00:00Z',
    last_used: '2023-05-20T00:00:00Z'
  },
  {
    id: '2',
    name: 'Helius API Key (Backup)',
    key: '4418d794-039b-4530-a9c4-6f8e325faa18',
    service: 'helius',
    created_at: '2023-02-10T00:00:00Z'
  },
  {
    id: '3',
    name: 'ElevenLabs API Key',
    key: '***************************************************',
    service: 'elevenlabs',
    created_at: '2023-03-05T00:00:00Z',
    last_used: '2023-05-18T00:00:00Z'
  },
  {
    id: '4',
    name: 'NFT.Storage API Key',
    key: 'fd56bf09.020c8783b870438d93f29d2655391154',
    service: 'nftstorage',
    created_at: '2023-04-20T00:00:00Z',
    last_used: '2023-05-15T00:00:00Z'
  }
];

const ApiKeys: React.FC = () => {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [visibleKeys, setVisibleKeys] = useState<Record<string, boolean>>({});
  const [newKey, setNewKey] = useState({
    name: '',
    key: '',
    service: 'helius'
  });

  useEffect(() => {
    // In a real implementation, we would fetch API keys from Supabase
    // For now, we'll use mock data
    setApiKeys(mockApiKeys);
    setIsLoading(false);
  }, []);

  const toggleKeyVisibility = (id: string) => {
    setVisibleKeys(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: 'Copied to clipboard',
      description: 'API key has been copied to your clipboard',
      duration: 3000
    });
  };

  const handleAddKey = () => {
    if (!newKey.name || !newKey.key || !newKey.service) {
      toast({
        title: 'Error',
        description: 'All fields are required',
        duration: 3000
      });
      return;
    }

    const newApiKey: ApiKey = {
      id: Date.now().toString(),
      name: newKey.name,
      key: newKey.key,
      service: newKey.service,
      created_at: new Date().toISOString()
    };

    setApiKeys(prev => [...prev, newApiKey]);
    
    // Reset form
    setNewKey({
      name: '',
      key: '',
      service: 'helius'
    });

    toast({
      title: 'API Key Added',
      description: 'The new API key has been added successfully',
      duration: 3000
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>API Keys Management</CardTitle>
          <CardDescription>
            Manage API keys for various services used in the application
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="keys">
            <TabsList className="grid grid-cols-2 mb-6">
              <TabsTrigger value="keys">API Keys</TabsTrigger>
              <TabsTrigger value="add">Add New Key</TabsTrigger>
            </TabsList>
            
            <TabsContent value="keys" className="space-y-4">
              {isLoading ? (
                <div className="text-center py-8">
                  <p>Loading API keys...</p>
                </div>
              ) : apiKeys.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No API keys found</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {apiKeys.map((apiKey) => (
                    <Card key={apiKey.id} className="overflow-hidden">
                      <div className="p-4 border-b bg-muted/30">
                        <div className="flex justify-between items-center">
                          <div>
                            <h3 className="font-medium">{apiKey.name}</h3>
                            <p className="text-xs text-muted-foreground capitalize">{apiKey.service}</p>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => toggleKeyVisibility(apiKey.id)}
                            >
                              {visibleKeys[apiKey.id] ? <EyeOff size={14} /> : <Eye size={14} />}
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => copyToClipboard(apiKey.key)}
                            >
                              <Copy size={14} />
                            </Button>
                          </div>
                        </div>
                      </div>
                      <div className="p-4">
                        <div className="mb-4">
                          <Label htmlFor={`key-${apiKey.id}`}>API Key</Label>
                          <div className="flex mt-1">
                            <Input
                              id={`key-${apiKey.id}`}
                              value={visibleKeys[apiKey.id] ? apiKey.key : '•'.repeat(Math.min(apiKey.key.length, 30))}
                              readOnly
                              className="font-mono text-sm"
                            />
                          </div>
                        </div>
                        <div className="flex justify-between text-xs text-muted-foreground">
                          <span>Created: {formatDate(apiKey.created_at)}</span>
                          {apiKey.last_used && (
                            <span>Last used: {formatDate(apiKey.last_used)}</span>
                          )}
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="add" className="space-y-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="key-name">Key Name</Label>
                      <Input
                        id="key-name"
                        placeholder="e.g., Helius API Key"
                        value={newKey.name}
                        onChange={(e) => setNewKey(prev => ({ ...prev, name: e.target.value }))}
                        className="mt-1"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="key-value">API Key</Label>
                      <Input
                        id="key-value"
                        placeholder="Enter API key"
                        value={newKey.key}
                        onChange={(e) => setNewKey(prev => ({ ...prev, key: e.target.value }))}
                        className="mt-1 font-mono"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="key-service">Service</Label>
                      <select
                        id="key-service"
                        value={newKey.service}
                        onChange={(e) => setNewKey(prev => ({ ...prev, service: e.target.value }))}
                        className="w-full mt-1 px-3 py-2 bg-background border border-input rounded-md"
                      >
                        <option value="helius">Helius</option>
                        <option value="elevenlabs">ElevenLabs</option>
                        <option value="nftstorage">NFT.Storage</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                    
                    <Button
                      onClick={handleAddKey}
                      className="w-full mt-4"
                    >
                      <Save size={16} className="mr-2" />
                      Save API Key
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default ApiKeys;
