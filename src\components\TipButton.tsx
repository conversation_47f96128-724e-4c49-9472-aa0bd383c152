import { useState } from 'react';
import { Button } from './ui/button';
import { Coins } from 'lucide-react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { useToast } from './ui/use-toast';
import { sendTip } from '@/services/tipService';
import { useAuth } from '@/contexts/AuthContext';
import { useNotifications } from '@/contexts/NotificationContext';

interface TipButtonProps {
  recipientId: string;
  messageId?: string;
}

export function TipButton({ recipientId, messageId }: TipButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [amount, setAmount] = useState('0.1');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();
  const { addNotification } = useNotifications();

  const handleTip = async () => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please sign in to send a tip",
      });
      return;
    }

    if (parseFloat(amount) <= 0) {
      toast({
        title: "Invalid amount",
        description: "Please enter a valid amount greater than 0",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);

    try {
      const result = await sendTip(
        user.id,
        recipientId,
        messageId || null,
        amount,
        'SOL'
      );

      if (result.success) {
        toast({
          title: "Tip sent successfully",
          description: `You sent ${amount} SOL to the creator`,
        });

        // Create notification for tip
        addNotification(
          'tip',
          user.id,
          recipientId,
          messageId,
          {
            tipAmount: amount,
            currency: 'SOL'
          }
        );

        setIsOpen(false);
      } else {
        throw new Error("Failed to send tip");
      }
    } catch (error) {
      console.error('Error sending tip:', error);
      toast({
        title: "Error",
        description: "Failed to send tip. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm" className="flex items-center gap-1">
          <Coins className="h-4 w-4" />
          <span>Tip</span>
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Send a tip</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="amount">Amount (SOL)</Label>
            <Input
              id="amount"
              type="number"
              step="0.01"
              min="0.01"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
            />
          </div>
          <Button 
            onClick={handleTip} 
            disabled={isLoading} 
            className="w-full"
          >
            {isLoading ? "Processing..." : "Send Tip"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}