
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Heart, ThumbsUp, Smile, Frown, Flame, Star, MessageCircle } from 'lucide-react';
import { toast } from '@/components/ui/sonner';
import { supabase } from '@/integrations/supabase/client';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useNotifications } from '@/contexts/NotificationContext';

// Define reaction types
export type ReactionType = 'like' | 'love' | 'haha' | 'wow' | 'sad' | 'angry' | 'fire';

// Define reaction data structure
export interface Reaction {
  id: string;
  postId: string;
  userId: string;
  type: ReactionType;
  timestamp: Date;
}

interface PostReactionsProps {
  postId: string;
  currentUserId: string;
  originalUserId?: string; // Add this to know who to notify
  initialReactions?: Reaction[];
  onReply?: () => void;
}

const reactionIcons: Record<ReactionType, React.ReactNode> = {
  like: <ThumbsUp className="h-4 w-4 text-blue-500" />,
  love: <Heart className="h-4 w-4 text-red-500" />,
  haha: <Smile className="h-4 w-4 text-yellow-500" />,
  wow: <Star className="h-4 w-4 text-purple-500" />,
  sad: <Frown className="h-4 w-4 text-gray-500" />,
  angry: <Frown className="h-4 w-4 text-orange-500" />,
  fire: <Flame className="h-4 w-4 text-orange-500" />
};

const reactionEmojis: Record<ReactionType, string> = {
  like: '👍',
  love: '❤️',
  haha: '😂',
  wow: '😮',
  sad: '😢',
  angry: '😠',
  fire: '🔥'
};

const PostReactions: React.FC<PostReactionsProps> = ({
  postId,
  currentUserId,
  originalUserId,
  initialReactions = [],
  onReply
}) => {
  const [reactions, setReactions] = useState<Reaction[]>(initialReactions);
  const [userReaction, setUserReaction] = useState<ReactionType | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const { addNotification } = useNotifications();

  // Group reactions by type
  const reactionCounts = reactions.reduce((acc, reaction) => {
    acc[reaction.type] = (acc[reaction.type] || 0) + 1;
    return acc;
  }, {} as Record<ReactionType, number>);

  // Debug reactions
  useEffect(() => {
    console.log('Current reactions:', reactions);
    console.log('Reaction counts:', reactionCounts);
    console.log('User reaction:', userReaction);
  }, [reactions, reactionCounts, userReaction]);

  // Check if user has already reacted
  useEffect(() => {
    const userReaction = reactions.find(r => r.userId === currentUserId);
    if (userReaction) {
      setUserReaction(userReaction.type);
    } else {
      setUserReaction(null);
    }
  }, [reactions, currentUserId]);

  // Load reactions from database
  useEffect(() => {
    const loadReactions = async () => {
      try {
        const { data, error } = await supabase
          .from('post_reactions')
          .select('*')
          .eq('post_id', postId);

        if (error) {
          console.error('Error loading reactions:', error);
          return;
        }

        if (data) {
          const mappedReactions: Reaction[] = data.map(item => ({
            id: item.id,
            postId: item.post_id,
            userId: item.user_id,
            type: item.reaction_type as ReactionType,
            timestamp: new Date(item.created_at)
          }));
          setReactions(mappedReactions);
        }
      } catch (error) {
        console.error('Error in loadReactions:', error);
      }
    };

    loadReactions();
  }, [postId]);

  // Add or update reaction
  const handleReaction = async (type: ReactionType) => {
    setIsLoading(true);
    setIsOpen(false);

    try {
      // If user already reacted with the same type, remove the reaction
      if (userReaction === type) {
        // Find the reaction ID
        const reactionToRemove = reactions.find(r => r.userId === currentUserId);

        if (reactionToRemove) {
          const { error } = await supabase
            .from('post_reactions')
            .delete()
            .eq('id', reactionToRemove.id);

          if (error) {
            console.error('Error removing reaction:', error);
            toast.error('Failed to remove reaction');
            return;
          }

          // Update local state
          setReactions(prev => prev.filter(r => r.id !== reactionToRemove.id));
          setUserReaction(null);
        }
      } else {
        // If user already reacted with a different type, update it
        const existingReaction = reactions.find(r => r.userId === currentUserId);

        if (existingReaction) {
          const { error } = await supabase
            .from('post_reactions')
            .update({ reaction_type: type })
            .eq('id', existingReaction.id);

          if (error) {
            console.error('Error updating reaction:', error);
            toast.error('Failed to update reaction');
            return;
          }

          // Update local state
          setReactions(prev => prev.map(r =>
            r.id === existingReaction.id ? { ...r, type } : r
          ));
        } else {
          // Add new reaction
          const newReaction = {
            id: crypto.randomUUID(),
            post_id: postId,
            user_id: currentUserId,
            reaction_type: type,
            created_at: new Date().toISOString()
          };

          const { error } = await supabase
            .from('post_reactions')
            .insert(newReaction);

          if (error) {
            console.error('Error adding reaction:', error);
            toast.error('Failed to add reaction');
            return;
          }

          // Update local state
          setReactions(prev => [...prev, {
            id: newReaction.id,
            postId: newReaction.post_id,
            userId: newReaction.user_id,
            type: newReaction.reaction_type as ReactionType,
            timestamp: new Date(newReaction.created_at)
          }]);

          // Create notification for new reaction (not for updates or removals)
          if (originalUserId && originalUserId !== currentUserId) {
            addNotification(
              'reaction',
              currentUserId,
              originalUserId,
              postId,
              {
                emoji: reactionEmojis[type],
                reactionType: type
              }
            );
          }
        }

        setUserReaction(type);
      }
    } catch (error) {
      console.error('Error in handleReaction:', error);
      toast.error('An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center gap-2">
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 px-2 text-muted-foreground hover:text-foreground"
            disabled={isLoading}
          >
            {userReaction ? (
              <span className="flex items-center">
                {reactionEmojis[userReaction]}
              </span>
            ) : (
              <span className="flex items-center gap-1">
                <ThumbsUp className="h-4 w-4" />
                <span className="text-xs">React</span>
              </span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-2">
          <div className="flex gap-1">
            {Object.entries(reactionEmojis).map(([type, emoji]) => (
              <TooltipProvider key={type}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 rounded-full"
                      onClick={() => handleReaction(type as ReactionType)}
                    >
                      <span className="text-lg">{emoji}</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="capitalize">{type}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ))}
          </div>
        </PopoverContent>
      </Popover>

      {/* Display reaction counts */}
      {Object.entries(reactionCounts).length > 0 && (
        <div className="flex items-center gap-1">
          {Object.entries(reactionCounts)
            .sort((a, b) => b[1] - a[1]) // Sort by count (highest first)
            .slice(0, 3) // Show top 3 reactions
            .map(([type, count]) => (
              <Badge key={type} variant="outline" className="px-1.5 py-0 h-5">
                <span>{reactionEmojis[type as ReactionType]}</span>
                <span className="text-xs ml-1">{count}</span>
              </Badge>
            ))}
        </div>
      )}

      {/* Reply button */}
      {onReply && (
        <Button
          variant="ghost"
          size="sm"
          className="h-8 px-2 text-muted-foreground hover:text-foreground"
          onClick={onReply}
        >
          <MessageCircle className="h-4 w-4 mr-1" />
          <span className="text-xs">Reply</span>
        </Button>
      )}
    </div>
  );
};

export default PostReactions;
