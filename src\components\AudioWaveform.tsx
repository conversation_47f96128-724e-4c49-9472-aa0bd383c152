
import React, { useEffect, useRef, useState } from 'react';
import { Waveform } from './Waveform';

interface AudioWaveformProps {
  audioUrl: string;
  audioRef: React.RefObject<HTMLAudioElement>;
  isPlaying: boolean;
  progress: number;
  setIsPlaying: (isPlaying: boolean) => void;
  duration: number;
}

const AudioWaveform: React.FC<AudioWaveformProps> = ({
  audioUrl,
  audioRef,
  isPlaying,
  progress,
  setIsPlaying,
  duration,
}) => {
  const [currentTime, setCurrentTime] = useState(0);
  const [audioDuration, setAudioDuration] = useState(duration);
  const [waveformData, setWaveformData] = useState<number[]>([]);
  
  // Generate random waveform data if not provided
  useEffect(() => {
    if (waveformData.length === 0) {
      const randomData = Array.from({ length: 40 }, () => Math.random());
      setWaveformData(randomData);
    }
  }, [waveformData]);
  
  // Update current time based on progress
  useEffect(() => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  }, [progress, audioRef]);

  // Load audio duration once audio is loaded
  useEffect(() => {
    const handleLoadedMetadata = () => {
      if (audioRef.current && audioRef.current.duration) {
        setAudioDuration(audioRef.current.duration);
      }
    };

    const audio = audioRef.current;
    if (audio) {
      audio.addEventListener('loadedmetadata', handleLoadedMetadata);
      
      // If already loaded
      if (audio.duration) {
        setAudioDuration(audio.duration);
      }
      
      return () => {
        audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      };
    }
  }, [audioRef]);

  // Handle seeking in the audio
  const handleSeek = (position: number) => {
    if (audioRef.current) {
      audioRef.current.currentTime = position;
      setCurrentTime(position);
    }
  };

  return (
    <div className="w-full">
      <Waveform
        isPlaying={isPlaying}
        duration={audioDuration}
        currentTime={currentTime}
        waveformData={waveformData}
        onSeek={handleSeek}
      />
      <div className="flex justify-between mt-1 text-xs text-muted-foreground">
        <span>{formatTime(currentTime)}</span>
        <span>{formatTime(audioDuration)}</span>
      </div>
    </div>
  );
};

// Format time in MM:SS format
const formatTime = (seconds: number): string => {
  if (isNaN(seconds)) return '0:00';
  
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
};

export default AudioWaveform;
