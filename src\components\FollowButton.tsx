
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { UserCheck, UserPlus } from 'lucide-react';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/components/ui/sonner';
import { NotificationType } from '@/types/notification';
import { supabase } from '@/integrations/supabase/client';
import { FollowService } from '@/services/followService';

interface FollowButtonProps {
  userAddress: string;
  variant?: 'default' | 'outline' | 'secondary';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  showIcon?: boolean;
  showFollowingText?: boolean;
}

const FollowButton: React.FC<FollowButtonProps> = ({
  userAddress,
  variant = 'default',
  size = 'default',
  className = '',
  showIcon = true,
  showFollowingText = true
}) => {
  const { currentProfile } = useProfiles();
  const { isAuthenticated } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);

  // Check if already following using FollowService
  React.useEffect(() => {
    const checkFollowStatus = async () => {
      if (!currentProfile || !userAddress) return;

      try {
        const isFollowingUser = await FollowService.isFollowing(currentProfile.address, userAddress);
        setIsFollowing(isFollowingUser);
      } catch (error) {
        console.error('Error checking follow status:', error);
      }
    };

    checkFollowStatus();
  }, [currentProfile, userAddress]);

  const handleFollowAction = async () => {
    if (!isAuthenticated) {
      toast.error('You must be logged in to follow users');
      return;
    }

    if (!currentProfile) {
      toast.error('Your profile could not be loaded');
      return;
    }

    setIsLoading(true);

    try {
      if (isFollowing) {
        // Unfollow user
        const success = await FollowService.unfollowUser(currentProfile.address, userAddress);
        if (success) {
          setIsFollowing(false);
          toast.success('Unfollowed successfully');
        } else {
          toast.error('Failed to unfollow user');
          return;
        }
      } else {
        // Follow user
        const success = await FollowService.followUser(currentProfile.address, userAddress);
        if (success) {
          setIsFollowing(true);
          toast.success('Following successfully');
        } else {
          toast.error('Failed to follow user');
          return;
        }
        
        // Send notification
        try {
          const notificationData = {
            type: 'follow' as NotificationType,
            from_address: currentProfile.address,
            to_address: userAddress,
            data: {
              follower_profile: {
                username: currentProfile.username,
                display_name: currentProfile.displayName,
                avatar_url: currentProfile.profileImageUrl
              }
            },
            created_at: new Date().toISOString(),
            read: false
          };
          
          await supabase.from('notifications').insert(notificationData);
        } catch (notifError) {
          console.error('Error creating notification:', notifError);
        }
      }
    } catch (error) {
      console.error('Error following/unfollowing user:', error);
      toast.error(isFollowing ? 'Failed to unfollow user' : 'Failed to follow user');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant={isFollowing ? 'outline' : variant}
      size={size}
      className={className}
      onClick={handleFollowAction}
      disabled={isLoading || currentProfile?.address === userAddress}
    >
      {showIcon && (isFollowing ? <UserCheck className="mr-2 h-4 w-4" /> : <UserPlus className="mr-2 h-4 w-4" />)}
      {isFollowing ? (showFollowingText ? 'Following' : '') : 'Follow'}
    </Button>
  );
};

export default FollowButton;
