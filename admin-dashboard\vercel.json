{"buildCommand": "npm run build", "outputDirectory": "dist", "framework": "vite", "rewrites": [{"source": "/(.*)", "destination": "/"}], "headers": [{"source": "/(.*).js", "headers": [{"key": "Content-Type", "value": "application/javascript"}]}, {"source": "/index.html", "headers": [{"key": "Clear-Site-Data", "value": "\"cache\", \"cookies\", \"storage\", \"executionContexts\""}, {"key": "Service-Worker-Allowed", "value": "/"}]}]}