import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useChannels } from '@/contexts/ChannelContext';
import VoiceMessageWithoutChannel from './VoiceMessageWithoutChannel';
import ChannelSections from './ChannelSections';
import RoleManagement from './RoleManagement';
import RoleBadge from './RoleBadge';
import {
  Hash,
  Users,
  Settings,
  UserPlus,
  Crown,
  Shield,
  Mic,
  MicOff,
  Volume2,
  VolumeX,
  MoreVertical,
  Info,
  Bell,
  BellOff,
  ArrowLeft,
  Radio,
  Trash2,
  Lock,
  Globe,
  MessageCircle
} from 'lucide-react';

interface ModernChannelViewProps {
  userAddress: string;
  onChannelSettings?: (channelId: string) => void;
  onChannelInvite?: (channelId: string) => void;
  forceChannelId?: string | null; // EMERGENCY: Force specific channel
}

const ModernChannelView: React.FC<ModernChannelViewProps> = ({
  userAddress,
  onChannelSettings,
  onChannelInvite,
  forceChannelId
}) => {
  const { activeChannel, getChannelById, isUserModerator, deleteChannel } = useChannels();
  const [isMuted, setIsMuted] = useState(false);
  const [isDeafened, setIsDeafened] = useState(false);
  const [showMemberList, setShowMemberList] = useState(true);
  const [currentView, setCurrentView] = useState<'sections' | 'section_content' | 'role_management'>('sections');
  const [selectedSection, setSelectedSection] = useState<{type: string, id: string} | null>(null);

  // DEBUG: Track activeChannel changes
  useEffect(() => {
    console.log('🔄 ModernChannelView - activeChannel changed to:', activeChannel);
  }, [activeChannel]);

  // EMERGENCY FIX: Use forced channel or fallback to context
  const effectiveChannelId = forceChannelId || activeChannel;
  const channel = effectiveChannelId ? getChannelById(effectiveChannelId) : null;

  // DEBUG: Log what's happening
  console.log('🔍 ModernChannelView - activeChannel:', activeChannel);
  console.log('🔍 ModernChannelView - forceChannelId:', forceChannelId);
  console.log('🔍 ModernChannelView - effectiveChannelId:', effectiveChannelId);
  console.log('🔍 ModernChannelView - channel found:', channel);

  if (!channel) {
    console.log('❌ ModernChannelView - No channel, showing welcome screen');
    if (activeChannel) {
      console.log('⚠️ ModernChannelView - activeChannel exists but getChannelById returned null!');
    }
    return (
      <div className="h-full flex items-center justify-center bg-gradient-to-br from-background to-secondary/10">
        <div className="text-center max-w-md mx-auto p-8">
          <div className="w-20 h-20 bg-gradient-to-br from-voicechain-purple/20 to-voicechain-accent/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <Hash size={40} className="text-voicechain-purple" />
          </div>
          <h2 className="text-2xl font-bold text-foreground mb-3">Welcome to Channels</h2>
          <p className="text-muted-foreground mb-6">
            Select a channel from the sidebar to start voice conversations with your community.
          </p>
          <div className="space-y-2 text-sm text-muted-foreground">
            <p>🎤 Share voice messages</p>
            <p>💬 Reply and react to others</p>
            <p>🌟 Build meaningful connections</p>
          </div>
        </div>
      </div>
    );
  }

  const isOwner = channel.members.some(m => m.address === userAddress && m.role === 'owner');
  const isModerator = isUserModerator(channel.id, userAddress);
  const canManage = isOwner || isModerator;

  const handleSectionSelect = (sectionType: string, sectionId: string) => {
    setSelectedSection({ type: sectionType, id: sectionId });
    setCurrentView('section_content');
  };

  const handleBackToSections = () => {
    setCurrentView('sections');
    setSelectedSection(null);
  };

  const handleShowRoleManagement = () => {
    setCurrentView('role_management');
  };

  const handleDeleteChannel = async () => {
    if (!channel || !isOwner) return;

    const confirmed = window.confirm(
      `Are you sure you want to delete "${channel.name}"? This action cannot be undone.`
    );

    if (confirmed) {
      try {
        await deleteChannel(channel.id);
        // Navigate back to channel list
        window.location.href = '/channels';
      } catch (error) {
        console.error('Error deleting channel:', error);
        alert('Failed to delete channel. Please try again.');
      }
    }
  };

  return (
    <div className="h-full flex bg-background">
      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Enhanced Channel Header with Overview */}
        <div className="border-b border-border/50 bg-card/50 backdrop-blur-sm">
          {/* Channel Banner */}
          <div className="relative">
            {channel.coverImageUrl ? (
              <div className="h-32 bg-cover bg-center"
                   style={{ backgroundImage: `url(${channel.coverImageUrl})` }}>
                <div className="absolute inset-0 bg-black/40" />
              </div>
            ) : (
              <div className="h-32 bg-gradient-to-r from-voicechain-purple/30 to-voicechain-accent/30">
                <div className="absolute inset-0 bg-black/20" />
              </div>
            )}

            {/* Channel Icon Overlay */}
            <div className="absolute bottom-4 left-4">
              <div className="w-16 h-16 bg-gradient-to-br from-voicechain-purple to-voicechain-accent rounded-2xl flex items-center justify-center border-4 border-background shadow-lg">
                <Hash size={28} className="text-white" />
              </div>
            </div>
          </div>

          {/* Channel Info */}
          <div className="p-4 pt-8">
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h1 className="text-2xl font-bold text-foreground">{channel.name}</h1>

                  {/* Visibility Badge */}
                  <Badge variant={channel.isPrivate ? "destructive" : "secondary"} className="text-xs">
                    {channel.isPrivate ? (
                      <>
                        <Lock size={12} className="mr-1" />
                        Private
                      </>
                    ) : (
                      <>
                        <Globe size={12} className="mr-1" />
                        Public
                      </>
                    )}
                  </Badge>

                  {/* Role Badges */}
                  {isOwner && <RoleBadge role="owner" />}
                  {isModerator && !isOwner && <RoleBadge role="moderator" />}
                </div>

                {/* Description */}
                {channel.description && (
                  <p className="text-muted-foreground mb-3 max-w-2xl">
                    {channel.description}
                  </p>
                )}

                {/* Stats Row */}
                <div className="flex items-center gap-6 text-sm text-muted-foreground mb-3">
                  <span className="flex items-center gap-1">
                    <Users size={14} />
                    {channel.members.length} members
                  </span>
                  <span className="flex items-center gap-1">
                    <MessageCircle size={14} />
                    Voice chat
                  </span>
                  <span className="flex items-center gap-1">
                    <Crown size={14} className="text-yellow-500" />
                    Created by {channel.members.find(m => m.role === 'owner')?.address.slice(0, 8)}...
                  </span>
                </div>

                {/* Tags */}
                {channel.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {channel.tags.map(tag => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        #{tag}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-2">
                {/* Voice Controls */}
                <div className="flex items-center gap-1 bg-secondary/50 rounded-lg p-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsMuted(!isMuted)}
                    className={`h-8 w-8 p-0 ${isMuted ? 'bg-red-500 text-white hover:bg-red-600' : ''}`}
                  >
                    {isMuted ? <MicOff size={16} /> : <Mic size={16} />}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsDeafened(!isDeafened)}
                    className={`h-8 w-8 p-0 ${isDeafened ? 'bg-red-500 text-white hover:bg-red-600' : ''}`}
                  >
                    {isDeafened ? <VolumeX size={16} /> : <Volume2 size={16} />}
                  </Button>
                </div>

                {/* Channel Actions */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowMemberList(!showMemberList)}
                  className="h-8 w-8 p-0"
                >
                  <Users size={16} />
                </Button>

                {canManage && (
                  <>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleShowRoleManagement}
                      className="h-8 w-8 p-0"
                      title="Manage Roles"
                    >
                      <Shield size={16} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onChannelInvite?.(channel.id)}
                      className="h-8 w-8 p-0"
                      title="Invite Members"
                    >
                      <UserPlus size={16} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onChannelSettings?.(channel.id)}
                      className="h-8 w-8 p-0"
                      title="Channel Settings"
                    >
                      <Settings size={16} />
                    </Button>

                    {/* Delete Channel - Owner Only */}
                    {isOwner && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleDeleteChannel}
                        className="h-8 w-8 p-0 text-red-500 hover:text-red-600 hover:bg-red-50"
                        title="Delete Channel"
                      >
                        <Trash2 size={16} />
                      </Button>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 overflow-y-auto bg-gradient-to-b from-background to-secondary/5">
          {currentView === 'sections' ? (
            <ChannelSections
              channelId={channel.id}
              userAddress={userAddress}
              isOwner={isOwner}
              onSectionSelect={handleSectionSelect}
            />
          ) : currentView === 'role_management' ? (
            <div className="p-4">
              {/* Back Button */}
              <div className="flex items-center gap-3 mb-6">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBackToSections}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft size={16} />
                  Back to Sections
                </Button>
                <div className="h-4 w-px bg-border" />
                <h2 className="text-xl font-semibold">Role Management</h2>
              </div>

              <RoleManagement
                channelId={channel.id}
                userAddress={userAddress}
                userRole={isOwner ? 'owner' : isModerator ? 'moderator' : 'member'}
                onClose={handleBackToSections}
              />
            </div>
          ) : (
            <div className="p-4">
              {/* Back Button */}
              <div className="flex items-center gap-3 mb-6">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBackToSections}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft size={16} />
                  Back to Sections
                </Button>
                <div className="h-4 w-px bg-border" />
                <h2 className="text-xl font-semibold">
                  {selectedSection?.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </h2>
              </div>

              {/* Section Content */}
              <div className="space-y-4">
                {selectedSection?.type === 'voice_posts' && (
                  <div className="text-center py-12">
                    <Mic size={48} className="mx-auto mb-4 text-muted-foreground" />
                    <h3 className="text-lg font-semibold mb-2">Voice Posts</h3>
                    <p className="text-muted-foreground mb-4">Share short voice messages with the community</p>
                    <Button className="bg-voicechain-purple hover:bg-voicechain-accent">
                      <Mic size={16} className="mr-2" />
                      Record Voice Post
                    </Button>
                  </div>
                )}

                {selectedSection?.type === 'live_streams' && (
                  <div className="text-center py-12">
                    <Radio size={48} className="mx-auto mb-4 text-red-500" />
                    <h3 className="text-lg font-semibold mb-2">Live Streams</h3>
                    <p className="text-muted-foreground mb-4">Host live voice rooms and discussions</p>
                    <Button className="bg-red-500 hover:bg-red-600">
                      <Radio size={16} className="mr-2" />
                      Start Live Stream
                    </Button>
                  </div>
                )}

                {/* Add more section types as needed */}
                {!['voice_posts', 'live_streams'].includes(selectedSection?.type || '') && (
                  <div className="text-center py-12">
                    <Hash size={48} className="mx-auto mb-4 text-muted-foreground" />
                    <h3 className="text-lg font-semibold mb-2">Coming Soon</h3>
                    <p className="text-muted-foreground">This section is under development</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Voice Input Area */}
        <div className="p-4 border-t border-border/50 bg-card/50 backdrop-blur-sm">
          <div className="flex items-center gap-3">
            <div className="flex-1 bg-secondary/50 rounded-lg p-3 border border-border/50">
              <p className="text-sm text-muted-foreground text-center">
                🎤 Hold spacebar or use the floating button to record voice messages
              </p>
            </div>
            <Button
              size="lg"
              className="bg-voicechain-purple hover:bg-voicechain-accent text-white rounded-full h-12 w-12 p-0"
            >
              <Mic size={20} />
            </Button>
          </div>
        </div>
      </div>

      {/* Member List Sidebar */}
      {showMemberList && (
        <div className="w-64 border-l border-border/50 bg-card/30 backdrop-blur-sm">
          <div className="p-4 border-b border-border/50">
            <h3 className="font-semibold text-foreground flex items-center gap-2">
              <Users size={16} />
              Members ({channel.members.length})
            </h3>
          </div>
          
          <div className="p-4 space-y-3 overflow-y-auto">
            {/* Group members by role */}
            {['owner', 'moderator', 'member'].map(role => {
              const roleMembers = channel.members.filter(m => m.role === role);
              if (roleMembers.length === 0) return null;

              return (
                <div key={role}>
                  <h4 className="text-xs font-medium text-muted-foreground uppercase tracking-wide mb-2">
                    {role === 'owner' ? 'Owner' : role === 'moderator' ? 'Moderators' : 'Members'} ({roleMembers.length})
                  </h4>
                  <div className="space-y-2">
                    {roleMembers.map(member => (
                      <div key={member.address} className="flex items-center gap-2 p-2 rounded-lg hover:bg-secondary/50 transition-colors">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback className="text-xs bg-voicechain-purple/20 text-voicechain-purple">
                            {member.address.slice(2, 4).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-foreground truncate">
                            {member.address.slice(0, 6)}...{member.address.slice(-4)}
                          </p>
                          <div className="flex items-center gap-1">
                            <RoleBadge role={member.role as any} size="sm" />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default ModernChannelView;
