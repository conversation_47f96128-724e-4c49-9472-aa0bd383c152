import React, { ReactNode } from 'react';
import { NotificationType } from '@/types/notification';
import { ChannelProvider } from '@/contexts/ChannelContext';

interface ChannelProviderWrapperProps {
  children: ReactNode;
  userAddress: string;
  addNotification: (
    type: NotificationType,
    fromAddress: string | null,
    toAddress: string,
    messageId: string | null,
    data?: Record<string, any>
  ) => Promise<void>;
}

export const ChannelProviderWrapper: React.FC<ChannelProviderWrapperProps> = ({
  children,
  userAddress,
  addNotification
}) => {
  // We're now using the ChannelProvider from ChannelContext.tsx
  // This wrapper is just to handle the circular dependency between NotificationProvider and ChannelProvider
  return (
    <ChannelProvider userAddress={userAddress}>
      {children}
    </ChannelProvider>
  );
};
