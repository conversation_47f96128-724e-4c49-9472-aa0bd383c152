import { supabase } from '@/lib/supabase';
import { VoiceMessageProps } from '@/types/voice-message';

export interface Reply {
  id: string;
  userAddress: string;
  timestamp: Date;
  transcript: string;
  audioUrl?: string;
  duration?: number;
  isTextOnly?: boolean;
  replies: Reply[];
  parentId: string;
}

/**
 * Load all replies (both voice and text) for a given post
 * @param postId - The ID of the parent post
 * @returns Array of replies
 */
export async function loadRepliesForPost(postId: string): Promise<Reply[]> {
  try {
    console.log('Loading replies for post:', postId);
    
    // Load voice replies from voice_messages table (without join to avoid FK issues)
    const { data: voiceReplies, error: voiceError } = await supabase
      .from('voice_messages')
      .select(`
        id,
        profile_id,
        transcript,
        audio_url,
        audio_duration,
        created_at
      `)
      .eq('parent_id', postId)
      .is('deleted_at', null)
      .order('created_at', { ascending: true });

    if (voiceError) {
      console.error('Error loading voice replies:', voiceError);
    }

    // Load text replies from comments table (without join since no FK relationship exists)
    const { data: textReplies, error: textError } = await supabase
      .from('comments')
      .select(`
        id,
        user_id,
        content,
        created_at
      `)
      .eq('post_id', postId)
      .order('created_at', { ascending: true });

    if (textError) {
      console.error('Error loading text replies:', textError);
    }

    const replies: Reply[] = [];

    // Collect all unique user IDs for batch profile lookup
    const userIds = new Set<string>();
    if (voiceReplies) {
      voiceReplies.forEach(reply => userIds.add(reply.profile_id));
    }
    if (textReplies) {
      textReplies.forEach(reply => userIds.add(reply.user_id));
    }

    // Batch fetch all wallet addresses
    const profileMap = new Map<string, string>();
    if (userIds.size > 0) {
      const { data: profilesData } = await supabase
        .from('profiles')
        .select('id, wallet_address')
        .in('id', Array.from(userIds));

      if (profilesData) {
        profilesData.forEach(profile => {
          profileMap.set(profile.id, profile.wallet_address);
        });
      }
    }

    // Convert voice replies
    if (voiceReplies) {
      voiceReplies.forEach(reply => {
        replies.push({
          id: reply.id,
          userAddress: profileMap.get(reply.profile_id) || reply.profile_id,
          timestamp: new Date(reply.created_at),
          transcript: reply.transcript || '',
          audioUrl: reply.audio_url,
          duration: Number(reply.audio_duration) || 0,
          isTextOnly: false,
          replies: [],
          parentId: postId
        });
      });
    }

    // Convert text replies
    if (textReplies) {
      textReplies.forEach(reply => {
        replies.push({
          id: reply.id,
          userAddress: profileMap.get(reply.user_id) || reply.user_id,
          timestamp: new Date(reply.created_at),
          transcript: reply.content,
          audioUrl: '',
          duration: 0,
          isTextOnly: true,
          replies: [],
          parentId: postId
        });
      });
    }

    // Sort all replies by timestamp
    replies.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

    console.log(`Loaded ${replies.length} replies (${voiceReplies?.length || 0} voice, ${textReplies?.length || 0} text)`);
    return replies;

  } catch (error) {
    console.error('Error loading replies:', error);
    return [];
  }
}

/**
 * Save a text reply to the database
 * @param postId - The ID of the parent post
 * @param content - The text content
 * @param userAddress - The user's wallet address
 * @param originalUserId - The ID of the original post author (for notifications)
 * @returns The saved reply
 */
export async function saveTextReply(postId: string, content: string, userAddress: string, originalUserId?: string): Promise<Reply> {
  try {
    // Get the profile UUID from the profiles table
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('id')
      .eq('wallet_address', userAddress)
      .single();

    if (profileError || !profileData) {
      throw new Error(`Profile not found for wallet address: ${userAddress}`);
    }

    // Save text reply to comments table
    const { data: savedComment, error: commentError } = await supabase
      .from('comments')
      .insert({
        user_id: profileData.id,
        post_id: postId,
        content: content,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (commentError) {
      throw new Error(`Failed to save text reply: ${commentError.message}`);
    }

    // Create notification if original user is different from replying user
    if (originalUserId && originalUserId !== userAddress) {
      try {
        await supabase.from('notifications').insert({
          type: 'reply',
          from_address: userAddress,
          to_address: originalUserId,
          message_id: postId,
          data: {
            replyContent: content.substring(0, 100), // First 100 chars
            isTextReply: true
          },
          read: false,
          created_at: new Date().toISOString()
        });
      } catch (notifError) {
        console.error('Error creating reply notification:', notifError);
        // Don't fail the reply if notification fails
      }
    }

    return {
      id: savedComment.id,
      userAddress: userAddress,
      timestamp: new Date(savedComment.created_at),
      transcript: content,
      audioUrl: '',
      duration: 0,
      isTextOnly: true,
      replies: [],
      parentId: postId
    };

  } catch (error) {
    console.error('Error saving text reply:', error);
    throw error;
  }
}

/**
 * Convert a Reply to VoiceMessageProps for compatibility
 * @param reply - The reply to convert
 * @returns VoiceMessageProps
 */
export function replyToVoiceMessage(reply: Reply): VoiceMessageProps {
  return {
    id: reply.id,
    userAddress: reply.userAddress,
    audioUrl: reply.audioUrl || '',
    transcript: reply.transcript,
    timestamp: reply.timestamp,
    duration: reply.duration || 0,
    isPinned: false,
    media: [],
    replies: [],
    parentId: reply.parentId,
    isReply: true,
    isTextOnly: reply.isTextOnly
  };
}
