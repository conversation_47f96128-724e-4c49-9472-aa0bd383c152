import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

type Theme = 'dark' | 'light' | 'system';

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  resolvedTheme: 'dark' | 'light';
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setThemeState] = useState<Theme>(() => {
    // Try to get the theme from localStorage
    const savedTheme = localStorage.getItem('voicechain-theme');
    return (savedTheme as Theme) || 'system';
  });

  const [resolvedTheme, setResolvedTheme] = useState<'dark' | 'light'>('dark');

  // Function to determine if the system prefers dark mode
  const getSystemTheme = (): 'dark' | 'light' => {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  };

  // Update the theme in localStorage and apply it to the document
  const setTheme = (newTheme: Theme) => {
    localStorage.setItem('voicechain-theme', newTheme);
    setThemeState(newTheme);
  };

  // Effect to apply the theme to the document
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    // Function to update the theme based on the current settings
    const updateTheme = () => {
      let resolvedTheme: 'dark' | 'light';

      if (theme === 'system') {
        resolvedTheme = getSystemTheme();
      } else {
        resolvedTheme = theme;
      }

      setResolvedTheme(resolvedTheme);

      // Apply the theme to the document
      if (resolvedTheme === 'dark') {
        document.documentElement.classList.add('dark');
        document.documentElement.classList.remove('light');
        document.body.style.backgroundColor = 'hsl(240, 20%, 12%)'; // Dark background
        document.body.style.color = 'hsl(210, 40%, 98%)'; // Light text
      } else {
        document.documentElement.classList.add('light');
        document.documentElement.classList.remove('dark');
        document.body.style.backgroundColor = 'hsl(0, 0%, 100%)'; // Light background
        document.body.style.color = 'hsl(240, 10%, 3.9%)'; // Dark text
      }
    };

    // Initial theme update
    updateTheme();

    // Listen for system theme changes
    const handleChange = () => {
      if (theme === 'system') {
        updateTheme();
      }
    };

    mediaQuery.addEventListener('change', handleChange);

    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, [theme]);

  return (
    <ThemeContext.Provider value={{ theme, setTheme, resolvedTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};
