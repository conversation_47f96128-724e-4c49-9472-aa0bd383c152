-- Create notifications table for the voice-first social platform
CREATE TABLE IF NOT EXISTS notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  type TEXT NOT NULL CHECK (type IN ('like', 'reply', 'tip', 'follow', 'summon', 'repost', 'reaction')),
  from_address TEXT NOT NULL,
  to_address TEXT NOT NULL,
  message_id UUID,
  data JSONB,
  read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own notifications" ON notifications
  FOR SELECT USING (to_address = auth.uid()::text OR to_address = (SELECT wallet_address FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can update their own notifications" ON notifications
  FOR UPDATE USING (to_address = auth.uid()::text OR to_address = (SELECT wallet_address FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Authenticated users can insert notifications" ON notifications
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notifications_to_address ON notifications(to_address);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_notifications_updated_at BEFORE UPDATE ON notifications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE notifications IS 'Stores user notifications for social interactions';
COMMENT ON COLUMN notifications.type IS 'Type of notification: like, reply, tip, follow, summon, repost, reaction';
COMMENT ON COLUMN notifications.from_address IS 'User ID or wallet address who triggered the notification';
COMMENT ON COLUMN notifications.to_address IS 'User ID or wallet address who receives the notification';
COMMENT ON COLUMN notifications.message_id IS 'Optional reference to the related voice message or post';
COMMENT ON COLUMN notifications.data IS 'Additional notification data (amounts, context, etc.)';
