
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  BrainCircuit,
  ListChecks,
  Hash,
  Sparkles,
  Lightbulb,
  ChevronDown,
  ChevronUp,
  SmilePlus,
  Meh,
  Frown
} from 'lucide-react';
import { SummaryResponse, generateSummary, extractTopics, generateHashtags } from '@/services/aiSummaryService';

interface VoiceSummaryProps {
  transcript: string;
  isExpanded?: boolean;
}

const VoiceSummary: React.FC<VoiceSummaryProps> = ({
  transcript,
  isExpanded = false
}) => {
  const [summary, setSummary] = useState<SummaryResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showSummary, setShowSummary] = useState(isExpanded);
  const [activeTab, setActiveTab] = useState('summary');

  useEffect(() => {
    // Only generate summary if transcript is long enough and summary is shown
    if (transcript.length > 50 && showSummary && !summary && !isLoading) {
      generateAISummary();
    }
  }, [transcript, showSummary]);

  const generateAISummary = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const summaryData = await generateSummary(transcript);
      setSummary(summaryData);
    } catch (err) {
      setError('Failed to generate summary. Please try again later.');
      console.error('Error generating summary:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Don't render anything if transcript is too short
  if (transcript.length < 50) {
    return null;
  }

  const getSentimentIcon = () => {
    if (!summary) return <Meh size={16} />;

    switch (summary.sentiment) {
      case 'positive':
        return <SmilePlus size={16} className="text-green-500" />;
      case 'negative':
        return <Frown size={16} className="text-red-500" />;
      default:
        return <Meh size={16} className="text-yellow-500" />;
    }
  };

  return (
    <div className="mt-2 mb-3" onClick={(e) => {
      // Stop propagation to prevent navigation to post detail
      e.stopPropagation();
      e.preventDefault();
    }} data-no-navigate="true">
      <Button
        variant="ghost"
        size="sm"
        className="w-full flex justify-between items-center p-2 text-xs text-muted-foreground hover:text-foreground"
        onClick={() => setShowSummary(!showSummary)}
      >
        <div className="flex items-center gap-1">
          <BrainCircuit size={14} className="text-voicechain-purple" />
          <span>Summarize</span>
        </div>
        {showSummary ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
      </Button>

      {showSummary && (
        <div className="mt-2 p-3 bg-secondary/30 rounded-lg">
          {isLoading ? (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4 rounded-full" />
                <Skeleton className="h-4 w-32" />
              </div>
              <Skeleton className="h-16 w-full" />
              <div className="flex gap-1 mt-2">
                <Skeleton className="h-5 w-16 rounded-full" />
                <Skeleton className="h-5 w-20 rounded-full" />
                <Skeleton className="h-5 w-12 rounded-full" />
              </div>
            </div>
          ) : error ? (
            <div className="text-center py-2">
              <p className="text-xs text-red-500">{error}</p>
              <Button
                variant="outline"
                size="sm"
                className="mt-2 text-xs"
                onClick={generateAISummary}
              >
                Try Again
              </Button>
            </div>
          ) : summary ? (
            <div className="space-y-3">
              <Tabs
                value={activeTab}
                onValueChange={setActiveTab}
                className="w-full"
                onClick={(e) => {
                  // Stop propagation to prevent navigation to post detail
                  e.stopPropagation();
                  e.preventDefault();
                }}
                data-no-navigate="true"
              >
                <TabsList className="grid w-full grid-cols-4 h-8">
                  <TabsTrigger value="summary" className="text-[10px]">Summary</TabsTrigger>
                  <TabsTrigger value="keypoints" className="text-[10px]">Key Points</TabsTrigger>
                  <TabsTrigger value="topics" className="text-[10px]">Topics</TabsTrigger>
                  <TabsTrigger value="tldr" className="text-[10px]">TL;DR</TabsTrigger>
                </TabsList>

                <TabsContent value="summary" className="pt-2">
                  <div className="flex items-center gap-1 mb-1">
                    <Sparkles size={14} className="text-voicechain-purple" />
                    <h4 className="text-xs font-medium">Summary</h4>
                    <div className="ml-auto flex items-center gap-1 text-xs text-muted-foreground">
                      <span>Sentiment:</span>
                      {getSentimentIcon()}
                    </div>
                  </div>
                  <p className="text-xs">{summary.summary}</p>
                </TabsContent>

                <TabsContent value="keypoints" className="pt-2">
                  <div className="flex items-center gap-1 mb-1">
                    <ListChecks size={14} className="text-voicechain-purple" />
                    <h4 className="text-xs font-medium">Key Points</h4>
                  </div>
                  <ul className="text-xs space-y-1 list-disc list-inside">
                    {summary.keyPoints.map((point, index) => (
                      <li key={index}>{point}</li>
                    ))}
                  </ul>
                </TabsContent>

                <TabsContent value="topics" className="pt-2">
                  <div className="flex items-center gap-1 mb-2">
                    <Hash size={14} className="text-voicechain-purple" />
                    <h4 className="text-xs font-medium">Topics</h4>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {summary.topics.map((topic, index) => (
                      <Badge key={index} variant="outline" className="text-[10px] bg-secondary/50">
                        #{topic}
                      </Badge>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="tldr" className="pt-2">
                  <div className="flex items-center gap-1 mb-1">
                    <Lightbulb size={14} className="text-voicechain-purple" />
                    <h4 className="text-xs font-medium">TL;DR</h4>
                  </div>
                  <p className="text-xs">{summary.tldr}</p>
                </TabsContent>
              </Tabs>
            </div>
          ) : (
            <div className="text-center py-4">
              <p className="text-xs text-muted-foreground">Generating AI summary...</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default VoiceSummary;
