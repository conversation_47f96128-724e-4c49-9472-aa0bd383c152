import { supabase } from '@/integrations/supabase/client';

export interface VoiceRecording {
  id: string;
  url: string;
  duration: number;
  transcript?: string;
  created_at: string;
}

export const voiceRecordingService = {
  // Start recording
  startRecording(): Promise<MediaRecorder | null> {
    return new Promise(async (resolve, reject) => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ 
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            sampleRate: 44100,
          }
        });

        const mediaRecorder = new MediaRecorder(stream, {
          mimeType: 'audio/webm;codecs=opus'
        });

        resolve(mediaRecorder);
      } catch (error) {
        console.error('Error starting recording:', error);
        reject(error);
      }
    });
  },

  // Stop recording and get blob
  stopRecording(mediaRecorder: MediaRecorder): Promise<Blob> {
    return new Promise((resolve, reject) => {
      const chunks: Blob[] = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'audio/webm' });
        resolve(blob);
      };

      mediaRecorder.onerror = (event) => {
        reject(event);
      };

      mediaRecorder.stop();
      
      // Stop all tracks to release microphone
      mediaRecorder.stream.getTracks().forEach(track => track.stop());
    });
  },

  // Upload voice recording to Supabase Storage
  async uploadVoiceRecording(
    blob: Blob, 
    userId: string, 
    chatId?: string
  ): Promise<string | null> {
    try {
      const timestamp = Date.now();
      const fileName = `voice_${userId}_${timestamp}.webm`;
      const filePath = chatId 
        ? `voice_chats/${chatId}/${fileName}`
        : `voice_messages/${userId}/${fileName}`;

      const { data, error } = await supabase.storage
        .from('voice-recordings')
        .upload(filePath, blob, {
          contentType: 'audio/webm',
          upsert: false
        });

      if (error) {
        console.error('Error uploading voice recording:', error);
        return null;
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('voice-recordings')
        .getPublicUrl(data.path);

      return urlData.publicUrl;
    } catch (error) {
      console.error('Error uploading voice recording:', error);
      return null;
    }
  },

  // Get voice recording duration
  getAudioDuration(blob: Blob): Promise<number> {
    return new Promise((resolve, reject) => {
      const audio = new Audio();
      const url = URL.createObjectURL(blob);
      
      audio.addEventListener('loadedmetadata', () => {
        URL.revokeObjectURL(url);
        resolve(audio.duration);
      });

      audio.addEventListener('error', (error) => {
        URL.revokeObjectURL(url);
        reject(error);
      });

      audio.src = url;
    });
  },

  // Transcribe audio (placeholder for future AI integration)
  async transcribeAudio(blob: Blob): Promise<string | null> {
    try {
      // TODO: Integrate with speech-to-text service
      // For now, return null - transcription can be added later
      return null;
    } catch (error) {
      console.error('Error transcribing audio:', error);
      return null;
    }
  },

  // Complete voice recording process
  async processVoiceRecording(
    blob: Blob, 
    userId: string, 
    chatId?: string
  ): Promise<VoiceRecording | null> {
    try {
      // Upload the recording
      const url = await this.uploadVoiceRecording(blob, userId, chatId);
      if (!url) return null;

      // Get duration
      const duration = await this.getAudioDuration(blob);

      // Get transcript (optional)
      const transcript = await this.transcribeAudio(blob);

      return {
        id: `voice_${Date.now()}`,
        url,
        duration,
        transcript: transcript || undefined,
        created_at: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error processing voice recording:', error);
      return null;
    }
  },

  // Check if browser supports voice recording
  isRecordingSupported(): boolean {
    return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
  },

  // Get microphone permission status
  async getMicrophonePermission(): Promise<'granted' | 'denied' | 'prompt'> {
    try {
      const result = await navigator.permissions.query({ name: 'microphone' as PermissionName });
      return result.state;
    } catch (error) {
      console.error('Error checking microphone permission:', error);
      return 'prompt';
    }
  },

  // Request microphone permission
  async requestMicrophonePermission(): Promise<boolean> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop());
      return true;
    } catch (error) {
      console.error('Microphone permission denied:', error);
      return false;
    }
  },
};
