import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://jcltjkaumevuycntdmds.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpjbHRqa2F1bWV2dXljbnRkbWRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTkwMDEsImV4cCI6MjA2MTg3NTAwMX0.LqQSk0NwB1mDB4Cznn0iQSrYvsAAWyUsaj8reo_etqM'
);

async function debugMediaPersistence() {
  console.log('🔍 DEBUGGING MEDIA PERSISTENCE ISSUE...\n');

  try {
    // 1. Check recent voice messages with media
    console.log('1️⃣ Checking recent voice messages...');
    const { data: recentMessages, error: messagesError } = await supabase
      .from('voice_messages')
      .select(`
        id,
        transcript,
        created_at,
        profile_id,
        voice_message_media (
          id,
          url,
          type,
          created_at
        )
      `)
      .order('created_at', { ascending: false })
      .limit(10);

    if (messagesError) {
      console.error('❌ Error fetching messages:', messagesError);
      return;
    }

    console.log(`📊 Found ${recentMessages.length} recent messages`);
    
    let messagesWithMedia = 0;
    let totalMediaFiles = 0;
    
    recentMessages.forEach((message, index) => {
      const mediaCount = message.voice_message_media?.length || 0;
      if (mediaCount > 0) {
        messagesWithMedia++;
        totalMediaFiles += mediaCount;
      }
      
      console.log(`  ${index + 1}. Message ${message.id.substring(0, 8)}... - ${mediaCount} media files`);
      
      if (message.voice_message_media && message.voice_message_media.length > 0) {
        message.voice_message_media.forEach((media, mediaIndex) => {
          console.log(`     📎 ${mediaIndex + 1}. ${media.type}: ${media.url}`);
          console.log(`        Created: ${media.created_at}`);
        });
      }
    });

    console.log(`\n📈 Summary: ${messagesWithMedia} messages with media, ${totalMediaFiles} total media files`);

    // 2. Check voice_message_media table directly
    console.log('\n2️⃣ Checking voice_message_media table directly...');
    const { data: allMedia, error: mediaError } = await supabase
      .from('voice_message_media')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(20);

    if (mediaError) {
      console.error('❌ Error fetching media:', mediaError);
    } else {
      console.log(`📊 Found ${allMedia.length} media records in database`);
      
      allMedia.forEach((media, index) => {
        console.log(`  ${index + 1}. ${media.type} - ${media.url.substring(0, 60)}...`);
        console.log(`      Message ID: ${media.voice_message_id}`);
        console.log(`      Created: ${media.created_at}`);
      });
    }

    // 3. Test URL accessibility
    console.log('\n3️⃣ Testing media URL accessibility...');
    if (allMedia && allMedia.length > 0) {
      const testMedia = allMedia.slice(0, 3); // Test first 3 URLs
      
      for (const media of testMedia) {
        try {
          console.log(`🔗 Testing URL: ${media.url.substring(0, 60)}...`);
          const response = await fetch(media.url, { method: 'HEAD' });
          
          if (response.ok) {
            console.log(`  ✅ URL accessible (${response.status})`);
          } else {
            console.log(`  ❌ URL not accessible (${response.status})`);
          }
        } catch (error) {
          console.log(`  ❌ URL failed to load: ${error.message}`);
        }
      }
    }

    // 4. Check storage bucket contents
    console.log('\n4️⃣ Checking storage bucket contents...');
    const { data: mediaFiles, error: storageError } = await supabase.storage
      .from('media')
      .list('', { limit: 20, sortBy: { column: 'created_at', order: 'desc' } });

    if (storageError) {
      console.error('❌ Error listing storage files:', storageError);
    } else {
      console.log(`📊 Found ${mediaFiles.length} files in media bucket`);
      
      mediaFiles.forEach((file, index) => {
        console.log(`  ${index + 1}. ${file.name} (${file.metadata?.size || 'unknown size'})`);
        console.log(`      Created: ${file.created_at}`);
        console.log(`      Updated: ${file.updated_at}`);
      });
    }

    // 5. Check for orphaned files (files in storage but not in database)
    console.log('\n5️⃣ Checking for orphaned files...');
    if (mediaFiles && allMedia) {
      const storageFileNames = mediaFiles.map(f => f.name);
      const dbUrls = allMedia.map(m => {
        // Extract filename from URL
        const urlParts = m.url.split('/');
        return urlParts[urlParts.length - 1];
      });

      const orphanedFiles = storageFileNames.filter(fileName => 
        !dbUrls.some(dbUrl => dbUrl.includes(fileName))
      );

      if (orphanedFiles.length > 0) {
        console.log(`⚠️  Found ${orphanedFiles.length} orphaned files in storage:`);
        orphanedFiles.forEach(file => console.log(`    - ${file}`));
      } else {
        console.log('✅ No orphaned files found');
      }
    }

    // 6. Check for broken database references (URLs in database but files don't exist)
    console.log('\n6️⃣ Checking for broken database references...');
    if (allMedia) {
      let brokenRefs = 0;
      
      for (const media of allMedia.slice(0, 5)) { // Check first 5
        try {
          const response = await fetch(media.url, { method: 'HEAD' });
          if (!response.ok) {
            brokenRefs++;
            console.log(`❌ Broken reference: ${media.url}`);
          }
        } catch (error) {
          brokenRefs++;
          console.log(`❌ Broken reference: ${media.url}`);
        }
      }
      
      if (brokenRefs === 0) {
        console.log('✅ No broken references found in sample');
      } else {
        console.log(`⚠️  Found ${brokenRefs} broken references in sample`);
      }
    }

    // 7. Test the complete flow
    console.log('\n7️⃣ Testing complete media flow...');
    
    // Create a test message with media
    const testMessageId = `test_${Date.now()}`;
    const testProfileId = 'test_profile';
    
    // Insert test message
    const { error: messageInsertError } = await supabase
      .from('voice_messages')
      .insert({
        id: testMessageId,
        profile_id: testProfileId,
        audio_url: 'test_audio.mp3',
        transcript: 'Test message for media debugging',
        audio_duration: 10,
        created_at: new Date().toISOString()
      });

    if (messageInsertError) {
      console.log('❌ Failed to insert test message:', messageInsertError);
    } else {
      console.log('✅ Test message inserted');
      
      // Insert test media
      const testMediaUrl = 'https://example.com/test-image.jpg';
      const { error: mediaInsertError } = await supabase
        .from('voice_message_media')
        .insert({
          id: `test_media_${Date.now()}`,
          voice_message_id: testMessageId,
          url: testMediaUrl,
          type: 'image',
          created_at: new Date().toISOString()
        });

      if (mediaInsertError) {
        console.log('❌ Failed to insert test media:', mediaInsertError);
      } else {
        console.log('✅ Test media inserted');
        
        // Try to retrieve the message with media
        const { data: retrievedMessage, error: retrieveError } = await supabase
          .from('voice_messages')
          .select(`
            id,
            transcript,
            voice_message_media (
              id,
              url,
              type
            )
          `)
          .eq('id', testMessageId)
          .single();

        if (retrieveError) {
          console.log('❌ Failed to retrieve test message:', retrieveError);
        } else {
          console.log('✅ Test message retrieved with media:', retrievedMessage);
          
          // Clean up test data
          await supabase.from('voice_message_media').delete().eq('voice_message_id', testMessageId);
          await supabase.from('voice_messages').delete().eq('id', testMessageId);
          console.log('🧹 Test data cleaned up');
        }
      }
    }

    // 8. Final diagnosis
    console.log('\n🔍 DIAGNOSIS:');
    
    if (totalMediaFiles === 0) {
      console.log('❌ ISSUE: No media files found in database');
      console.log('   This suggests media is not being saved to the database at all');
      console.log('   Check the media upload and save process in your app');
    } else if (messagesWithMedia === 0) {
      console.log('❌ ISSUE: Media files exist but not linked to messages');
      console.log('   This suggests a problem with the voice_message_id linking');
    } else {
      console.log('✅ Media files are being saved to database');
      console.log('   The issue might be in the frontend loading or URL generation');
    }

    console.log('\n🛠️  NEXT STEPS:');
    console.log('1. Check browser console for media loading errors');
    console.log('2. Verify that media URLs are being properly generated');
    console.log('3. Check if media is being filtered out during message loading');
    console.log('4. Test uploading a new post and immediately check the database');

  } catch (error) {
    console.error('❌ Debug script failed:', error);
  }
}

// Run the debug script
debugMediaPersistence().catch(console.error);
