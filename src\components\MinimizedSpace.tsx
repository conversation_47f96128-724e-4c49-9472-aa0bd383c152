import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  Mi<PERSON>, 
  MicOff, 
  Volume2, 
  VolumeX, 
  Maximize2, 
  X,
  Users
} from 'lucide-react';

interface MinimizedSpaceProps {
  space: {
    id: string;
    title: string;
    host_profile_id: string;
    participant_count: number;
  };
  isHost: boolean;
  isMuted: boolean;
  isListening: boolean;
  onToggleMute: () => void;
  onToggleListening: () => void;
  onMaximize: () => void;
  onClose: () => void;
}

export const MinimizedSpace: React.FC<MinimizedSpaceProps> = ({
  space,
  isHost,
  isMuted,
  isListening,
  onToggleMute,
  onToggleListening,
  onMaximize,
  onClose
}) => {
  return (
    <div className="fixed bottom-4 right-4 z-50 block">
      <div className="bg-gray-900 border border-gray-700 rounded-lg p-3 shadow-lg min-w-[280px] max-w-[320px]">
        {/* Header */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
            <span className="text-white text-sm font-medium truncate max-w-[150px]">
              {space.title}
            </span>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={onMaximize}
              className="text-white hover:bg-gray-800 w-6 h-6 p-0"
            >
              <Maximize2 className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-white hover:bg-gray-800 w-6 h-6 p-0"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>

        {/* Info */}
        <div className="flex items-center gap-2 mb-3">
          <Avatar className="w-6 h-6">
            <AvatarFallback className="bg-gray-700 text-white text-xs">
              {space.host_profile_id.slice(0, 2).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <span className="text-gray-300 text-xs">
            {space.host_profile_id.slice(0, 8)}...
          </span>
          <div className="flex items-center gap-1 text-gray-400 text-xs ml-auto">
            <Users className="h-3 w-3" />
            {space.participant_count}
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center justify-center gap-2">
          {/* Mic Control (only for speakers) */}
          {isHost && (
            <Button
              variant={isMuted ? "secondary" : "destructive"}
              size="sm"
              onClick={onToggleMute}
              className="w-8 h-8 p-0"
            >
              {isMuted ? <MicOff className="h-3 w-3" /> : <Mic className="h-3 w-3" />}
            </Button>
          )}

          {/* Audio Control */}
          <Button
            variant={isListening ? "default" : "secondary"}
            size="sm"
            onClick={onToggleListening}
            className="w-8 h-8 p-0"
          >
            {isListening ? <Volume2 className="h-3 w-3" /> : <VolumeX className="h-3 w-3" />}
          </Button>

          {/* Expand Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={onMaximize}
            className="flex-1 text-white border-gray-600 hover:bg-gray-800"
          >
            Open Space
          </Button>
        </div>
      </div>
    </div>
  );
};
