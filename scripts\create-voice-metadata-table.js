/**
 * <PERSON><PERSON><PERSON> to create the voice_metadata table in Supabase
 * This table is used to store voice message metadata
 * 
 * Run this script with:
 * node scripts/create-voice-metadata-table.js
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://jcltjkaumevuycntdmds.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Error: Supabase key not found in environment variables');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function createVoiceMetadataTable() {
  try {
    console.log('Creating voice_metadata table in Supabase...');

    // Create the table using SQL
    const { error } = await supabase.rpc('exec_sql', {
      sql_query: `
        CREATE TABLE IF NOT EXISTS voice_metadata (
          id UUID PRIMARY KEY,
          content JSONB NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          deleted_at TIMESTAMP WITH TIME ZONE
        );

        -- Add indexes for better performance
        CREATE INDEX IF NOT EXISTS idx_voice_metadata_created_at ON voice_metadata(created_at);
        CREATE INDEX IF NOT EXISTS idx_voice_metadata_deleted_at ON voice_metadata(deleted_at);
        
        -- Add a GIN index for efficient JSON querying
        CREATE INDEX IF NOT EXISTS idx_voice_metadata_content ON voice_metadata USING GIN (content jsonb_path_ops);

        -- Enable Row Level Security
        ALTER TABLE voice_metadata ENABLE ROW LEVEL SECURITY;

        -- Create policies
        DO $$
        BEGIN
          -- Drop existing policies if they exist
          DROP POLICY IF EXISTS "Allow select for all users" ON voice_metadata;
          DROP POLICY IF EXISTS "Allow insert for authenticated users" ON voice_metadata;
          DROP POLICY IF EXISTS "Allow update for message owners" ON voice_metadata;
          DROP POLICY IF EXISTS "Allow delete for message owners" ON voice_metadata;
          
          -- Create new policies
          CREATE POLICY "Allow select for all users" 
            ON voice_metadata FOR SELECT 
            USING (deleted_at IS NULL);
            
          CREATE POLICY "Allow insert for authenticated users" 
            ON voice_metadata FOR INSERT 
            WITH CHECK (auth.uid() IS NOT NULL);
            
          CREATE POLICY "Allow update for message owners" 
            ON voice_metadata FOR UPDATE 
            USING (
              (content->>'userAddress')::text = auth.uid()::text OR
              EXISTS (
                SELECT 1 FROM profiles
                WHERE profiles.wallet_address = (content->>'userAddress')::text
                AND profiles.id = auth.uid()
              )
            );
            
          CREATE POLICY "Allow delete for message owners" 
            ON voice_metadata FOR DELETE 
            USING (
              (content->>'userAddress')::text = auth.uid()::text OR
              EXISTS (
                SELECT 1 FROM profiles
                WHERE profiles.wallet_address = (content->>'userAddress')::text
                AND profiles.id = auth.uid()
              )
            );
        END
        $$;
      `
    });

    if (error) {
      console.error('Error creating voice_metadata table:', error);
      return false;
    }

    console.log('Successfully created voice_metadata table in Supabase');
    return true;
  } catch (error) {
    console.error('Error creating voice_metadata table:', error);
    return false;
  }
}

// Run the function
createVoiceMetadataTable()
  .then(success => {
    if (success) {
      console.log('Script completed successfully');
      process.exit(0);
    } else {
      console.error('Script failed');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
