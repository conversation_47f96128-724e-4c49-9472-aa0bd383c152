-- Check if voice_reactions table exists
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'voice_reactions') THEN
    -- Table exists, check its structure
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'voice_reactions' AND column_name = 'voice_message_id') THEN
      -- Add voice_message_id column if it doesn't exist
      ALTER TABLE public.voice_reactions ADD COLUMN voice_message_id TEXT NOT NULL;
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'voice_reactions' AND column_name = 'emoji') THEN
      -- Add emoji column if it doesn't exist
      ALTER TABLE public.voice_reactions ADD COLUMN emoji TEXT NOT NULL;
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'voice_reactions' AND column_name = 'profile_id') THEN
      -- Add profile_id column if it doesn't exist
      ALTER TABLE public.voice_reactions ADD COLUMN profile_id TEXT NOT NULL;
    END IF;
  ELSE
    -- Create the table if it doesn't exist
    CREATE TABLE public.voice_reactions (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      voice_message_id TEXT NOT NULL,
      profile_id TEXT NOT NULL,
      emoji TEXT NOT NULL,
      created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
    );
  END IF;
  
  -- Enable RLS on voice_reactions table
  ALTER TABLE public.voice_reactions ENABLE ROW LEVEL SECURITY;
  
  -- Create or replace policies for voice_reactions table
  
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Users can view all voice reactions" ON public.voice_reactions;
  DROP POLICY IF EXISTS "Users can insert their own voice reactions" ON public.voice_reactions;
  DROP POLICY IF EXISTS "Users can delete their own voice reactions" ON public.voice_reactions;
  
  -- Create new policies
  CREATE POLICY "Users can view all voice reactions"
  ON public.voice_reactions
  FOR SELECT
  USING (true);
  
  CREATE POLICY "Users can insert their own voice reactions"
  ON public.voice_reactions
  FOR INSERT
  WITH CHECK (profile_id = auth.uid());
  
  CREATE POLICY "Users can delete their own voice reactions"
  ON public.voice_reactions
  FOR DELETE
  USING (profile_id = auth.uid());
  
  -- Create indexes for better performance
  CREATE INDEX IF NOT EXISTS voice_reactions_voice_message_id_idx ON public.voice_reactions(voice_message_id);
  CREATE INDEX IF NOT EXISTS voice_reactions_profile_id_idx ON public.voice_reactions(profile_id);
  CREATE INDEX IF NOT EXISTS voice_reactions_emoji_idx ON public.voice_reactions(emoji);
END $$;
