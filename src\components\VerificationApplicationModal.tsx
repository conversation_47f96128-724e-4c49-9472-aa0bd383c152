
import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/sonner';
import { VerificationType } from './VerificationBadge';
import VerificationRequirements from './VerificationRequirements';
import { Check, Shield, AlertCircle, Clock, XCircle } from 'lucide-react';
import { submitVerificationApplication, canApplyForVerification } from '@/services/verificationService';
import { formatDistanceToNow, format } from 'date-fns';

interface VerificationApplicationModalProps {
  isOpen: boolean;
  onClose: () => void;
  userAddress: string;
}

const VerificationApplicationModal: React.FC<VerificationApplicationModalProps> = ({
  isOpen,
  onClose,
  userAddress
}) => {
  const [applicationType, setApplicationType] = useState<VerificationType>('creator');
  const [reason, setReason] = useState('');
  const [socialProof, setSocialProof] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [canApply, setCanApply] = useState<boolean | null>(null);
  const [cannotApplyReason, setCannotApplyReason] = useState<string | null>(null);
  const [cooldownEnds, setCooldownEnds] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check if the user can apply for verification
  useEffect(() => {
    const checkEligibility = async () => {
      try {
        setIsLoading(true);
        const { canApply: isEligible, reason, cooldownEnds: cooldownDate } = await canApplyForVerification(userAddress);
        setCanApply(isEligible);
        setCannotApplyReason(reason || null);
        setCooldownEnds(cooldownDate || null);
      } catch (error) {
        console.error('Error checking verification eligibility:', error);
        setCanApply(false);
        setCannotApplyReason('Error checking eligibility. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen && userAddress) {
      checkEligibility();
    }
  }, [isOpen, userAddress]);

  const verificationTypes = [
    { value: 'creator', label: 'Content Creator' },
    { value: 'developer', label: 'Developer' },
    { value: 'community', label: 'Community Leader' },
    { value: 'partner', label: 'Official Partner' },
    { value: 'investor', label: 'Investor' },
    { value: 'early', label: 'Early Adopter' },
    { value: 'artist', label: 'Visual Artist' },
    { value: 'musician', label: 'Musician' },
    { value: 'journalist', label: 'Journalist' },
    { value: 'educator', label: 'Educator' },
    { value: 'nonprofit', label: 'Nonprofit Organization' },
    { value: 'government', label: 'Government Entity' },
    { value: 'celebrity', label: 'Public Figure' }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!reason.trim()) {
      toast.error('Please provide a reason for verification');
      return;
    }

    if (!socialProof.trim()) {
      toast.error('Please provide social proof or links');
      return;
    }

    setIsSubmitting(true);

    try {
      await submitVerificationApplication(
        userAddress,
        applicationType,
        reason,
        socialProof
      );

      toast.success('Verification application submitted successfully');
      onClose();

      // Reset form
      setReason('');
      setSocialProof('');
    } catch (error) {
      console.error('Error submitting verification application:', error);

      // Show specific error message if available
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error('Failed to submit verification application');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
        <DialogTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5 text-primary" />
          Apply for Verification
        </DialogTitle>

        <DialogDescription>
          Verification badges help users identify authentic accounts on Audra.
          Each badge type has specific requirements. Applications are reviewed by our team.
        </DialogDescription>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full" />
            <span className="ml-2">Checking eligibility...</span>
          </div>
        ) : canApply === false ? (
          <div className="bg-destructive/10 p-4 rounded-md my-4">
            <div className="flex items-start">
              <XCircle className="h-5 w-5 text-destructive shrink-0 mt-0.5 mr-2" />
              <div>
                <h4 className="font-medium text-destructive">Cannot Apply for Verification</h4>
                <p className="text-sm mt-1">{cannotApplyReason}</p>

                {cooldownEnds && (
                  <div className="mt-3 bg-background/80 p-2 rounded-md">
                    <p className="text-sm flex items-center">
                      <Clock className="h-4 w-4 mr-1 text-muted-foreground" />
                      You can apply again on {format(new Date(cooldownEnds), 'MMMM d, yyyy')}
                      <span className="text-xs ml-1 text-muted-foreground">
                        ({formatDistanceToNow(new Date(cooldownEnds), { addSuffix: true })})
                      </span>
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : (
          <>
            <div className="bg-secondary/30 p-3 rounded-md mb-4">
              <h4 className="text-sm font-medium mb-1 flex items-center">
                <Shield className="h-4 w-4 mr-1 text-voicechain-accent" />
                Verification Process
              </h4>
              <ol className="text-xs text-muted-foreground space-y-1 ml-5 list-decimal">
                <li>Submit your application with required information</li>
                <li>Our team will review your application (typically within 1-3 days)</li>
                <li>You'll receive a notification when your application is approved or rejected</li>
                <li>If approved, your verification badge will appear on your profile</li>
              </ol>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="verificationType">Verification Type</Label>
                <Select
                  value={applicationType}
                  onValueChange={(value) => setApplicationType(value as VerificationType)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select verification type" />
                  </SelectTrigger>
                  <SelectContent>
                    {verificationTypes.map(type => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Detailed verification requirements */}
              <div className="mt-4 mb-2">
                <VerificationRequirements selectedType={applicationType} />
              </div>

              <div className="space-y-2">
                <Label htmlFor="reason">Why should you be verified?</Label>
                <Textarea
                  id="reason"
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  placeholder="Explain why you qualify for this verification type"
                  className="resize-none"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="socialProof">Social Proof or Links</Label>
                <Textarea
                  id="socialProof"
                  value={socialProof}
                  onChange={(e) => setSocialProof(e.target.value)}
                  placeholder="Provide links to your social media, projects, or other proof"
                  className="resize-none"
                  rows={3}
                />
              </div>

              <DialogFooter className="sticky bottom-0 bg-background pt-2 pb-1 border-t mt-4">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting} className="bg-voicechain-purple hover:bg-voicechain-accent">
                  {isSubmitting ? (
                    <span className="flex items-center gap-1">
                      <span className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                      Submitting...
                    </span>
                  ) : (
                    <span className="flex items-center gap-1">
                      <Check className="h-4 w-4" />
                      Submit Application
                    </span>
                  )}
                </Button>
              </DialogFooter>
            </form>
          </>
        )}

        {/* Footer for ineligible users */}
        {!isLoading && canApply === false && (
          <DialogFooter className="sticky bottom-0 bg-background pt-2 pb-1 border-t mt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Close
            </Button>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default VerificationApplicationModal;
