-- Create a function to get voice messages for a user without using policies
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION get_user_voice_messages(user_id_param TEXT)
RETURNS TABLE (
  id TEXT,
  profile_id TEXT,
  channel_id TEXT,
  parent_id TEXT,
  transcript TEXT,
  audio_url TEXT,
  audio_duration INTEGER,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  is_pinned BOOLEAN,
  deleted_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    vm.id,
    vm.profile_id,
    vm.channel_id,
    vm.parent_id,
    vm.transcript,
    vm.audio_url,
    vm.audio_duration,
    vm.created_at,
    vm.updated_at,
    vm.is_pinned,
    vm.deleted_at
  FROM 
    public.voice_messages vm
  WHERE 
    vm.profile_id = user_id_param
    AND vm.deleted_at IS NULL
  ORDER BY 
    vm.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get voice message media
CREATE OR <PERSON><PERSON>LACE FUNCTION get_voice_message_media(message_id_param TEXT)
RETURNS TABLE (
  id TEXT,
  voice_message_id TEXT,
  url TEXT,
  type TEXT,
  created_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    vmm.id,
    vmm.voice_message_id,
    vmm.url,
    vmm.type,
    vmm.created_at
  FROM 
    public.voice_message_media vmm
  WHERE 
    vmm.voice_message_id = message_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get user journals
CREATE OR REPLACE FUNCTION get_user_journals(user_id_param TEXT)
RETURNS TABLE (
  id TEXT,
  profile_id TEXT,
  title TEXT,
  transcript TEXT,
  audio_url TEXT,
  audio_duration INTEGER,
  is_locked BOOLEAN,
  scheduled_for TIMESTAMPTZ,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    j.id,
    j.profile_id,
    j.title,
    j.transcript,
    j.audio_url,
    j.audio_duration,
    j.is_locked,
    j.scheduled_for,
    j.created_at,
    j.updated_at
  FROM 
    public.journals j
  WHERE 
    j.profile_id = user_id_param
  ORDER BY 
    j.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
