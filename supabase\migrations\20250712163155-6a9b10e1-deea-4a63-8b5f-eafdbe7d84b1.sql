-- Add foreign key constraints to fix the relationship between likes and voice_messages
-- First, check if the relationship exists already
DO $$
BEGIN
  -- Add foreign key constraint between likes and voice_messages if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'likes_voice_message_id_fkey'
  ) THEN
    -- For text-based IDs, we need to handle this carefully
    -- Add the foreign key constraint
    ALTER TABLE likes 
    ADD CONSTRAINT likes_voice_message_id_fkey 
    FOREIGN KEY (voice_message_id) REFERENCES voice_messages(id) ON DELETE CASCADE;
  END IF;
END $$;