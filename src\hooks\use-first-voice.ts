
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export function useFirstVoice(userAddress: string) {
  const [isFirstVoice, setIsFirstVoice] = useState<boolean>(false);
  
  useEffect(() => {
    if (!userAddress) return;

    const checkFirstVoice = async () => {
      try {
        // First, get the profile ID from the wallet address
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('id')
          .ilike('wallet_address', userAddress)
          .single();

        if (profileError || !profileData) {
          console.log('No profile found for user, this will be their first voice');
          setIsFirstVoice(true);
          return;
        }

        // Check if the user has any voice messages in Supabase using profile ID
        const { data, error } = await supabase
          .from('voice_messages')
          .select('id')
          .eq('profile_id', profileData.id)
          .limit(1);

        if (error) {
          console.error('Error checking first voice:', error);
          setIsFirstVoice(true); // Default to true if there's an error
          return;
        }

        // If no messages, this will be their first voice
        setIsFirstVoice(!data || data.length === 0);
      } catch (error) {
        console.error('Error in checkFirstVoice:', error);
        setIsFirstVoice(true);
      }
    };

    checkFirstVoice();
  }, [userAddress]);
  
  const markFirstVoiceRecorded = async () => {
    if (userAddress) {
      // We don't use localStorage, only rely on Supabase
      setIsFirstVoice(false);
    }
  };
  
  return { isFirstVoice, markFirstVoiceRecorded };
}
