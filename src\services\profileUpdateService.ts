/**
 * Profile Update Service - Handles profile updates with proper error handling and storage uploads
 */

import { UserProfile, UserProfileUpdate } from '@/types/user-profile';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/sonner';

interface ProfileUpdateResult {
  success: boolean;
  profile?: UserProfile;
  error?: string;
}

export class ProfileUpdateService {
  /**
   * Upload an image to Supabase storage
   */
  static async uploadImage(file: File, profileAddress: string, imageType: 'profile' | 'cover'): Promise<string> {
    try {
      console.log(`Uploading ${imageType} image for ${profileAddress}`, {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type
      });

      // Create storage bucket if it doesn't exist
      await this.ensureStorageBucket();

      const fileExt = file.name.split('.').pop();
      const fileName = `${profileAddress}/${imageType}-${Date.now()}.${fileExt}`;

      // Upload to storage
      const { data, error } = await supabase.storage
        .from('profiles')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (error) {
        console.error(`Error uploading ${imageType} image:`, error);
        throw new Error(`Failed to upload ${imageType} image: ${error.message}`);
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('profiles')
        .getPublicUrl(data.path);

      console.log(`Successfully uploaded ${imageType} image:`, urlData.publicUrl);
      return urlData.publicUrl;
    } catch (error) {
      console.error(`Error in ${imageType} image upload:`, error);
      throw error;
    }
  }

  /**
   * Ensure the profiles storage bucket exists
   */
  private static async ensureStorageBucket() {
    try {
      // Check if bucket exists
      const { data: buckets, error: listError } = await supabase.storage.listBuckets();
      
      if (listError) {
        console.error('Error listing buckets:', listError);
        return;
      }

      const profilesBucket = buckets?.find(bucket => bucket.name === 'profiles');
      
      if (!profilesBucket) {
        // Create the bucket
        const { error: createError } = await supabase.storage.createBucket('profiles', {
          public: true,
          allowedMimeTypes: ['image/png', 'image/jpeg', 'image/gif', 'image/webp'],
          fileSizeLimit: 5242880 // 5MB
        });

        if (createError) {
          console.error('Error creating profiles bucket:', createError);
        } else {
          console.log('Successfully created profiles storage bucket');
        }
      }
    } catch (error) {
      console.error('Error ensuring storage bucket:', error);
    }
  }

  /**
   * Update profile with proper image handling
   */
  static async updateProfileWithImages(
    address: string,
    update: UserProfileUpdate,
    profileImageFile?: File,
    coverImageFile?: File
  ): Promise<ProfileUpdateResult> {
    try {
      console.log(`Starting profile update for ${address}`, update);

      // Upload images if provided
      const updatedData: UserProfileUpdate = { ...update };

      if (profileImageFile) {
        try {
          updatedData.profileImageUrl = await this.uploadImage(profileImageFile, address, 'profile');
          console.log('Profile image uploaded successfully:', updatedData.profileImageUrl);
        } catch (error) {
          console.error('Profile image upload failed:', error);
          return {
            success: false,
            error: `Failed to upload profile image: ${error.message}`
          };
        }
      }

      if (coverImageFile) {
        try {
          updatedData.coverImageUrl = await this.uploadImage(coverImageFile, address, 'cover');
          console.log('Cover image uploaded successfully:', updatedData.coverImageUrl);
        } catch (error) {
          console.error('Cover image upload failed:', error);
          return {
            success: false,
            error: `Failed to upload cover image: ${error.message}`
          };
        }
      }

      // Update profile in database
      const result = await this.updateProfileInDatabase(address, updatedData);
      
      if (result.success) {
        console.log('Profile updated successfully in database');
        return result;
      } else {
        console.error('Database update failed:', result.error);
        return result;
      }
    } catch (error) {
      console.error('Profile update failed:', error);
      return {
        success: false,
        error: `Profile update failed: ${error.message}`
      };
    }
  }

  /**
   * Update profile in database using the edge function
   */
  private static async updateProfileInDatabase(address: string, update: UserProfileUpdate): Promise<ProfileUpdateResult> {
    try {
      // Call the update-profile edge function
      const { data, error } = await supabase.functions.invoke('update-profile', {
        body: {
          wallet_address: address,
          username: update.username,
          display_name: update.displayName,
          bio: update.bio,
          avatar_url: update.profileImageUrl,
          cover_image_url: update.coverImageUrl,
          social_links: update.socialLinks
        }
      });

      if (error) {
        console.error('Edge function error:', error);
        return {
          success: false,
          error: `Database update failed: ${error.message}`
        };
      }

      if (data?.success) {
        console.log('Edge function returned success:', data);
        
        // Convert response to UserProfile format
        const profileData = data.data[0];
        const profile: UserProfile = {
          address: address,
          walletAddress: profileData.wallet_address,
          username: profileData.username,
          displayName: profileData.display_name,
          bio: profileData.bio || '',
          profileImageUrl: profileData.avatar_url || '',
          coverImageUrl: profileData.cover_image_url || '',
          socialLinks: profileData.social_links || {},
          stats: {
            posts: profileData.post_count || 0,
            likes: profileData.like_count || 0,
            tips: profileData.tip_count || 0
          },
          joinedDate: new Date(profileData.created_at),
          verification: {
            isVerified: !!profileData.verification_type,
            type: profileData.verification_type,
            since: profileData.verified_at ? new Date(profileData.verified_at) : undefined
          }
        };

        return {
          success: true,
          profile: profile
        };
      } else {
        return {
          success: false,
          error: 'Database update failed: No success response'
        };
      }
    } catch (error) {
      console.error('Database update error:', error);
      return {
        success: false,
        error: `Database update failed: ${error.message}`
      };
    }
  }
}

export default ProfileUpdateService;