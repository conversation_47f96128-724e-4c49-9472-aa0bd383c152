import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import {
  Wand2,
  Music,
  Volume,
  Mic,
  Radio,
  Waves,
  VolumeX,
  Sparkles,
  Zap,
  Megaphone,
  Headphones
} from 'lucide-react';

export type VoiceFilter = {
  id: string;
  name: string;
  icon: React.ReactNode;
  description: string;
  audioParams: {
    pitch?: number;
    echo?: number;
    reverb?: number;
    distortion?: number;
    bassBoost?: number;
    noiseReduction?: number;
  };
};

export const voiceFilters: VoiceFilter[] = [
  {
    id: 'normal',
    name: 'Normal',
    icon: <Mic className="h-4 w-4" />,
    description: 'Your natural voice without effects',
    audioParams: {}
  },
  {
    id: 'deep',
    name: 'Deep Voice',
    icon: <Volume className="h-4 w-4" />,
    description: 'Lower pitch for a deeper voice',
    audioParams: { pitch: -5, bassBoost: 6 }
  },
  {
    id: 'high',
    name: 'High Pitch',
    icon: <Megaphone className="h-4 w-4" />,
    description: 'Higher pitch for a lighter voice',
    audioParams: { pitch: 5 }
  },
  {
    id: 'radio',
    name: 'Radio',
    icon: <Radio className="h-4 w-4" />,
    description: 'Classic radio announcer effect',
    audioParams: { distortion: 2, echo: 0.2 }
  },
  {
    id: 'echo',
    name: 'Echo',
    icon: <Waves className="h-4 w-4" />,
    description: 'Add echo to your voice',
    audioParams: { echo: 0.5 }
  },
  {
    id: 'reverb',
    name: 'Reverb',
    icon: <Music className="h-4 w-4" />,
    description: 'Add spacious reverb effect',
    audioParams: { reverb: 0.6 }
  },
  {
    id: 'clear',
    name: 'Crystal Clear',
    icon: <Sparkles className="h-4 w-4" />,
    description: 'Enhanced clarity with noise reduction',
    audioParams: { noiseReduction: 0.8 }
  },
  {
    id: 'robot',
    name: 'Robot',
    icon: <Zap className="h-4 w-4" />,
    description: 'Robotic voice effect',
    audioParams: { distortion: 4, echo: 0.1 }
  },
  {
    id: 'asmr',
    name: 'ASMR',
    icon: <Headphones className="h-4 w-4" />,
    description: 'Optimized for ASMR recording',
    audioParams: { bassBoost: 3, noiseReduction: 0.9 }
  },
  {
    id: 'muffled',
    name: 'Muffled',
    icon: <VolumeX className="h-4 w-4" />,
    description: 'Sounds like talking through a wall',
    audioParams: { distortion: 1, reverb: 0.3 }
  }
];

interface VoiceFilterSelectorProps {
  selectedFilter: string;
  onFilterChange: (filterId: string) => void;
  onCustomFilterChange?: (params: VoiceFilter['audioParams']) => void;
}

const VoiceFilterSelector: React.FC<VoiceFilterSelectorProps> = ({
  selectedFilter,
  onFilterChange,
  onCustomFilterChange
}) => {
  const [customParams, setCustomParams] = React.useState<VoiceFilter['audioParams']>({
    pitch: 0,
    echo: 0,
    reverb: 0,
    distortion: 0,
    bassBoost: 0,
    noiseReduction: 0
  });

  const handleCustomParamChange = (param: keyof VoiceFilter['audioParams'], value: number) => {
    const newParams = { ...customParams, [param]: value };
    setCustomParams(newParams);
    if (onCustomFilterChange) {
      onCustomFilterChange(newParams);
    }
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2 mb-2">
        <Wand2 className="h-4 w-4 text-voicechain-purple" />
        <h3 className="text-sm font-medium">Voice Filters</h3>
      </div>

      <Tabs defaultValue="preset" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="preset" className="text-xs">Preset Filters</TabsTrigger>
          <TabsTrigger value="custom" className="text-xs">Custom Filter</TabsTrigger>
        </TabsList>
        
        <TabsContent value="preset" className="mt-2">
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
            {voiceFilters.map((filter) => (
              <Button
                key={filter.id}
                variant={selectedFilter === filter.id ? "default" : "outline"}
                size="sm"
                className={`flex items-center justify-start gap-2 h-auto py-2 px-3 ${
                  selectedFilter === filter.id ? "bg-voicechain-purple text-white" : ""
                }`}
                onClick={() => onFilterChange(filter.id)}
              >
                {filter.icon}
                <span className="text-xs">{filter.name}</span>
              </Button>
            ))}
          </div>
          
          <p className="text-xs text-muted-foreground mt-2">
            {voiceFilters.find(f => f.id === selectedFilter)?.description || 'Select a voice filter'}
          </p>
        </TabsContent>
        
        <TabsContent value="custom" className="mt-2 space-y-3">
          <div className="space-y-3">
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-xs font-medium">Pitch</label>
                <span className="text-xs text-muted-foreground">{customParams.pitch}</span>
              </div>
              <Slider
                value={[customParams.pitch || 0]}
                min={-10}
                max={10}
                step={1}
                onValueChange={(value) => handleCustomParamChange('pitch', value[0])}
              />
            </div>
            
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-xs font-medium">Echo</label>
                <span className="text-xs text-muted-foreground">{customParams.echo?.toFixed(1)}</span>
              </div>
              <Slider
                value={[customParams.echo || 0]}
                min={0}
                max={1}
                step={0.1}
                onValueChange={(value) => handleCustomParamChange('echo', value[0])}
              />
            </div>
            
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-xs font-medium">Reverb</label>
                <span className="text-xs text-muted-foreground">{customParams.reverb?.toFixed(1)}</span>
              </div>
              <Slider
                value={[customParams.reverb || 0]}
                min={0}
                max={1}
                step={0.1}
                onValueChange={(value) => handleCustomParamChange('reverb', value[0])}
              />
            </div>
            
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-xs font-medium">Distortion</label>
                <span className="text-xs text-muted-foreground">{customParams.distortion?.toFixed(1)}</span>
              </div>
              <Slider
                value={[customParams.distortion || 0]}
                min={0}
                max={5}
                step={0.1}
                onValueChange={(value) => handleCustomParamChange('distortion', value[0])}
              />
            </div>
            
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-xs font-medium">Bass Boost</label>
                <span className="text-xs text-muted-foreground">{customParams.bassBoost?.toFixed(1)}</span>
              </div>
              <Slider
                value={[customParams.bassBoost || 0]}
                min={0}
                max={10}
                step={0.1}
                onValueChange={(value) => handleCustomParamChange('bassBoost', value[0])}
              />
            </div>
            
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-xs font-medium">Noise Reduction</label>
                <span className="text-xs text-muted-foreground">{customParams.noiseReduction?.toFixed(1)}</span>
              </div>
              <Slider
                value={[customParams.noiseReduction || 0]}
                min={0}
                max={1}
                step={0.1}
                onValueChange={(value) => handleCustomParamChange('noiseReduction', value[0])}
              />
            </div>
          </div>
          
          <Button 
            size="sm" 
            variant="outline" 
            className="w-full text-xs"
            onClick={() => {
              setCustomParams({
                pitch: 0,
                echo: 0,
                reverb: 0,
                distortion: 0,
                bassBoost: 0,
                noiseReduction: 0
              });
              if (onCustomFilterChange) {
                onCustomFilterChange({});
              }
            }}
          >
            Reset to Default
          </Button>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default VoiceFilterSelector;
