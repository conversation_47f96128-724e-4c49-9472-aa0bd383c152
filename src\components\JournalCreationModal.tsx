import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { CalendarIcon, Lock, Clock, Users, FileText, Image, Eye, EyeOff, Globe, DollarSign, UserPlus, X } from 'lucide-react';
import AudioRecorder from './AudioRecorder';
import { MediaFile } from './MediaUploader';
import { toast } from '@/components/ui/sonner';
import { JournalEntry } from '@/types/journal';

export type UnlockConditionType = 'time' | 'token' | 'event';

interface JournalCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (journal: JournalEntry) => void;
  userAddress: string;
}

const JournalCreationModal: React.FC<JournalCreationModalProps> = ({
  isOpen,
  onClose,
  onSave,
  userAddress,
}) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [privacyLevel, setPrivacyLevel] = useState<'my_journal' | 'public' | 'locked_public' | 'private' | 'locked_private'>('public');
  const [summonedUsers, setSummonedUsers] = useState<string[]>([]);
  const [tipToUnlockAmount, setTipToUnlockAmount] = useState<number>(0.1);
  const [tipToUnlockCurrency, setTipToUnlockCurrency] = useState<string>('SOL');
  const [summonUserInput, setSummonUserInput] = useState<string>('');
  const [unlockType, setUnlockType] = useState<UnlockConditionType>('time');
  const [unlockDate, setUnlockDate] = useState<Date | undefined>(undefined);
  const [tokenAddress, setTokenAddress] = useState('');
  const [eventId, setEventId] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [recordingComplete, setRecordingComplete] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [transcript, setTranscript] = useState('');
  const [audioDuration, setAudioDuration] = useState(0);
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([]);

  const handleRecordingStart = () => {
    setIsRecording(true);
    setRecordingComplete(false);
  };

  const handleRecordingComplete = (blob: Blob, text: string, duration: number, media?: MediaFile[]) => {
    setAudioBlob(blob);
    setTranscript(text);
    setAudioDuration(duration);
    setIsRecording(false);
    setRecordingComplete(true);

    // Update media files if provided
    if (media && media.length > 0) {
      setMediaFiles(media);
    }
  };

  const handleSave = () => {
    if (!title) {
      toast.error('Please enter a title');
      return;
    }

    if (!audioBlob) {
      toast.error('Please record a voice journal entry');
      return;
    }

    if (unlockType === 'time' && !unlockDate) {
      toast.error('Please select an unlock date');
      return;
    }

    if (unlockType === 'token' && !tokenAddress) {
      toast.error('Please enter a token address');
      return;
    }

    if (unlockType === 'event' && !eventId) {
      toast.error('Please enter an event ID');
      return;
    }

    // Create object URL for the audio blob
    const audioUrl = URL.createObjectURL(audioBlob);

    // Create a new journal entry (using legacy format until database is updated)
    const isLocked = (unlockType === 'time' && unlockDate && unlockDate > new Date()) ||
                     (unlockType === 'token' && tokenAddress) ||
                     (unlockType === 'event' && eventId);

    const journalEntry: JournalEntry = {
      id: crypto.randomUUID(),
      title,
      description,
      audioUrl,
      transcript,
      createdAt: new Date(),
      userAddress,
      privacyLevel,
      isLocked,
      isUnlocked: !isLocked,
      isPrivate: privacyLevel === 'private' || privacyLevel === 'locked_private', // Legacy field
      summonedUsers: (privacyLevel === 'private' || privacyLevel === 'locked_private') ? summonedUsers : undefined,
      tipToUnlockAmount: privacyLevel === 'locked_private' ? tipToUnlockAmount : undefined,
      tipToUnlockCurrency: privacyLevel === 'locked_private' ? tipToUnlockCurrency : undefined,
      unlockCondition: {
        type: unlockType,
        value: unlockType === 'time'
          ? unlockDate?.toISOString() || ''
          : unlockType === 'token'
            ? tokenAddress
            : eventId,
        unlockDate,
        tokenAddress: unlockType === 'token' ? tokenAddress : undefined,
        eventId: unlockType === 'event' ? eventId : undefined,
      },
      duration: audioDuration,
      media: mediaFiles.length > 0 ? mediaFiles : undefined,
    };

    onSave(journalEntry);
    resetForm();
    onClose();
  };

  const resetForm = () => {
    setTitle('');
    setDescription('');
    setPrivacyLevel('public');
    setSummonedUsers([]);
    setTipToUnlockAmount(0.1);
    setSummonUserInput('');
    setUnlockType('time');
    setUnlockDate(undefined);
    setTokenAddress('');
    setEventId('');
    setAudioBlob(null);
    setTranscript('');
    setRecordingComplete(false);
    setIsRecording(false);

    // Clear media files and revoke object URLs
    mediaFiles.forEach(media => {
      URL.revokeObjectURL(media.url);
    });
    setMediaFiles([]);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) {
        resetForm();
        onClose();
      }
    }}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText size={18} className="text-voicechain-purple" />
            Create Voice Journal
          </DialogTitle>
          <DialogDescription>
            Record a voice journal entry that will be stored on-chain with custom unlock conditions.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="My Voice Journal"
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="description">Description (optional)</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="A brief description of this journal entry..."
              className="resize-none"
              rows={2}
            />
          </div>

          <div className="grid gap-2">
            <Label>Privacy Level</Label>
            <Select value={privacyLevel} onValueChange={(value) => setPrivacyLevel(value as any)}>
              <SelectTrigger>
                <SelectValue placeholder="Select privacy level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="my_journal">
                  <div className="flex items-center gap-2">
                    <EyeOff size={14} />
                    <div>
                      <div className="font-medium">My Journal</div>
                      <div className="text-xs text-muted-foreground">Only visible to me</div>
                    </div>
                  </div>
                </SelectItem>
                <SelectItem value="public">
                  <div className="flex items-center gap-2">
                    <Globe size={14} />
                    <div>
                      <div className="font-medium">Public</div>
                      <div className="text-xs text-muted-foreground">Visible to everyone, unlocked immediately</div>
                    </div>
                  </div>
                </SelectItem>
                <SelectItem value="locked_public">
                  <div className="flex items-center gap-2">
                    <Clock size={14} />
                    <div>
                      <div className="font-medium">Locked Public</div>
                      <div className="text-xs text-muted-foreground">Visible to everyone, unlocks on schedule</div>
                    </div>
                  </div>
                </SelectItem>
                <SelectItem value="private">
                  <div className="flex items-center gap-2">
                    <Users size={14} />
                    <div>
                      <div className="font-medium">Private</div>
                      <div className="text-xs text-muted-foreground">Only summoned users can access</div>
                    </div>
                  </div>
                </SelectItem>
                <SelectItem value="locked_private">
                  <div className="flex items-center gap-2">
                    <DollarSign size={14} />
                    <div>
                      <div className="font-medium">Locked Private</div>
                      <div className="text-xs text-muted-foreground">Tip to unlock or be summoned</div>
                    </div>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Summon Users for Private Journals */}
          {(privacyLevel === 'private' || privacyLevel === 'locked_private') && (
            <div className="grid gap-2">
              <Label>Summon Users</Label>
              <div className="space-y-2">
                <div className="flex gap-2">
                  <Input
                    placeholder="Enter user ID or wallet address"
                    value={summonUserInput}
                    onChange={(e) => setSummonUserInput(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter' && summonUserInput.trim()) {
                        setSummonedUsers([...summonedUsers, summonUserInput.trim()]);
                        setSummonUserInput('');
                      }
                    }}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (summonUserInput.trim()) {
                        setSummonedUsers([...summonedUsers, summonUserInput.trim()]);
                        setSummonUserInput('');
                      }
                    }}
                  >
                    <UserPlus size={14} />
                  </Button>
                </div>
                {summonedUsers.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {summonedUsers.map((user, index) => (
                      <div key={index} className="flex items-center gap-1 bg-secondary px-2 py-1 rounded text-xs">
                        <span>{user}</span>
                        <button
                          type="button"
                          onClick={() => setSummonedUsers(summonedUsers.filter((_, i) => i !== index))}
                          className="text-muted-foreground hover:text-foreground"
                        >
                          <X size={12} />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Tip Amount for Locked Private Journals */}
          {privacyLevel === 'locked_private' && (
            <div className="grid gap-2">
              <Label>Tip to Unlock Amount</Label>
              <div className="flex gap-2">
                <Input
                  type="number"
                  step="0.01"
                  min="0"
                  value={tipToUnlockAmount}
                  onChange={(e) => setTipToUnlockAmount(parseFloat(e.target.value) || 0)}
                  placeholder="0.1"
                />
                <Select value={tipToUnlockCurrency} onValueChange={setTipToUnlockCurrency}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="SOL">SOL</SelectItem>
                    <SelectItem value="USDC">USDC</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <p className="text-xs text-muted-foreground">
                Users can pay this amount to unlock your private journal
              </p>
            </div>
          )}

          <div className="grid gap-2">
            <Label>Unlock Condition</Label>
            <Select value={unlockType} onValueChange={(value) => setUnlockType(value as UnlockConditionType)}>
              <SelectTrigger>
                <SelectValue placeholder="Select unlock condition" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="time" className="flex items-center gap-2">
                  <div className="flex items-center gap-2">
                    <Clock size={14} />
                    <span>Time-based</span>
                  </div>
                </SelectItem>
                <SelectItem value="token">
                  <div className="flex items-center gap-2">
                    <Users size={14} />
                    <span>Token-based</span>
                  </div>
                </SelectItem>
                <SelectItem value="event">
                  <div className="flex items-center gap-2">
                    <FileText size={14} />
                    <span>Event-based</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {unlockType === 'time' && (
            <div className="grid gap-2">
              <Label>Unlock Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={`w-full justify-start text-left font-normal ${!unlockDate && 'text-muted-foreground'}`}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {unlockDate ? format(unlockDate, 'PPP') : 'Select a date'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={unlockDate}
                    onSelect={setUnlockDate}
                    initialFocus
                    disabled={(date) => date < new Date()}
                  />
                </PopoverContent>
              </Popover>
            </div>
          )}

          {unlockType === 'token' && (
            <div className="grid gap-2">
              <Label htmlFor="tokenAddress">Token Address</Label>
              <Input
                id="tokenAddress"
                value={tokenAddress}
                onChange={(e) => setTokenAddress(e.target.value)}
                placeholder="Enter token address for gated access"
              />
              <p className="text-xs text-muted-foreground">
                Only holders of this token will be able to access this journal entry.
              </p>
            </div>
          )}

          {unlockType === 'event' && (
            <div className="grid gap-2">
              <Label htmlFor="eventId">Event ID</Label>
              <Input
                id="eventId"
                value={eventId}
                onChange={(e) => setEventId(e.target.value)}
                placeholder="Enter on-chain event ID"
              />
              <p className="text-xs text-muted-foreground">
                This journal will unlock when the specified on-chain event occurs.
              </p>
            </div>
          )}

          <div className="grid gap-2">
            <Label>Record Your Journal Entry</Label>
            <div className="bg-secondary/50 rounded-lg p-4">
              <AudioRecorder
                onRecordingStart={handleRecordingStart}
                onRecordingComplete={handleRecordingComplete}
                placeholder="Record your voice journal..."
                maxMediaFiles={4}
              />

              {recordingComplete && transcript && (
                <div className="mt-4 p-3 bg-background rounded-md">
                  <p className="text-sm font-medium mb-1">Transcript:</p>
                  <p className="text-sm">{transcript}</p>
                </div>
              )}

              {/* Show media count if any media is attached */}
              {mediaFiles.length > 0 && (
                <div className="mt-3 flex items-center text-sm text-muted-foreground">
                  <Image size={16} className="mr-2" />
                  <span>{mediaFiles.length} {mediaFiles.length === 1 ? 'media file' : 'media files'} attached</span>
                </div>
              )}
            </div>
          </div>
        </div>

        <DialogFooter className="flex justify-between sm:justify-between sticky bottom-0 bg-background pt-2 pb-1 border-t mt-4">
          <Button
            variant="outline"
            onClick={() => {
              resetForm();
              onClose();
            }}
          >
            Cancel
          </Button>
          <Button
            disabled={!recordingComplete || isRecording || !title}
            onClick={handleSave}
            className="bg-voicechain-purple hover:bg-voicechain-accent"
          >
            Save Journal
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default JournalCreationModal;
