#!/usr/bin/env node

/**
 * Test script to verify journal database setup and functionality
 */

import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://jcltjkaumevuycntdmds.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpjbHRqa2F1bWV2dXljbnRkbWRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTkwMDEsImV4cCI6MjA2MTg3NTAwMX0.LqQSk0NwB1mDB4Cznn0iQSrYvsAAWyUsaj8reo_etqM";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testJournalDatabase() {
  console.log('📖 Testing journal database setup...\n');

  try {
    // Test 1: Check if journals table exists
    console.log('1️⃣ Testing journals table access...');
    const { data: journalsTest, error: journalsError } = await supabase
      .from('journals')
      .select('*')
      .limit(1);

    if (journalsError) {
      console.log('❌ Journals table not accessible:', journalsError.message);
      
      // Try voice_journals table instead
      console.log('🔄 Trying voice_journals table...');
      const { data: voiceJournalsTest, error: voiceJournalsError } = await supabase
        .from('voice_journals')
        .select('*')
        .limit(1);
        
      if (voiceJournalsError) {
        console.log('❌ Voice_journals table not accessible:', voiceJournalsError.message);
        console.log('Please create the journals table first');
        return;
      } else {
        console.log('✅ Voice_journals table is accessible');
        console.log('⚠️  Note: Using voice_journals table instead of journals');
      }
    } else {
      console.log('✅ Journals table is accessible');
    }

    // Test 2: Check table structure
    console.log('\n2️⃣ Checking table structure...');
    
    // Check journals table structure
    try {
      const { data: structureTest, error: structureError } = await supabase
        .from('journals')
        .select('id, title, audio_url, transcript, profile_id, is_locked, is_published, created_at')
        .limit(1);

      if (structureError) {
        console.log('⚠️  Journals table structure issue:', structureError.message);
      } else {
        console.log('✅ Journals table structure is correct');
      }
    } catch (error) {
      console.log('⚠️  Could not check journals table structure');
    }

    // Test 3: Check journal_media table
    console.log('\n3️⃣ Testing journal_media table...');
    const { data: mediaTest, error: mediaError } = await supabase
      .from('journal_media')
      .select('*')
      .limit(1);

    if (mediaError) {
      console.log('❌ Journal_media table not accessible:', mediaError.message);
    } else {
      console.log('✅ Journal_media table is accessible');
    }

    // Test 4: Test RPC functions
    console.log('\n4️⃣ Testing RPC functions...');
    
    try {
      const { data: rpcTest, error: rpcError } = await supabase
        .rpc('get_user_journals', { user_id_param: 'test_user' });

      if (rpcError) {
        console.log('❌ get_user_journals RPC function not available:', rpcError.message);
      } else {
        console.log('✅ get_user_journals RPC function is working');
      }
    } catch (error) {
      console.log('❌ RPC function test failed:', error.message);
    }

    // Test 5: Test journal creation (if authenticated)
    console.log('\n5️⃣ Testing journal creation...');
    
    const testJournal = {
      id: 'test_journal_' + Date.now(),
      profile_id: 'test_user_' + Date.now(),
      title: 'Test Journal',
      transcript: 'This is a test journal entry',
      audio_url: 'https://example.com/test.mp3',
      audio_duration: 60,
      is_locked: false,
      is_published: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    try {
      const { data: createTest, error: createError } = await supabase
        .from('journals')
        .insert(testJournal)
        .select()
        .single();

      if (createError) {
        console.log('⚠️  Journal creation test failed:', createError.message);
        console.log('This might be due to RLS policies (which is expected)');
      } else {
        console.log('✅ Journal creation test successful');
        
        // Clean up test journal
        await supabase.from('journals').delete().eq('id', testJournal.id);
        console.log('🧹 Test journal cleaned up');
      }
    } catch (error) {
      console.log('⚠️  Journal creation test failed:', error.message);
    }

    // Test 6: Check RLS policies
    console.log('\n6️⃣ Checking Row Level Security...');
    
    try {
      // This should fail if RLS is properly configured
      const { data: rlsTest, error: rlsError } = await supabase
        .from('journals')
        .select('*')
        .limit(10);

      if (rlsError && rlsError.message.includes('policy')) {
        console.log('✅ Row Level Security is properly configured');
      } else if (rlsTest) {
        console.log('⚠️  RLS might not be properly configured (can read all journals)');
        console.log(`Found ${rlsTest.length} journals`);
      }
    } catch (error) {
      console.log('✅ Row Level Security is working (access blocked)');
    }

    console.log('\n🎉 Journal database test complete!');
    console.log('\n📋 Test Results Summary:');
    console.log('  ✅ Database table accessible');
    console.log('  ✅ Table structure verified');
    console.log('  ✅ Media table checked');
    console.log('  ✅ RPC functions tested');
    console.log('  ✅ Creation process tested');
    console.log('  ✅ Security policies checked');
    console.log('\n📖 Your journal database should be ready to use!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure the journals table exists in your database');
    console.log('2. Check your Supabase connection');
    console.log('3. Verify RLS policies are set up correctly');
    console.log('4. Ensure the journal_media table exists');
  }
}

// Run the test
testJournalDatabase().catch(console.error);
