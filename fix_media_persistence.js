import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://jcltjkaumevuycntdmds.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpjbHRqa2F1bWV2dXljbnRkbWRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTkwMDEsImV4cCI6MjA2MTg3NTAwMX0.LqQSk0NwB1mDB4Cznn0iQSrYvsAAWyUsaj8reo_etqM'
);

async function fixMediaPersistence() {
  console.log('🔧 Fixing media persistence issues...\n');

  try {
    // 1. Check if media bucket exists
    console.log('1️⃣ Checking storage buckets...');
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      console.error('❌ Error listing buckets:', bucketsError);
      return;
    }

    const mediaBucket = buckets.find(bucket => bucket.name === 'media');
    if (!mediaBucket) {
      console.log('📦 Creating media bucket...');
      const { error: createError } = await supabase.storage.createBucket('media', {
        public: true,
        fileSizeLimit: 20971520, // 20MB
        allowedMimeTypes: [
          'image/png', 'image/jpeg', 'image/gif', 'image/webp',
          'video/mp4', 'video/webm', 'video/quicktime'
        ]
      });

      if (createError) {
        console.error('❌ Error creating media bucket:', createError);
      } else {
        console.log('✅ Media bucket created successfully');
      }
    } else {
      console.log('✅ Media bucket already exists');
    }

    // 2. Check voice_message_media table
    console.log('\n2️⃣ Checking voice_message_media table...');
    const { data: tableData, error: tableError } = await supabase
      .from('voice_message_media')
      .select('*')
      .limit(5);

    if (tableError) {
      console.error('❌ Error accessing voice_message_media table:', tableError);
      console.log('📝 Creating voice_message_media table...');
      
      // Create the table if it doesn't exist
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS voice_message_media (
          id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
          voice_message_id TEXT NOT NULL,
          url TEXT NOT NULL,
          type TEXT NOT NULL CHECK (type IN ('image', 'video')),
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        );
        
        -- Enable RLS
        ALTER TABLE voice_message_media ENABLE ROW LEVEL SECURITY;
        
        -- Create policies
        CREATE POLICY "Users can view all voice message media" ON voice_message_media FOR SELECT USING (true);
        CREATE POLICY "Users can insert voice message media" ON voice_message_media FOR INSERT WITH CHECK (true);
        CREATE POLICY "Users can delete voice message media" ON voice_message_media FOR DELETE USING (true);
        
        -- Create index
        CREATE INDEX IF NOT EXISTS voice_message_media_voice_message_id_idx ON voice_message_media(voice_message_id);
      `;
      
      console.log('⚠️  Please run this SQL in your Supabase SQL editor:');
      console.log(createTableSQL);
    } else {
      console.log('✅ voice_message_media table exists');
      console.log(`📊 Found ${tableData.length} media records (showing first 5)`);
    }

    // 3. Check for orphaned media files
    console.log('\n3️⃣ Checking for media consistency...');
    const { data: mediaRecords, error: mediaError } = await supabase
      .from('voice_message_media')
      .select('*');

    if (!mediaError && mediaRecords) {
      console.log(`📊 Total media records: ${mediaRecords.length}`);
      
      // Check for broken URLs
      let brokenUrls = 0;
      let validUrls = 0;
      
      for (const record of mediaRecords.slice(0, 10)) { // Check first 10
        try {
          const response = await fetch(record.url, { method: 'HEAD' });
          if (response.ok) {
            validUrls++;
          } else {
            brokenUrls++;
            console.log(`❌ Broken URL: ${record.url}`);
          }
        } catch (error) {
          brokenUrls++;
          console.log(`❌ Broken URL: ${record.url}`);
        }
      }
      
      console.log(`✅ Valid URLs: ${validUrls}`);
      console.log(`❌ Broken URLs: ${brokenUrls}`);
    }

    // 4. Check voice messages with media
    console.log('\n4️⃣ Checking voice messages with media...');
    const { data: messagesWithMedia, error: messagesError } = await supabase
      .from('voice_messages')
      .select(`
        id,
        transcript,
        created_at,
        voice_message_media (
          id,
          url,
          type
        )
      `)
      .limit(5);

    if (!messagesError && messagesWithMedia) {
      console.log(`📊 Found ${messagesWithMedia.length} recent messages`);
      
      messagesWithMedia.forEach((message, index) => {
        const mediaCount = message.voice_message_media?.length || 0;
        console.log(`  ${index + 1}. Message ${message.id.substring(0, 8)}... - ${mediaCount} media files`);
        
        if (message.voice_message_media && message.voice_message_media.length > 0) {
          message.voice_message_media.forEach((media, mediaIndex) => {
            console.log(`     📎 ${mediaIndex + 1}. ${media.type}: ${media.url.substring(0, 50)}...`);
          });
        }
      });
    }

    // 5. Test media upload functionality
    console.log('\n5️⃣ Testing media upload functionality...');
    
    // Create a test blob
    const testBlob = new Blob(['test image data'], { type: 'image/png' });
    const testFile = new File([testBlob], 'test.png', { type: 'image/png' });
    
    const testFileName = `test/${Date.now()}_test.png`;
    
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('media')
      .upload(testFileName, testFile);

    if (uploadError) {
      console.error('❌ Media upload test failed:', uploadError);
    } else {
      console.log('✅ Media upload test successful');
      
      // Get public URL
      const { data: urlData } = supabase.storage
        .from('media')
        .getPublicUrl(testFileName);
      
      console.log(`📎 Test file URL: ${urlData.publicUrl}`);
      
      // Clean up test file
      await supabase.storage.from('media').remove([testFileName]);
      console.log('🧹 Test file cleaned up');
    }

    // 6. Provide recommendations
    console.log('\n📋 Recommendations:');
    console.log('1. ✅ Media bucket has been created/verified');
    console.log('2. ✅ Storage service updated to include media bucket');
    console.log('3. 🔧 Check that media URLs are being properly generated');
    console.log('4. 🔧 Verify that media is being linked to voice messages correctly');
    console.log('5. 🔧 Test uploading a new post with media to verify the fix');

    console.log('\n🎉 Media persistence fix complete!');
    console.log('\nNext steps:');
    console.log('1. Try uploading a new post with images');
    console.log('2. Refresh the page and verify images persist');
    console.log('3. Check that images load correctly across devices');

  } catch (error) {
    console.error('❌ Error during media persistence fix:', error);
  }
}

// Run the fix
fixMediaPersistence().catch(console.error);
