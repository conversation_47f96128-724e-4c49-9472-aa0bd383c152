# Audra Admin Dashboard Setup Guide

This guide will help you set up the Audra Admin Dashboard, a separate application for managing the Audra voice-based Web3 social platform.

## Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Supabase account with access to the Audra project

## Setup Steps

### 1. Clone the Repository

```bash
git clone <repository-url>
cd admin-dashboard
```

### 2. Install Dependencies

```bash
npm install
# or
yarn install
```

### 3. Configure Environment Variables

Create a `.env` file in the root directory with the following variables:

```
VITE_SUPABASE_URL=https://jcltjkaumevuycntdmds.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Wy-QQBnxvBuHXJKhgvQxXSIGCME9Jvl3Tz0vZnXlx0c
```

### 4. Set Up Supabase Tables

You'll need to create the following tables in your Supabase project:

#### admin_profiles

This table stores information about admin users:

```sql
CREATE TABLE admin_profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  email TEXT NOT NULL,
  role TEXT NOT NULL DEFAULT 'admin',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies
ALTER TABLE admin_profiles ENABLE ROW LEVEL SECURITY;

-- Only allow admins to read admin profiles
CREATE POLICY "Admins can read all admin profiles" 
ON admin_profiles FOR SELECT 
USING (auth.uid() IN (SELECT id FROM admin_profiles));

-- Only allow super_admins to insert/update/delete admin profiles
CREATE POLICY "Super admins can manage admin profiles" 
ON admin_profiles FOR ALL 
USING (auth.uid() IN (SELECT id FROM admin_profiles WHERE role = 'super_admin'));
```

### 5. Create an Admin User

1. Create a user in Supabase Auth:
   - Go to Authentication > Users in the Supabase dashboard
   - Click "Add User" and enter the admin's email and password

2. Add the user to the admin_profiles table:
   - Go to Table Editor > admin_profiles
   - Click "Insert" and add a new record with:
     - id: The UUID of the user you just created
     - email: The admin's email
     - role: 'super_admin' for the first user, 'admin' for others

### 6. Run the Development Server

```bash
npm run dev
# or
yarn dev
```

The admin dashboard should now be running at http://localhost:5173

### 7. Build for Production

```bash
npm run build
# or
yarn build
```

The built files will be in the `dist` directory, which you can deploy to your hosting provider.

## Security Considerations

1. **Separate Deployment**: Deploy the admin dashboard on a separate domain from the main application.

2. **IP Restrictions**: Consider setting up IP restrictions in your hosting environment to limit access to trusted IP addresses.

3. **Two-Factor Authentication**: Implement 2FA for admin accounts for additional security.

4. **Audit Logging**: Implement comprehensive logging for all administrative actions.

## Troubleshooting

### Authentication Issues

If you're having trouble logging in:

1. Check that the user exists in Supabase Auth
2. Verify the user has a record in the admin_profiles table
3. Ensure the VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are correct

### Database Access Issues

If you're seeing database errors:

1. Check that the RLS policies are correctly set up
2. Verify the admin user has the correct role in the admin_profiles table
3. Check the Supabase logs for any SQL errors

## Support

For additional help, please contact the development team.
