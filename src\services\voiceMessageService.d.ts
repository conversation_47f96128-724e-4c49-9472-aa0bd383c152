// Type definitions for voiceMessageService.js

import { VoiceMessageProps } from '@/types/voice-message';

/**
 * Filter out deleted posts from a list
 */
export function filterDeletedPosts(posts: VoiceMessageProps[]): VoiceMessageProps[];

/**
 * Save a voice message
 */
export function saveVoiceMessage(
  audioUrl: string,
  transcript: string,
  userAddress: string,
  duration: number,
  media?: any[],
  customId?: string | null
): Promise<VoiceMessageProps>;

/**
 * Delete a voice message
 */
export function deleteVoiceMessage(
  messageId: string,
  userAddress: string
): Promise<boolean>;

/**
 * Toggle pin status of a voice message
 */
export function togglePinMessage(
  messageId: string,
  userAddress: string,
  isPinned: boolean
): Promise<boolean>;

declare const _default: {
  filterDeletedPosts: typeof filterDeletedPosts;
  saveVoiceMessage: typeof saveVoiceMessage;
  deleteVoiceMessage: typeof deleteVoiceMessage;
  togglePinMessage: typeof togglePinMessage;
};

export default _default;
