import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://jcltjkaumevuycntdmds.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpjbHRqa2F1bWV2dXljbnRkbWRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTkwMDEsImV4cCI6MjA2MTg3NTAwMX0.LqQSk0NwB1mDB4Cznn0iQSrYvsAAWyUsaj8reo_etqM'
);

async function debugJournalUser() {
  console.log('🔍 Debugging journal user matching...\n');
  
  try {
    // Get the journal from database
    const { data: journals, error } = await supabase
      .from('journals')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);
      
    if (error || !journals || journals.length === 0) {
      console.log('❌ No journals found');
      return;
    }
    
    const journal = journals[0];
    console.log('📖 Found journal:');
    console.log(`   Title: ${journal.title}`);
    console.log(`   User ID in DB: ${journal.profile_id}`);
    console.log(`   Published: ${journal.is_published}`);
    console.log(`   Locked: ${journal.is_locked}`);
    console.log('');
    
    // Test different user ID formats
    const testUserIds = [
      journal.profile_id, // Exact match
      journal.profile_id.toLowerCase(), // Lowercase
      journal.profile_id.toUpperCase(), // Uppercase
    ];
    
    console.log('🧪 Testing user ID matching:');
    testUserIds.forEach((testId, i) => {
      const matches = testId === journal.profile_id || testId.toLowerCase() === journal.profile_id.toLowerCase();
      console.log(`   ${i+1}. "${testId}" -> ${matches ? '✅ MATCH' : '❌ NO MATCH'}`);
    });
    
    console.log('');
    
    // Simulate the journal filtering logic
    console.log('🔍 Simulating journal filtering:');
    
    const userAddress = journal.profile_id; // Use the exact user ID from DB
    
    // Test getUserJournals logic
    const userJournals = journals.filter(j => 
      j.profile_id === userAddress || 
      j.profile_id.toLowerCase() === userAddress.toLowerCase()
    );
    console.log(`   getUserJournals: Found ${userJournals.length} journals`);
    
    // Test getUnlockedJournals logic (owner should always see their journals)
    const unlockedJournals = journals.filter(j => {
      // If it's the user's own journal, they can always see it
      if (j.profile_id === userAddress || j.profile_id.toLowerCase() === userAddress.toLowerCase()) {
        return true;
      }
      return false; // For simplicity, not checking unlock conditions for others
    });
    console.log(`   getUnlockedJournals: Found ${unlockedJournals.length} journals`);
    
    // Test the converted journal object
    console.log('');
    console.log('📝 Converted journal object would be:');
    const convertedJournal = {
      id: journal.id,
      title: journal.title,
      userAddress: journal.profile_id,
      isPrivate: !journal.is_published,
      isLocked: journal.is_locked || false,
      isUnlocked: !journal.is_locked, // If not locked, it's unlocked
      isPublished: journal.is_published || false,
    };
    
    console.log(`   userAddress: "${convertedJournal.userAddress}"`);
    console.log(`   isPrivate: ${convertedJournal.isPrivate}`);
    console.log(`   isLocked: ${convertedJournal.isLocked}`);
    console.log(`   isUnlocked: ${convertedJournal.isUnlocked}`);
    console.log(`   isPublished: ${convertedJournal.isPublished}`);
    
    console.log('');
    console.log('🎯 Expected behavior:');
    console.log('   - Should appear in "My Journals" (getUserJournals)');
    console.log('   - Should appear in "Unlocked" tab (getUnlockedJournals)');
    console.log('   - Should appear in "Public" tab if isPublished=true');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

debugJournalUser().catch(console.error);
