-- Create a test live stream to verify the functionality
INSERT INTO live_streams (
    id,
    title,
    description,
    host_profile_id,
    channel_id,
    status,
    scheduled_start,
    actual_start,
    chat_enabled,
    audience_can_request_mic,
    max_participants,
    created_at
) VALUES (
    gen_random_uuid(),
    'Test Live Space',
    'This is a test live space to verify the functionality is working',
    '0f59ebfe-9b88-48ca-a0e4-66a3f533d843'::uuid,
    'e17cc18d-5861-4ac4-a3dc-78e1c9e398f8'::uuid,
    'live',
    now(),
    now(),
    true,
    true,
    1000,
    now()
);