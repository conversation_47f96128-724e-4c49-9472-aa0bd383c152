import React from 'react';

interface WaveformProps {
  isPlaying: boolean;
  duration: number;
  currentTime: number;
  waveformData: number[];
  color?: string;
  activeColor?: string;
  height?: number;
  onSeek?: (position: number) => void;
}

export const Waveform: React.FC<WaveformProps> = ({
  isPlaying,
  duration,
  currentTime,
  waveformData,
  color = '#6b7280',
  activeColor = '#a78bfa',
  height = 40,
  onSeek,
}) => {
  // Calculate the progress percentage
  const progress = duration > 0 ? currentTime / duration : 0;

  // Create a simplified waveform using divs instead of canvas
  const bars = Array.from({ length: 40 }, (_, i) => {
    const isActive = i / 40 < progress;
    const barHeight = Math.max(0.3, Math.random()) * height;

    return (
      <div
        key={i}
        className={`mx-px ${isActive ? 'bg-voicechain-purple' : 'bg-gray-400'}`}
        style={{
          height: `${barHeight}px`,
          width: '2px',
          transition: 'height 0.2s ease'
        }}
      />
    );
  });

  return (
    <div className="relative w-full" style={{ height: `${height}px` }}>
      <div className="absolute inset-0 flex items-center">
        <div className="flex items-end w-full h-full">
          {bars}
        </div>
      </div>

      {/* Progress indicator */}
      <div
        className="absolute top-0 bottom-0 w-px bg-voicechain-purple"
        style={{ left: `${progress * 100}%` }}
      />

      {/* Clickable overlay for seeking */}
      <div
        className="absolute inset-0 cursor-pointer"
        onClick={(e) => {
          const rect = e.currentTarget.getBoundingClientRect();
          const clickPosition = (e.clientX - rect.left) / rect.width;
          const seekPosition = clickPosition * duration;

          // Call the onSeek callback if provided
          if (onSeek) {
            onSeek(seekPosition);
          } else {
            console.log('Seek to:', seekPosition);
          }
        }}
      />
    </div>
  );
};
