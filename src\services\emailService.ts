import { supabase } from '@/integrations/supabase/client';
import { VerificationType } from '@/components/VerificationBadge';

// Email templates
const EMAIL_TEMPLATES = {
  VERIFICATION_SUBMITTED: 'verification_submitted',
  VERIFICATION_APPROVED: 'verification_approved',
  VERIFICATION_REJECTED: 'verification_rejected',
  VERIFICATION_REVOKED: 'verification_revoked'
};

/**
 * Send an email notification
 * @param userId User ID to send the email to
 * @param templateId Email template ID
 * @param data Data to include in the email
 */
export const sendEmailNotification = async (
  userId: string,
  templateId: string,
  data: Record<string, any>
): Promise<boolean> => {
  try {
    // First get the user's email
    const { data: userData, error: userError } = await supabase
      .from('profiles')
      .select('email')
      .eq('id', userId)
      .single();

    if (userError || !userData?.email) {
      console.error('Error getting user email:', userError);
      return false;
    }

    // Send the email using Supabase Edge Functions
    const { error } = await supabase.functions.invoke('send-email', {
      body: {
        to: userData.email,
        templateId,
        data: {
          ...data,
          userEmail: userData.email
        }
      }
    });

    if (error) {
      console.error('Error sending email notification:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error sending email notification:', error);
    return false;
  }
};

/**
 * Send a verification submitted notification
 * @param userId User ID
 * @param applicationType Verification type
 */
export const sendVerificationSubmittedEmail = async (
  userId: string,
  applicationType: VerificationType
): Promise<boolean> => {
  return sendEmailNotification(userId, EMAIL_TEMPLATES.VERIFICATION_SUBMITTED, {
    verificationType: applicationType,
    submittedAt: new Date().toISOString()
  });
};

/**
 * Send a verification approved notification
 * @param userId User ID
 * @param applicationType Verification type
 */
export const sendVerificationApprovedEmail = async (
  userId: string,
  applicationType: VerificationType
): Promise<boolean> => {
  return sendEmailNotification(userId, EMAIL_TEMPLATES.VERIFICATION_APPROVED, {
    verificationType: applicationType,
    approvedAt: new Date().toISOString()
  });
};

/**
 * Send a verification rejected notification
 * @param userId User ID
 * @param applicationType Verification type
 * @param reason Rejection reason
 */
export const sendVerificationRejectedEmail = async (
  userId: string,
  applicationType: VerificationType,
  reason?: string
): Promise<boolean> => {
  return sendEmailNotification(userId, EMAIL_TEMPLATES.VERIFICATION_REJECTED, {
    verificationType: applicationType,
    rejectedAt: new Date().toISOString(),
    reason: reason || 'Your application did not meet our requirements.'
  });
};

/**
 * Send a verification revoked notification
 * @param userId User ID
 * @param reason Revocation reason
 */
export const sendVerificationRevokedEmail = async (
  userId: string,
  reason?: string
): Promise<boolean> => {
  return sendEmailNotification(userId, EMAIL_TEMPLATES.VERIFICATION_REVOKED, {
    revokedAt: new Date().toISOString(),
    reason: reason || 'Your verification has been revoked.'
  });
};
