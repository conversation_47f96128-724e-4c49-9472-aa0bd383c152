-- Fix infinite recursion in RLS policies for admin_profiles table
-- This script drops and recreates the RLS policies with proper conditions to avoid recursion

-- First, disable <PERSON><PERSON> temporarily to allow fixing the policies
ALTER TABLE admin_profiles DISABLE ROW LEVEL SECURITY;

-- Drop existing policies that might be causing recursion
DROP POLICY IF EXISTS "Ad<PERSON> can view all admin profiles" ON admin_profiles;
DROP POLICY IF EXISTS "Admins can update their own profile" ON admin_profiles;
DROP POLICY IF EXISTS "Super admins can update any profile" ON admin_profiles;
DROP POLICY IF EXISTS "Super admins can delete profiles" ON admin_profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can insert profiles" ON admin_profiles;

-- Create admin_settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS admin_settings (
  id TEXT PRIMARY KEY,
  settings JSONB NOT NULL DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default settings if they don't exist
INSERT INTO admin_settings (id, settings)
VALUES ('global', '{
  "session_timeout_hours": 24,
  "max_login_attempts": 5,
  "require_2fa_for_super_admin": false,
  "password_expiry_days": 90,
  "audit_log_retention_days": 365
}'::jsonb)
ON CONFLICT (id) DO NOTHING;

-- Enable RLS on admin_settings
ALTER TABLE admin_settings ENABLE ROW LEVEL SECURITY;

-- Drop existing policies on admin_settings
DROP POLICY IF EXISTS "Admins can view settings" ON admin_settings;
DROP POLICY IF EXISTS "Super admins can update settings" ON admin_settings;

-- Create proper RLS policies for admin_settings
CREATE POLICY "Admins can view settings"
ON admin_settings FOR SELECT
USING (
  -- Allow any authenticated user with a record in admin_profiles
  EXISTS (
    SELECT 1 FROM admin_profiles 
    WHERE id = auth.uid()
  )
);

CREATE POLICY "Super admins can update settings"
ON admin_settings FOR UPDATE
USING (
  -- Only super_admin can update settings
  EXISTS (
    SELECT 1 FROM admin_profiles 
    WHERE id = auth.uid() 
    AND role = 'super_admin'
  )
);

-- Now recreate the RLS policies for admin_profiles with proper conditions
-- Enable RLS again
ALTER TABLE admin_profiles ENABLE ROW LEVEL SECURITY;

-- Create proper RLS policies for admin_profiles
CREATE POLICY "Admins can view all admin profiles"
ON admin_profiles FOR SELECT
USING (
  -- Any authenticated user with a record in admin_profiles can view all profiles
  auth.uid() IN (SELECT id FROM admin_profiles)
);

CREATE POLICY "Admins can update their own profile"
ON admin_profiles FOR UPDATE
USING (
  -- Admins can only update their own profile
  auth.uid() = id
)
WITH CHECK (
  -- Ensure they can't change their role
  auth.uid() = id AND
  (
    -- Either the role is not being changed
    (SELECT role FROM admin_profiles WHERE id = auth.uid()) = role
    OR
    -- Or the user is a super_admin (can change their own role)
    (SELECT role FROM admin_profiles WHERE id = auth.uid()) = 'super_admin'
  )
);

CREATE POLICY "Super admins can update any profile"
ON admin_profiles FOR UPDATE
USING (
  -- Only super_admin can update any profile
  EXISTS (
    SELECT 1 FROM admin_profiles 
    WHERE id = auth.uid() 
    AND role = 'super_admin'
  )
);

CREATE POLICY "Super admins can delete profiles"
ON admin_profiles FOR DELETE
USING (
  -- Only super_admin can delete profiles
  EXISTS (
    SELECT 1 FROM admin_profiles 
    WHERE id = auth.uid() 
    AND role = 'super_admin'
  ) AND
  -- Prevent super_admin from deleting their own profile
  id != auth.uid()
);

CREATE POLICY "Admins can insert profiles"
ON admin_profiles FOR INSERT
WITH CHECK (
  -- Only super_admin can insert new profiles
  EXISTS (
    SELECT 1 FROM admin_profiles 
    WHERE id = auth.uid() 
    AND role = 'super_admin'
  )
);

-- Create a function to check if a user is an admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM admin_profiles 
    WHERE id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if a user is a super admin
CREATE OR REPLACE FUNCTION is_super_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM admin_profiles 
    WHERE id = auth.uid() 
    AND role = 'super_admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Instructions:
-- 1. Run this script in the Supabase SQL editor
-- 2. This will fix the infinite recursion in RLS policies
-- 3. It will also create the admin_settings table if it doesn't exist
-- 4. And add helper functions for checking admin roles
