
import React, { useState } from 'react';
import { useWallet } from '@/contexts/WalletContext';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Copy, ExternalLink, ArrowDownToLine, QrCode, Loader2 } from 'lucide-react';
import { toast } from '@/components/ui/sonner';
import walletService from '@/services/walletService';

const WalletFunding: React.FC = () => {
  const { wallet, refreshBalance, refreshSolanaBalance } = useWallet();
  const [copied, setCopied] = useState(false);
  const [solCopied, setSolCopied] = useState(false);
  const [showQr, setShowQr] = useState(false);
  const [showSolQr, setShowSolQr] = useState(false);
  const [activeTab, setActiveTab] = useState('deposit');
  const [blockchain, setBlockchain] = useState('ethereum');
  const [isGeneratingQr, setIsGeneratingQr] = useState(false);
  const [isGeneratingSolQr, setIsGeneratingSolQr] = useState(false);
  const [qrDataUrl, setQrDataUrl] = useState<string | null>(null);
  const [solQrDataUrl, setSolQrDataUrl] = useState<string | null>(null);

  // Copy ETH address to clipboard
  const copyAddress = () => {
    if (!wallet) return;
    
    navigator.clipboard.writeText(wallet.address)
      .then(() => {
        setCopied(true);
        toast.success('ETH address copied to clipboard');
        setTimeout(() => setCopied(false), 2000);
      })
      .catch(() => toast.error('Failed to copy address'));
  };
  
  // Copy SOL address to clipboard
  const copySolAddress = () => {
    if (!wallet || !wallet.solana) return;
    
    navigator.clipboard.writeText(wallet.solana.address)
      .then(() => {
        setSolCopied(true);
        toast.success('SOL address copied to clipboard');
        setTimeout(() => setSolCopied(false), 2000);
      })
      .catch(() => toast.error('Failed to copy address'));
  };
  
  // Open ETH address in block explorer
  const openInExplorer = () => {
    if (!wallet) return;
    
    const url = walletService.getAddressUrl(wallet.address, wallet.network);
    window.open(url, '_blank');
  };
  
  // Open SOL address in block explorer
  const openInSolExplorer = () => {
    if (!wallet || !wallet.solana) return;
    
    const url = walletService.getSolanaAddressUrl(wallet.solana.address, wallet.solana.network);
    window.open(url, '_blank');
  };
  
  // Generate QR code for ETH wallet address
  const generateQrCode = async () => {
    if (!wallet) return;
    
    try {
      setIsGeneratingQr(true);
      
      // Using a simple approach to generate QR codes using the qrcode library
      const qrData = walletService.generateAddressQRData(wallet.address);
      
      // We'd normally use a QR code library here, but for demonstration we'll use a placeholder
      // This would be replaced with actual QR code generation in production
      setTimeout(() => {
        // Placeholder QR code data URL (would be generated by library)
        setQrDataUrl(`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrData)}`);
        setShowQr(true);
        setIsGeneratingQr(false);
      }, 1000);
      
    } catch (error) {
      console.error('Error generating QR code:', error);
      toast.error('Failed to generate QR code');
      setIsGeneratingQr(false);
    }
  };
  
  // Generate QR code for SOL wallet address
  const generateSolQrCode = async () => {
    if (!wallet || !wallet.solana) return;
    
    try {
      setIsGeneratingSolQr(true);
      
      // Generate QR code for Solana address
      const qrData = `solana:${wallet.solana.address}`;
      
      setTimeout(() => {
        // Placeholder QR code data URL (would be generated by library)
        setSolQrDataUrl(`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrData)}`);
        setShowSolQr(true);
        setIsGeneratingSolQr(false);
      }, 1000);
      
    } catch (error) {
      console.error('Error generating Solana QR code:', error);
      toast.error('Failed to generate QR code');
      setIsGeneratingSolQr(false);
    }
  };
  
  // Send tokens to external wallet section
  const [recipient, setRecipient] = useState('');
  const [amount, setAmount] = useState('');
  const [isSending, setIsSending] = useState(false);
  
  const handleSendTokens = async () => {
    // Validation would happen in the TokenManager component
    // This is just a placeholder for the UI
    toast.info('Please use the Tokens tab to send tokens to external wallets');
  };

  if (!wallet) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Fund Wallet</CardTitle>
          <CardDescription>You don't have a wallet yet</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            A wallet will be created for you automatically during registration.
          </p>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Fund & Withdraw</CardTitle>
        <CardDescription>Manage funds in your wallet</CardDescription>
      </CardHeader>
      
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="deposit">Deposit Funds</TabsTrigger>
            <TabsTrigger value="withdraw">Withdraw Funds</TabsTrigger>
          </TabsList>
          
          <TabsContent value="deposit" className="pt-4 space-y-4">
            {/* Blockchain selector */}
            <div className="space-y-2">
              <Label>Select Blockchain</Label>
              <div className="flex space-x-2">
                <Button 
                  variant={blockchain === 'ethereum' ? 'default' : 'outline'} 
                  onClick={() => setBlockchain('ethereum')}
                  className="flex-1"
                >
                  <img src="/images/chains/ethereum.png" alt="ETH" className="w-4 h-4 mr-2" />
                  Ethereum (Base)
                </Button>
                <Button 
                  variant={blockchain === 'solana' ? 'default' : 'outline'} 
                  onClick={() => setBlockchain('solana')}
                  className="flex-1"
                >
                  <img src="/images/chains/solana.png" alt="SOL" className="w-4 h-4 mr-2" />
                  Solana
                </Button>
              </div>
            </div>
            
            {/* Ethereum (Base) deposit section */}
            {blockchain === 'ethereum' && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Your ETH Wallet Address</Label>
                  <div className="flex items-center gap-2">
                    <Input 
                      value={wallet.address} 
                      readOnly 
                      className="font-mono text-sm"
                    />
                    <Button variant="outline" size="icon" onClick={copyAddress}>
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon" onClick={openInExplorer}>
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <p className="text-sm text-muted-foreground">
                    Use the address above to send ETH or tokens from external wallets.
                  </p>
                  
                  {!showQr ? (
                    <Button 
                      variant="outline" 
                      className="w-full" 
                      onClick={generateQrCode}
                      disabled={isGeneratingQr}
                    >
                      {isGeneratingQr ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Generating QR Code...
                        </>
                      ) : (
                        <>
                          <QrCode className="h-4 w-4 mr-2" />
                          Show QR Code
                        </>
                      )}
                    </Button>
                  ) : qrDataUrl && (
                    <div className="flex flex-col items-center">
                      <img 
                        src={qrDataUrl} 
                        alt="ETH Wallet QR Code" 
                        className="w-40 h-40 mb-2"
                      />
                      <p className="text-sm">Scan to send ETH to this wallet</p>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        className="mt-2"
                        onClick={() => setShowQr(false)}
                      >
                        Hide QR Code
                      </Button>
                    </div>
                  )}
                </div>
                
                <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-md p-3 text-sm">
                  <p className="text-yellow-500 font-medium mb-1">Network Information</p>
                  <p className="text-muted-foreground">
                    This wallet is on the {wallet.network === 'base' ? 'Base Mainnet' : 'Base Goerli Testnet'} network.
                    Only send {wallet.network === 'base' ? 'Base' : 'Base Goerli'} ETH and tokens to this address.
                  </p>
                </div>
                
                <div className="pt-2">
                  <Button 
                    variant="secondary" 
                    className="w-full"
                    onClick={refreshBalance}
                  >
                    <ArrowDownToLine className="h-4 w-4 mr-2" />
                    Refresh ETH Balance
                  </Button>
                </div>
              </div>
            )}
            
            {/* Solana deposit section */}
            {blockchain === 'solana' && wallet.solana && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Your Solana Wallet Address</Label>
                  <div className="flex items-center gap-2">
                    <Input 
                      value={wallet.solana.address} 
                      readOnly 
                      className="font-mono text-sm"
                    />
                    <Button variant="outline" size="icon" onClick={copySolAddress}>
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon" onClick={openInSolExplorer}>
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <p className="text-sm text-muted-foreground">
                    Use the address above to send SOL or tokens from external wallets.
                  </p>
                  
                  {!showSolQr ? (
                    <Button 
                      variant="outline" 
                      className="w-full" 
                      onClick={generateSolQrCode}
                      disabled={isGeneratingSolQr}
                    >
                      {isGeneratingSolQr ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Generating QR Code...
                        </>
                      ) : (
                        <>
                          <QrCode className="h-4 w-4 mr-2" />
                          Show QR Code
                        </>
                      )}
                    </Button>
                  ) : solQrDataUrl && (
                    <div className="flex flex-col items-center">
                      <img 
                        src={solQrDataUrl} 
                        alt="SOL Wallet QR Code" 
                        className="w-40 h-40 mb-2"
                      />
                      <p className="text-sm">Scan to send SOL to this wallet</p>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        className="mt-2"
                        onClick={() => setShowSolQr(false)}
                      >
                        Hide QR Code
                      </Button>
                    </div>
                  )}
                </div>
                
                <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-md p-3 text-sm">
                  <p className="text-yellow-500 font-medium mb-1">Network Information</p>
                  <p className="text-muted-foreground">
                    This wallet is on the {wallet.solana.network} Solana network.
                    Only send SOL and SPL tokens to this address.
                  </p>
                </div>
                
                <div className="pt-2">
                  <Button 
                    variant="secondary" 
                    className="w-full"
                    onClick={refreshSolanaBalance}
                  >
                    <ArrowDownToLine className="h-4 w-4 mr-2" />
                    Refresh SOL Balance
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="withdraw" className="pt-4">
            <p className="text-sm text-muted-foreground mb-4">
              Send your funds to an external wallet. You can use this to withdraw tips you've received.
            </p>
            
            {/* Blockchain selector for withdrawals */}
            <div className="space-y-2 mb-4">
              <Label>Select Blockchain</Label>
              <div className="flex space-x-2">
                <Button 
                  variant={blockchain === 'ethereum' ? 'default' : 'outline'} 
                  onClick={() => setBlockchain('ethereum')}
                  className="flex-1"
                >
                  <img src="/images/chains/ethereum.png" alt="ETH" className="w-4 h-4 mr-2" />
                  Ethereum (Base)
                </Button>
                <Button 
                  variant={blockchain === 'solana' ? 'default' : 'outline'} 
                  onClick={() => setBlockchain('solana')}
                  className="flex-1"
                >
                  <img src="/images/chains/solana.png" alt="SOL" className="w-4 h-4 mr-2" />
                  Solana
                </Button>
              </div>
            </div>
            
            <div className="space-y-4 mb-4">
              <div className="space-y-2">
                <Label htmlFor="recipient">Recipient Address</Label>
                <Input 
                  id="recipient" 
                  placeholder={blockchain === 'ethereum' ? "0x..." : "sol..."}
                  value={recipient}
                  onChange={(e) => setRecipient(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="amount">Amount</Label>
                <Input 
                  id="amount" 
                  type="number" 
                  min="0" 
                  step="0.001"
                  placeholder="0.0" 
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                />
                <p className="text-xs text-muted-foreground">
                  {blockchain === 'ethereum' 
                    ? `Available balance: ${wallet.balance} ETH` 
                    : `Available balance: ${wallet.solana?.balance || '0.0'} SOL`}
                </p>
              </div>
            </div>
            
            <Button 
              className="w-full"
              disabled={true}
              onClick={handleSendTokens}
            >
              Please use the Tokens tab to send funds
            </Button>
            
            <div className="mt-4 bg-yellow-500/10 border border-yellow-500/20 rounded-md p-3 text-sm">
              <p className="text-yellow-500 font-medium mb-1">Withdrawal Information</p>
              <p className="text-muted-foreground">
                To withdraw your funds, please use the Tokens tab where you'll find detailed token management
                and sending options for both {blockchain === 'ethereum' ? 'ETH' : 'SOL'} and other tokens.
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      
      <CardFooter className="flex justify-between">
        <p className="text-xs text-muted-foreground">
          {blockchain === 'ethereum' 
            ? `Connected to ${wallet.network === 'base' ? 'Base Mainnet' : 'Base Goerli Testnet'}` 
            : `Connected to Solana ${wallet.solana?.network || 'Mainnet'}`}
        </p>
      </CardFooter>
    </Card>
  );
};

export default WalletFunding;
