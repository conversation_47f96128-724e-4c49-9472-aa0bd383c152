/**
 * Profile Sync Service
 * Handles synchronizing profiles between devices via Supabase
 */

import { supabase } from '@/integrations/supabase/client';
import { UserProfile, UserProfileUpdate } from '@/types/user-profile';
import { toast } from '@/components/ui/sonner';
import { v4 as uuidv4 } from 'uuid';

/**
 * Create a function to handle profile updates in Supabase
 * Uses a retry mechanism to handle potential failures
 */
export async function createUpdateProfileByAddressFunction(): Promise<void> {
  const maxRetries = 3;
  let retries = 0;

  while (retries < maxRetries) {
    try {
      console.log(`Attempt ${retries + 1} to create update_profile_by_address function`);

      // Try to execute the function creation query
      const { error } = await supabase.rpc('exec_sql', {
        sql_query: `
          CREATE OR REPLACE FUNCTION update_profile_by_address(
            p_wallet_address TEXT,
            p_username TEXT,
            p_display_name TEXT,
            p_bio TEXT,
            p_avatar_url TEXT,
            p_cover_image_url TEXT,
            p_social_links JSONB
          ) RETURNS JSONB
          LANGUAGE plpgsql
          SECURITY DEFINER
          AS $$
          DECLARE
            v_profile_id UUID;
            v_wallet_address TEXT;
            v_result JSONB;
            v_is_uuid BOOLEAN;
          BEGIN
            -- Normalize the address
            v_wallet_address := LOWER(p_wallet_address);

            -- Check if the input looks like a UUID
            BEGIN
              v_profile_id := v_wallet_address::UUID;
              v_is_uuid := TRUE;
            EXCEPTION WHEN others THEN
              v_is_uuid := FALSE;
            END;

            -- Check if profile exists by wallet address
            IF v_is_uuid THEN
              -- If it might be a UUID, check both id and wallet_address
              SELECT id INTO v_profile_id
              FROM profiles
              WHERE id = v_wallet_address::UUID OR LOWER(wallet_address) = v_wallet_address
              LIMIT 1;
            ELSE
              -- If definitely not a UUID, just check wallet_address
              SELECT id INTO v_profile_id
              FROM profiles
              WHERE LOWER(wallet_address) = v_wallet_address
              LIMIT 1;
            END IF;

            IF v_profile_id IS NULL THEN
              -- Insert new profile with a generated UUID
              v_profile_id := gen_random_uuid();

              INSERT INTO profiles (
                id,
                wallet_address,
                username,
                display_name,
                bio,
                avatar_url,
                cover_image_url,
                social_links,
                created_at,
                updated_at
              ) VALUES (
                v_profile_id,
                v_wallet_address,
                p_username,
                p_display_name,
                p_bio,
                p_avatar_url,
                p_cover_image_url,
                p_social_links,
                NOW(),
                NOW()
              )
              RETURNING to_jsonb(profiles.*) INTO v_result;
            ELSE
              -- Update existing profile
              UPDATE profiles
              SET
                username = p_username,
                display_name = p_display_name,
                bio = p_bio,
                avatar_url = p_avatar_url,
                cover_image_url = p_cover_image_url,
                social_links = p_social_links,
                updated_at = NOW()
              WHERE id = v_profile_id
              RETURNING to_jsonb(profiles.*) INTO v_result;
            END IF;

            RETURN v_result;
          END;
          $$;
        `
      });

      if (error) {
        console.error('Error creating update_profile_by_address function:', error);
        throw error;
      } else {
        console.log('Created update_profile_by_address function successfully');
        return;
      }
    } catch (error) {
      console.error(`Error creating function (attempt ${retries + 1}):`, error);
      retries++;

      // Wait before retrying
      if (retries < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 1000 * retries));
      }
    }
  }

  // If we've reached here, all attempts failed
  console.log('Will need to use standard upsert approach for profile syncing');
}

/**
 * Alternative approach to create the function
 */
async function createFunctionAlternative(): Promise<void> {
  try {
    // Check if the function already exists by calling it
    const { data, error } = await supabase.rpc('update_profile_by_address', {
      p_wallet_address: 'test',
      p_username: 'test',
      p_display_name: 'test',
      p_bio: '',
      p_avatar_url: '',
      p_cover_image_url: '',
      p_social_links: {}
    });

    if (!error) {
      console.log('update_profile_by_address function already exists');
      return;
    }

    // Function doesn't exist or failed, handle this case
    console.log('Will need to use standard upsert approach for profile syncing');
  } catch (error) {
    console.error('Error checking update_profile_by_address function:', error);
  }
}

/**
 * Sync a profile to Supabase
 * @param profile The profile to sync
 * @returns True if sync was successful
 */
export async function syncProfileToSupabase(profile: UserProfile): Promise<boolean> {
  if (!profile || !profile.address) {
    console.error('Invalid profile for syncing to Supabase');
    return false;
  }

  // Number of retry attempts
  const maxRetries = 3;
  let retries = 0;

  while (retries < maxRetries) {
    try {
      console.log(`Attempt ${retries + 1} to sync profile to Supabase for ${profile.address}`);

      // Normalize the address
      const normalizedAddress = profile.address.toLowerCase();

      // Make sure we have a valid UUID for the ID
      // If the address appears to be a UUID, use it; otherwise generate one
      let profileId: string;
      try {
        // Check if address follows a UUID pattern
        if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(normalizedAddress)) {
          profileId = normalizedAddress;
        } else {
          // Generate a UUID based on the address for consistency
          profileId = crypto.randomUUID();
        }
      } catch (e) {
        // Fallback if crypto.randomUUID is not available
        profileId = uuidv4();
      }

      // First try using RPC function
      try {
        const { data, error } = await supabase.rpc('update_profile_by_address', {
          p_wallet_address: normalizedAddress,
          p_username: profile.username,
          p_display_name: profile.displayName,
          p_bio: profile.bio || '',
          p_avatar_url: profile.profileImageUrl || '',
          p_cover_image_url: profile.coverImageUrl || '',
          p_social_links: profile.socialLinks || {}
        });

        if (error) {
          console.error('Error using RPC to update profile:', error);
        } else {
          console.log('Profile synced successfully via RPC:', data);
          return true;
        }
      } catch (rpcError) {
        console.error('RPC method failed, falling back to direct insert/update:', rpcError);
      }

      // Fall back to standard insert/update approach
      console.log('Falling back to direct database operations');

      // First check if profile exists
      const { data: existingProfileData, error: queryError } = await supabase
        .from('profiles')
        .select('id')
        .eq('wallet_address', normalizedAddress)
        .maybeSingle();

      if (queryError) {
        console.error('Error querying profile:', queryError);
        throw queryError;
      }

      if (existingProfileData) {
        // Update existing profile
        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            username: profile.username,
            display_name: profile.displayName,
            bio: profile.bio || '',
            avatar_url: profile.profileImageUrl || '',
            cover_image_url: profile.coverImageUrl || '',
            social_links: profile.socialLinks || {},
            updated_at: new Date().toISOString()
          })
          .eq('id', existingProfileData.id);

        if (updateError) {
          console.error('Error updating profile:', updateError);
          throw updateError;
        }
      } else {
        // Insert new profile
        const { error: insertError } = await supabase
          .from('profiles')
          .insert({
            id: profileId,
            wallet_address: normalizedAddress,
            username: profile.username,
            display_name: profile.displayName,
            bio: profile.bio || '',
            avatar_url: profile.profileImageUrl || '',
            cover_image_url: profile.coverImageUrl || '',
            social_links: profile.socialLinks || {},
            post_count: profile.stats?.posts || 0,
            like_count: profile.stats?.likes || 0,
            tip_count: profile.stats?.tips || 0,
            is_verified: profile.verification?.isVerified || false,
            verification_type: profile.verification?.type || null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (insertError) {
          console.error('Error inserting profile:', insertError);
          throw insertError;
        }
      }

      console.log('Profile synced successfully to Supabase via direct database operations');
      return true;
    } catch (error) {
      console.error(`Error in syncProfileToSupabase (attempt ${retries + 1}):`, error);
      retries++;

      // Wait before retrying
      if (retries < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 1000 * retries));
      }
    }
  }

  console.error(`Failed to sync profile to Supabase after ${maxRetries} attempts`);
  return false;
}

/**
 * Sync a profile from Supabase
 * @param address The wallet address
 * @returns The synced profile or null if not found
 */
export async function syncProfileFromSupabase(address: string): Promise<UserProfile | null> {
  try {
    console.log(`Syncing profile from Supabase for ${address}`);

    // Normalize the address
    const normalizedAddress = address.toLowerCase();

    // Get profile from Supabase
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('wallet_address', normalizedAddress)
      .maybeSingle();

    if (error) {
      console.error('Error getting profile from Supabase:', error);
      return null;
    }

    if (!data) {
      console.log(`No profile found in Supabase for ${normalizedAddress}`);
      return null;
    }

    console.log(`Found profile in Supabase for ${normalizedAddress}`);

    // Convert to UserProfile format
    const profile: UserProfile = {
      address: normalizedAddress,
      walletAddress: data.wallet_address,
      username: data.username || `user_${normalizedAddress.substring(2, 8)}`,
      displayName: data.display_name || `User ${normalizedAddress.substring(0, 6)}`,
      bio: data.bio || '',
      profileImageUrl: data.avatar_url || '',
      coverImageUrl: data.cover_image_url || '',
      socialLinks: data.social_links as UserProfile['socialLinks'] || {},
      stats: {
        posts: data.post_count || 0,
        likes: data.like_count || 0,
        tips: data.tip_count || 0
      },
      joinedDate: new Date(data.created_at),
      verification: {
        isVerified: !!data.is_verified,
        type: data.verification_type as any,
        since: data.verified_at ? new Date(data.verified_at) : undefined
      }
    };

    return profile;
  } catch (error) {
    console.error('Error syncing profile from Supabase:', error);
    return null;
  }
}

/**
 * Update a profile in Supabase
 * @param address The wallet address
 * @param update The profile update data
 * @returns True if the update was successful
 */
export async function updateProfileInSupabase(address: string, update: UserProfileUpdate): Promise<boolean> {
  try {
    console.log(`Updating profile in Supabase for ${address}`);

    // Normalize the address
    const normalizedAddress = address.toLowerCase();

    // Convert update to the format expected by the database
    const updateData: any = {};

    if (update.username) updateData.username = update.username;
    if (update.displayName) updateData.display_name = update.displayName;
    if (update.bio !== undefined) updateData.bio = update.bio;
    if (update.profileImageUrl) updateData.avatar_url = update.profileImageUrl;
    if (update.coverImageUrl) updateData.cover_image_url = update.coverImageUrl;
    if (update.socialLinks) updateData.social_links = update.socialLinks;
    if (update.stats) {
      if (update.stats.posts !== undefined) updateData.post_count = update.stats.posts;
      if (update.stats.likes !== undefined) updateData.like_count = update.stats.likes;
      if (update.stats.tips !== undefined) updateData.tip_count = update.stats.tips;
    }
    updateData.updated_at = new Date().toISOString();

    // Try using RPC function first if it might be available
    if (update.username && update.displayName) {
      try {
        const { data, error } = await supabase.rpc('update_profile_by_address', {
          p_wallet_address: normalizedAddress,
          p_username: update.username,
          p_display_name: update.displayName,
          p_bio: update.bio || '',
          p_avatar_url: update.profileImageUrl || '',
          p_cover_image_url: update.coverImageUrl || '',
          p_social_links: update.socialLinks || {}
        });

        if (error) {
          console.error('Error using RPC to update profile:', error);
        } else {
          console.log('Profile updated successfully via RPC:', data);
          return true;
        }
      } catch (rpcError) {
        console.error('RPC method failed, falling back to direct update:', rpcError);
      }
    }

    // Try the standard update approach
    const { error } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('wallet_address', normalizedAddress);

    if (error) {
      console.error('Error updating profile in Supabase:', error);
      return false;
    }

    console.log('Profile updated successfully in Supabase');
    return true;
  } catch (error) {
    console.error('Error updating profile in Supabase:', error);
    return false;
  }
}

/**
 * Sync a profile to Supabase with error handling and retries
 * @param profile The profile to sync
 * @returns True if sync was successful
 */
export async function forceSyncProfileToSupabase(profile: UserProfile): Promise<boolean> {
  if (!profile || !profile.address) {
    console.error('Invalid profile for syncing to Supabase');
    return false;
  }

  try {
    console.log(`Forcing sync of profile to Supabase for ${profile.address}`);

    // Normalize the address
    const normalizedAddress = profile.address.toLowerCase();

    // First check if this wallet address already has a profile in Supabase
    const { data: existingProfile, error: queryError } = await supabase
      .from('profiles')
      .select('id, username')
      .eq('wallet_address', normalizedAddress)
      .maybeSingle();

    if (queryError) {
      console.error('Error checking for existing profile:', queryError);
      throw queryError;
    }

    // Generate a UUID for the profile ID if we don't have an existing ID
    const profileId = existingProfile?.id || uuidv4();

    // Check if the username already exists (if it's not the current user's username)
    let username = profile.username;
    if (username && (!existingProfile || existingProfile.username !== username)) {
      const { data: usernameCheck, error: usernameError } = await supabase
        .from('profiles')
        .select('username')
        .eq('username', username)
        .neq('id', profileId) // Exclude the current profile
        .maybeSingle();

      if (usernameError) {
        console.error('Error checking username uniqueness:', usernameError);
      } else if (usernameCheck) {
        // Username already exists, generate a unique one
        console.log(`Username ${username} already exists, generating a unique one`);
        const randomSuffix = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        username = `${username}_${randomSuffix}`;
        console.log(`Generated unique username: ${username}`);
      }
    }

    // Convert the profile to Supabase format
    const supabaseProfile = {
      id: profileId,
      wallet_address: normalizedAddress,
      username: username,
      display_name: profile.displayName,
      bio: profile.bio || '',
      avatar_url: profile.profileImageUrl || '',
      cover_image_url: profile.coverImageUrl || '',
      social_links: profile.socialLinks || {},
      post_count: profile.stats?.posts || 0,
      like_count: profile.stats?.likes || 0,
      tip_count: profile.stats?.tips || 0,
      is_verified: profile.verification?.isVerified || false,
      verification_type: profile.verification?.type || null,
      updated_at: new Date().toISOString()
    };

    // Use direct upsert to save the profile
    const { error: upsertError } = await supabase
      .from('profiles')
      .upsert(supabaseProfile, {
        onConflict: 'id',
        ignoreDuplicates: false
      });

    if (upsertError) {
      // If we get a unique constraint error, try one more approach
      if (upsertError.code === '23505' && upsertError.message.includes('profiles_username_key')) {
        console.log('Got username conflict, trying with timestamp suffix');

        // Add timestamp to make it truly unique
        const timestampSuffix = Date.now().toString().slice(-6);
        supabaseProfile.username = `${username}_${timestampSuffix}`;

        // Try one more time
        const { error: finalError } = await supabase
          .from('profiles')
          .upsert(supabaseProfile, {
            onConflict: 'id',
            ignoreDuplicates: false
          });

        if (finalError) {
          console.error('Error in final attempt to upsert profile:', finalError);
          throw finalError;
        }
      } else {
        console.error('Error upserting profile to Supabase:', upsertError);
        throw upsertError;
      }
    }

    console.log(`Successfully force synced profile to Supabase for ${normalizedAddress}`);
    return true;
  } catch (error) {
    console.error('Error force syncing profile to Supabase:', error);
    toast.error('Failed to sync profile to Supabase');
    return false;
  }
}

/**
 * Cleanup a profile in Supabase
 * @param address The wallet address
 * @returns True if cleanup was successful
 */
export async function cleanupProfileData(address: string): Promise<boolean> {
  try {
    console.log(`Cleaning up profile data for ${address}`);

    // Normalize the address
    const normalizedAddress = address.toLowerCase();

    // First try using RPC function
    try {
      // Delete profile directly instead of using exec_sql
      const { data, error } = await supabase
        .from('profiles')
        .delete()
        .eq('wallet_address', normalizedAddress);

      if (error) {
        console.error('Error using RPC to delete profile:', error);
      } else {
        console.log('Profile data cleaned up successfully via RPC:', data);
        return true;
      }
    } catch (rpcError) {
      console.error('RPC method failed, falling back to direct delete:', rpcError);
    }

    // Fall back to standard delete approach
    console.log('Falling back to direct database operations');

    // First check if profile exists
    const { data: existingProfileData, error: queryError } = await supabase
      .from('profiles')
      .select('id')
      .eq('wallet_address', normalizedAddress)
      .maybeSingle();

    if (queryError) {
      console.error('Error querying profile:', queryError);
      throw queryError;
    }

    if (existingProfileData) {
      // Delete existing profile
      const { error: deleteError } = await supabase
        .from('profiles')
        .delete()
        .eq('id', existingProfileData.id);

      if (deleteError) {
        console.error('Error deleting profile:', deleteError);
        throw deleteError;
      }
    }

    console.log('Profile data cleaned up successfully');
    return true;
  } catch (error) {
    console.error('Error cleaning up profile data:', error);
    return false;
  }
}

export default {
  createUpdateProfileByAddressFunction,
  syncProfileToSupabase,
  syncProfileFromSupabase,
  updateProfileInSupabase,
  forceSyncProfileToSupabase,
  cleanupProfileData
};
