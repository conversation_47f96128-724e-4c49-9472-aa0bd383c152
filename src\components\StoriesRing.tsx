import React, { useState, useEffect } from 'react';
import { VoiceStory, storyService } from '@/services/storyService';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { StoryViewer } from './StoryViewer';
import { StoryCreationModal } from './StoryCreationModal';
import { supabase } from '@/integrations/supabase/client';

interface StoriesRingProps {
  currentUserId?: string;
}

interface UserStoryGroup {
  userId: string;
  username: string;
  stories: VoiceStory[];
  hasUnviewed: boolean;
  totalStories: number;
  viewedCount: number;
}

export const StoriesRing: React.FC<StoriesRingProps> = ({ currentUserId }) => {
  const [stories, setStories] = useState<VoiceStory[]>([]);
  const [userStoryGroups, setUserStoryGroups] = useState<UserStoryGroup[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedUserIndex, setSelectedUserIndex] = useState<number | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [usernames, setUsernames] = useState<{[key: string]: string}>({});

  useEffect(() => {
    loadStories();
  }, [currentUserId]);

  // Fetch usernames for story creators
  useEffect(() => {
    const fetchUsernames = async () => {
      if (stories.length === 0) return;

      const uniqueProfileIds = [...new Set(stories.map(story => story.profile_id))];
      const usernameMap: {[key: string]: string} = {};

      for (const profileId of uniqueProfileIds) {
        try {
          const { data: profile } = await supabase
            .from('profiles')
            .select('username, wallet_address')
            .eq('wallet_address', profileId)
            .single();

          if (profile?.username) {
            usernameMap[profileId] = profile.username;
          } else {
            usernameMap[profileId] = profileId.startsWith('0x')
              ? `${profileId.slice(0, 6)}...${profileId.slice(-4)}`
              : profileId;
          }
        } catch (error) {
          usernameMap[profileId] = profileId.startsWith('0x')
            ? `${profileId.slice(0, 6)}...${profileId.slice(-4)}`
            : profileId;
        }
      }

      setUsernames(usernameMap);
    };

    fetchUsernames();
  }, [stories]);

  const getDisplayName = (profileId: string) => {
    return usernames[profileId] || 'Loading...';
  };

  // Group stories by user (Instagram style - one ring per user)
  const groupStoriesByUser = (stories: VoiceStory[]): UserStoryGroup[] => {
    const userGroups: { [userId: string]: UserStoryGroup } = {};

    stories.forEach(story => {
      const userId = story.profile_id;

      if (!userGroups[userId]) {
        userGroups[userId] = {
          userId,
          username: getDisplayName(userId),
          stories: [],
          hasUnviewed: false,
          totalStories: 0,
          viewedCount: 0
        };
      }

      userGroups[userId].stories.push(story);
      userGroups[userId].totalStories++;

      if (story.has_viewed) {
        userGroups[userId].viewedCount++;
      } else {
        userGroups[userId].hasUnviewed = true;
      }
    });

    // Sort stories within each group by creation date
    Object.values(userGroups).forEach(group => {
      group.stories.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
    });

    // Sort user groups - current user first, then by most recent story
    const sortedGroups = Object.values(userGroups).sort((a, b) => {
      // Current user's stories first
      if (currentUserId && a.userId === currentUserId) return -1;
      if (currentUserId && b.userId === currentUserId) return 1;

      // Then by most recent story
      const aLatest = Math.max(...a.stories.map(s => new Date(s.created_at).getTime()));
      const bLatest = Math.max(...b.stories.map(s => new Date(s.created_at).getTime()));
      return bLatest - aLatest;
    });

    return sortedGroups;
  };

  const loadStories = async () => {
    try {
      setIsLoading(true);
      console.log('🔄 StoriesRing: Loading stories for user:', currentUserId);
      const activeStories = await storyService.getActiveStories(currentUserId);
      console.log('📚 StoriesRing: Loaded stories:', activeStories);
      setStories(activeStories);

      // Group stories by user (Instagram style)
      const groupedStories = groupStoriesByUser(activeStories);
      console.log('👥 StoriesRing: Grouped stories by user:', groupedStories);
      console.log('📊 StoriesRing: Total users with stories:', groupedStories.length);
      groupedStories.forEach(group => {
        console.log(`👤 User ${group.username}: ${group.totalStories} stories (${group.viewedCount} viewed)`);
      });
      setUserStoryGroups(groupedStories);
    } catch (error) {
      console.error('❌ StoriesRing: Error loading stories:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUserStoryClick = (userIndex: number) => {
    setSelectedUserIndex(userIndex);
  };

  const handleCloseViewer = () => {
    setSelectedUserIndex(null);
  };

  const handleStoryCreated = () => {
    loadStories(); // Refresh stories after creation
  };

  // Check if current user has stories
  const currentUserStoryGroup = userStoryGroups.find(group => group.userId === currentUserId);
  const hasCurrentUserStories = currentUserStoryGroup && currentUserStoryGroup.totalStories > 0;

  const handleYourStoryClick = () => {
    if (hasCurrentUserStories) {
      // User has stories, show them
      const userIndex = userStoryGroups.findIndex(group => group.userId === currentUserId);
      if (userIndex !== -1) {
        handleUserStoryClick(userIndex);
      }
    } else {
      // User has no stories, open creation modal
      setShowCreateModal(true);
    }
  };

  // Component for segmented story ring (Instagram style)
  const SegmentedStoryRing: React.FC<{
    userGroup: UserStoryGroup;
    size?: number;
  }> = ({ userGroup, size = 64 }) => {
    const { totalStories, viewedCount, hasUnviewed } = userGroup;
    const radius = (size - 8) / 2; // Account for border
    const circumference = 2 * Math.PI * radius;

    if (totalStories === 1) {
      // Single story - simple ring
      return (
        <div
          className={`rounded-full p-1 ${
            hasUnviewed
              ? 'bg-gradient-to-r from-primary to-secondary'
              : 'bg-gradient-to-r from-gray-300 to-gray-400'
          }`}
          style={{ width: size, height: size }}
        >
          <div
            className="w-full h-full rounded-full bg-cover bg-center flex items-center justify-center text-white font-semibold"
            style={{
              backgroundColor: userGroup.stories[0]?.background_color,
              backgroundImage: userGroup.stories[0]?.background_image_url
                ? `url(${userGroup.stories[0].background_image_url})`
                : undefined
            }}
          >
            {!userGroup.stories[0]?.background_image_url && (
              <span className="text-sm">
                {userGroup.userId.slice(0, 2).toUpperCase()}
              </span>
            )}
          </div>
        </div>
      );
    }

    // Multiple stories - segmented ring
    const segmentAngle = 360 / totalStories;
    const gapAngle = 2; // Small gap between segments

    return (
      <div className="relative" style={{ width: size, height: size }}>
        <svg
          width={size}
          height={size}
          className="absolute inset-0 transform -rotate-90"
        >
          {userGroup.stories.map((story, index) => {
            const startAngle = index * segmentAngle;
            const endAngle = (index + 1) * segmentAngle - gapAngle;
            const isViewed = story.has_viewed;

            const startAngleRad = (startAngle * Math.PI) / 180;
            const endAngleRad = (endAngle * Math.PI) / 180;

            const x1 = size / 2 + radius * Math.cos(startAngleRad);
            const y1 = size / 2 + radius * Math.sin(startAngleRad);
            const x2 = size / 2 + radius * Math.cos(endAngleRad);
            const y2 = size / 2 + radius * Math.sin(endAngleRad);

            const largeArcFlag = endAngle - startAngle > 180 ? 1 : 0;

            const pathData = [
              `M ${size / 2} ${size / 2}`,
              `L ${x1} ${y1}`,
              `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
              'Z'
            ].join(' ');

            return (
              <path
                key={index}
                d={pathData}
                fill={isViewed ? '#9CA3AF' : '#8B5CF6'}
                opacity={0.8}
              />
            );
          })}
        </svg>

        {/* Profile image in center */}
        <div
          className="absolute inset-2 rounded-full bg-cover bg-center flex items-center justify-center text-white font-semibold border-2 border-white"
          style={{
            backgroundColor: userGroup.stories[0]?.background_color,
            backgroundImage: userGroup.stories[0]?.background_image_url
              ? `url(${userGroup.stories[0].background_image_url})`
              : undefined
          }}
        >
          {!userGroup.stories[0]?.background_image_url && (
            <span className="text-sm">
              {userGroup.userId.slice(0, 2).toUpperCase()}
            </span>
          )}
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="flex gap-4 p-4 overflow-x-auto">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex-shrink-0">
            <div className="w-16 h-16 bg-gray-200 rounded-full animate-pulse" />
            <div className="w-12 h-3 bg-gray-200 rounded mt-2 mx-auto animate-pulse" />
          </div>
        ))}
      </div>
    );
  }

  return (
    <>
      <div className="flex gap-4 p-4 overflow-x-auto scrollbar-hide">
        {/* Your Story Button */}
        {currentUserId && (
          <div className="flex-shrink-0 text-center relative">
            {hasCurrentUserStories ? (
              // User has stories - show their story ring
              <div
                className="cursor-pointer"
                onClick={handleYourStoryClick}
              >
                <SegmentedStoryRing userGroup={currentUserStoryGroup!} />
                {currentUserStoryGroup!.totalStories > 1 && (
                  <div className="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold shadow-lg">
                    {currentUserStoryGroup!.totalStories}
                  </div>
                )}
              </div>
            ) : (
              // User has no stories - show add button
              <Button
                onClick={handleYourStoryClick}
                className="w-16 h-16 rounded-full border-2 border-dashed border-primary/50 bg-background hover:bg-accent p-0"
                variant="ghost"
              >
                <Plus className="h-6 w-6 text-primary" />
              </Button>
            )}
            <p className="text-xs text-muted-foreground mt-2 max-w-[64px] truncate">
              Your Story
            </p>
            {/* Add story button overlay for existing stories */}
            {hasCurrentUserStories && (
              <Button
                onClick={() => setShowCreateModal(true)}
                className="absolute bottom-8 right-0 w-6 h-6 rounded-full bg-primary hover:bg-primary/90 p-0 shadow-lg"
                size="sm"
              >
                <Plus className="h-3 w-3 text-white" />
              </Button>
            )}
          </div>
        )}

        {/* User Story Groups (Instagram Style) */}
        {userStoryGroups.map((userGroup, index) => (
          <div
            key={userGroup.userId}
            className="flex-shrink-0 text-center cursor-pointer relative"
            onClick={() => handleUserStoryClick(index)}
          >
            <SegmentedStoryRing userGroup={userGroup} />

            {/* Story count badge */}
            {userGroup.totalStories > 1 && (
              <div className="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold shadow-lg">
                {userGroup.totalStories}
              </div>
            )}

            <p className="text-xs text-muted-foreground mt-2 max-w-[64px] truncate">
              {userGroup.username}
            </p>
          </div>
        ))}

        {/* Empty State */}
        {userStoryGroups.length === 0 && (
          <div className="flex-1 text-center py-8">
            <p className="text-muted-foreground text-sm">
              No stories available. Be the first to share one!
            </p>
          </div>
        )}
      </div>

      {/* Story Viewer */}
      {selectedUserIndex !== null && userStoryGroups[selectedUserIndex] && (
        <StoryViewer
          stories={userStoryGroups[selectedUserIndex].stories}
          initialIndex={0}
          onClose={handleCloseViewer}
          currentUserId={currentUserId}
          userGroup={userStoryGroups[selectedUserIndex]}
        />
      )}

      {/* Story Creation Modal */}
      {currentUserId && (
        <StoryCreationModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onStoryCreated={handleStoryCreated}
          profileId={currentUserId}
        />
      )}
    </>
  );
};