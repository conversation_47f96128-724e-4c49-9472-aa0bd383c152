/**
 * Ceramic Profile Service
 * This service handles storing and retrieving user profiles using Ceramic Network
 * This is the primary and permanent storage solution for user profiles
 */

import { ceramic, authenticateDID } from './disabledCeramicClient';
import { UserProfile, UserProfileUpdate } from '@/types/user-profile';
import { uploadBlobToIPFS } from './nftStorage';
import { toast } from '@/components/ui/sonner';
import { TileDocument } from '@ceramicnetwork/stream-tile';

// In-memory cache for profiles to avoid unnecessary network requests during a session
const profileCache: Record<string, { profile: UserProfile; timestamp: number }> = {};
const CACHE_EXPIRY = 5 * 60 * 1000; // 5 minutes

/**
 * Helper function to prepare a profile for Ceramic storage
 * Converts Date objects to ISO strings to avoid CBOR encoding errors
 */
function prepareProfileForCeramic(profile: UserProfile): any {
  // Create a deep copy of the profile to avoid modifying the original
  const result = JSON.parse(JSON.stringify(profile));

  // Convert joinedDate to ISO string if it's a Date
  if (profile.joinedDate instanceof Date) {
    result.joinedDate = profile.joinedDate.toISOString();
  }

  // Convert verification.since to ISO string if it exists and is a Date
  if (profile.verification?.since instanceof Date) {
    result.verification.since = profile.verification.since.toISOString();
  }

  return result;
}

/**
 * Helper function to convert a profile from Ceramic storage back to a UserProfile
 * Converts ISO string dates back to Date objects
 */
function convertProfileFromCeramic(ceramicProfile: any): UserProfile {
  if (!ceramicProfile) return null;

  const result = { ...ceramicProfile };

  // Convert joinedDate from ISO string to Date if it's a string
  if (typeof result.joinedDate === 'string') {
    try {
      result.joinedDate = new Date(result.joinedDate);
    } catch (e) {
      console.warn('Error converting joinedDate to Date:', e);
    }
  }

  // Convert verification.since from ISO string to Date if it exists and is a string
  if (result.verification?.since && typeof result.verification.since === 'string') {
    try {
      result.verification.since = new Date(result.verification.since);
    } catch (e) {
      console.warn('Error converting verification.since to Date:', e);
    }
  }

  return result;
}

// IndexedDB for persistent local caching
const DB_NAME = 'AudraProfilesDB';
const PROFILE_STORE = 'profiles';
const DOC_ID_STORE = 'documentIds';

/**
 * Initialize the IndexedDB database
 * @returns A promise that resolves when the database is ready
 */
async function initIndexedDB(): Promise<IDBDatabase> {
  return new Promise((resolve, reject) => {
    if (!window.indexedDB) {
      console.warn('IndexedDB not supported. Profile persistence may be limited.');
      reject(new Error('IndexedDB not supported'));
      return;
    }

    const request = window.indexedDB.open(DB_NAME, 1);

    request.onerror = (event) => {
      console.error('Error opening IndexedDB:', event);
      reject(new Error('Error opening IndexedDB'));
    };

    request.onsuccess = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      resolve(db);
    };

    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;

      // Create profiles store
      if (!db.objectStoreNames.contains(PROFILE_STORE)) {
        db.createObjectStore(PROFILE_STORE, { keyPath: 'address' });
      }

      // Create document IDs store
      if (!db.objectStoreNames.contains(DOC_ID_STORE)) {
        db.createObjectStore(DOC_ID_STORE, { keyPath: 'address' });
      }
    };
  });
}

/**
 * Store a profile in IndexedDB
 * @param profile The profile to store
 */
async function storeProfileInIndexedDB(profile: UserProfile): Promise<void> {
  try {
    const db = await initIndexedDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([PROFILE_STORE], 'readwrite');
      const store = transaction.objectStore(PROFILE_STORE);

      const request = store.put(profile);

      request.onsuccess = () => {
        resolve();
      };

      request.onerror = (event) => {
        console.error('Error storing profile in IndexedDB:', event);
        reject(new Error('Error storing profile in IndexedDB'));
      };
    });
  } catch (error) {
    console.warn('Failed to store profile in IndexedDB:', error);
  }
}

/**
 * Get a profile from IndexedDB
 * This function is used by getProfileByAddress to check the cache
 * @param address The wallet address
 * @returns The profile or null if not found
 */
async function getProfileFromIndexedDB(address: string): Promise<UserProfile | null> {
  try {
    const db = await initIndexedDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([PROFILE_STORE], 'readonly');
      const store = transaction.objectStore(PROFILE_STORE);

      const request = store.get(address.toLowerCase());

      request.onsuccess = (event) => {
        const profile = (event.target as IDBRequest).result;
        resolve(profile || null);
      };

      request.onerror = (event) => {
        console.error('Error getting profile from IndexedDB:', event);
        reject(new Error('Error getting profile from IndexedDB'));
      };
    });
  } catch (error) {
    console.warn('Failed to get profile from IndexedDB:', error);
    return null;
  }
}

/**
 * Store a document ID mapping in IndexedDB
 * @param address The wallet address
 * @param documentId The Ceramic document ID
 */
async function storeDocumentIdInIndexedDB(address: string, documentId: string): Promise<void> {
  try {
    const db = await initIndexedDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([DOC_ID_STORE], 'readwrite');
      const store = transaction.objectStore(DOC_ID_STORE);

      const request = store.put({ address: address.toLowerCase(), documentId });

      request.onsuccess = () => {
        resolve();
      };

      request.onerror = (event) => {
        console.error('Error storing document ID in IndexedDB:', event);
        reject(new Error('Error storing document ID in IndexedDB'));
      };
    });
  } catch (error) {
    console.warn('Failed to store document ID in IndexedDB:', error);
  }
}

/**
 * Get a document ID from IndexedDB
 * @param address The wallet address
 * @returns The document ID or null if not found
 */
async function getDocumentIdFromIndexedDB(address: string): Promise<string | null> {
  try {
    const db = await initIndexedDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([DOC_ID_STORE], 'readonly');
      const store = transaction.objectStore(DOC_ID_STORE);

      const request = store.get(address.toLowerCase());

      request.onsuccess = (event) => {
        const result = (event.target as IDBRequest).result;
        resolve(result ? result.documentId : null);
      };

      request.onerror = (event) => {
        console.error('Error getting document ID from IndexedDB:', event);
        reject(new Error('Error getting document ID from IndexedDB'));
      };
    });
  } catch (error) {
    console.warn('Failed to get document ID from IndexedDB:', error);
    return null;
  }
}

/**
 * Create a user profile document in Ceramic
 * @param profile The user profile to store
 * @returns The Ceramic document ID
 */
export async function createProfile(profile: UserProfile): Promise<string> {
  try {
    // Ensure we have an authenticated DID
    await ensureAuthenticated();

    // Upload profile image to IPFS if it's a blob URL
    if (profile.profileImageUrl && profile.profileImageUrl.startsWith('blob:')) {
      try {
        const response = await fetch(profile.profileImageUrl);
        const blob = await response.blob();
        const fileName = `profile_${profile.address.substring(0, 10)}_${Date.now()}.${blob.type.split('/')[1] || 'jpg'}`;
        const ipfsUri = await uploadBlobToIPFS(blob, fileName);
        profile.profileImageUrl = ipfsUri;
      } catch (error) {
        console.error('Error uploading profile image to IPFS:', error);
      }
    }

    // Upload cover image to IPFS if it's a blob URL
    if (profile.coverImageUrl && profile.coverImageUrl.startsWith('blob:')) {
      try {
        const response = await fetch(profile.coverImageUrl);
        const blob = await response.blob();
        const fileName = `cover_${profile.address.substring(0, 10)}_${Date.now()}.${blob.type.split('/')[1] || 'jpg'}`;
        const ipfsUri = await uploadBlobToIPFS(blob, fileName);
        profile.coverImageUrl = ipfsUri;
      } catch (error) {
        console.error('Error uploading cover image to IPFS:', error);
      }
    }

    // Prepare the profile for Ceramic by converting Date objects to ISO strings
    const ceramicProfile = prepareProfileForCeramic(profile);

    // Create a new document with the profile data using TileDocument
    const doc = await TileDocument.create(
      ceramic,
      ceramicProfile,
      {
        controllers: [ceramic.did?.id || ''],
        family: 'userProfile',
        tags: ['profile', profile.address.toLowerCase()]
      }
    );

    // Get the document ID
    const documentId = doc.id.toString();
    console.log('Successfully created profile in Ceramic with ID:', documentId);

    // Add ceramicDocId to the profile
    const profileWithDocId = {
      ...profile,
      ceramicDocId: documentId
    };

    // Add to in-memory cache
    profileCache[profile.address.toLowerCase()] = {
      profile: profileWithDocId,
      timestamp: Date.now()
    };

    // Store in IndexedDB
    await storeProfileInIndexedDB(profileWithDocId);

    // Store the document ID mapping
    await storeProfileDocumentId(profile.address.toLowerCase(), documentId);

    // Return the document ID
    return documentId;
  } catch (error) {
    console.error('Error creating profile in Ceramic:', error);
    throw error;
  }
}

/**
 * Update a user profile document in Ceramic
 * @param documentId The Ceramic document ID
 * @param update The profile updates
 * @returns The updated document ID (should be the same)
 */
export async function updateProfile(
  documentId: string,
  profile: UserProfile,
  update: UserProfileUpdate
): Promise<string> {
  try {
    // Ensure we have an authenticated DID
    await ensureAuthenticated();

    // Create updated profile
    const updatedProfile: UserProfile = {
      ...profile,
      ...update,
      socialLinks: {
        ...profile.socialLinks,
        ...update.socialLinks
      },
      stats: {
        ...profile.stats,
        ...update.stats
      },
      // Ensure ceramicDocId is preserved
      ceramicDocId: documentId
    };

    // Upload profile image to IPFS if it's a blob URL
    if (update.profileImageUrl && update.profileImageUrl.startsWith('blob:')) {
      try {
        const response = await fetch(update.profileImageUrl);
        const blob = await response.blob();
        const fileName = `profile_${profile.address.substring(0, 10)}_${Date.now()}.${blob.type.split('/')[1] || 'jpg'}`;
        const ipfsUri = await uploadBlobToIPFS(blob, fileName);
        updatedProfile.profileImageUrl = ipfsUri;
      } catch (error) {
        console.error('Error uploading profile image to IPFS:', error);
      }
    }

    // Upload cover image to IPFS if it's a blob URL
    if (update.coverImageUrl && update.coverImageUrl.startsWith('blob:')) {
      try {
        const response = await fetch(update.coverImageUrl);
        const blob = await response.blob();
        const fileName = `cover_${profile.address.substring(0, 10)}_${Date.now()}.${blob.type.split('/')[1] || 'jpg'}`;
        const ipfsUri = await uploadBlobToIPFS(blob, fileName);
        updatedProfile.coverImageUrl = ipfsUri;
      } catch (error) {
        console.error('Error uploading cover image to IPFS:', error);
      }
    }

    // Prepare the profile for Ceramic by converting Date objects to ISO strings
    const ceramicProfile = prepareProfileForCeramic(updatedProfile);

    // Load the document using TileDocument
    const doc = await TileDocument.load(ceramic, documentId);

    // Update the document content
    await doc.update(ceramicProfile);
    console.log('Successfully updated profile in Ceramic with ID:', documentId);

    // Update in-memory cache
    profileCache[profile.address.toLowerCase()] = {
      profile: updatedProfile,
      timestamp: Date.now()
    };

    // Update IndexedDB cache
    await storeProfileInIndexedDB(updatedProfile);

    // Return the document ID
    return documentId;
  } catch (error) {
    console.error('Error updating profile in Ceramic:', error);
    throw error;
  }
}

/**
 * Get a user profile document from Ceramic by document ID
 * @param documentId The Ceramic document ID
 * @returns The user profile
 */
export async function getProfileByDocumentId(documentId: string): Promise<UserProfile> {
  try {
    // Load the document using TileDocument
    const doc = await TileDocument.load(ceramic, documentId);

    // Convert the document content from Ceramic format to UserProfile
    const profile = convertProfileFromCeramic(doc.content);

    // If we have an address, cache the profile
    if (profile && profile.address) {
      // Update in-memory cache
      profileCache[profile.address.toLowerCase()] = {
        profile,
        timestamp: Date.now()
      };

      // Update IndexedDB cache
      await storeProfileInIndexedDB(profile);
    }

    return profile;
  } catch (error) {
    console.error('Error getting profile from Ceramic:', error);
    throw error;
  }
}

/**
 * Query for a user profile by wallet address
 * @param address The wallet address
 * @returns The user profile and document ID
 */
export async function getProfileByAddress(address: string): Promise<{ profile: UserProfile; documentId: string } | null> {
  try {
    if (!address) {
      throw new Error('Address is required');
    }

    const normalizedAddress = address.toLowerCase();

    // Check in-memory cache first (for performance during a session)
    if (
      profileCache[normalizedAddress] &&
      Date.now() - profileCache[normalizedAddress].timestamp < CACHE_EXPIRY
    ) {
      console.log(`Using in-memory cached profile for ${normalizedAddress}`);
      return {
        profile: profileCache[normalizedAddress].profile,
        documentId: profileCache[normalizedAddress].profile.ceramicDocId || ''
      };
    }

    // Try to get the document ID from IndexedDB
    const documentId = await getDocumentIdFromIndexedDB(normalizedAddress);
    if (documentId) {
      try {
        // Load the profile from Ceramic using the document ID
        const profile = await getProfileByDocumentId(documentId);
        if (profile && profile.address.toLowerCase() === normalizedAddress) {
          // Add the ceramicDocId to the profile
          const profileWithDocId = {
            ...profile,
            ceramicDocId: documentId
          };

          // Add to in-memory cache
          profileCache[normalizedAddress] = {
            profile: profileWithDocId,
            timestamp: Date.now()
          };

          // Also update IndexedDB cache
          await storeProfileInIndexedDB(profileWithDocId);

          return {
            profile: profileWithDocId,
            documentId
          };
        }
      } catch (error) {
        console.warn(`Error loading profile document for ${normalizedAddress} from Ceramic:`, error);

        // Try to get from IndexedDB as a fallback
        const cachedProfile = await getProfileFromIndexedDB(normalizedAddress);
        if (cachedProfile && cachedProfile.ceramicDocId) {
          console.log(`Using cached profile from IndexedDB for ${normalizedAddress}`);
          return {
            profile: cachedProfile,
            documentId: cachedProfile.ceramicDocId
          };
        }
      }
    }

    // If we couldn't find the profile, return null
    console.log(`No profile found for address: ${normalizedAddress}`);
    return null;
  } catch (error) {
    console.error('Error querying profile from Ceramic:', error);
    throw error;
  }
}

/**
 * Store the mapping between wallet addresses and Ceramic document IDs
 * @param address The wallet address
 * @param documentId The Ceramic document ID
 */
export async function storeProfileDocumentId(address: string, documentId: string): Promise<void> {
  try {
    if (!address || !documentId) {
      return;
    }

    const normalizedAddress = address.toLowerCase();

    // Store in IndexedDB
    await storeDocumentIdInIndexedDB(normalizedAddress, documentId);

    console.log(`Stored profile document ID for ${normalizedAddress}: ${documentId}`);
  } catch (error) {
    console.error('Error storing profile document ID:', error);
  }
}

/**
 * Ensure the Ceramic client is authenticated
 */
async function ensureAuthenticated(): Promise<void> {
  if (!ceramic.did) {
    try {
      await authenticateDID();
    } catch (error) {
      console.error('Error authenticating with Ceramic:', error);
      toast.error('Error connecting to decentralized storage. Please try again.');
      throw new Error('Failed to authenticate with Ceramic');
    }
  }
}

/**
 * Clear the profile cache
 */
export function clearProfileCache(): void {
  Object.keys(profileCache).forEach(key => {
    delete profileCache[key];
  });
}

// Add ceramicDocId to the UserProfile interface
declare module '@/types/user-profile' {
  interface UserProfile {
    ceramicDocId?: string;
  }
}
