import { supabase } from '@/integrations/supabase/client';

/**
 * Service for managing muted users and posts
 */
class MuteService {
  private readonly MUTED_USERS_KEY = 'audra_muted_users';
  private readonly MUTED_POSTS_KEY = 'audra_muted_posts';

  /**
   * Get all muted users for the current user
   */
  public getMutedUsers(currentUserAddress: string): string[] {
    try {
      // First try to get from localStorage
      const mutedUsersJson = localStorage.getItem(this.MUTED_USERS_KEY);
      if (mutedUsersJson) {
        const mutedUsersMap = JSON.parse(mutedUsersJson);
        return mutedUsersMap[currentUserAddress.toLowerCase()] || [];
      }
      return [];
    } catch (error) {
      console.error('Error getting muted users:', error);
      return [];
    }
  }

  /**
   * Check if a user is muted
   */
  public isUserMuted(currentUserAddress: string, userToCheckAddress: string): boolean {
    const mutedUsers = this.getMutedUsers(currentUserAddress);
    return mutedUsers.includes(userToCheckAddress.toLowerCase());
  }

  /**
   * Mute a user
   */
  public muteUser(currentUserAddress: string, userToMuteAddress: string): boolean {
    try {
      // Don't allow muting yourself
      if (currentUserAddress.toLowerCase() === userToMuteAddress.toLowerCase()) {
        console.error('Cannot mute yourself');
        return false;
      }

      // Check if already muted
      if (this.isUserMuted(currentUserAddress, userToMuteAddress)) {
        console.log('User is already muted');
        return false;
      }

      // Get current muted users
      const mutedUsers = this.getMutedUsers(currentUserAddress);
      
      // Add the new user to mute
      mutedUsers.push(userToMuteAddress.toLowerCase());
      
      // Save to localStorage
      const mutedUsersJson = localStorage.getItem(this.MUTED_USERS_KEY);
      const mutedUsersMap = mutedUsersJson ? JSON.parse(mutedUsersJson) : {};
      mutedUsersMap[currentUserAddress.toLowerCase()] = mutedUsers;
      localStorage.setItem(this.MUTED_USERS_KEY, JSON.stringify(mutedUsersMap));
      
      // Also save to database if possible
      this.saveMuteToDatabase(currentUserAddress, userToMuteAddress, 'user');
      
      return true;
    } catch (error) {
      console.error('Error muting user:', error);
      return false;
    }
  }

  /**
   * Unmute a user
   */
  public unmuteUser(currentUserAddress: string, userToUnmuteAddress: string): boolean {
    try {
      // Check if actually muted
      if (!this.isUserMuted(currentUserAddress, userToUnmuteAddress)) {
        console.log('User is not muted');
        return false;
      }

      // Get current muted users
      const mutedUsers = this.getMutedUsers(currentUserAddress);
      
      // Remove the user from muted list
      const updatedMutedUsers = mutedUsers.filter(
        address => address.toLowerCase() !== userToUnmuteAddress.toLowerCase()
      );
      
      // Save to localStorage
      const mutedUsersJson = localStorage.getItem(this.MUTED_USERS_KEY);
      const mutedUsersMap = mutedUsersJson ? JSON.parse(mutedUsersJson) : {};
      mutedUsersMap[currentUserAddress.toLowerCase()] = updatedMutedUsers;
      localStorage.setItem(this.MUTED_USERS_KEY, JSON.stringify(mutedUsersMap));
      
      // Also remove from database if possible
      this.removeMuteFromDatabase(currentUserAddress, userToUnmuteAddress, 'user');
      
      return true;
    } catch (error) {
      console.error('Error unmuting user:', error);
      return false;
    }
  }

  /**
   * Get all muted posts for the current user
   */
  public getMutedPosts(currentUserAddress: string): string[] {
    try {
      // First try to get from localStorage
      const mutedPostsJson = localStorage.getItem(this.MUTED_POSTS_KEY);
      if (mutedPostsJson) {
        const mutedPostsMap = JSON.parse(mutedPostsJson);
        return mutedPostsMap[currentUserAddress.toLowerCase()] || [];
      }
      return [];
    } catch (error) {
      console.error('Error getting muted posts:', error);
      return [];
    }
  }

  /**
   * Check if a post is muted
   */
  public isPostMuted(currentUserAddress: string, postId: string): boolean {
    const mutedPosts = this.getMutedPosts(currentUserAddress);
    return mutedPosts.includes(postId);
  }

  /**
   * Mute a post
   */
  public mutePost(currentUserAddress: string, postId: string): boolean {
    try {
      // Check if already muted
      if (this.isPostMuted(currentUserAddress, postId)) {
        console.log('Post is already muted');
        return false;
      }

      // Get current muted posts
      const mutedPosts = this.getMutedPosts(currentUserAddress);
      
      // Add the new post to mute
      mutedPosts.push(postId);
      
      // Save to localStorage
      const mutedPostsJson = localStorage.getItem(this.MUTED_POSTS_KEY);
      const mutedPostsMap = mutedPostsJson ? JSON.parse(mutedPostsJson) : {};
      mutedPostsMap[currentUserAddress.toLowerCase()] = mutedPosts;
      localStorage.setItem(this.MUTED_POSTS_KEY, JSON.stringify(mutedPostsMap));
      
      // Also save to database if possible
      this.saveMuteToDatabase(currentUserAddress, postId, 'post');
      
      return true;
    } catch (error) {
      console.error('Error muting post:', error);
      return false;
    }
  }

  /**
   * Unmute a post
   */
  public unmutePost(currentUserAddress: string, postId: string): boolean {
    try {
      // Check if actually muted
      if (!this.isPostMuted(currentUserAddress, postId)) {
        console.log('Post is not muted');
        return false;
      }

      // Get current muted posts
      const mutedPosts = this.getMutedPosts(currentUserAddress);
      
      // Remove the post from muted list
      const updatedMutedPosts = mutedPosts.filter(id => id !== postId);
      
      // Save to localStorage
      const mutedPostsJson = localStorage.getItem(this.MUTED_POSTS_KEY);
      const mutedPostsMap = mutedPostsJson ? JSON.parse(mutedPostsJson) : {};
      mutedPostsMap[currentUserAddress.toLowerCase()] = updatedMutedPosts;
      localStorage.setItem(this.MUTED_POSTS_KEY, JSON.stringify(mutedPostsMap));
      
      // Also remove from database if possible
      this.removeMuteFromDatabase(currentUserAddress, postId, 'post');
      
      return true;
    } catch (error) {
      console.error('Error unmuting post:', error);
      return false;
    }
  }

  /**
   * Save mute to database
   */
  private async saveMuteToDatabase(
    currentUserAddress: string, 
    targetId: string, 
    type: 'user' | 'post'
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('user_mutes')
        .insert({
          id: crypto.randomUUID(),
          user_id: currentUserAddress.toLowerCase(),
          target_id: type === 'user' ? targetId.toLowerCase() : targetId,
          type: type,
          created_at: new Date().toISOString()
        });

      if (error) {
        console.error('Error saving mute to database:', error);
      }
    } catch (error) {
      console.error('Error in saveMuteToDatabase:', error);
    }
  }

  /**
   * Remove mute from database
   */
  private async removeMuteFromDatabase(
    currentUserAddress: string, 
    targetId: string, 
    type: 'user' | 'post'
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('user_mutes')
        .delete()
        .eq('user_id', currentUserAddress.toLowerCase())
        .eq('target_id', type === 'user' ? targetId.toLowerCase() : targetId)
        .eq('type', type);

      if (error) {
        console.error('Error removing mute from database:', error);
      }
    } catch (error) {
      console.error('Error in removeMuteFromDatabase:', error);
    }
  }
}

// Export a singleton instance
export const muteService = new MuteService();
export default muteService;
