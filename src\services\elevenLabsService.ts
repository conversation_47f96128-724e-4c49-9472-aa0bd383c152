import { apiKeyManager, ApiKeyType } from './apiKeyManager';

// Interface for ElevenLabs voice options
export interface ElevenLabsVoiceOptions {
  voiceId: string;
  stability?: number;
  similarityBoost?: number;
  style?: number;
  speakerBoost?: boolean;
}

// Interface for ElevenLabs voice
export interface ElevenLabsVoice {
  voice_id: string;
  name: string;
  settings: {
    stability: number;
    similarity_boost: number;
    style: number;
    speaker_boost: boolean;
  };
  category?: string;         // Make optional
  description?: string;      // Make optional
  preview_url?: string;      // Make optional
  available_for_tiers?: string[]; // Make optional
}

class ElevenLabsService {
  private apiKey: string = '';
  private apiUrl: string = 'https://api.elevenlabs.io/v1';
  private defaultVoiceId: string = 'EXAVITQu4vr4xnSDxMaL'; // Antoni voice

  // Cache for voices
  private voicesCache: ElevenLabsVoice[] | null = null;

  constructor() {
    // Get API key from the API Key Manager
    this.apiKey = apiKeyManager.getApiKey(ApiKeyType.ELEVEN_LABS);

    // Listen for API key changes
    apiKeyManager.addListener(ApiKeyType.ELEVEN_LABS, (apiKey) => {
      this.apiKey = apiKey;
      console.log('ElevenLabs API key updated');

      // Initialize the service with the new API key
      if (apiKey) {
        this.initialize();
      }
    });

    // Initialize the service if API key is already set
    if (this.apiKey) {
      setTimeout(() => {
        this.initialize();
      }, 1000); // Delay initialization to avoid blocking the UI
    }
  }

  /**
   * Set the API key for ElevenLabs
   */
  public setApiKey(apiKey: string): void {
    // Update the API key in the API Key Manager
    apiKeyManager.setApiKey(ApiKeyType.ELEVEN_LABS, apiKey);

    // The listener will update this.apiKey
    console.log('ElevenLabs API key set successfully');
  }

  /**
   * Initialize the service with default settings
   */
  public initialize(): void {
    console.log('Initializing ElevenLabs service...');
    // Pre-fetch voices to validate the API key
    this.getVoices().then(voices => {
      if (voices.length > 0) {
        console.log(`ElevenLabs service initialized with ${voices.length} voices available`);
      } else {
        console.warn('ElevenLabs service initialized but no voices were found');
      }
    }).catch(error => {
      console.error('Error initializing ElevenLabs service:', error);
    });
  }

  /**
   * Set the default voice ID
   */
  public setDefaultVoiceId(voiceId: string): void {
    this.defaultVoiceId = voiceId;
  }

  /**
   * Get all available voices
   */
  public async getVoices(): Promise<ElevenLabsVoice[]> {
    // Return cached voices if available
    if (this.voicesCache) {
      return this.voicesCache;
    }

    try {
      const response = await fetch(`${this.apiUrl}/voices`, {
        method: 'GET',
        headers: {
          'xi-api-key': this.apiKey,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`ElevenLabs API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      this.voicesCache = data.voices;
      return data.voices;
    } catch (error) {
      console.error('Error fetching voices:', error);
      return [];
    }
  }

  /**
   * Get a specific voice by ID
   */
  public async getVoice(voiceId: string): Promise<ElevenLabsVoice | null> {
    try {
      // List of known good voice IDs
      const knownVoiceIds = [
        'EXAVITQu4vr4xnSDxMaL', // Antoni
        '21m00Tcm4TlvDq8ikWAM', // Josh
        'AZnzlk1XvdvUeBnXmlld', // Domi
        'MF3mGyEYCl7XYWbV9V6O', // Elli
        'TxGEqnHWrfWFTfGW9XjX', // Adam
        'VR6AewLTigWG4xSOukaG', // Sam
        'pNInz6obpgDQGcFmaJgB', // Bella
        'yoZ06aMxZJJ28mfd3POQ', // Harry
        'jBpfuIE2acCO8z3wKNLl', // Liam
        'jsCqWAovK2LkecY7zXl4'  // Dorothy
      ];

      // Check if the voice ID is valid
      if (!knownVoiceIds.includes(voiceId)) {
        console.warn(`Voice ID ${voiceId} not recognized. Using default voice.`);
        // Use default voice ID
        voiceId = 'EXAVITQu4vr4xnSDxMaL';
      }

      const response = await fetch(`${this.apiUrl}/voices/${voiceId}`, {
        method: 'GET',
        headers: {
          'xi-api-key': this.apiKey,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        console.warn(`Error fetching voice ${voiceId}: ${response.status} ${response.statusText}`);
        // Return a default voice object
        return {
          voice_id: 'EXAVITQu4vr4xnSDxMaL',
          name: 'Antoni',
          settings: {
            stability: 0.5,
            similarity_boost: 0.75,
            style: 0,
            speaker_boost: true
          }
        };
      }

      return await response.json();
    } catch (error) {
      console.error(`Error fetching voice ${voiceId}:`, error);
      // Return a default voice object
      return {
        voice_id: 'EXAVITQu4vr4xnSDxMaL',
        name: 'Antoni',
        settings: {
          stability: 0.5,
          similarity_boost: 0.75,
          style: 0,
          speaker_boost: true
        }
      };
    }
  }

  /**
   * Generate speech from text
   */
  public async generateSpeech(
    text: string,
    options: Partial<ElevenLabsVoiceOptions> = {}
  ): Promise<{ audioUrl: string; audioDuration: number }> {
    try {
      // Use the provided voice ID or the default
      const voiceId = options.voiceId || this.defaultVoiceId;

      // List of known good voice IDs
      const knownVoiceIds = [
        'EXAVITQu4vr4xnSDxMaL', // Antoni
        '21m00Tcm4TlvDq8ikWAM', // Josh
        'AZnzlk1XvdvUeBnXmlld', // Domi
        'MF3mGyEYCl7XYWbV9V6O', // Elli
        'TxGEqnHWrfWFTfGW9XjX', // Adam
        'VR6AewLTigWG4xSOukaG', // Sam
        'pNInz6obpgDQGcFmaJgB', // Bella
        'yoZ06aMxZJJ28mfd3POQ', // Harry
        'jBpfuIE2acCO8z3wKNLl', // Liam
        'jsCqWAovK2LkecY7zXl4'  // Dorothy
      ];

      // Check if the voice ID is valid
      const actualVoiceId = knownVoiceIds.includes(voiceId) ? voiceId : 'EXAVITQu4vr4xnSDxMaL';

      if (actualVoiceId !== voiceId) {
        console.warn(`Voice ID ${voiceId} not recognized. Using default voice.`);
      }

      // Prepare the request body with default settings
      const body = {
        text,
        model_id: 'eleven_monolingual_v1',
        voice_settings: {
          stability: options.stability ?? 0.5,
          similarity_boost: options.similarityBoost ?? 0.75,
          style: options.style ?? 0,
          speaker_boost: options.speakerBoost ?? true
        }
      };

      // Make the API request
      const response = await fetch(`${this.apiUrl}/text-to-speech/${actualVoiceId}`, {
        method: 'POST',
        headers: {
          'xi-api-key': this.apiKey,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      });

      if (!response.ok) {
        throw new Error(`ElevenLabs API error: ${response.status} ${response.statusText}`);
      }

      // Get the audio data
      const audioBlob = await response.blob();

      // Create a URL for the audio blob
      const audioUrl = URL.createObjectURL(audioBlob);

      // Estimate the duration based on word count (about 150 words per minute)
      const wordCount = text.split(/\s+/).length;
      const estimatedDuration = (wordCount / 150) * 60;

      return {
        audioUrl,
        audioDuration: estimatedDuration
      };
    } catch (error) {
      console.error('Error generating speech:', error);

      // Return a placeholder in case of error
      return {
        audioUrl: '',
        audioDuration: 0
      };
    }
  }

  /**
   * Get available models
   */
  public async getModels(): Promise<any[]> {
    try {
      const response = await fetch(`${this.apiUrl}/models`, {
        method: 'GET',
        headers: {
          'xi-api-key': this.apiKey,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`ElevenLabs API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching models:', error);
      return [];
    }
  }

  /**
   * Get user subscription info
   */
  public async getUserSubscriptionInfo(): Promise<any> {
    try {
      const response = await fetch(`${this.apiUrl}/user/subscription`, {
        method: 'GET',
        headers: {
          'xi-api-key': this.apiKey,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`ElevenLabs API error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching user subscription info:', error);
      return null;
    }
  }

  /**
   * Create a new voice
   */
  public async createVoice(
    name: string,
    files: File[],
    description = 'Custom voice',
    stability = 0.5,
    similarityBoost = 0.75,
    style = 0,
    speakerBoost = true
  ): Promise<ElevenLabsVoice | null> {
    try {
      const formData = new FormData();
      formData.append('name', name);
      formData.append('description', description);
      formData.append('stability', stability.toString());
      formData.append('similarity_boost', similarityBoost.toString());
      formData.append('style', style.toString());
      formData.append('speaker_boost', speakerBoost.toString());
      files.forEach(file => formData.append('files', file));

      const response = await fetch(`${this.apiUrl}/voices`, {
        method: 'POST',
        headers: {
          'xi-api-key': this.apiKey,
          'Content-Type': 'multipart/form-data'
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`ElevenLabs API error: ${response.status} ${response.statusText}`);
      }

      const newVoice = await response.json();
      return {
        voice_id: newVoice.voice_id,
        name: newVoice.name,
        settings: {
          stability,
          similarity_boost: similarityBoost,
          style,
          speaker_boost: speakerBoost
        },
        category: 'custom',
        description: description,
        preview_url: '', // Can be empty as it's now optional
        available_for_tiers: ['creator'] // Add a placeholder value
      };
    } catch (error) {
      console.error('Error creating voice:', error);
      return null;
    }
  }
}

// Export a singleton instance
export const elevenLabsService = new ElevenLabsService();
