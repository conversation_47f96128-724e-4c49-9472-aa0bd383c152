
export interface UserProfile {
  id?: string;
  username: string;
  displayName: string;
  profileImageUrl: string;
  coverImageUrl: string;
  bio: string;
  walletAddress: string;
  socialLinks: {
    twitter: string;
    github: string;
    website: string;
  };
  stats: {
    posts: number;
    likes: number;
    tips: number;
  };
  verification?: {
    isVerified: boolean;
    type?: string;
  };
}

export interface RegistrationData {
  email: string;
  password: string;
  username: string;
  displayName: string;
}

export interface LoginData {
  email: string;
  password: string;
  rememberMe?: boolean;
}

// Additional interfaces needed for proper typing
export interface UserProfileUpdate {
  displayName?: string;
  username?: string;
  bio?: string;
  profileImageUrl?: string;
  coverImageUrl?: string;
  socialLinks?: {
    twitter?: string;
    github?: string;
    website?: string;
  };
  stats?: {
    posts?: number;
    likes?: number;
    tips?: number;
  };
}

export function createDefaultProfile(address: string): UserProfile {
  return {
    id: address,
    username: `user_${address.substring(0, 6)}`,
    displayName: `User ${address.substring(0, 6)}`,
    profileImageUrl: '',
    coverImageUrl: '',
    bio: '',
    walletAddress: address,
    socialLinks: {
      twitter: '',
      github: '',
      website: ''
    },
    stats: {
      posts: 0,
      likes: 0,
      tips: 0
    }
  };
}
