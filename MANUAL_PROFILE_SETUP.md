# 👤 Manual Profile RPC Functions Setup

Your profile system needs these RPC functions to work properly. Here's how to create them:

## Step 1: Go to Supabase SQL Editor

1. Open your **Supabase Dashboard**
2. Go to **SQL Editor**
3. Create a **New Query**

## Step 2: Create get_or_create_profile Function

Copy and paste this SQL:

```sql
-- Function to get profile by wallet address or create if not exists
CREATE OR REPLACE FUNCTION get_or_create_profile(
  p_wallet_address TEXT
)
RETURNS TABLE(
  id UUID,
  wallet_address TEXT,
  username TEXT,
  display_name TEXT,
  bio TEXT,
  avatar_url TEXT,
  cover_image_url TEXT,
  social_links JSONB,
  post_count INTEGER,
  like_count INTEGER,
  tip_count INTEGER,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  profile_id UUID;
  default_username TEXT;
  username_counter INTEGER := 0;
BEGIN
  -- Normalize the wallet address
  p_wallet_address := LOWER(p_wallet_address);
  
  -- Check if profile already exists
  SELECT profiles.id INTO profile_id
  FROM profiles
  WHERE profiles.wallet_address = p_wallet_address;
  
  -- If profile doesn't exist, create one
  IF profile_id IS NULL THEN
    -- Generate a new UUID for the profile
    profile_id := gen_random_uuid();
    
    -- Generate a default username
    default_username := 'user_' || SUBSTRING(p_wallet_address FROM 1 FOR 8);
    
    -- Ensure username is unique
    WHILE EXISTS (SELECT 1 FROM profiles WHERE username = default_username) LOOP
      username_counter := username_counter + 1;
      default_username := 'user_' || SUBSTRING(p_wallet_address FROM 1 FOR 8) || '_' || username_counter::TEXT;
    END LOOP;
    
    -- Insert new profile
    INSERT INTO profiles (
      id,
      wallet_address,
      username,
      display_name,
      bio,
      avatar_url,
      cover_image_url,
      social_links,
      post_count,
      like_count,
      tip_count,
      created_at,
      updated_at
    ) VALUES (
      profile_id,
      p_wallet_address,
      default_username,
      'User ' || SUBSTRING(p_wallet_address FROM 1 FOR 6),
      '',
      '',
      '',
      '{}'::JSONB,
      0,
      0,
      0,
      NOW(),
      NOW()
    );
  END IF;
  
  -- Return the profile
  RETURN QUERY
  SELECT 
    profiles.id,
    profiles.wallet_address,
    profiles.username,
    profiles.display_name,
    profiles.bio,
    profiles.avatar_url,
    profiles.cover_image_url,
    profiles.social_links,
    profiles.post_count,
    profiles.like_count,
    profiles.tip_count,
    profiles.created_at,
    profiles.updated_at
  FROM profiles
  WHERE profiles.id = profile_id;
END;
$$;
```

## Step 3: Create update_profile_by_address Function

Copy and paste this SQL:

```sql
-- Function to update or create a profile by wallet address
CREATE OR REPLACE FUNCTION update_profile_by_address(
  p_wallet_address TEXT,
  p_username TEXT DEFAULT NULL,
  p_display_name TEXT DEFAULT NULL,
  p_bio TEXT DEFAULT NULL,
  p_avatar_url TEXT DEFAULT NULL,
  p_cover_image_url TEXT DEFAULT NULL,
  p_social_links JSONB DEFAULT NULL
)
RETURNS TABLE(
  id UUID,
  wallet_address TEXT,
  username TEXT,
  display_name TEXT,
  bio TEXT,
  avatar_url TEXT,
  cover_image_url TEXT,
  social_links JSONB,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  profile_id UUID;
  final_username TEXT;
  username_counter INTEGER := 0;
BEGIN
  -- Normalize the wallet address
  p_wallet_address := LOWER(p_wallet_address);
  
  -- Check if profile already exists
  SELECT profiles.id INTO profile_id
  FROM profiles
  WHERE profiles.wallet_address = p_wallet_address;
  
  -- If profile doesn't exist, create one
  IF profile_id IS NULL THEN
    -- Generate a new UUID for the profile
    profile_id := gen_random_uuid();
    
    -- Ensure username is unique
    final_username := p_username;
    IF final_username IS NULL OR final_username = '' THEN
      final_username := 'user_' || SUBSTRING(p_wallet_address FROM 1 FOR 8);
    END IF;
    
    -- Check for username conflicts and resolve them
    WHILE EXISTS (SELECT 1 FROM profiles WHERE username = final_username) LOOP
      username_counter := username_counter + 1;
      final_username := p_username || '_' || username_counter::TEXT;
    END LOOP;
    
    -- Insert new profile
    INSERT INTO profiles (
      id,
      wallet_address,
      username,
      display_name,
      bio,
      avatar_url,
      cover_image_url,
      social_links,
      post_count,
      like_count,
      tip_count,
      created_at,
      updated_at
    ) VALUES (
      profile_id,
      p_wallet_address,
      final_username,
      COALESCE(p_display_name, 'User ' || SUBSTRING(p_wallet_address FROM 1 FOR 6)),
      COALESCE(p_bio, ''),
      COALESCE(p_avatar_url, ''),
      COALESCE(p_cover_image_url, ''),
      COALESCE(p_social_links, '{}'::JSONB),
      0,
      0,
      0,
      NOW(),
      NOW()
    );
  ELSE
    -- Update existing profile
    final_username := p_username;
    
    -- If username is being changed, ensure it's unique
    IF p_username IS NOT NULL AND p_username != '' THEN
      -- Check if this username is already taken by another profile
      WHILE EXISTS (
        SELECT 1 FROM profiles 
        WHERE username = final_username 
        AND id != profile_id
      ) LOOP
        username_counter := username_counter + 1;
        final_username := p_username || '_' || username_counter::TEXT;
      END LOOP;
    END IF;
    
    -- Update the profile
    UPDATE profiles SET
      username = COALESCE(final_username, username),
      display_name = COALESCE(p_display_name, display_name),
      bio = COALESCE(p_bio, bio),
      avatar_url = COALESCE(p_avatar_url, avatar_url),
      cover_image_url = COALESCE(p_cover_image_url, cover_image_url),
      social_links = COALESCE(p_social_links, social_links),
      updated_at = NOW()
    WHERE profiles.id = profile_id;
  END IF;
  
  -- Return the updated/created profile
  RETURN QUERY
  SELECT 
    profiles.id,
    profiles.wallet_address,
    profiles.username,
    profiles.display_name,
    profiles.bio,
    profiles.avatar_url,
    profiles.cover_image_url,
    profiles.social_links,
    profiles.created_at,
    profiles.updated_at
  FROM profiles
  WHERE profiles.id = profile_id;
END;
$$;
```

## Step 4: Grant Permissions

Copy and paste this SQL:

```sql
-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION update_profile_by_address TO authenticated;
GRANT EXECUTE ON FUNCTION get_or_create_profile TO authenticated;
GRANT EXECUTE ON FUNCTION update_profile_by_address TO anon;
GRANT EXECUTE ON FUNCTION get_or_create_profile TO anon;
```

## Step 5: Test the Functions

Copy and paste this SQL to test:

```sql
-- Test creating a profile
SELECT * FROM get_or_create_profile('test_user_123');

-- Test updating a profile
SELECT * FROM update_profile_by_address(
  'test_user_123',
  'my_username',
  'My Display Name',
  'This is my bio',
  'https://example.com/avatar.jpg',
  'https://example.com/cover.jpg',
  '{"twitter": "@myhandle", "website": "https://mysite.com"}'::jsonb
);

-- Clean up test data
DELETE FROM profiles WHERE wallet_address = 'test_user_123';
```

## Step 6: Refresh Your App

After creating the functions:

1. **Hard refresh your app** (Ctrl+F5)
2. **Check the browser console** - should see fewer errors
3. **Try editing your profile** - should work now
4. **Check that changes persist** after refresh

## ✅ Expected Results

After setup, you should see:
- ✅ No more "404 Not Found" errors for profile RPC functions
- ✅ Profile editing works and saves to database
- ✅ Profile changes persist after page refresh
- ✅ Automatic profile creation for new users

## 🔧 Troubleshooting

If you still get errors:
1. **Check function names** - Make sure they're exactly as shown
2. **Run one function at a time** - Don't paste all SQL at once
3. **Check for typos** - SQL is case-sensitive
4. **Verify permissions** - Make sure GRANT statements ran successfully

Your profile system should work perfectly after this setup! 🎉
