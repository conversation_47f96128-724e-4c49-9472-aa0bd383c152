import { supabase } from '@/integrations/supabase/client';

export type RelationshipType = 'follow' | 'block' | 'mute';

export interface UserRelationship {
  id: string;
  fromAddress: string;
  toAddress: string;
  type: RelationshipType;
  createdAt: Date;
}

/**
 * Service for managing user relationships (follow, block, mute)
 */
class UserRelationshipService {
  /**
   * Follow a user
   */
  public async followUser(fromAddress: string, toAddress: string): Promise<boolean> {
    try {
      // Check if already following
      const { data: existingData } = await supabase
        .from('user_relationships')
        .select('*')
        .eq('from_address', fromAddress)
        .eq('to_address', toAddress)
        .eq('type', 'follow')
        .single();
      
      if (existingData) {
        console.log('Already following this user');
        return true;
      }
      
      // Create the relationship
      const { error } = await supabase
        .from('user_relationships')
        .insert({
          id: crypto.randomUUID(),
          from_address: fromAddress,
          to_address: toAddress,
          type: 'follow',
          created_at: new Date().toISOString()
        });
      
      if (error) {
        console.error('Error following user:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Error in followUser:', error);
      return false;
    }
  }
  
  /**
   * Unfollow a user
   */
  public async unfollowUser(fromAddress: string, toAddress: string): Promise<boolean> {
    try {
      // Delete the relationship
      const { error } = await supabase
        .from('user_relationships')
        .delete()
        .eq('from_address', fromAddress)
        .eq('to_address', toAddress)
        .eq('type', 'follow');
      
      if (error) {
        console.error('Error unfollowing user:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Error in unfollowUser:', error);
      return false;
    }
  }
  
  /**
   * Block a user
   */
  public async blockUser(fromAddress: string, toAddress: string): Promise<boolean> {
    try {
      // Check if already blocked
      const { data: existingData } = await supabase
        .from('user_relationships')
        .select('*')
        .eq('from_address', fromAddress)
        .eq('to_address', toAddress)
        .eq('type', 'block')
        .single();
      
      if (existingData) {
        console.log('Already blocking this user');
        return true;
      }
      
      // Create the relationship
      const { error } = await supabase
        .from('user_relationships')
        .insert({
          id: crypto.randomUUID(),
          from_address: fromAddress,
          to_address: toAddress,
          type: 'block',
          created_at: new Date().toISOString()
        });
      
      if (error) {
        console.error('Error blocking user:', error);
        return false;
      }
      
      // If we're blocking, also unfollow if following
      await this.unfollowUser(fromAddress, toAddress);
      
      return true;
    } catch (error) {
      console.error('Error in blockUser:', error);
      return false;
    }
  }
  
  /**
   * Unblock a user
   */
  public async unblockUser(fromAddress: string, toAddress: string): Promise<boolean> {
    try {
      // Delete the relationship
      const { error } = await supabase
        .from('user_relationships')
        .delete()
        .eq('from_address', fromAddress)
        .eq('to_address', toAddress)
        .eq('type', 'block');
      
      if (error) {
        console.error('Error unblocking user:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Error in unblockUser:', error);
      return false;
    }
  }
  
  /**
   * Mute a user
   */
  public async muteUser(fromAddress: string, toAddress: string): Promise<boolean> {
    try {
      // Check if already muted
      const { data: existingData } = await supabase
        .from('user_relationships')
        .select('*')
        .eq('from_address', fromAddress)
        .eq('to_address', toAddress)
        .eq('type', 'mute')
        .single();
      
      if (existingData) {
        console.log('Already muting this user');
        return true;
      }
      
      // Create the relationship
      const { error } = await supabase
        .from('user_relationships')
        .insert({
          id: crypto.randomUUID(),
          from_address: fromAddress,
          to_address: toAddress,
          type: 'mute',
          created_at: new Date().toISOString()
        });
      
      if (error) {
        console.error('Error muting user:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Error in muteUser:', error);
      return false;
    }
  }
  
  /**
   * Unmute a user
   */
  public async unmuteUser(fromAddress: string, toAddress: string): Promise<boolean> {
    try {
      // Delete the relationship
      const { error } = await supabase
        .from('user_relationships')
        .delete()
        .eq('from_address', fromAddress)
        .eq('to_address', toAddress)
        .eq('type', 'mute');
      
      if (error) {
        console.error('Error unmuting user:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Error in unmuteUser:', error);
      return false;
    }
  }
  
  /**
   * Check if a user is following another user
   */
  public async isFollowing(fromAddress: string, toAddress: string): Promise<boolean> {
    try {
      const { data } = await supabase
        .from('user_relationships')
        .select('*')
        .eq('from_address', fromAddress)
        .eq('to_address', toAddress)
        .eq('type', 'follow')
        .single();
      
      return !!data;
    } catch (error) {
      console.error('Error in isFollowing:', error);
      return false;
    }
  }
  
  /**
   * Check if a user is blocked
   */
  public async isBlocked(fromAddress: string, toAddress: string): Promise<boolean> {
    try {
      const { data } = await supabase
        .from('user_relationships')
        .select('*')
        .eq('from_address', fromAddress)
        .eq('to_address', toAddress)
        .eq('type', 'block')
        .single();
      
      return !!data;
    } catch (error) {
      console.error('Error in isBlocked:', error);
      return false;
    }
  }
  
  /**
   * Check if a user is muted
   */
  public async isMuted(fromAddress: string, toAddress: string): Promise<boolean> {
    try {
      const { data } = await supabase
        .from('user_relationships')
        .select('*')
        .eq('from_address', fromAddress)
        .eq('to_address', toAddress)
        .eq('type', 'mute')
        .single();
      
      return !!data;
    } catch (error) {
      console.error('Error in isMuted:', error);
      return false;
    }
  }
  
  /**
   * Get all users that a user is following
   */
  public async getFollowing(userAddress: string): Promise<string[]> {
    try {
      const { data } = await supabase
        .from('user_relationships')
        .select('to_address')
        .eq('from_address', userAddress)
        .eq('type', 'follow');
      
      return data ? data.map(item => item.to_address) : [];
    } catch (error) {
      console.error('Error in getFollowing:', error);
      return [];
    }
  }
  
  /**
   * Get all users that are following a user
   */
  public async getFollowers(userAddress: string): Promise<string[]> {
    try {
      const { data } = await supabase
        .from('user_relationships')
        .select('from_address')
        .eq('to_address', userAddress)
        .eq('type', 'follow');
      
      return data ? data.map(item => item.from_address) : [];
    } catch (error) {
      console.error('Error in getFollowers:', error);
      return [];
    }
  }
}

// Export a singleton instance
export const userRelationshipService = new UserRelationshipService();
export default userRelationshipService;
