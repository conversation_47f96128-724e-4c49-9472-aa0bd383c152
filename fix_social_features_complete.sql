-- Complete Fix for All Social Features
-- Run this SQL in your Supabase SQL Editor

-- 1. Fix follows table structure and RLS policies
CREATE TABLE IF NOT EXISTS follows (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
  follower_id TEXT NOT NULL,
  following_id TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(follower_id, following_id)
);

-- Enable RLS on follows table
ALTER TABLE follows ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Allow all operations on follows" ON follows;
DROP POLICY IF EXISTS "Users can view all follows" ON follows;
DROP POLICY IF EXISTS "Users can insert follows" ON follows;
DROP POLICY IF EXISTS "Users can delete follows" ON follows;

-- Create permissive RLS policies for follows
CREATE POLICY "Allow all operations on follows" 
ON follows 
FOR ALL 
USING (true) 
WITH CHECK (true);

-- Create indexes for follows
CREATE INDEX IF NOT EXISTS follows_follower_id_idx ON follows(follower_id);
CREATE INDEX IF NOT EXISTS follows_following_id_idx ON follows(following_id);

-- 2. Fix profiles table to use TEXT IDs
-- Check if profiles table needs ID conversion
DO $$
DECLARE
    profiles_id_type TEXT;
BEGIN
    SELECT data_type INTO profiles_id_type
    FROM information_schema.columns 
    WHERE table_name = 'profiles' 
    AND column_name = 'id' 
    AND table_schema = 'public';
    
    RAISE NOTICE 'profiles.id column type: %', profiles_id_type;
    
    -- If profiles.id is UUID, convert it to TEXT
    IF profiles_id_type = 'uuid' THEN
        RAISE NOTICE 'Converting profiles.id from UUID to TEXT...';
        
        -- Drop foreign key constraints temporarily
        ALTER TABLE voice_messages DROP CONSTRAINT IF EXISTS voice_messages_profile_id_fkey;
        
        -- Convert profiles.id to TEXT
        ALTER TABLE profiles 
        ALTER COLUMN id TYPE TEXT USING id::text;
        
        -- Update default value
        ALTER TABLE profiles 
        ALTER COLUMN id SET DEFAULT gen_random_uuid()::text;
        
        -- Recreate foreign key constraint
        ALTER TABLE voice_messages 
        ADD CONSTRAINT voice_messages_profile_id_fkey 
        FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'profiles table converted to TEXT successfully';
    ELSE
        RAISE NOTICE 'profiles.id is already TEXT type';
    END IF;
END $$;

-- 3. Ensure voice_messages table has proper pin support
ALTER TABLE voice_messages 
ADD COLUMN IF NOT EXISTS is_pinned BOOLEAN DEFAULT FALSE;

-- Create index for pinned posts
CREATE INDEX IF NOT EXISTS voice_messages_is_pinned_idx ON voice_messages(profile_id, is_pinned);

-- 4. Fix notifications table structure
CREATE TABLE IF NOT EXISTS notifications (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
  type TEXT NOT NULL,
  from_address TEXT NOT NULL,
  to_address TEXT NOT NULL,
  data JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  read BOOLEAN DEFAULT FALSE
);

-- Enable RLS on notifications table
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Drop existing notification policies
DROP POLICY IF EXISTS "Allow all operations on notifications" ON notifications;
DROP POLICY IF EXISTS "Users can view all notifications" ON notifications;
DROP POLICY IF EXISTS "Users can insert notifications" ON notifications;
DROP POLICY IF EXISTS "Users can update notifications" ON notifications;

-- Create permissive RLS policies for notifications
CREATE POLICY "Allow all operations on notifications" 
ON notifications 
FOR ALL 
USING (true) 
WITH CHECK (true);

-- Create indexes for notifications
CREATE INDEX IF NOT EXISTS notifications_to_address_idx ON notifications(to_address);
CREATE INDEX IF NOT EXISTS notifications_from_address_idx ON notifications(from_address);
CREATE INDEX IF NOT EXISTS notifications_created_at_idx ON notifications(created_at DESC);

-- 5. Grant necessary permissions
GRANT ALL ON follows TO anon;
GRANT ALL ON follows TO authenticated;
GRANT ALL ON notifications TO anon;
GRANT ALL ON notifications TO authenticated;
GRANT ALL ON profiles TO anon;
GRANT ALL ON profiles TO authenticated;
GRANT ALL ON voice_messages TO anon;
GRANT ALL ON voice_messages TO authenticated;

-- 6. Create helper functions for social features

-- Function to get follower count
CREATE OR REPLACE FUNCTION get_follower_count(user_address TEXT)
RETURNS INTEGER AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)::INTEGER
    FROM follows
    WHERE following_id = LOWER(user_address)
  );
END;
$$ LANGUAGE plpgsql;

-- Function to get following count
CREATE OR REPLACE FUNCTION get_following_count(user_address TEXT)
RETURNS INTEGER AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)::INTEGER
    FROM follows
    WHERE follower_id = LOWER(user_address)
  );
END;
$$ LANGUAGE plpgsql;

-- Function to check if user is following another user
CREATE OR REPLACE FUNCTION is_following(follower_address TEXT, following_address TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM follows
    WHERE follower_id = LOWER(follower_address)
    AND following_id = LOWER(following_address)
  );
END;
$$ LANGUAGE plpgsql;

-- Function to unpin all user messages (for pin functionality)
CREATE OR REPLACE FUNCTION unpin_all_user_messages(user_id TEXT)
RETURNS VOID AS $$
BEGIN
  UPDATE voice_messages
  SET is_pinned = FALSE
  WHERE profile_id = user_id
  AND is_pinned = TRUE;
END;
$$ LANGUAGE plpgsql;

-- 7. Test the social features setup
DO $$
DECLARE
    test_user1 TEXT := 'test_user_1_' || extract(epoch from now())::text;
    test_user2 TEXT := 'test_user_2_' || extract(epoch from now())::text;
    test_post_id TEXT := 'test_post_' || extract(epoch from now())::text;
BEGIN
    -- Test follow functionality
    INSERT INTO follows (follower_id, following_id) 
    VALUES (test_user1, test_user2);
    
    -- Test follower count function
    PERFORM get_follower_count(test_user2);
    PERFORM get_following_count(test_user1);
    
    -- Test is_following function
    PERFORM is_following(test_user1, test_user2);
    
    -- Test profile creation
    INSERT INTO profiles (id, wallet_address, username, display_name, bio)
    VALUES (test_user1, test_user1, 'test_user', 'Test User', 'Test bio');
    
    -- Test post creation
    INSERT INTO voice_messages (id, profile_id, audio_url, transcript, audio_duration, is_pinned)
    VALUES (test_post_id, test_user1, 'https://example.com/test.mp3', 'Test post', 10, FALSE);
    
    -- Test pin functionality
    UPDATE voice_messages SET is_pinned = TRUE WHERE id = test_post_id;
    
    -- Test notification creation
    INSERT INTO notifications (type, from_address, to_address, data)
    VALUES ('follow', test_user1, test_user2, '{"test": true}');
    
    RAISE NOTICE 'All social features test successful!';
    
    -- Clean up test data
    DELETE FROM notifications WHERE from_address = test_user1;
    DELETE FROM voice_messages WHERE id = test_post_id;
    DELETE FROM profiles WHERE id = test_user1;
    DELETE FROM follows WHERE follower_id = test_user1;
    
    RAISE NOTICE 'Test data cleaned up';
    
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Social features test failed: %', SQLERRM;
END $$;

-- 8. Show final table structures
SELECT 
    'follows' as table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'follows'
AND table_schema = 'public'
ORDER BY ordinal_position;

SELECT 
    'notifications' as table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'notifications'
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Success message
SELECT 'All social features fixed and ready to use! 🎉' as status;
