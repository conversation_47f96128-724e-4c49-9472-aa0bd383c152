import { ChainEvent, ChainEventType } from './chainTypes';

export interface ContextualizedEvent {
  event: ChainEvent;
  narrationText: string;
  title: string;
  tags: string[];
  relevantChannels: string[];
  importance: 'low' | 'medium' | 'high' | 'critical';
}

class EventContextualizerService {
  /**
   * Process a chain event and add context for narration
   */
  public contextualizeEvent(event: ChainEvent): ContextualizedEvent {
    // Generate narration text based on event type
    const narrationText = this.generateNarrationText(event);

    // Generate a title for the event
    const title = this.generateTitle(event);

    // Determine relevant tags
    const tags = this.generateTags(event);

    // Determine relevant channels
    const relevantChannels = this.determineRelevantChannels(event);

    // Determine importance level
    const importance = this.determineImportance(event);

    return {
      event,
      narrationText,
      title,
      tags,
      relevantChannels,
      importance
    };
  }

  /**
   * Generate human-readable narration text for the event
   */
  private generateNarrationText(event: ChainEvent): string {
    switch (event.type) {
      case ChainEventType.FUNDING_ROUND:
        return this.generateFundingRoundText(event);

      case ChainEventType.DAO_PROPOSAL_CREATED:
        return this.generateDAOProposalCreatedText(event);

      case ChainEventType.DAO_PROPOSAL_EXECUTED:
        return this.generateDAOProposalExecutedText(event);

      // No NFT-related events

      case ChainEventType.WHALE_MOVEMENT:
        return this.generateWhaleMovementText(event);

      case ChainEventType.SECURITY_INCIDENT:
        return this.generateSecurityIncidentText(event);

      case ChainEventType.BRIDGE_TRANSACTION:
        return this.generateBridgeTransactionText(event);

      case 'welcome':
        return this.generateWelcomeText(event);

      default:
        return this.generateGenericText(event);
    }
  }

  /**
   * Generate a title for the event
   */
  private generateTitle(event: ChainEvent): string {
    switch (event.type) {
      case ChainEventType.FUNDING_ROUND:
        return `${event.data.project} Receives ${this.formatCurrency(event.data.amount, event.data.currency)} Funding`;

      case ChainEventType.DAO_PROPOSAL_EXECUTED:
        return `${event.data.dao} Passes Proposal: ${event.data.proposal}`;

      // No NFT-related events

      case ChainEventType.SECURITY_INCIDENT:
        return `ALERT: Security Incident at ${event.data.protocol}`;

      case 'welcome':
        return `Welcome to Audra - Voice-Based Web3 Social Platform`;

      default:
        return `${event.source.chain.charAt(0).toUpperCase() + event.source.chain.slice(1)} ${event.type.replace(/_/g, ' ').toLowerCase()
          }`;
    }
  }

  /**
   * Generate tags for the event
   */
  private generateTags(event: ChainEvent): string[] {
    const tags: string[] = [
      event.source.chain,
      event.type.toLowerCase().replace(/_/g, '-')
    ];

    if (event.source.project) {
      tags.push(event.source.project.toLowerCase().replace(/\s+/g, '-'));
    }

    if (event.source.dao) {
      tags.push(event.source.dao.toLowerCase().replace(/\s+/g, '-'));
      tags.push('dao');
    }

    switch (event.type) {
      case ChainEventType.FUNDING_ROUND:
        tags.push('funding');
        tags.push('investment');
        break;

      // No NFT-related events

      case ChainEventType.SECURITY_INCIDENT:
        tags.push('security');
        tags.push('alert');
        break;
    }

    return [...new Set(tags)]; // Remove duplicates
  }

  /**
   * Determine which channels this event should be posted to
   */
  private determineRelevantChannels(event: ChainEvent): string[] {
    const channels: string[] = [
      event.source.chain, // e.g., #solana, #ethereum
    ];

    switch (event.type) {
      case ChainEventType.FUNDING_ROUND:
        channels.push('funding');
        break;

      case ChainEventType.DAO_PROPOSAL_CREATED:
      case ChainEventType.DAO_PROPOSAL_EXECUTED:
      case ChainEventType.DAO_VOTE:
        channels.push('daos');
        if (event.source.dao) {
          channels.push(event.source.dao.toLowerCase().replace(/\s+/g, '-'));
        }
        break;

      // No NFT-related events

      case ChainEventType.SECURITY_INCIDENT:
        channels.push('security');
        channels.push('alerts');
        break;

      case ChainEventType.WHALE_MOVEMENT:
        channels.push('whales');
        break;
    }

    return channels;
  }

  /**
   * Determine the importance level of the event
   */
  private determineImportance(event: ChainEvent): 'low' | 'medium' | 'high' | 'critical' {
    switch (event.type) {
      case ChainEventType.SECURITY_INCIDENT:
        return 'critical';

      case ChainEventType.FUNDING_ROUND:
        // High importance for large funding rounds
        if (event.data.amount > 1000000) {
          return 'high';
        }
        return 'medium';

      case ChainEventType.WHALE_MOVEMENT:
        return 'high';

      case ChainEventType.DAO_PROPOSAL_EXECUTED:
        return 'medium';

      // No NFT-related events

      default:
        return 'low';
    }
  }

  // Helper methods for generating specific narration texts

  private generateFundingRoundText(event: ChainEvent): string {
    const { project, amount, currency, funder, round, valuation, details } = event.data;

    const formattedAmount = this.formatCurrency(amount, currency);
    const formattedValuation = valuation ? this.formatCurrency(valuation, currency) : null;

    return `
      Major funding announcement in the crypto space.
      ${project} has secured ${formattedAmount} in funding from ${funder}.
      ${round ? `This investment is part of their ${round} funding round.` : ''}
      ${formattedValuation ? `The deal values the project at approximately ${formattedValuation}.` : ''}
      ${details ? details : ''}
      This investment signals strong confidence in the project's vision and technology.
    `.trim().replace(/\s+/g, ' ');
  }

  private generateDAOProposalCreatedText(event: ChainEvent): string {
    return `${event.data.dao} has submitted a new proposal to ${event.data.proposal}. Voting is now open.`;
  }

  private generateDAOProposalExecutedText(event: ChainEvent): string {
    const { dao, proposal, voteCount, approved, quorum, votingPeriod, implementation } = event.data;

    return `
      Breaking news from the ${dao} governance.
      A proposal to ${proposal} has been ${approved ? 'approved' : 'rejected'} with ${voteCount} votes.
      ${quorum ? `The proposal reached a quorum of ${quorum}.` : ''}
      ${votingPeriod ? `Voting took place over a period of ${votingPeriod}.` : ''}
      ${implementation ? `Implementation details: ${implementation}.` : ''}
      This decision will significantly impact the future direction of the protocol.
    `.trim().replace(/\s+/g, ' ');
  }

  // No NFT-related methods

  private generateWhaleMovementText(event: ChainEvent): string {
    return `Whale wallet ${event.data.wallet} just moved ${this.formatCurrency(event.data.amount, event.data.currency)
      } in ${event.data.token} tokens.`;
  }

  private generateSecurityIncidentText(event: ChainEvent): string {
    const { protocol, amount, currency, issue, status, recommendation, details } = event.data;

    const formattedAmount = this.formatCurrency(amount, currency);

    return `
      Urgent security alert in the crypto space.
      ${protocol} has experienced a critical ${issue}.
      Approximately ${formattedAmount} of user funds are currently at risk.
      ${status ? `Current status: ${status}.` : ''}
      ${recommendation ? `Security recommendation: ${recommendation}.` : ''}
      ${details ? details : ''}
      We will continue to monitor this situation and provide updates as they become available.
    `.trim().replace(/\s+/g, ' ');
  }

  private generateBridgeTransactionText(event: ChainEvent): string {
    return `${event.data.protocol} is now live on ${event.source.chain}. First bridge transactions are flowing.`;
  }

  private generateWelcomeText(_event: ChainEvent): string {
    return `
      Welcome to Audra, the voice-based Web3 social platform!

      We're excited to have you join our community. Audra is a unique platform where you can share your thoughts through voice messages, create voice journals, and stay updated on blockchain events.

      Here's what you can do:
      - Record voice messages and share them with the community
      - Create voice journals with scheduled unlock dates
      - Follow other users and channels
      - Stay updated on blockchain events through our Chain Listener

      Get started by recording your first voice message or exploring channels!
    `.trim().replace(/\s+/g, ' ');
  }

  private generateGenericText(event: ChainEvent): string {
    return `A new ${event.type.replace(/_/g, ' ').toLowerCase()} event has occurred on ${event.source.chain}.`;
  }

  /**
   * Format currency values for narration
   */
  private formatCurrency(amount: number, currency: string): string {
    if (currency === 'USD') {
      return `$${amount.toLocaleString()}`;
    }

    return `${amount.toLocaleString()} ${currency}`;
  }
}

// Export a singleton instance
export const eventContextualizer = new EventContextualizerService();
