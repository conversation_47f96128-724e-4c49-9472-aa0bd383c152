import React, { useState, useEffect } from 'react';
import { Separator } from '@/components/ui/separator';
import { useChannels } from '@/contexts/ChannelContext';
import CreateChannelModal from '@/components/CreateChannelModal';
import JoinChannelModal from '@/components/JoinChannelModal';
import ChannelInviteModal from '@/components/ChannelInviteModal';
import ChannelSettings from '@/components/ChannelSettings';
import ChannelList from '@/components/ChannelList';
import ChannelView from '@/components/ChannelView';
import ChannelGuide from '@/components/ChannelGuide';
import { Button } from '@/components/ui/button';
import { Menu, X, Plus, Hash, HelpCircle } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';

interface ChannelsProps {
  userAddress?: string;
}

const Channels: React.FC<ChannelsProps> = ({ userAddress = '' }) => {
  const { activeChannel, getChannelById, setActiveChannel } = useChannels();
  const isMobile = useIsMobile();

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isJoinModalOpen, setIsJoinModalOpen] = useState(false);
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [isGuideModalOpen, setIsGuideModalOpen] = useState(false);
  const [selectedChannelId, setSelectedChannelId] = useState<string | null>(null);
  const [replyingToId, setReplyingToId] = useState<string | null>(null);
  const [showSidebar, setShowSidebar] = useState(!isMobile);

  // Update sidebar visibility when screen size changes
  useEffect(() => {
    setShowSidebar(!isMobile);
  }, [isMobile]);

  // Close sidebar when a channel is selected on mobile
  useEffect(() => {
    if (isMobile && activeChannel) {
      setShowSidebar(false);
    }
  }, [activeChannel, isMobile]);

  const handleChannelSettings = (channelId: string) => {
    setSelectedChannelId(channelId);
    setIsSettingsModalOpen(true);
  };

  const handleChannelInvite = (channelId: string) => {
    setSelectedChannelId(channelId);
    setIsInviteModalOpen(true);
  };

  const handleReply = (messageId: string) => {
    setReplyingToId(messageId);
    // In a real implementation, you would open a reply modal here
    // For now, we'll just log it
    console.log('Replying to message:', messageId);
  };

  const toggleSidebar = () => {
    setShowSidebar(prev => !prev);
  };

  const selectedChannel = selectedChannelId ? getChannelById(selectedChannelId) : null;

  return (
    <div className="flex-1 flex flex-col h-[calc(100vh-60px)]">
      <div className="sticky top-[60px] bg-voicechain-dark/95 backdrop-blur-md py-2 z-20 px-4 flex items-center justify-between">
        <div className="flex items-center gap-2">
          {isMobile && (
            <Button
              variant={showSidebar ? "default" : "ghost"}
              size="icon"
              onClick={toggleSidebar}
              className={`mr-2 ${showSidebar ? "bg-voicechain-purple hover:bg-voicechain-accent" : "hover:bg-secondary/80"}`}
            >
              {showSidebar ? <X size={20} /> : <Menu size={20} />}
            </Button>
          )}
          <h1 className="text-2xl font-bold">Channels</h1>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsGuideModalOpen(true)}
            className="text-xs flex items-center gap-1"
          >
            <HelpCircle size={12} />
            <span className="hidden sm:inline">Help</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsJoinModalOpen(true)}
            className="text-xs flex items-center gap-1"
          >
            <Plus size={12} className="hidden sm:inline" />
            Join Channel
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={() => setIsCreateModalOpen(true)}
            className="text-xs flex items-center gap-1 bg-voicechain-purple hover:bg-voicechain-accent"
          >
            <Hash size={12} className="hidden sm:inline" />
            Create Channel
          </Button>
        </div>
      </div>
      <Separator className="bg-border/50" />

      <div className="flex flex-1 relative">
        {/* Mobile Overlay */}
        {isMobile && showSidebar && (
          <div
            className="fixed inset-0 bg-black/50 z-10"
            onClick={toggleSidebar}
          />
        )}

        {/* Sidebar */}
        <div
          className={`
            ${showSidebar ? 'translate-x-0' : '-translate-x-full'}
            ${isMobile ? 'absolute z-20 h-full shadow-lg' : 'relative'}
            transition-transform duration-300 ease-in-out
            bg-background border-r border-border/50 w-full md:w-64 lg:w-72
          `}
        >
          <div className="p-4 h-full overflow-y-auto">
            <ChannelList
              userAddress={userAddress}
              onCreateChannel={() => setIsCreateModalOpen(true)}
              onJoinChannel={() => setIsJoinModalOpen(true)}
              onChannelSettings={handleChannelSettings}
            />
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 min-w-0">
          <ChannelView
            userAddress={userAddress}
            onChannelSettings={handleChannelSettings}
            onChannelInvite={handleChannelInvite}
            onReply={handleReply}
            onToggleSidebar={toggleSidebar}
          />
        </div>
      </div>

      {/* Modals */}
      <CreateChannelModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
      />

      <JoinChannelModal
        isOpen={isJoinModalOpen}
        onClose={() => setIsJoinModalOpen(false)}
      />

      <ChannelGuide
        isOpen={isGuideModalOpen}
        onClose={() => setIsGuideModalOpen(false)}
      />

      {selectedChannel && (
        <>
          <ChannelInviteModal
            isOpen={isInviteModalOpen}
            onClose={() => setIsInviteModalOpen(false)}
            channel={selectedChannel}
          />

          <ChannelSettings
            isOpen={isSettingsModalOpen}
            onClose={() => setIsSettingsModalOpen(false)}
            channel={selectedChannel}
            userAddress={userAddress}
          />
        </>
      )}
    </div>
  );
};

export default Channels;
