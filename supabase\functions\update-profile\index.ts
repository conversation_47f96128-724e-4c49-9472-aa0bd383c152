
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";

// Create a Supabase client with the Admin key
const supabaseUrl = Deno.env.get("SUPABASE_URL") ?? "";
const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "";
const supabase = createClient(supabaseUrl, supabaseServiceKey);

serve(async (req: Request) => {
  try {
    // Check if request is a POST request
    if (req.method !== "POST") {
      return new Response(
        JSON.stringify({ error: "Method not allowed" }),
        { status: 405, headers: { "Content-Type": "application/json" } }
      );
    }

    // Parse the request body
    const body = await req.json();
    const { 
      wallet_address, 
      username, 
      display_name, 
      bio, 
      avatar_url, 
      cover_image_url,
      social_links 
    } = body;

    // Validate the required fields
    if (!wallet_address) {
      return new Response(
        JSON.stringify({ error: "Wallet address is required" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    const normalizedAddress = wallet_address.toLowerCase();

    // Check if the profile already exists
    const { data: existingProfile, error: checkError } = await supabase
      .from("profiles")
      .select("id")
      .eq("wallet_address", normalizedAddress)
      .maybeSingle();

    if (checkError) {
      return new Response(
        JSON.stringify({ error: `Error checking profile: ${checkError.message}` }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }

    let result;

    if (existingProfile) {
      // Update existing profile
      const { data, error } = await supabase
        .from("profiles")
        .update({
          username: username || undefined,
          display_name: display_name || undefined,
          bio: bio || undefined,
          avatar_url: avatar_url || undefined,
          cover_image_url: cover_image_url || undefined,
          social_links: social_links || undefined,
          updated_at: new Date().toISOString()
        })
        .eq("wallet_address", normalizedAddress)
        .select();

      if (error) {
        return new Response(
          JSON.stringify({ error: `Error updating profile: ${error.message}` }),
          { status: 500, headers: { "Content-Type": "application/json" } }
        );
      }

      result = data;
    } else {
      // Insert new profile
      const { data, error } = await supabase
        .from("profiles")
        .insert({
          wallet_address: normalizedAddress,
          username: username || `user_${normalizedAddress.substring(2, 8)}`,
          display_name: display_name || `User ${normalizedAddress.substring(0, 6)}`,
          bio: bio || "",
          avatar_url: avatar_url || "",
          cover_image_url: cover_image_url || "",
          social_links: social_links || {},
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select();

      if (error) {
        return new Response(
          JSON.stringify({ error: `Error creating profile: ${error.message}` }),
          { status: 500, headers: { "Content-Type": "application/json" } }
        );
      }

      result = data;
    }

    // Return the updated/created profile
    return new Response(
      JSON.stringify({ success: true, data: result }),
      { status: 200, headers: { "Content-Type": "application/json" } }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: `Unexpected error: ${error.message}` }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
});
