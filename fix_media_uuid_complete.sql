-- Complete Fix for Media Persistence - Handle ALL Foreign Key Constraints
-- This script properly handles all foreign key constraints before converting UUID to TEXT

-- Step 1: Drop ALL foreign key constraints that reference voice_messages.id
DO $$
DECLARE
    constraint_record RECORD;
BEGIN
    -- Find and drop all foreign key constraints that reference voice_messages.id
    FOR constraint_record IN
        SELECT 
            tc.table_name,
            tc.constraint_name
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu 
            ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage ccu 
            ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND ccu.table_name = 'voice_messages'
        AND ccu.column_name = 'id'
    LOOP
        EXECUTE format('ALTER TABLE %I DROP CONSTRAINT IF EXISTS %I', 
                      constraint_record.table_name, 
                      constraint_record.constraint_name);
        RAISE NOTICE 'Dropped constraint % from table %', 
                     constraint_record.constraint_name, 
                     constraint_record.table_name;
    END LOOP;
    
    -- Also drop the self-referencing constraint on voice_messages
    ALTER TABLE voice_messages DROP CONSTRAINT IF EXISTS voice_messages_parent_id_fkey;
    RAISE NOTICE 'Dropped voice_messages self-referencing constraint';
END $$;

-- Step 2: Convert voice_message_media columns to TEXT
ALTER TABLE voice_message_media 
ALTER COLUMN id TYPE TEXT USING id::text;

ALTER TABLE voice_message_media 
ALTER COLUMN voice_message_id TYPE TEXT USING voice_message_id::text;

-- Step 3: Convert voice_messages columns to TEXT
ALTER TABLE voice_messages 
ALTER COLUMN id TYPE TEXT USING id::text;

-- Convert parent_id if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'voice_messages' 
               AND column_name = 'parent_id' 
               AND table_schema = 'public') THEN
        ALTER TABLE voice_messages 
        ALTER COLUMN parent_id TYPE TEXT USING parent_id::text;
        RAISE NOTICE 'Converted voice_messages.parent_id to TEXT';
    END IF;
END $$;

-- Step 4: Update default values to generate TEXT UUIDs
ALTER TABLE voice_message_media 
ALTER COLUMN id SET DEFAULT gen_random_uuid()::text;

ALTER TABLE voice_messages 
ALTER COLUMN id SET DEFAULT gen_random_uuid()::text;

-- Step 5: Recreate foreign key constraints with TEXT types
-- Recreate voice_message_media -> voice_messages constraint
ALTER TABLE voice_message_media 
ADD CONSTRAINT voice_message_media_voice_message_id_fkey 
FOREIGN KEY (voice_message_id) REFERENCES voice_messages(id) ON DELETE CASCADE;

-- Recreate voice_messages self-referencing constraint (for replies)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'voice_messages' 
               AND column_name = 'parent_id' 
               AND table_schema = 'public') THEN
        ALTER TABLE voice_messages 
        ADD CONSTRAINT voice_messages_parent_id_fkey 
        FOREIGN KEY (parent_id) REFERENCES voice_messages(id) ON DELETE CASCADE;
        RAISE NOTICE 'Recreated voice_messages self-referencing constraint';
    END IF;
END $$;

-- Step 6: Create indexes for better performance
CREATE INDEX IF NOT EXISTS voice_message_media_voice_message_id_idx 
ON voice_message_media(voice_message_id);

CREATE INDEX IF NOT EXISTS voice_messages_id_idx 
ON voice_messages(id);

CREATE INDEX IF NOT EXISTS voice_messages_parent_id_idx 
ON voice_messages(parent_id);

-- Step 7: Ensure RLS policies are in place for voice_message_media
ALTER TABLE voice_message_media ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Allow all operations on voice_message_media" ON voice_message_media;
DROP POLICY IF EXISTS "Users can view all voice message media" ON voice_message_media;
DROP POLICY IF EXISTS "Users can insert voice message media" ON voice_message_media;
DROP POLICY IF EXISTS "Users can delete voice message media" ON voice_message_media;

-- Create permissive RLS policies for voice_message_media
CREATE POLICY "Allow all operations on voice_message_media" 
ON voice_message_media 
FOR ALL 
USING (true) 
WITH CHECK (true);

-- Step 8: Ensure RLS policies are in place for voice_messages
ALTER TABLE voice_messages ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Allow all operations on voice_messages" ON voice_messages;
DROP POLICY IF EXISTS "Users can view all voice messages" ON voice_messages;
DROP POLICY IF EXISTS "Users can insert voice messages" ON voice_messages;
DROP POLICY IF EXISTS "Users can delete voice messages" ON voice_messages;

-- Create permissive RLS policies for voice_messages
CREATE POLICY "Allow all operations on voice_messages" 
ON voice_messages 
FOR ALL 
USING (true) 
WITH CHECK (true);

-- Step 9: Grant necessary permissions
GRANT ALL ON voice_message_media TO anon;
GRANT ALL ON voice_message_media TO authenticated;
GRANT ALL ON voice_messages TO anon;
GRANT ALL ON voice_messages TO authenticated;

-- Step 10: Test the complete fix
DO $$
DECLARE
    test_message_id TEXT := 'test_' || extract(epoch from now())::text;
    test_media_id TEXT := 'media_' || extract(epoch from now())::text;
    test_reply_id TEXT := 'reply_' || extract(epoch from now())::text;
BEGIN
    -- Insert test voice message
    INSERT INTO voice_messages (
        id, 
        profile_id, 
        audio_url, 
        transcript, 
        audio_duration, 
        created_at
    ) VALUES (
        test_message_id,
        'test_profile',
        'https://example.com/test.mp3',
        'Test message for media fix',
        10,
        NOW()
    );
    
    -- Insert test media
    INSERT INTO voice_message_media (
        id,
        voice_message_id,
        url,
        type,
        created_at
    ) VALUES (
        test_media_id,
        test_message_id,
        'https://example.com/test-image.jpg',
        'image',
        NOW()
    );
    
    -- Test reply functionality if parent_id exists
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'voice_messages' 
               AND column_name = 'parent_id' 
               AND table_schema = 'public') THEN
        INSERT INTO voice_messages (
            id,
            profile_id,
            audio_url,
            transcript,
            audio_duration,
            parent_id,
            created_at
        ) VALUES (
            test_reply_id,
            'test_profile',
            'https://example.com/test-reply.mp3',
            'Test reply message',
            5,
            test_message_id,
            NOW()
        );
        RAISE NOTICE 'Test reply insert successful';
    END IF;
    
    RAISE NOTICE 'All test inserts successful - media persistence should now work!';
    
    -- Clean up test data
    DELETE FROM voice_message_media WHERE id = test_media_id;
    DELETE FROM voice_messages WHERE id IN (test_message_id, test_reply_id);
    
    RAISE NOTICE 'Test data cleaned up successfully';
    
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Test insert failed: %', SQLERRM;
    -- Try to clean up even if test failed
    DELETE FROM voice_message_media WHERE id = test_media_id;
    DELETE FROM voice_messages WHERE id IN (test_message_id, test_reply_id);
END $$;

-- Step 11: Show final table structures
SELECT
    'voice_messages' as table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'voice_messages'
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Show voice_message_media table structure
SELECT
    'voice_message_media' as table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'voice_message_media'
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Step 12: Show recreated constraints
SELECT 
    tc.table_name,
    tc.constraint_name,
    tc.constraint_type,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu 
    ON tc.constraint_name = kcu.constraint_name
LEFT JOIN information_schema.constraint_column_usage ccu 
    ON ccu.constraint_name = tc.constraint_name
WHERE tc.table_name IN ('voice_messages', 'voice_message_media')
AND tc.constraint_type = 'FOREIGN KEY'
ORDER BY tc.table_name, tc.constraint_name;

-- Success message
SELECT 'Complete media persistence UUID/TEXT fix successful! 🎉' as status;
