
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/components/ui/sonner';
import { CheckCheck, X, Shield, AlertTriangle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import {
  getVerificationApplications,
  updateVerificationStatus
} from '@/services/verificationService';
import { VerificationApplication } from '@/types/verification';
import { OWNER_ADDRESS } from '@/utils/verification-data';

const AdminVerification = () => {
  const navigate = useNavigate();
  const { isAuthenticated, user } = useAuth();
  const [applications, setApplications] = useState<VerificationApplication[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [isAuthorized, setIsAuthorized] = useState<boolean>(false);

  // Check if user is authorized (owner) and fetch applications on component mount
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    // Check if the current user is the owner
    const currentUserAddress = user?.walletAddress || localStorage.getItem('connectedAccount');
    const isOwner = currentUserAddress &&
      currentUserAddress.toLowerCase() === OWNER_ADDRESS.toLowerCase();

    setIsAuthorized(isOwner);

    if (isOwner) {
      fetchApplications();
    }
  }, [isAuthenticated, navigate, user]);

  const fetchApplications = async () => {
    setLoading(true);
    try {
      const apps = await getVerificationApplications();
      setApplications(apps);
    } catch (error) {
      console.error('Error fetching verification applications:', error);
      toast.error('Failed to load verification applications');
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (id: string) => {
    try {
      const success = await updateVerificationStatus(id, 'approved');
      if (success) {
        toast.success('Application approved');
        // Update local state
        setApplications(prev => prev.map(app =>
          app.id === id ? { ...app, status: 'approved', reviewedAt: new Date().toISOString() } : app
        ));
      } else {
        toast.error('Failed to approve application');
      }
    } catch (error) {
      console.error('Error approving application:', error);
      toast.error('Failed to approve application');
    }
  };

  const handleReject = async (id: string) => {
    try {
      const success = await updateVerificationStatus(id, 'rejected');
      if (success) {
        toast.success('Application rejected');
        // Update local state
        setApplications(prev => prev.map(app =>
          app.id === id ? { ...app, status: 'rejected', reviewedAt: new Date().toISOString() } : app
        ));
      } else {
        toast.error('Failed to reject application');
      }
    } catch (error) {
      console.error('Error rejecting application:', error);
      toast.error('Failed to reject application');
    }
  };

  // Filter applications
  const pendingApps = applications.filter(app => app.status === 'pending');
  const approvedApps = applications.filter(app => app.status === 'approved');
  const rejectedApps = applications.filter(app => app.status === 'rejected');

  return (
    <div className="container max-w-5xl mx-auto p-4">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold">Verification Administration</h1>
        {isAuthorized && (
          <Button onClick={fetchApplications} variant="outline">Refresh</Button>
        )}
      </div>

      {!isAuthorized ? (
        <Card className="mb-4">
          <CardHeader>
            <CardTitle className="flex items-center text-destructive">
              <AlertTriangle className="mr-2 h-5 w-5" />
              Unauthorized Access
            </CardTitle>
            <CardDescription>
              You do not have permission to access this page
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4">This page is restricted to the application owner only.</p>
            <Button onClick={() => navigate('/')} variant="default">
              Return to Home
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Tabs defaultValue="pending" className="w-full">
          <TabsList className="grid grid-cols-3 mb-8">
            <TabsTrigger value="pending" className="relative">
              Pending
              {pendingApps.length > 0 && (
                <Badge variant="purple" className="ml-2">{pendingApps.length}</Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="approved">
              Approved
              {approvedApps.length > 0 && (
                <Badge variant="purple" className="ml-2">{approvedApps.length}</Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="rejected">
              Rejected
              {rejectedApps.length > 0 && (
                <Badge variant="purple" className="ml-2">{rejectedApps.length}</Badge>
              )}
            </TabsTrigger>
          </TabsList>

          {/* Pending Applications */}
          <TabsContent value="pending">
            {loading ? (
              <Card>
                <CardContent className="pt-6">
                  <p>Loading applications...</p>
                </CardContent>
              </Card>
            ) : pendingApps.length === 0 ? (
              <Card>
                <CardContent className="pt-6">
                  <p>No pending applications</p>
                </CardContent>
              </Card>
            ) : (
              pendingApps.map((app) => (
                <ApplicationCard
                  key={app.id}
                  application={app}
                  onApprove={() => handleApprove(app.id)}
                  onReject={() => handleReject(app.id)}
                />
              ))
            )}
          </TabsContent>

          {/* Approved Applications */}
          <TabsContent value="approved">
            {loading ? (
              <p>Loading applications...</p>
            ) : approvedApps.length === 0 ? (
              <Card>
                <CardContent className="pt-6">
                  <p>No approved applications</p>
                </CardContent>
              </Card>
            ) : (
              approvedApps.map((app) => (
                <ApplicationCard
                  key={app.id}
                  application={app}
                  readOnly
                />
              ))
            )}
          </TabsContent>

          {/* Rejected Applications */}
          <TabsContent value="rejected">
            {loading ? (
              <p>Loading applications...</p>
            ) : rejectedApps.length === 0 ? (
              <Card>
                <CardContent className="pt-6">
                  <p>No rejected applications</p>
                </CardContent>
              </Card>
            ) : (
              rejectedApps.map((app) => (
                <ApplicationCard
                  key={app.id}
                  application={app}
                  readOnly
                />
              ))
            )}
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};

interface ApplicationCardProps {
  application: VerificationApplication;
  readOnly?: boolean;
  onApprove?: () => void;
  onReject?: () => void;
}

const ApplicationCard: React.FC<ApplicationCardProps> = ({
  application,
  readOnly = false,
  onApprove,
  onReject
}) => {
  const statusColors = {
    pending: 'bg-yellow-100 text-yellow-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800'
  };

  const statusClass = statusColors[application.status];

  return (
    <Card className="mb-4">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="flex items-center">
              <Shield className="mr-2 h-5 w-5 text-voicechain-purple" />
              {application.type} Verification Request
            </CardTitle>
            <CardDescription>
              From: {application.userAddress}
            </CardDescription>
          </div>
          <Badge className={statusClass}>
            {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4">
          <div>
            <h3 className="text-sm font-medium mb-1">Reason for Request</h3>
            <p className="text-sm text-gray-500">{application.reason}</p>
          </div>

          <div>
            <h3 className="text-sm font-medium mb-1">Social Proof</h3>
            <p className="text-sm text-gray-500 break-all">{application.socialProof}</p>
          </div>

          <div className="text-xs text-gray-400 mt-2">
            Submitted on: {new Date(application.submittedAt).toLocaleDateString()}
            {application.reviewedAt && (
              <div>Reviewed on: {new Date(application.reviewedAt).toLocaleDateString()}</div>
            )}
          </div>

          {!readOnly && (
            <div className="flex gap-2 justify-end mt-4">
              <Button
                variant="outline"
                size="sm"
                className="border-red-500 text-red-500 hover:bg-red-50"
                onClick={onReject}
              >
                <X className="mr-1 h-4 w-4" />
                Reject
              </Button>
              <Button
                size="sm"
                className="bg-voicechain-purple hover:bg-voicechain-accent"
                onClick={onApprove}
              >
                <CheckCheck className="mr-1 h-4 w-4" />
                Approve
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default AdminVerification;
