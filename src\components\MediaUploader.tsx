
import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { UploadCloud, X, Image, FileVideo } from 'lucide-react';
import { toast } from '@/components/ui/sonner';

// Define the MediaFile interface
export interface MediaFile {
  url: string;
  file: File;
  type: "image" | "video";
  id: string;
}

export interface MediaUploaderProps {
  maxFiles?: number;
  accept?: string;
  maxSizeMB?: number;
  selectedFiles?: MediaFile[];
  onFilesSelected?: (files: MediaFile[]) => void;
}

const MediaUploader: React.FC<MediaUploaderProps> = ({
  maxFiles = 4,
  accept = "image/*,video/*",
  maxSizeMB = 10,
  selectedFiles = [],
  onFilesSelected,
}) => {
  const [files, setFiles] = useState<MediaFile[]>(selectedFiles || []);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const fileList = e.target.files;
    if (!fileList || fileList.length === 0) return;

    // Check if adding these files would exceed the limit
    if (files.length + fileList.length > maxFiles) {
      toast.error(`You can only upload up to ${maxFiles} files`);
      return;
    }

    const newFiles: MediaFile[] = [];

    // Process each selected file
    Array.from(fileList).forEach(file => {
      // Check file size
      if (file.size > maxSizeMB * 1024 * 1024) {
        toast.error(`File ${file.name} exceeds the maximum size of ${maxSizeMB}MB`);
        return;
      }

      // Determine file type
      let type: "image" | "video" = file.type.startsWith('image/') ? "image" : "video";
      if (!file.type.startsWith('image/') && !file.type.startsWith('video/')) {
        toast.error(`File ${file.name} is not a supported media type`);
        return;
      }

      // Create object URL for preview
      const url = URL.createObjectURL(file);

      // Add to new files array
      newFiles.push({
        url,
        file,
        type,
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      });
    });

    // Update state with new files
    const updatedFiles = [...files, ...newFiles];
    setFiles(updatedFiles);

    // Call the callback
    if (onFilesSelected) {
      onFilesSelected(updatedFiles);
    }

    // Reset the input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeFile = (id: string) => {
    const fileToRemove = files.find(f => f.id === id);
    if (fileToRemove) {
      // Revoke the object URL to prevent memory leaks
      URL.revokeObjectURL(fileToRemove.url);
    }

    // Filter out the removed file
    const updatedFiles = files.filter(f => f.id !== id);
    setFiles(updatedFiles);

    // Call the callback
    if (onFilesSelected) {
      onFilesSelected(updatedFiles);
    }
  };

  return (
    <div className="space-y-4">
      {/* File input */}
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept={accept}
        onChange={handleFileSelect}
        multiple={maxFiles > 1}
      />

      {/* Upload button */}
      {files.length < maxFiles && (
        <Button
          variant="outline"
          onClick={() => fileInputRef.current?.click()}
          className="w-full border-dashed border-2 h-20"
          type="button"
        >
          <UploadCloud className="mr-2 h-5 w-5" />
          Upload Media
        </Button>
      )}

      {/* Preview area */}
      {files.length > 0 && (
        <div className="grid grid-cols-2 gap-2">
          {files.map(file => (
            <div 
              key={file.id} 
              className="relative aspect-square rounded-md overflow-hidden border bg-muted/20"
            >
              {/* Preview */}
              {file.type === "image" ? (
                <img 
                  src={file.url} 
                  alt="Preview" 
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="flex items-center justify-center w-full h-full bg-black/5">
                  <FileVideo className="h-8 w-8 text-muted-foreground" />
                </div>
              )}

              {/* Type icon */}
              <div className="absolute top-1 left-1 bg-background/80 p-1 rounded-md text-xs font-medium">
                {file.type === "image" ? (
                  <Image className="h-3 w-3" />
                ) : (
                  <FileVideo className="h-3 w-3" />
                )}
              </div>
              
              {/* Remove button */}
              <Button
                size="icon"
                variant="destructive"
                className="absolute top-1 right-1 h-6 w-6 rounded-full p-0"
                onClick={() => removeFile(file.id)}
                type="button"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default MediaUploader;
