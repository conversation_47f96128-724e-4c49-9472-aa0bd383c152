import React, { useState, useEffect, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import CustomAvatarImage from '@/components/CustomAvatarImage';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import { useNotifications } from '@/contexts/NotificationContext';
import { useChannels } from '@/contexts/ChannelContext';
import AudioRecorder from './AudioRecorder';
import { toast } from '@/components/ui/sonner';
import { AtSign, User, Mic, Send, Play, Square } from 'lucide-react';
import { UserProfile } from '@/types/user-profile';
import { MediaFile } from '@/types/media';

interface VoiceSummonModalProps {
  isOpen: boolean;
  onClose: () => void;
  recipientAddress: string;
  channelId?: string;
  messageId?: string;
  context?: 'channel' | 'feed' | 'profile';
}

const VoiceSummonModal: React.FC<VoiceSummonModalProps> = ({
  isOpen,
  onClose,
  recipientAddress,
  channelId,
  messageId,
  context = 'feed',
}) => {
  const { getProfileByAddress, profiles } = useProfiles();
  const { addNotification } = useNotifications();
  const { getChannelById } = useChannels();
  const [isRecording, setIsRecording] = useState(false);
  const [recordingComplete, setRecordingComplete] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [transcript, setTranscript] = useState('');
  const [audioDuration, setAudioDuration] = useState(0);
  const [recipientProfile, setRecipientProfile] = useState<UserProfile | null>(null);
  const [channel, setChannel] = useState<any | undefined>(undefined);
  const [tagInputValue, setTagInputValue] = useState('');
  const [suggestedUsers, setSuggestedUsers] = useState<UserProfile[]>([]);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);

  // Get recipient profile using useEffect
  useEffect(() => {
    if (recipientAddress) {
      const profile = getProfileByAddress(recipientAddress);
      setRecipientProfile(profile);
      if (profile) {
        setTagInputValue(`@${profile.username}`);
      }
    }
  }, [recipientAddress, getProfileByAddress]);

  // Get channel using useEffect
  useEffect(() => {
    if (channelId) {
      const channelData = getChannelById(channelId);
      setChannel(channelData);
    }
  }, [channelId, getChannelById]);

  // Handle audio playback
  useEffect(() => {
    return () => {
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  // Get context description
  const getContextDescription = () => {
    switch (context) {
      case 'channel':
        return channel ? `in the ${channel.name} channel` : 'in this channel';
      case 'profile':
        return 'from their profile';
      case 'feed':
      default:
        return 'from the main feed';
    }
  };

  const handleRecordingStart = () => {
    setIsRecording(true);
    setRecordingComplete(false);
    
    // Request microphone permission explicitly
    navigator.mediaDevices.getUserMedia({ audio: true })
      .then(stream => {
        console.log('Microphone permission granted');
        // Don't need to do anything here since AudioRecorder will handle the stream
        // Just close the stream to release the microphone
        stream.getTracks().forEach(track => track.stop());
      })
      .catch(err => {
        console.error('Error accessing microphone:', err);
        toast.error('Microphone access denied. Please enable microphone permissions to record.');
        setIsRecording(false);
      });
  };

  const handleRecordingComplete = (blob: Blob, text: string, duration?: number, media?: MediaFile[]) => {
    setAudioBlob(blob);
    setTranscript(text);
    setAudioDuration(duration || 0);
    setIsRecording(false);
    setRecordingComplete(true);
    
    // Create object URL for audio preview
    if (blob) {
      const url = URL.createObjectURL(blob);
      setAudioUrl(url);
    }
  };

  const handleTagInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setTagInputValue(value);
    
    // If input starts with @ and has at least 2 characters after @, show suggestions
    if (value.startsWith('@') && value.length >= 2) {
      const searchTerm = value.substring(1).toLowerCase();
      const results = Object.values(profiles).filter(profile => 
        profile.username.toLowerCase().includes(searchTerm) ||
        profile.displayName.toLowerCase().includes(searchTerm)
      ).slice(0, 5);
      setSuggestedUsers(results);
    } else {
      setSuggestedUsers([]);
    }
  };

  const selectUser = (profile: UserProfile) => {
    setTagInputValue(`@${profile.username}`);
    setRecipientProfile(profile);
    setSuggestedUsers([]);
  };

  const toggleAudioPlayback = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play().catch(error => {
          console.error("Error playing audio:", error);
          toast.error("Could not play audio. Please try again.");
        });
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleAudioEnded = () => {
    setIsPlaying(false);
  };

  const handleSummon = () => {
    if (!audioBlob || !transcript) {
      toast.error('Please record a voice message first');
      return;
    }

    if (!recipientProfile) {
      toast.error('Please select a recipient');
      return;
    }

    // Create a unique ID for the summon
    const summonId = `summon-${Date.now()}`;

    // Create a notification for the recipient
    const currentAccount = localStorage.getItem('connectedAccount');
    if (currentAccount) {
      // Allow self-summoning by setting the recipient address
      addNotification(
        'summon',
        currentAccount,
        recipientProfile.address,
        messageId || summonId,
        {
          text: transcript,
          summonQuestion: transcript,
          summonAudioUrl: audioUrl,
          summonDuration: audioDuration,
          channelId: channelId,
          context: context,
          messageId: messageId
        }
      );

      toast.success(`Voice summon sent to ${recipientProfile.displayName}`);
      onClose();
    } else {
      toast.error('You must be logged in to summon');
    }
  };

  const resetModal = () => {
    setAudioBlob(null);
    setTranscript('');
    setRecordingComplete(false);
    setIsRecording(false);
    setAudioUrl(null);
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }
  };

  // Check for microphone permissions when opening the modal
  useEffect(() => {
    if (isOpen) {
      navigator.mediaDevices.getUserMedia({ audio: true })
        .then(stream => {
          // Just checking for permission, we don't need to keep the stream
          stream.getTracks().forEach(track => track.stop());
        })
        .catch(err => {
          console.error('Error accessing microphone:', err);
          toast.error('Microphone access is required for voice summons. Please enable it in your browser settings.');
        });
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) {
        resetModal();
        onClose();
      }
    }}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AtSign size={18} className="text-voicechain-purple" />
            Summon User
          </DialogTitle>
          <DialogDescription>
            Summon a user to get their opinion on this post.
            They'll receive a notification and can respond with their voice.
          </DialogDescription>
        </DialogHeader>

        {/* Tag input field */}
        <div className="relative mt-2">
          <div className="flex items-center border border-input rounded-md px-3 py-2 focus-within:ring-2 focus-within:ring-voicechain-purple focus-within:border-voicechain-purple">
            <User className="mr-2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="@username"
              value={tagInputValue}
              onChange={handleTagInputChange}
              className="border-0 p-0 focus-visible:ring-0 focus-visible:ring-offset-0"
            />
          </div>

          {/* User suggestions dropdown */}
          {suggestedUsers.length > 0 && (
            <div className="absolute z-10 mt-1 w-full bg-background border border-border rounded-md shadow-lg max-h-60 overflow-auto">
              {suggestedUsers.map(profile => (
                <div
                  key={profile.address}
                  className="flex items-center px-3 py-2 hover:bg-secondary cursor-pointer"
                  onClick={() => selectUser(profile)}
                >
                  <Avatar className="h-6 w-6 mr-2">
                    <CustomAvatarImage src={profile.profileImageUrl} alt={profile.displayName} />
                    <AvatarFallback className="bg-voicechain-accent text-white text-xs">
                      {profile.displayName.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="text-sm font-medium">{profile.displayName}</p>
                    <p className="text-xs text-muted-foreground">@{profile.username}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {recipientProfile && (
          <div className="flex items-center space-x-4 py-2">
            <Avatar className="h-10 w-10">
              <CustomAvatarImage src={recipientProfile.profileImageUrl} alt={recipientProfile.displayName} />
              <AvatarFallback className="bg-voicechain-accent text-white">
                {recipientProfile.displayName.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <div>
              <h4 className="font-medium">{recipientProfile.displayName}</h4>
              <p className="text-sm text-muted-foreground">@{recipientProfile.username}</p>
            </div>
          </div>
        )}

        <div className="text-sm text-muted-foreground mb-2">
          {channel ? `Channel: ${channel.name}` : `Context: ${getContextDescription()}`}
        </div>

        <div className="bg-secondary/50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <p className="text-sm font-medium">Record your question:</p>
            <Mic size={16} className="text-voicechain-purple" />
          </div>
          <AudioRecorder
            onRecordingStart={handleRecordingStart}
            onRecordingComplete={handleRecordingComplete}
            placeholder="What would you like to ask?"
          />

          {recordingComplete && transcript && (
            <div className="mt-4 space-y-3">
              {/* Audio preview */}
              {audioUrl && (
                <div className="bg-background rounded-md p-3">
                  <div className="flex items-center">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 rounded-full"
                      onClick={toggleAudioPlayback}
                    >
                      {isPlaying ? (
                        <Square size={14} className="text-voicechain-purple" />
                      ) : (
                        <Play size={14} className="text-voicechain-purple" />
                      )}
                    </Button>
                    <div className="ml-2 text-sm">Preview your recording</div>
                    <audio 
                      ref={audioRef}
                      src={audioUrl}
                      onEnded={handleAudioEnded}
                      className="hidden"
                      preload="auto"
                    />
                  </div>
                </div>
              )}
              
              {/* Transcript */}
              <div className="bg-background rounded-md p-3">
                <p className="text-sm font-medium mb-1">Your question:</p>
                <p className="text-sm">{transcript}</p>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-between sm:justify-between">
          <Button
            variant="outline"
            onClick={() => {
              resetModal();
              onClose();
            }}
          >
            Cancel
          </Button>
          <Button
            disabled={!recordingComplete || isRecording || !recipientProfile}
            onClick={handleSummon}
            className="bg-voicechain-purple hover:bg-voicechain-accent"
          >
            <Send size={16} className="mr-2" />
            Summon
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default VoiceSummonModal;
