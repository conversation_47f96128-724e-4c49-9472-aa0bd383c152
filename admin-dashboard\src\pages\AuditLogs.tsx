import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Search,
  Filter,
  Calendar,
  User,
  Shield,
  FileText,
  Trash2,
  Settings,
  AlertTriangle,
  CheckCircle,
  Loader2,
  Download,
  FileDown
} from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';
import { toast } from '@/components/ui/toast';
import { supabase } from '@/services/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { exportData } from '@/utils/exportData';

interface AuditLog {
  id: string;
  admin_id: string;
  admin_email: string;
  admin_role: string;
  action: string;
  entity_type: string;
  entity_id: string;
  details: any;
  ip_address: string;
  user_agent: string;
  created_at: string;
}

const AuditLogs: React.FC = () => {
  const { user } = useAuth();
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [actionFilter, setActionFilter] = useState<string>('all_actions');
  const [entityFilter, setEntityFilter] = useState<string>('all_entities');
  const [adminFilter, setAdminFilter] = useState<string>('all_admins');
  const [dateFilter, setDateFilter] = useState<string>('7d');
  const [filteredLogs, setFilteredLogs] = useState<AuditLog[]>([]);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const logsPerPage = 20;

  useEffect(() => {
    fetchAuditLogs();
  }, [dateFilter]);

  useEffect(() => {
    applyFilters();
  }, [logs, searchQuery, actionFilter, entityFilter, adminFilter]);

  const fetchAuditLogs = async () => {
    setIsLoading(true);

    // Calculate date range based on filter
    let startDate = new Date();
    switch (dateFilter) {
      case '1d':
        startDate.setDate(startDate.getDate() - 1);
        break;
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      case 'all':
        startDate = new Date(2020, 0, 1); // Assuming platform started in 2020
        break;
    }

    try {
      // First try using the RPC function
      try {
        const { data, error } = await supabase.rpc('get_audit_logs', {
          p_limit: 500, // Get more logs than we need for client-side filtering
          p_offset: 0,
          p_action: null,
          p_entity_type: null,
          p_admin_id: null,
          p_start_date: startDate.toISOString(),
          p_end_date: new Date().toISOString()
        });

        if (error) {
          throw error;
        }

        setLogs(data || []);
        return;
      } catch (rpcError) {
        console.error('Error using RPC function, falling back to direct query:', rpcError);
      }

      // Fallback to direct query if RPC function doesn't exist
      try {
        // First try with the admin_profiles join
        const { data: auditLogsData, error: auditLogsError } = await supabase
          .from('audit_logs')
          .select(`
            id,
            admin_id,
            action,
            entity_type,
            entity_id,
            details,
            ip_address,
            user_agent,
            created_at,
            admin_profiles:admin_id (
              email,
              role
            )
          `)
          .gte('created_at', startDate.toISOString())
          .order('created_at', { ascending: false })
          .limit(500);

        if (auditLogsError) {
          throw auditLogsError;
        }

        // Transform the data to match the expected format
        const transformedData = auditLogsData?.map(log => {
          // Define the admin_profiles type to avoid TypeScript errors
          // The admin_profiles could be an array or a single object, handle both cases
          let adminEmail = 'Unknown';
          let adminRole = 'Unknown';

          if (log.admin_profiles) {
            // Check if it's an array
            if (Array.isArray(log.admin_profiles)) {
              // Use the first item if it exists
              if (log.admin_profiles.length > 0) {
                const profile = log.admin_profiles[0];
                if (profile && typeof profile === 'object') {
                  adminEmail = (profile as any).email || 'Unknown';
                  adminRole = (profile as any).role || 'Unknown';
                }
              }
            } else {
              // It's a single object - use type assertion to avoid TypeScript errors
              const profile = log.admin_profiles as any;
              if (profile && typeof profile === 'object') {
                adminEmail = profile.email || 'Unknown';
                adminRole = profile.role || 'Unknown';
              }
            }
          }

          return {
            id: log.id,
            admin_id: log.admin_id,
            admin_email: adminEmail,
            admin_role: adminRole,
            action: log.action,
            entity_type: log.entity_type,
            entity_id: log.entity_id,
            details: log.details,
            ip_address: log.ip_address,
            user_agent: log.user_agent,
            created_at: log.created_at
          };
        }) || [];

        setLogs(transformedData);
        return;
      } catch (joinError) {
        console.error('Error with join query, falling back to simple query:', joinError);
      }

      // If the join fails, try a simple query without the join
      const { data: simpleAuditLogsData, error: simpleAuditLogsError } = await supabase
        .from('audit_logs')
        .select(`
          id,
          admin_id,
          action,
          entity_type,
          entity_id,
          details,
          ip_address,
          user_agent,
          created_at
        `)
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: false })
        .limit(500);

      if (simpleAuditLogsError) {
        throw simpleAuditLogsError;
      }

      // Transform the data to match the expected format
      const transformedData = simpleAuditLogsData?.map(log => ({
        id: log.id,
        admin_id: log.admin_id,
        admin_email: 'Unknown', // No admin_profiles in simple query
        admin_role: 'Unknown', // No admin_profiles in simple query
        action: log.action,
        entity_type: log.entity_type,
        entity_id: log.entity_id,
        details: log.details,
        ip_address: log.ip_address,
        user_agent: log.user_agent,
        created_at: log.created_at
      })) || [];

      setLogs(transformedData);
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch audit logs',
        duration: 3000
      });

      // Fallback to empty array
      setLogs([]);
    } finally {
      setIsLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...logs];

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(log =>
        log.admin_email.toLowerCase().includes(query) ||
        log.action.toLowerCase().includes(query) ||
        log.entity_type.toLowerCase().includes(query) ||
        log.entity_id?.toLowerCase().includes(query) ||
        JSON.stringify(log.details).toLowerCase().includes(query)
      );
    }

    // Apply action filter
    if (actionFilter && actionFilter !== 'all_actions') {
      filtered = filtered.filter(log => log.action === actionFilter);
    }

    // Apply entity filter
    if (entityFilter && entityFilter !== 'all_entities') {
      filtered = filtered.filter(log => log.entity_type === entityFilter);
    }

    // Apply admin filter
    if (adminFilter && adminFilter !== 'all_admins') {
      filtered = filtered.filter(log => log.admin_id === adminFilter);
    }

    // Calculate total pages
    setTotalPages(Math.ceil(filtered.length / logsPerPage));

    // Apply pagination
    const startIndex = (page - 1) * logsPerPage;
    const endIndex = startIndex + logsPerPage;
    setFilteredLogs(filtered.slice(startIndex, endIndex));

    // Reset page if no results on current page
    if (filtered.length > 0 && filtered.slice(startIndex, endIndex).length === 0) {
      setPage(1);
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'create':
        return <FileText className="h-4 w-4" />;
      case 'update':
        return <Settings className="h-4 w-4" />;
      case 'delete':
        return <Trash2 className="h-4 w-4" />;
      case 'verify':
        return <CheckCircle className="h-4 w-4" />;
      case 'suspend':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <Shield className="h-4 w-4" />;
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'create':
        return 'bg-green-500';
      case 'update':
        return 'bg-blue-500';
      case 'delete':
        return 'bg-red-500';
      case 'verify':
        return 'bg-purple-500';
      case 'suspend':
        return 'bg-orange-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getEntityIcon = (entityType: string) => {
    switch (entityType) {
      case 'user':
        return <User className="h-4 w-4" />;
      case 'content':
        return <FileText className="h-4 w-4" />;
      case 'admin':
        return <Shield className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getUniqueActions = () => {
    const actions = new Set(logs.map(log => log.action));
    return Array.from(actions);
  };

  const getUniqueEntities = () => {
    const entities = new Set(logs.map(log => log.entity_type));
    return Array.from(entities);
  };

  const getUniqueAdmins = () => {
    const admins = new Map();
    logs.forEach(log => {
      admins.set(log.admin_id, log.admin_email);
    });
    return Array.from(admins).map(([id, email]) => ({ id, email }));
  };

  const handleExportLogs = (type: 'csv' | 'json') => {
    // Prepare logs for export by formatting dates and removing sensitive data
    const exportableLogs = filteredLogs.map(log => ({
      id: log.id,
      admin_email: log.admin_email,
      admin_role: log.admin_role,
      action: log.action,
      entity_type: log.entity_type,
      entity_id: log.entity_id,
      details: log.details,
      created_at: format(new Date(log.created_at), 'yyyy-MM-dd HH:mm:ss'),
      ip_address: log.ip_address
    }));

    // Export data based on type
    exportData(
      exportableLogs,
      type,
      `audit_logs_${dateFilter}`
    );
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Audit Logs</CardTitle>
          <CardDescription>
            Track and review admin actions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Filters */}
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search logs..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleExportLogs('csv')}
                  disabled={filteredLogs.length === 0 || isLoading}
                  className="hidden sm:flex"
                >
                  <FileDown className="h-4 w-4 mr-1" />
                  Export CSV
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleExportLogs('json')}
                  disabled={filteredLogs.length === 0 || isLoading}
                  className="hidden sm:flex"
                >
                  <Download className="h-4 w-4 mr-1" />
                  Export JSON
                </Button>
              </div>

              <div className="flex flex-wrap gap-2">
                <Select value={actionFilter} onValueChange={setActionFilter}>
                  <SelectTrigger className="w-[130px]">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Action" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all_actions">All Actions</SelectItem>
                    {getUniqueActions().map(action => (
                      <SelectItem key={action} value={action}>
                        {action.charAt(0).toUpperCase() + action.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={entityFilter} onValueChange={setEntityFilter}>
                  <SelectTrigger className="w-[130px]">
                    <FileText className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Entity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all_entities">All Entities</SelectItem>
                    {getUniqueEntities().map(entity => (
                      <SelectItem key={entity} value={entity}>
                        {entity.charAt(0).toUpperCase() + entity.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={adminFilter} onValueChange={setAdminFilter}>
                  <SelectTrigger className="w-[180px]">
                    <User className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Admin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all_admins">All Admins</SelectItem>
                    {getUniqueAdmins().map(admin => (
                      <SelectItem key={admin.id} value={admin.id}>
                        {admin.email}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={dateFilter} onValueChange={setDateFilter}>
                  <SelectTrigger className="w-[130px]">
                    <Calendar className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Date Range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1d">Last 24 Hours</SelectItem>
                    <SelectItem value="7d">Last 7 Days</SelectItem>
                    <SelectItem value="30d">Last 30 Days</SelectItem>
                    <SelectItem value="90d">Last 90 Days</SelectItem>
                    <SelectItem value="all">All Time</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Logs List */}
            {isLoading ? (
              <div className="text-center py-8">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
                <p>Loading audit logs...</p>
              </div>
            ) : filteredLogs.length === 0 ? (
              <div className="text-center py-8">
                <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium">No audit logs found</h3>
                <p className="text-muted-foreground">
                  Try adjusting your filters or search query
                </p>
              </div>
            ) : (
              <>
                <div className="space-y-4">
                  {filteredLogs.map((log) => (
                    <Card key={log.id} className="overflow-hidden">
                      <div className="p-4">
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4">
                          <div className="flex items-center mb-2 sm:mb-0">
                            <Badge className={`${getActionColor(log.action)} text-white mr-2`}>
                              {getActionIcon(log.action)}
                              <span className="ml-1">{log.action.charAt(0).toUpperCase() + log.action.slice(1)}</span>
                            </Badge>
                            <Badge variant="outline" className="mr-2">
                              {getEntityIcon(log.entity_type)}
                              <span className="ml-1">{log.entity_type.charAt(0).toUpperCase() + log.entity_type.slice(1)}</span>
                            </Badge>
                            {log.entity_id && (
                              <span className="text-sm text-muted-foreground">
                                ID: {log.entity_id.substring(0, 8)}...
                              </span>
                            )}
                          </div>

                          <div className="text-sm text-muted-foreground">
                            {formatDistanceToNow(new Date(log.created_at), { addSuffix: true })}
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span className="text-sm">
                              {log.admin_email}
                              <Badge variant="outline" className="ml-2">
                                {log.admin_role}
                              </Badge>
                            </span>
                          </div>

                          {log.details && (
                            <div className="text-sm mt-2 p-2 bg-muted rounded">
                              <pre className="whitespace-pre-wrap font-mono text-xs">
                                {JSON.stringify(log.details, null, 2)}
                              </pre>
                            </div>
                          )}

                          <div className="text-xs text-muted-foreground mt-2">
                            {format(new Date(log.created_at), 'PPpp')} • IP: {log.ip_address || 'Unknown'}
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex justify-between items-center mt-6">
                    <div className="text-sm text-muted-foreground">
                      Page {page} of {totalPages}
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setPage(p => Math.max(1, p - 1))}
                        disabled={page === 1}
                      >
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                        disabled={page === totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AuditLogs;
