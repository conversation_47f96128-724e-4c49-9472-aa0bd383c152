import { Orbis } from "@orbisclub/orbis-sdk";
import { toast } from '@/components/ui/sonner';
import { UserProfile } from '@/types/user-profile';
import { uploadBlobToIPFS } from './nftStorage';

// Create a new Orbis instance
export const orbis = new Orbis();

/**
 * Connect to Orbis with the user's wallet
 * This function will prompt the user to connect their wallet
 * @param forceConnect Force connection even if auto-connect is disabled
 * @returns The connected user's DID
 */
export async function connectOrbis(forceConnect: boolean = false): Promise<string | null> {
  try {
    // Check if auto-connect is enabled
    const shouldAutoConnect = localStorage.getItem('autoConnectWallet') === 'true';

    if (!shouldAutoConnect && !forceConnect) {
      console.log('Auto wallet connection is disabled. Skipping Orbis connection.');
      return null;
    }

    console.log('Connecting to Orbis...');
    const res = await orbis.connect();

    if (res.status === 200) {
      console.log('Successfully connected to Orbis:', res);
      return res.did;
    } else {
      console.error('Failed to connect to Orbis:', res);
      toast.error('Failed to connect to Orbis. Please try again.');
      return null;
    }
  } catch (error) {
    console.error('Error connecting to Orbis:', error);
    toast.error('Error connecting to Orbis. Please try again.');
    return null;
  }
}

/**
 * Check if the user is connected to Orbis
 * @returns True if connected, false otherwise
 */
export async function isConnectedToOrbis(): Promise<boolean> {
  try {
    const { status, did } = await orbis.isConnected();
    return status === 200 && !!did;
  } catch (error) {
    console.error('Error checking Orbis connection:', error);
    return false;
  }
}

/**
 * Get the current user's DID
 * @returns The user's DID or null if not connected
 */
export async function getCurrentUserDid(): Promise<string | null> {
  try {
    const { status, did } = await orbis.isConnected();
    return status === 200 ? did : null;
  } catch (error) {
    console.error('Error getting current user DID:', error);
    return null;
  }
}

/**
 * Create or update a user profile in Orbis
 * @param profile The user profile data
 * @returns The profile details or null if failed
 */
export async function createOrUpdateProfile(profile: UserProfile): Promise<any | null> {
  try {
    // Ensure we're connected to Orbis
    const isConnected = await isConnectedToOrbis();
    if (!isConnected) {
      const did = await connectOrbis();
      if (!did) {
        throw new Error('Failed to connect to Orbis');
      }
    }

    // Upload profile image to IPFS if it's a blob URL
    let pfpUrl = profile.profileImageUrl;
    if (pfpUrl && pfpUrl.startsWith('blob:')) {
      try {
        const response = await fetch(pfpUrl);
        const blob = await response.blob();
        const fileName = `profile_${profile.address.substring(0, 10)}_${Date.now()}.${blob.type.split('/')[1] || 'jpg'}`;
        pfpUrl = await uploadBlobToIPFS(blob, fileName);
      } catch (error) {
        console.error('Error uploading profile image to IPFS:', error);
      }
    }

    // Upload cover image to IPFS if it's a blob URL
    let coverUrl = profile.coverImageUrl;
    if (coverUrl && coverUrl.startsWith('blob:')) {
      try {
        const response = await fetch(coverUrl);
        const blob = await response.blob();
        const fileName = `cover_${profile.address.substring(0, 10)}_${Date.now()}.${blob.type.split('/')[1] || 'jpg'}`;
        coverUrl = await uploadBlobToIPFS(blob, fileName);
      } catch (error) {
        console.error('Error uploading cover image to IPFS:', error);
      }
    }

    // Prepare profile data for Orbis
    const orbisProfile = {
      username: profile.username,
      description: profile.bio,
      pfp: pfpUrl,
      cover: coverUrl,
      // Add any additional fields needed
      displayName: profile.displayName,
      address: profile.address,
      walletAddress: profile.walletAddress,
      // Convert social links to Orbis format
      data: {
        socialLinks: profile.socialLinks,
        stats: profile.stats,
        joinedDate: profile.joinedDate instanceof Date ? profile.joinedDate.toISOString() : profile.joinedDate,
        verification: profile.verification ? {
          ...profile.verification,
          since: profile.verification.since instanceof Date ?
            profile.verification.since.toISOString() :
            profile.verification.since
        } : undefined
      }
    };

    // Update the profile
    console.log('Updating Orbis profile with data:', orbisProfile);
    const res = await orbis.updateProfile(orbisProfile);

    if (res.status === 200) {
      console.log('Successfully updated profile in Orbis:', res);
      return res.doc;
    } else {
      console.error('Failed to update profile in Orbis:', res);
      toast.error('Failed to update profile. Please try again.');
      return null;
    }
  } catch (error) {
    console.error('Error updating profile in Orbis:', error);
    toast.error('Error updating profile. Please try again.');
    return null;
  }
}

/**
 * Get a user profile from Orbis
 * @param did The user's DID or address
 * @returns The profile or null if not found
 */
export async function getProfile(did: string): Promise<UserProfile | null> {
  try {
    console.log('Getting profile from Orbis for DID/address:', did);

    // Check if this is an address or a DID
    const isDID = did.startsWith('did:');

    // Get the profile from Orbis
    const { data, error } = await orbis.getProfile(did);

    if (error) {
      console.error('Error getting profile from Orbis:', error);
      return null;
    }

    if (!data) {
      console.log('No profile found in Orbis for:', did);
      return null;
    }

    console.log('Found profile in Orbis:', data);

    // Convert Orbis profile to our UserProfile format
    const profile: UserProfile = {
      address: isDID ? data.address : did.toLowerCase(),
      walletAddress: isDID ? data.address : did.toLowerCase(),
      username: data.username || `user_${did.substring(0, 8).toLowerCase()}`,
      displayName: data.displayName || data.username || `User ${did.substring(0, 6)}`,
      bio: data.description || '',
      profileImageUrl: data.pfp || '',
      coverImageUrl: data.cover || '',
      socialLinks: data.data?.socialLinks || {},
      stats: data.data?.stats || { posts: 0, likes: 0, tips: 0 },
      joinedDate: data.data?.joinedDate ? new Date(data.data.joinedDate) : new Date(),
      verification: data.data?.verification ? {
        ...data.data.verification,
        since: data.data.verification.since ? new Date(data.data.verification.since) : new Date()
      } : undefined,
      orbisDid: data.did
    };

    return profile;
  } catch (error) {
    console.error('Error getting profile from Orbis:', error);
    return null;
  }
}

/**
 * Create a post in Orbis
 * @param content The post content
 * @param context The context (channel) to post in
 * @returns The post details or null if failed
 */
export async function createPost(content: any, context?: string): Promise<any | null> {
  try {
    // Ensure we're connected to Orbis
    const isConnected = await isConnectedToOrbis();
    if (!isConnected) {
      // Force connection since the user is actively trying to create a post
      const did = await connectOrbis(true);
      if (!did) {
        throw new Error('Failed to connect to Orbis');
      }
    }

    // Create the post
    const res = await orbis.createPost({
      body: content.body,
      context: context,
      data: content.data || {}
    });

    if (res.status === 200) {
      console.log('Successfully created post in Orbis:', res);
      return res.doc;
    } else {
      console.error('Failed to create post in Orbis:', res);
      toast.error('Failed to create post. Please try again.');
      return null;
    }
  } catch (error) {
    console.error('Error creating post in Orbis:', error);
    toast.error('Error creating post. Please try again.');
    return null;
  }
}
