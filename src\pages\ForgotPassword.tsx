import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import PasswordResetForm from '@/components/PasswordResetForm';

const ForgotPassword: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  
  // Redirect to home if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, navigate]);
  
  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-background">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-voicechain-purple">VoiceChain</h1>
          <p className="text-muted-foreground mt-2">Reset your password</p>
        </div>
        
        <PasswordResetForm />
      </div>
    </div>
  );
};

export default ForgotPassword;
