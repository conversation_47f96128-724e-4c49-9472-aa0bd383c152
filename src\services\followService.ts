import { supabase } from '@/integrations/supabase/client';

/**
 * Service for managing follow/unfollow functionality
 */
export class FollowService {
  /**
   * Follow a user
   */
  static async followUser(followerAddress: string, followingAddress: string): Promise<boolean> {
    try {
      console.log(`Following user: ${followerAddress} -> ${followingAddress}`);
      
      // Normalize addresses
      const normalizedFollower = followerAddress.toLowerCase();
      const normalizedFollowing = followingAddress.toLowerCase();
      
      // Check if already following
      const { data: existingFollow } = await supabase
        .from('follows')
        .select('*')
        .eq('follower_id', normalizedFollower)
        .eq('following_id', normalizedFollowing)
        .single();
      
      if (existingFollow) {
        console.log('Already following this user');
        return true;
      }
      
      // Create follow relationship
      const { error } = await supabase
        .from('follows')
        .insert({
          follower_id: normalizedFollower,
          following_id: normalizedFollowing,
          created_at: new Date().toISOString()
        });
      
      if (error) {
        console.error('Error following user:', error);
        return false;
      }
      
      console.log('Successfully followed user');
      return true;
    } catch (error) {
      console.error('Error in followUser:', error);
      return false;
    }
  }
  
  /**
   * Unfollow a user
   */
  static async unfollowUser(followerAddress: string, followingAddress: string): Promise<boolean> {
    try {
      console.log(`Unfollowing user: ${followerAddress} -> ${followingAddress}`);
      
      // Normalize addresses
      const normalizedFollower = followerAddress.toLowerCase();
      const normalizedFollowing = followingAddress.toLowerCase();
      
      // Delete follow relationship
      const { error } = await supabase
        .from('follows')
        .delete()
        .eq('follower_id', normalizedFollower)
        .eq('following_id', normalizedFollowing);
      
      if (error) {
        console.error('Error unfollowing user:', error);
        return false;
      }
      
      console.log('Successfully unfollowed user');
      return true;
    } catch (error) {
      console.error('Error in unfollowUser:', error);
      return false;
    }
  }
  
  /**
   * Check if user is following another user
   */
  static async isFollowing(followerAddress: string, followingAddress: string): Promise<boolean> {
    try {
      const normalizedFollower = followerAddress.toLowerCase();
      const normalizedFollowing = followingAddress.toLowerCase();
      
      const { data, error } = await supabase
        .from('follows')
        .select('*')
        .eq('follower_id', normalizedFollower)
        .eq('following_id', normalizedFollowing)
        .single();
      
      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found" error
        console.error('Error checking follow status:', error);
        return false;
      }
      
      return !!data;
    } catch (error) {
      console.error('Error in isFollowing:', error);
      return false;
    }
  }
  
  /**
   * Get follower count for a user
   */
  static async getFollowerCount(userAddress: string): Promise<number> {
    try {
      const normalizedAddress = userAddress.toLowerCase();
      
      const { count, error } = await supabase
        .from('follows')
        .select('*', { count: 'exact', head: true })
        .eq('following_id', normalizedAddress);
      
      if (error) {
        console.error('Error getting follower count:', error);
        return 0;
      }
      
      return count || 0;
    } catch (error) {
      console.error('Error in getFollowerCount:', error);
      return 0;
    }
  }
  
  /**
   * Get following count for a user
   */
  static async getFollowingCount(userAddress: string): Promise<number> {
    try {
      const normalizedAddress = userAddress.toLowerCase();
      
      const { count, error } = await supabase
        .from('follows')
        .select('*', { count: 'exact', head: true })
        .eq('follower_id', normalizedAddress);
      
      if (error) {
        console.error('Error getting following count:', error);
        return 0;
      }
      
      return count || 0;
    } catch (error) {
      console.error('Error in getFollowingCount:', error);
      return 0;
    }
  }
  
  /**
   * Get followers list for a user
   */
  static async getFollowers(userAddress: string): Promise<string[]> {
    try {
      const normalizedAddress = userAddress.toLowerCase();
      
      const { data, error } = await supabase
        .from('follows')
        .select('follower_id')
        .eq('following_id', normalizedAddress);
      
      if (error) {
        console.error('Error getting followers:', error);
        return [];
      }
      
      return data?.map(follow => follow.follower_id) || [];
    } catch (error) {
      console.error('Error in getFollowers:', error);
      return [];
    }
  }
  
  /**
   * Get following list for a user
   */
  static async getFollowing(userAddress: string): Promise<string[]> {
    try {
      const normalizedAddress = userAddress.toLowerCase();
      
      const { data, error } = await supabase
        .from('follows')
        .select('following_id')
        .eq('follower_id', normalizedAddress);
      
      if (error) {
        console.error('Error getting following:', error);
        return [];
      }
      
      return data?.map(follow => follow.following_id) || [];
    } catch (error) {
      console.error('Error in getFollowing:', error);
      return [];
    }
  }
}
