
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { toast } from '@/components/ui/sonner';
import { Loader2, CheckCircle2, AlertCircle } from 'lucide-react';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import { UserProfile } from '@/types/user-profile';
import { supabase } from '@/integrations/supabase/client';

const SignupForm: React.FC = () => {
  const navigate = useNavigate();
  const { register, isLoading } = useAuth();
  const { addProfile } = useProfiles();
  
  const [formData, setFormData] = useState({
    email: '',
    username: '',
    displayName: '',
    password: '',
    confirmPassword: '',
    bio: '',
    acceptTerms: false
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [step, setStep] = useState(1);
  
  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user types
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };
  
  // Handle checkbox change
  const handleCheckboxChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, acceptTerms: checked }));
    
    // Clear error when user checks
    if (errors.acceptTerms) {
      setErrors(prev => ({ ...prev, acceptTerms: '' }));
    }
  };
  
  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    // Step 1 validation
    if (step === 1) {
      if (!formData.email) {
        newErrors.email = 'Email is required';
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        newErrors.email = 'Email is invalid';
      }
      
      if (!formData.username) {
        newErrors.username = 'Username is required';
      } else if (formData.username.length < 3) {
        newErrors.username = 'Username must be at least 3 characters';
      }
      
      if (!formData.displayName) {
        newErrors.displayName = 'Display name is required';
      }
    }
    
    // Step 2 validation
    if (step === 2) {
      if (!formData.password) {
        newErrors.password = 'Password is required';
      } else if (formData.password.length < 8) {
        newErrors.password = 'Password must be at least 8 characters';
      }
      
      if (!formData.confirmPassword) {
        newErrors.confirmPassword = 'Please confirm your password';
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
      
      if (!formData.acceptTerms) {
        newErrors.acceptTerms = 'You must accept the terms and conditions';
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Handle next step
  const handleNextStep = () => {
    if (validateForm()) {
      setStep(2);
    }
  };
  
  // Handle previous step
  const handlePrevStep = () => {
    setStep(1);
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      // Register the user
      const registerResult = await register({
        email: formData.email,
        username: formData.username,
        displayName: formData.displayName,
        password: formData.password,
        bio: formData.bio
      });
      
      // Get current user details
      const user = registerResult?.user;
      
      if (user && user.id) {
        console.log('Successfully registered. Creating profile for:', user.id);
        
        // Create a new user profile
        const newProfile: UserProfile = {
          address: user.id,
          walletAddress: user.walletAddress || '',
          username: formData.username,
          displayName: formData.displayName,
          bio: formData.bio || '',
          profileImageUrl: '',
          coverImageUrl: '',
          socialLinks: { twitter: '', github: '', website: '' },
          stats: { 
            posts: 0, 
            likes: 0, 
            tips: 0,
            followers: 0,
            following: 0
          },
          joinedDate: new Date(),
          verification: {
            isVerified: false
          }
        };
        
        try {
          // Save profile directly to Supabase as a backup/redundancy
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .insert({
              id: user.id,
              wallet_address: user.walletAddress || '',
              username: formData.username,
              display_name: formData.displayName,
              bio: formData.bio || '',
              avatar_url: '',
              cover_image_url: '',
              social_links: { twitter: '', github: '', website: '' },
              is_verified: false,
              post_count: 0,
              like_count: 0,
              tip_count: 0,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .select();
            
          if (profileError) {
            console.error('Error saving profile to Supabase:', profileError);
          } else {
            console.log('Profile saved to Supabase successfully:', profileData);
          }
        } catch (dbError) {
          console.error('Error with database operation:', dbError);
        }
        
        // Add the profile to context
        addProfile(newProfile);
        
        // Store connected account
        localStorage.setItem('connectedAccount', user.id);
        
        toast.success('Account created successfully! Welcome to Audra.');
      }
      
      // Navigate to home page after successful registration
      navigate('/');
    } catch (error: any) {
      console.error('Registration error:', error);
      toast.error(error.message || 'Registration failed. Please try again.');
    }
  };
  
  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Create Your Account</CardTitle>
        <CardDescription>
          Join the voice-based Web3 social platform
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {step === 1 ? (
            <>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="Enter your email"
                  value={formData.email}
                  onChange={handleChange}
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && (
                  <p className="text-xs text-red-500">{errors.email}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  name="username"
                  placeholder="Choose a unique username"
                  value={formData.username}
                  onChange={handleChange}
                  className={errors.username ? 'border-red-500' : ''}
                />
                {errors.username && (
                  <p className="text-xs text-red-500">{errors.username}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="displayName">Display Name</Label>
                <Input
                  id="displayName"
                  name="displayName"
                  placeholder="Your display name"
                  value={formData.displayName}
                  onChange={handleChange}
                  className={errors.displayName ? 'border-red-500' : ''}
                />
                {errors.displayName && (
                  <p className="text-xs text-red-500">{errors.displayName}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="bio">Bio (Optional)</Label>
                <Textarea
                  id="bio"
                  name="bio"
                  placeholder="Tell us about yourself"
                  value={formData.bio}
                  onChange={handleChange}
                  rows={3}
                />
              </div>
              
              <Button 
                type="button" 
                className="w-full bg-voicechain-purple hover:bg-voicechain-accent"
                onClick={handleNextStep}
              >
                Continue
              </Button>
            </>
          ) : (
            <>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  placeholder="Create a password"
                  value={formData.password}
                  onChange={handleChange}
                  className={errors.password ? 'border-red-500' : ''}
                />
                {errors.password && (
                  <p className="text-xs text-red-500">{errors.password}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm Password</Label>
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  placeholder="Confirm your password"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  className={errors.confirmPassword ? 'border-red-500' : ''}
                />
                {errors.confirmPassword && (
                  <p className="text-xs text-red-500">{errors.confirmPassword}</p>
                )}
              </div>
              
              <div className="space-y-4">
                <div className="flex items-start space-x-2">
                  <Checkbox 
                    id="acceptTerms" 
                    checked={formData.acceptTerms}
                    onCheckedChange={handleCheckboxChange}
                  />
                  <div className="grid gap-1.5 leading-none">
                    <label
                      htmlFor="acceptTerms"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      I accept the terms and conditions
                    </label>
                    <p className="text-xs text-muted-foreground">
                      By creating an account, you agree to our Terms of Service and Privacy Policy.
                    </p>
                  </div>
                </div>
                {errors.acceptTerms && (
                  <p className="text-xs text-red-500">{errors.acceptTerms}</p>
                )}
              </div>
              
              <div className="flex flex-col space-y-2">
                <Button 
                  type="submit" 
                  className="w-full bg-voicechain-purple hover:bg-voicechain-accent"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating Account...
                    </>
                  ) : (
                    'Create Account'
                  )}
                </Button>
                
                <Button 
                  type="button" 
                  variant="outline"
                  className="w-full"
                  onClick={handlePrevStep}
                  disabled={isLoading}
                >
                  Back
                </Button>
              </div>
              
              <div className="p-3 bg-blue-50 dark:bg-blue-950 rounded-md">
                <div className="flex items-start gap-3">
                  <CheckCircle2 className="h-5 w-5 text-blue-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">In-App Wallet</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      A secure wallet will be automatically created for you when you register. You'll be able to use it to send and receive tips within the platform.
                    </p>
                  </div>
                </div>
              </div>
            </>
          )}
        </form>
      </CardContent>
      
      <CardFooter>
        <p className="text-xs text-muted-foreground text-center w-full">
          Already have an account? <a href="/login" className="text-voicechain-purple hover:underline">Log in</a>
        </p>
      </CardFooter>
    </Card>
  );
};

export default SignupForm;
