
/**
 * Audio Storage Helper
 * Provides utilities for storing and retrieving audio recordings
 * using IndexedDB for persistence across page refreshes
 */

const DB_NAME = 'AudraAudioStorage';
const DB_VERSION = 1;
const AUDIO_STORE = 'recordings';

/**
 * Open the IndexedDB database
 * @returns Promise that resolves to the database instance
 */
export const openAudioDatabase = (): Promise<IDBDatabase> => {
  return new Promise((resolve, reject) => {
    try {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onupgradeneeded = (event) => {
        const db = request.result;
        
        // Create object stores if they don't exist
        if (!db.objectStoreNames.contains(AUDIO_STORE)) {
          db.createObjectStore(AUDIO_STORE, { keyPath: 'id' });
        }
      };

      request.onsuccess = () => {
        resolve(request.result);
      };

      request.onerror = (event) => {
        console.error('Error opening IndexedDB:', event);
        reject(new Error('Failed to open IndexedDB'));
      };
    } catch (error) {
      console.error('Error accessing IndexedDB:', error);
      reject(error);
    }
  });
};

/**
 * Store audio in IndexedDB
 * @param id The unique ID for the recording
 * @param audioBlob The audio blob to store
 * @param userId The user ID (wallet address)
 * @param metadata Additional metadata to store
 */
export const storeAudioInIndexedDB = async (
  id: string, 
  audioBlob: Blob, 
  userId: string,
  metadata: Record<string, any> = {}
): Promise<void> => {
  return new Promise(async (resolve, reject) => {
    try {
      const db = await openAudioDatabase();
      const transaction = db.transaction([AUDIO_STORE], 'readwrite');
      const store = transaction.objectStore(AUDIO_STORE);
      
      // Store the recording with metadata
      const putRequest = store.put({
        id,
        blob: audioBlob,
        userId,
        timestamp: Date.now(),
        type: audioBlob.type,
        ...metadata
      });
      
      putRequest.onsuccess = () => {
        console.log(`Audio stored in IndexedDB with ID: ${id}`);
        
        // Also store in the global cache for immediate access
        if (typeof window !== 'undefined') {
          if (!(window as any).audioCache) {
            (window as any).audioCache = {};
          }
          (window as any).audioCache[`indexeddb://${id}`] = audioBlob;
        }
        
        resolve();
      };
      
      putRequest.onerror = (event) => {
        console.error('Error storing audio in IndexedDB:', event);
        reject(new Error('Failed to store audio in IndexedDB'));
      };
      
      transaction.oncomplete = () => {
        db.close();
      };
    } catch (error) {
      console.error('Error in storeAudioInIndexedDB:', error);
      reject(error);
    }
  });
};

/**
 * Get audio from IndexedDB
 * @param id The ID of the audio to retrieve
 * @returns The audio data or null if not found
 */
export const getAudioFromIndexedDB = async (id: string): Promise<{blob: Blob, metadata: any} | null> => {
  return new Promise(async (resolve, reject) => {
    try {
      const db = await openAudioDatabase();
      const transaction = db.transaction([AUDIO_STORE], 'readonly');
      const store = transaction.objectStore(AUDIO_STORE);
      
      const getRequest = store.get(id);
      
      getRequest.onsuccess = () => {
        if (getRequest.result) {
          console.log(`Audio retrieved from IndexedDB with ID: ${id}`);
          
          // Extract the blob and metadata
          const { blob, ...metadata } = getRequest.result;
          
          // Store in the global cache for future use
          if (typeof window !== 'undefined') {
            if (!(window as any).audioCache) {
              (window as any).audioCache = {};
            }
            (window as any).audioCache[`indexeddb://${id}`] = blob;
          }
          
          resolve({ blob, metadata });
        } else {
          console.log(`No audio found in IndexedDB for ID: ${id}`);
          resolve(null);
        }
      };
      
      getRequest.onerror = (event) => {
        console.error('Error retrieving audio from IndexedDB:', event);
        reject(new Error('Failed to retrieve audio from IndexedDB'));
      };
      
      transaction.oncomplete = () => {
        db.close();
      };
    } catch (error) {
      console.error('Error in getAudioFromIndexedDB:', error);
      reject(error);
    }
  });
};

/**
 * Get a playable URL for audio stored in IndexedDB
 * @param id The ID of the audio to retrieve
 * @returns A blob URL that can be used to play the audio
 */
export const getPlayableAudioUrl = async (url: string): Promise<string> => {
  try {
    // Check if this is an IndexedDB URL
    if (url.startsWith('indexeddb://')) {
      const id = url.replace('indexeddb://', '');
      
      // Check if we have this in the global cache first
      if (typeof window !== 'undefined' && 
          (window as any).audioCache && 
          (window as any).audioCache[url]) {
        console.log('Found audio in global cache');
        const cachedBlob = (window as any).audioCache[url];
        return URL.createObjectURL(cachedBlob);
      }
      
      // Try to get from IndexedDB
      const audioData = await getAudioFromIndexedDB(id);
      if (audioData) {
        return URL.createObjectURL(audioData.blob);
      }
    }
    
    // If it's not an IndexedDB URL or the audio wasn't found, return the original URL
    return url;
  } catch (error) {
    console.error('Error getting playable audio URL:', error);
    return url;
  }
};

/**
 * Store audio URL mapping in Supabase
 * This helps track which posts are using which audio URLs
 * @param postId The ID of the post
 * @param audioUrl The audio URL
 */
export const storeAudioUrlMapping = async (postId: string, audioUrl: string): Promise<void> => {
  try {
    const user = supabase.auth.getUser();
    if (!user) {
      console.warn('User not authenticated, cannot store audio URL mapping');
      return;
    }

    const { error } = await supabase
      .from('audio_url_mappings')
      .upsert({
        post_id: postId,
        audio_url: audioUrl,
        created_at: new Date().toISOString()
      }, { 
        onConflict: 'post_id' 
      });
    
    if (error) {
      console.error('Error storing audio URL mapping in Supabase:', error);
    } else {
      console.log(`Stored audio URL mapping for post ${postId}: ${audioUrl}`);
    }
  } catch (error) {
    console.error('Error storing audio URL mapping:', error);
  }
};

/**
 * Get audio URL mapping from Supabase
 * @param postId The ID of the post
 * @returns The audio URL or null if not found
 */
export const getAudioUrlMapping = async (postId: string): Promise<string | null> => {
  try {
    const { data, error } = await supabase
      .from('audio_url_mappings')
      .select('audio_url')
      .eq('post_id', postId)
      .single();
    
    if (error) {
      console.error('Error getting audio URL mapping from Supabase:', error);
      return null;
    }
    
    return data?.audio_url || null;
  } catch (error) {
    console.error('Error getting audio URL mapping:', error);
    return null;
  }
};
