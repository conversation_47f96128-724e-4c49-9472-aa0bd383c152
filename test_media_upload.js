import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://jcltjkaumevuycntdmds.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpjbHRqa2F1bWV2dXljbnRkbWRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTkwMDEsImV4cCI6MjA2MTg3NTAwMX0.LqQSk0NwB1mDB4Cznn0iQSrYvsAAWyUsaj8reo_etqM'
);

async function testMediaUpload() {
  console.log('🧪 Testing media upload after RLS fix...\n');

  try {
    // 1. Test storage bucket access
    console.log('1️⃣ Testing storage bucket access...');
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      console.error('❌ Error listing buckets:', bucketsError);
      return;
    }

    const mediaBucket = buckets.find(bucket => bucket.name === 'media');
    if (mediaBucket) {
      console.log('✅ Media bucket exists:', mediaBucket);
    } else {
      console.log('❌ Media bucket not found');
      return;
    }

    // 2. Test file upload
    console.log('\n2️⃣ Testing file upload...');
    
    // Create a test image blob
    const canvas = document.createElement('canvas');
    canvas.width = 100;
    canvas.height = 100;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = '#FF0000';
    ctx.fillRect(0, 0, 100, 100);
    
    // Convert to blob
    const testBlob = await new Promise(resolve => {
      canvas.toBlob(resolve, 'image/png');
    });
    
    const testFile = new File([testBlob], 'test-image.png', { type: 'image/png' });
    const testFileName = `test/${Date.now()}_test.png`;
    
    console.log('📤 Uploading test file:', testFileName);
    
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('media')
      .upload(testFileName, testFile, {
        cacheControl: '3600',
        upsert: true
      });

    if (uploadError) {
      console.error('❌ Upload failed:', uploadError);
      
      if (uploadError.message.includes('row-level security')) {
        console.log('\n🔧 RLS Policy Issue Detected!');
        console.log('Please run the SQL script: fix_media_rls_policies.sql');
        console.log('This will fix the Row Level Security policies blocking uploads.');
      }
      
      return;
    }

    console.log('✅ Upload successful:', uploadData);

    // 3. Test getting public URL
    console.log('\n3️⃣ Testing public URL generation...');
    const { data: urlData } = supabase.storage
      .from('media')
      .getPublicUrl(testFileName);
    
    console.log('✅ Public URL generated:', urlData.publicUrl);

    // 4. Test database insertion
    console.log('\n4️⃣ Testing database insertion...');
    
    const testMediaRecord = {
      id: `test_${Date.now()}`,
      voice_message_id: 'test_message_id',
      url: urlData.publicUrl,
      type: 'image'
    };

    const { data: insertData, error: insertError } = await supabase
      .from('voice_message_media')
      .insert(testMediaRecord)
      .select()
      .single();

    if (insertError) {
      console.error('❌ Database insertion failed:', insertError);
      
      if (insertError.message.includes('row-level security')) {
        console.log('\n🔧 Database RLS Policy Issue!');
        console.log('The voice_message_media table needs RLS policies.');
        console.log('Please run the SQL script: fix_media_rls_policies.sql');
      }
      
      return;
    }

    console.log('✅ Database insertion successful:', insertData);

    // 5. Test retrieval
    console.log('\n5️⃣ Testing media retrieval...');
    const { data: retrievedData, error: retrieveError } = await supabase
      .from('voice_message_media')
      .select('*')
      .eq('id', testMediaRecord.id)
      .single();

    if (retrieveError) {
      console.error('❌ Retrieval failed:', retrieveError);
      return;
    }

    console.log('✅ Media retrieval successful:', retrievedData);

    // 6. Clean up test data
    console.log('\n6️⃣ Cleaning up test data...');
    
    // Delete from database
    await supabase
      .from('voice_message_media')
      .delete()
      .eq('id', testMediaRecord.id);
    
    // Delete from storage
    await supabase.storage
      .from('media')
      .remove([testFileName]);
    
    console.log('✅ Test data cleaned up');

    // 7. Final results
    console.log('\n🎉 Media Upload Test Results:');
    console.log('✅ Storage bucket access: WORKING');
    console.log('✅ File upload: WORKING');
    console.log('✅ Public URL generation: WORKING');
    console.log('✅ Database insertion: WORKING');
    console.log('✅ Media retrieval: WORKING');
    console.log('\n🚀 Media persistence should now work correctly!');
    console.log('\nNext steps:');
    console.log('1. Try uploading a post with images in your app');
    console.log('2. Refresh the page and verify images persist');
    console.log('3. Check that images load on different devices');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure you ran the fix_media_rls_policies.sql script');
    console.log('2. Check that all storage buckets have proper RLS policies');
    console.log('3. Verify the voice_message_media table exists and has RLS policies');
  }
}

// Only run if in browser environment
if (typeof window !== 'undefined') {
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', testMediaUpload);
  } else {
    testMediaUpload();
  }
} else {
  console.log('This test needs to run in a browser environment');
}
