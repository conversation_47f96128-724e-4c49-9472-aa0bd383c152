import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../services/supabase';

interface User {
  id: string;
  email: string;
  role: 'admin' | 'super_admin';
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  login: async () => ({ success: false }),
  logout: async () => { },
});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // SIMPLIFIED SESSION CHECK - EMERGENCY FIX
    const checkSession = async () => {
      try {
        // Check Supabase session
        const { data, error } = await supabase.auth.getSession();

        if (error || !data.session) {
          console.log('No valid session found');
          setUser(null);
          localStorage.removeItem('admin_user');
        } else {
          console.log('Session found, creating admin user');

          // Create a default admin user
          const adminUser: User = {
            id: data.session.user.id,
            email: data.session.user.email || '',
            role: 'super_admin', // Default to super_admin
          };

          setUser(adminUser);
          localStorage.setItem('admin_user', JSON.stringify(adminUser));
        }
      } catch (error) {
        console.error('Error checking session:', error);
        setUser(null);
        localStorage.removeItem('admin_user');
      } finally {
        // Always set loading to false to prevent UI from being stuck
        setIsLoading(false);
      }
    };

    // Set a timeout to ensure loading state is cleared even if the check fails
    const timeoutId = setTimeout(() => {
      setIsLoading(false);
    }, 5000);

    // Subscribe to auth changes
    const { data: authListener } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session);

      if (event === 'SIGNED_IN' && session) {
        // Create a default admin user on sign in
        const adminUser: User = {
          id: session.user.id,
          email: session.user.email || '',
          role: 'super_admin', // Default to super_admin for simplicity
        };

        setUser(adminUser);
        localStorage.setItem('admin_user', JSON.stringify(adminUser));
      } else if (event === 'SIGNED_OUT') {
        setUser(null);
        localStorage.removeItem('admin_user');
      }
    });

    checkSession();

    return () => {
      clearTimeout(timeoutId);
      authListener.subscription.unsubscribe();
    };
  }, []);

  const login = async (email: string, password: string) => {
    try {
      console.log('Attempting login with:', { email });

      // Use Supabase auth for proper authentication
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('Supabase auth error:', error);
        return { success: false, error: error.message };
      }

      if (!data.user) {
        return { success: false, error: 'No user returned from login' };
      }

      console.log('User authenticated:', data.user);

      // SIMPLIFIED: Create admin user directly without checking profiles
      // This is a temporary solution to fix the loading issue
      const adminUser: User = {
        id: data.user.id,
        email: data.user.email || '',
        role: 'super_admin', // Default to super_admin for all users
      };

      // Set the user in state
      setUser(adminUser);

      // Store in localStorage for persistence
      localStorage.setItem('admin_user', JSON.stringify(adminUser));

      return { success: true };
    } catch (error) {
      console.error('Unexpected error during login:', error);
      return { success: false, error: 'An unexpected error occurred. Please try again.' };
    }
  };

  const logout = async () => {
    try {
      // Sign out from Supabase
      await supabase.auth.signOut();

      // Remove the user from localStorage
      localStorage.removeItem('admin_user');

      // Clear the user state
      setUser(null);
    } catch (error) {
      console.error('Error signing out:', error);

      // Even if Supabase sign out fails, clear local state
      localStorage.removeItem('admin_user');
      setUser(null);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated: !!user,
        isLoading,
        login,
        logout,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
