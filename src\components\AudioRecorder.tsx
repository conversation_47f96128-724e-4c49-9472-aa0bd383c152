import React, { useState, useRef, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Mic, Square, Loader2, Trash2, Save } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { toast } from '@/components/ui/sonner';
import { uploadAudio } from '@/services/supabaseStorageService';
import { useWhisper } from '@/hooks/use-whisper';
import AudioPlayer from '@/components/AudioPlayer';

// Use the consistent type for media files
import { MediaFile } from '@/types/media';

interface AudioRecorderProps {
  onRecordingComplete?: (audioBlob: Blob, transcript: string, duration?: number, media?: MediaFile[]) => void;
  onRecordingStart?: () => void;
  onCancel?: () => void;
  maxDuration?: number;
  placeholder?: string;
  maxMediaFiles?: number;
  initialTranscript?: string;
}

const AudioRecorder: React.FC<AudioRecorderProps> = ({
  onRecordingComplete,
  onRecordingStart,
  onCancel,
  maxDuration = 300, // 5 minutes
  placeholder = "Record your voice...",
  maxMediaFiles = 0,
  initialTranscript,
}) => {
  // Recording state
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [audioUrl, setAudioUrl] = useState<string>('');
  const [transcript, setTranscript] = useState<string>(initialTranscript || '');
  const [isSaving, setIsSaving] = useState(false);
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([]);

  // Refs for handling recordings
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const recordingStartTimeRef = useRef<number>(0);

  // Use the whisper hook for transcription
  const { transcribing, startTranscription } = useWhisper({
    onTranscriptionProgress: (text) => {
      console.log('Transcription in progress:', text);
      setTranscript(text);
    },
    onTranscriptionComplete: (text) => {
      console.log('Transcription complete:', text);
      setTranscript(text);
    }
  });

  // Reset everything when the component unmounts
  useEffect(() => {
    return () => {
      stopRecording();
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  // Start recording audio
  const startRecording = async () => {
    try {
      // Clear any existing audio
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
        setAudioUrl('');
      }
      setAudioBlob(null);
      setTranscript(initialTranscript || '');

      // Reset recording timer
      setRecordingTime(0);
      recordingStartTimeRef.current = Date.now();

      // Notify parent component
      if (onRecordingStart) {
        onRecordingStart();
      }

      // Request microphone access
      console.log('Requesting microphone access...');
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      console.log('Microphone access granted');

      // Configure and start the media recorder
      const options = { mimeType: 'audio/webm' };
      const mediaRecorder = new MediaRecorder(stream, options);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      // Handle data available event
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      // Handle recording stop
      mediaRecorder.onstop = async () => {
        // Combine all chunks into a single blob
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        setAudioBlob(audioBlob);

        // Create a URL for the audio blob
        const url = URL.createObjectURL(audioBlob);
        setAudioUrl(url);

        // Calculate the final duration
        const finalDuration = (Date.now() - recordingStartTimeRef.current) / 1000;
        setRecordingTime(Math.ceil(finalDuration));

        // PWA-specific: Auto-play for transcription
        setTimeout(() => {
          const audioElement = document.createElement('audio');
          audioElement.src = url;
          audioElement.preload = 'auto';

          // Try to play audio briefly to trigger transcription
          const playForTranscription = async () => {
            try {
              console.log('🎵 PWA: Auto-playing audio for transcription...');

              // Set volume low for background transcription
              audioElement.volume = 0.1;

              // Play audio
              await audioElement.play();

              // Let it play for a moment then pause for transcription
              setTimeout(() => {
                audioElement.pause();
                audioElement.currentTime = 0;
                console.log('🎵 PWA: Audio played for transcription trigger');
              }, 200); // Play for 200ms to trigger transcription

            } catch (playError) {
              console.log('PWA: Auto-play blocked, proceeding with transcription:', playError);
            }
          };

          playForTranscription();
        }, 100);

        // Start transcription
        await transcribeRecording(audioBlob);

        // Stop all tracks on the stream
        stream.getTracks().forEach(track => track.stop());
      };

      // Handle errors
      mediaRecorder.onerror = (event) => {
        console.error('MediaRecorder error:', event);
        toast.error('Error recording audio');
        stopRecording();
      };

      // Start the media recorder
      mediaRecorder.start(1000); // Collect data every second
      setIsRecording(true);

      // Start the timer
      timerRef.current = setInterval(() => {
        const elapsed = (Date.now() - recordingStartTimeRef.current) / 1000;
        setRecordingTime(Math.ceil(elapsed));

        // Stop recording if max duration is reached
        if (elapsed >= maxDuration) {
          toast.info(`Maximum recording duration of ${maxDuration} seconds reached`);
          stopRecording();
        }
      }, 1000);
    } catch (error) {
      console.error('Error starting recording:', error);
      toast.error('Could not access microphone. Please check permissions.');
    }
  };

  // Stop the recording
  const stopRecording = () => {
    // Clear the timer
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    // Stop the media recorder if it's running
    if (mediaRecorderRef.current && isRecording) {
      try {
        mediaRecorderRef.current.stop();
      } catch (error) {
        console.error('Error stopping MediaRecorder:', error);
      }
      setIsRecording(false);
    }
  };

  // Transcribe the recording
  const transcribeRecording = async (blob: Blob) => {
    if (!blob) return;

    try {
      console.log('Starting transcription...');
      // Clear any existing transcript
      setTranscript('');

      // Start the transcription process
      const transcriptText = await startTranscription(blob);
      console.log('Transcription result:', transcriptText);

      // Set the transcript (may be redundant if onTranscriptionComplete is doing this)
      setTranscript(transcriptText);
    } catch (error) {
      console.error('Error transcribing audio:', error);
      setTranscript(`Voice message recorded at ${new Date().toLocaleTimeString()}`);
    }
  };

  // Handle media file selection from the MediaUploader component
  const handleMediaSelected = (files: MediaFile[]) => {
    setMediaFiles(files);
  };

  // Handle transcript text changes when user edits
  const handleTranscriptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTranscript(e.target.value);
  };

  // Handle cancellation
  const handleCancel = () => {
    // Stop recording if needed
    stopRecording();

    // Clear audio data
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
      setAudioUrl('');
    }
    setAudioBlob(null);
    setTranscript(initialTranscript || '');
    setRecordingTime(0);

    // Clear media files
    setMediaFiles([]);

    // Notify parent component
    if (onCancel) {
      onCancel();
    }
  };

  // Handle save/complete
  const handleComplete = async () => {
    if (!audioBlob) {
      toast.error('No recording to save');
      return;
    }

    setIsSaving(true);
    try {
      // Get the user ID from localStorage
      const userId = localStorage.getItem('connectedAccount') || 'anonymous';

      // Log what we're saving
      console.log('Saving recording with transcript:', transcript);

      // Upload the audio to storage if needed
      let persistentUrl = audioUrl;
      try {
        persistentUrl = await uploadAudio(audioBlob, userId);
        console.log('Audio uploaded to storage:', persistentUrl);
      } catch (error) {
        console.error('Error uploading audio:', error);
        toast.error('Could not upload audio, using temporary storage');
      }

      // Notify parent component
      if (onRecordingComplete) {
        console.log('Calling onRecordingComplete with transcript:', transcript);
        onRecordingComplete(audioBlob, transcript, recordingTime, mediaFiles.length > 0 ? mediaFiles : undefined);

        // Update post count in localStorage
        try {
          // Get current user address
          const userAddress = localStorage.getItem('connectedAccount');

          if (userAddress) {
            // Get current post count
            const currentCount = parseInt(localStorage.getItem(`post_count_${userAddress}`) || '0', 10);

            // Increment and save
            localStorage.setItem(`post_count_${userAddress}`, String(currentCount + 1));

            // Also dispatch an event to update UI
            const event = new CustomEvent('post-count-update', {
              detail: {
                userAddress,
                newCount: currentCount + 1
              }
            });
            window.dispatchEvent(event);
          }
        } catch (error) {
          console.error('Error updating post count:', error);
        }
      }

      // Reset the component if we're not unmounting
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
      setAudioBlob(null);
      setAudioUrl('');
      setTranscript(initialTranscript || '');
      setRecordingTime(0);
      setMediaFiles([]);

      toast.success('Recording saved successfully');
    } catch (error) {
      console.error('Error saving recording:', error);
      toast.error('Could not save recording');
    } finally {
      setIsSaving(false);
    }
  };

  // Format time as MM:SS
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  // Determine what to render based on the current state
  const renderContent = () => {
    if (isRecording) {
      // Recording in progress
      return (
        <div className="space-y-4">
          <div className="flex justify-center">
            <div className="relative">
              <div className="animate-pulse w-16 h-16 bg-red-500 rounded-full flex items-center justify-center">
                <Mic className="h-8 w-8 text-white" />
              </div>
              <div className="absolute -bottom-8 left-1/2 -translate-x-1/2 text-sm font-medium">
                {formatTime(recordingTime)}
              </div>
            </div>
          </div>

          <Progress value={(recordingTime / maxDuration) * 100} className="h-2" />

          <div className="flex justify-center pt-6">
            <Button
              variant="destructive"
              onClick={stopRecording}
              size="lg"
              className="rounded-full px-6"
            >
              <Square className="mr-2 h-5 w-5" />
              Stop Recording
            </Button>
          </div>
        </div>
      );
    }

    if (audioUrl && audioBlob) {
      // Recording finished, show audio player and transcript
      return (
        <div className="space-y-4">
          {/* Audio player */}
          <div className="bg-muted/30 rounded-lg p-3">
            <AudioPlayer
              src={audioUrl}
              className="w-full"
            />
          </div>

          {/* Transcript editor */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium">
                Transcript {transcribing && <span className="text-muted-foreground">(transcribing...)</span>}
              </label>
              {transcribing && <Loader2 className="h-4 w-4 animate-spin" />}
            </div>
            <Textarea
              placeholder={placeholder}
              value={transcript}
              onChange={handleTranscriptChange}
              className="min-h-[100px]"
              disabled={transcribing}
            />
          </div>

          {/* Action buttons */}
          <div className="flex justify-between pt-2">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={isSaving}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>

            <Button
              onClick={handleComplete}
              disabled={isSaving}
            >
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save
                </>
              )}
            </Button>
          </div>
        </div>
      );
    }

    // Initial state, show record button
    return (
      <div className="flex justify-center py-8">
        <Button
          variant="outline"
          size="lg"
          onClick={startRecording}
          className="rounded-full h-16 w-16 flex items-center justify-center bg-voicechain-purple hover:bg-voicechain-accent text-white"
        >
          <Mic className="h-6 w-6 text-white" style={{ color: 'white' }} />
        </Button>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {renderContent()}
    </div>
  );
};

export default AudioRecorder;
