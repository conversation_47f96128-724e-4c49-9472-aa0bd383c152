// <PERSON>ript to verify the owner account in Supabase
// Run this script with: node src/scripts/verify-owner.js

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://jcltjkaumevuycntdmds.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Wy-QQBnxvBuHXJKhgvQxXSIGCME9Jvl3Tz0vZnXlx0c';

// Owner address
const OWNER_ADDRESS = '******************************************';

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyOwner() {
  console.log('Starting owner verification process...');
  
  try {
    // First, check if the profile exists
    const { data: existingProfile, error: fetchError } = await supabase
      .from('profiles')
      .select('*')
      .eq('wallet_address', OWNER_ADDRESS.toLowerCase())
      .single();
    
    if (fetchError && fetchError.code !== 'PGRST116') {
      console.error('Error fetching profile:', fetchError);
      return;
    }
    
    if (!existingProfile) {
      console.log('Profile not found. Creating a new profile for the owner...');
      
      // Create a new profile for the owner
      const { error: createError } = await supabase
        .from('profiles')
        .insert({
          id: OWNER_ADDRESS.toLowerCase(),
          wallet_address: OWNER_ADDRESS.toLowerCase(),
          username: 'owner',
          display_name: 'Audra Owner',
          bio: 'Creator and owner of Audra',
          is_verified: true,
          verification_type: 'owner',
          verified_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      
      if (createError) {
        console.error('Error creating owner profile:', createError);
        return;
      }
      
      console.log('Owner profile created successfully!');
    } else {
      console.log('Profile found. Updating verification status...');
      
      // Update the existing profile with owner verification
      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          is_verified: true,
          verification_type: 'owner',
          verified_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('wallet_address', OWNER_ADDRESS.toLowerCase());
      
      if (updateError) {
        console.error('Error updating owner profile:', updateError);
        return;
      }
      
      console.log('Owner profile updated successfully!');
    }
    
    // Also create a verification application record for reference
    const { error: appError } = await supabase
      .from('verification_applications')
      .upsert({
        id: `owner-${Date.now()}`,
        user_address: OWNER_ADDRESS.toLowerCase(),
        type: 'owner',
        reason: 'Automatic verification for owner account',
        social_proof: 'N/A',
        status: 'approved',
        submitted_at: new Date().toISOString(),
        reviewed_at: new Date().toISOString()
      });
    
    if (appError) {
      console.error('Error creating verification application record:', appError);
      // Continue anyway as this is just for record-keeping
    } else {
      console.log('Verification application record created successfully!');
    }
    
    console.log('Owner verification process completed successfully!');
  } catch (error) {
    console.error('Unexpected error during owner verification:', error);
  }
}

// Run the verification function
verifyOwner().catch(console.error);
