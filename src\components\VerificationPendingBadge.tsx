import React from 'react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { Clock } from 'lucide-react';

interface VerificationPendingBadgeProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showTooltip?: boolean;
}

const VerificationPendingBadge: React.FC<VerificationPendingBadgeProps> = ({
  size = 'md',
  className,
  showTooltip = true
}) => {
  // Size classes - reduced sizes to match VerificationBadge
  const sizeClasses = {
    sm: 'w-3.5 h-3.5',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  };

  // Badge element - redesigned to match VerificationBadge
  const badge = (
    <div className={cn(
      'relative flex items-center justify-center rounded-full',
      'bg-amber-500', // Amber color for pending
      sizeClasses[size],
      'ring-[1.5px] ring-white', // Add white outline
      className
    )}>
      <Clock className="text-white z-10 w-[55%] h-[55%] stroke-[3px]" />
    </div>
  );

  // Return with or without tooltip
  if (showTooltip) {
    return (
      <TooltipProvider>
        <Tooltip delayDuration={300}>
          <TooltipTrigger asChild>
            {badge}
          </TooltipTrigger>
          <TooltipContent className="px-3 py-1.5 text-xs">
            Verification Pending
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return badge;
};

export default VerificationPendingBadge;
