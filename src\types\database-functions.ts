import { SupabaseClient } from '@supabase/supabase-js';

// Extend the SupabaseClient type to include our custom RPC functions
declare module '@supabase/supabase-js' {
  interface SupabaseClient {
    rpc<T = any>(
      fn: 'get_user_voice_messages' | 'get_voice_message_media' | 'get_user_journals' | 'get_message_voice_reactions' | 'delete_post' | 'execute_sql',
      params?: {
        user_id_param?: string;
        message_id_param?: string;
        post_id?: string;
        sql_query?: string;
      }
    ): Promise<{ data: T; error: Error | null }>;
  }
}

// Define the return types for our database functions

// Voice Message from get_user_voice_messages function
export interface DbVoiceMessage {
  id: string;
  profile_id: string;
  channel_id: string | null;
  parent_id: string | null;
  transcript: string | null;
  audio_url: string;
  audio_duration: number;
  created_at: string;
  updated_at: string;
  is_pinned: boolean;
  deleted_at: string | null;
}

// Voice Message Media from get_voice_message_media function
export interface DbVoiceMessageMedia {
  id: string;
  voice_message_id: string;
  url: string;
  type: string;
  created_at: string;
}

// Journal from get_user_journals function
export interface DbJournal {
  id: string;
  profile_id: string;
  title: string | null;
  transcript: string | null;
  audio_url: string | null;
  audio_duration: number | null;
  is_locked: boolean;
  scheduled_for: string | null;
  created_at: string;
  updated_at: string;
}

// Voice Reaction from get_message_voice_reactions function
export interface DbVoiceReaction {
  id: string;
  voice_message_id: string;
  profile_id: string;
  emoji: string;
  created_at: string;
}
