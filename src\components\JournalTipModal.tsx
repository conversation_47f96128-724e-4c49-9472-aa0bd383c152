import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle } from '@/components/ui/drawer';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/sonner';
import { sendJournalTip } from '@/services/tipService';
import { useAuth } from '@/contexts/AuthContext';
import { useIsMobile } from '@/hooks/use-mobile';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import { Coins, DollarSign, X, User, Wallet } from 'lucide-react';
import walletService from '@/services/walletService';
import ethereumIcon from '/images/chains/ethereum.png';
import solanaIcon from '/images/chains/solana.png';

interface JournalTipModalProps {
  isOpen: boolean;
  onClose: () => void;
  journalId: string;
  journalOwnerId: string;
  journalTitle: string;
}

export default function JournalTipModal({ 
  isOpen, 
  onClose, 
  journalId, 
  journalOwnerId, 
  journalTitle 
}: JournalTipModalProps) {
  console.log('JournalTipModal rendered with props:', { isOpen, journalId, journalOwnerId, journalTitle });
  const [amount, setAmount] = useState('');
  const [currency, setCurrency] = useState('SOL');
  const [isProcessing, setIsProcessing] = useState(false);
  const { user } = useAuth();
  const { getProfileByAddress } = useProfiles();
  const isMobile = useIsMobile();

  // Get creator profile information
  const creatorProfile = getProfileByAddress(journalOwnerId);
  const creatorName = creatorProfile?.displayName || creatorProfile?.username || 'Unknown Creator';
  const formattedWallet = walletService.formatAddress(journalOwnerId);

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === '' || /^\d*\.?\d*$/.test(value)) {
      setAmount(value);
    }
  };

  const handleSendTip = async () => {
    if (!user) {
      toast.error('Please sign in to send a tip');
      return;
    }

    if (!amount || parseFloat(amount) <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    if (user.id === journalOwnerId) {
      toast.error('You cannot tip your own journal');
      return;
    }

    setIsProcessing(true);

    try {
      const result = await sendJournalTip(
        user.id,
        journalId,
        journalOwnerId,
        amount,
        currency
      );

      if (result.success) {
        toast.success(`Successfully sent ${amount} ${currency} to ${creatorName}!`);
        setAmount('');
        onClose();
      } else {
        throw new Error('Failed to send tip');
      }
    } catch (error) {
      console.error('Error sending journal tip:', error);
      toast.error('Failed to send tip. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const ModalContent = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center w-10 h-10 bg-primary/10 rounded-full">
            <Coins className="w-5 h-5 text-primary" />
          </div>
          <div>
            <h3 className="text-lg font-semibold">Tip Journal Creator</h3>
            <p className="text-sm text-muted-foreground">
              Show appreciation for "{journalTitle}"
            </p>
          </div>
        </div>
        <Button variant="ghost" size="icon" onClick={onClose} disabled={isProcessing}>
          <X size={20} />
        </Button>
      </div>

      {/* Creator Info */}
      <div className="bg-muted/30 rounded-lg p-4 space-y-3">
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">Creator</span>
        </div>
        <div className="space-y-1">
          <p className="font-medium">{creatorName}</p>
          <div className="flex items-center gap-2">
            <Wallet className="h-3 w-3 text-muted-foreground" />
            <p className="text-xs text-muted-foreground font-mono">{formattedWallet}</p>
          </div>
        </div>
      </div>

      {/* Currency Selection */}
      <div className="space-y-2">
        <Label htmlFor="currency">Select Currency</Label>
        <Select value={currency} onValueChange={setCurrency} disabled={isProcessing}>
          <SelectTrigger className="w-full">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ETH">
              <div className="flex items-center gap-2">
                <img src={ethereumIcon} alt="Ethereum" className="w-4 h-4" />
                <span>Ethereum (ETH)</span>
              </div>
            </SelectItem>
            <SelectItem value="SOL">
              <div className="flex items-center gap-2">
                <img src={solanaIcon} alt="Solana" className="w-4 h-4" />
                <span>Solana (SOL)</span>
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Amount Input */}
      <div className="space-y-2">
        <Label htmlFor="amount">Tip Amount ({currency})</Label>
        <div className="relative">
          <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            id="amount"
            type="text"
            placeholder="0.00"
            value={amount}
            onChange={handleAmountChange}
            className="pl-10"
            disabled={isProcessing}
          />
        </div>
        
        {/* Quick Amount Buttons */}
        <div className="grid grid-cols-4 gap-2 mt-3">
          {['0.1', '0.5', '1.0', '5.0'].map((preset) => (
            <Button
              key={preset}
              variant="outline"
              size="sm"
              onClick={() => setAmount(preset)}
              className={`text-xs ${amount === preset ? 'bg-primary text-primary-foreground' : ''}`}
              disabled={isProcessing}
            >
              {preset}
            </Button>
          ))}
        </div>
        
        <p className="text-xs text-muted-foreground">
          Choose a quick amount or enter your own
        </p>
      </div>

      {/* Send Button */}
      <Button
        onClick={handleSendTip}
        disabled={isProcessing || !amount || parseFloat(amount) <= 0}
        className="w-full"
        size="lg"
      >
        {isProcessing ? (
          'Processing...'
        ) : (
          <>
            <DollarSign className="mr-2 h-4 w-4" />
            Send {amount || '0'} {currency} Tip
          </>
        )}
      </Button>

      <p className="text-xs text-center text-muted-foreground">
        Tips are sent directly to the creator's wallet
      </p>
    </div>
  );

  if (isMobile) {
    return (
      <Drawer open={isOpen} onOpenChange={onClose}>
        <DrawerContent className="px-4 pb-6">
          <DrawerHeader>
            <DrawerTitle>Send Tip</DrawerTitle>
          </DrawerHeader>
          <ModalContent />
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <ModalContent />
      </DialogContent>
    </Dialog>
  );
}