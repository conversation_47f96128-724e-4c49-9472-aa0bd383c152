import React, { useEffect } from 'react';
import { ChainVoiceFeed } from '@/components/ChainVoiceFeed';
import { chainVoicePostService } from '@/services/chainVoicePostService';
import { useParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Info } from 'lucide-react';
import { Link } from 'react-router-dom';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ChainEvent } from '@/services/chainTypes';

const ChainVoiceMomentsPage: React.FC = () => {
  const { filter, value } = useParams<{ filter?: string; value?: string }>();

  // Start the chain voice post service when the page loads
  useEffect(() => {
    console.log('Starting chain voice post service');
    chainVoicePostService.start();

    return () => {
      // Don't actually stop the service when navigating away,
      // as it should continue running in the background
      // chainVoicePostService.stop();
    };
  }, []);

  // Function to manually trigger a sample event
  const triggerSampleEvent = () => {
    console.log('Manually triggering sample event');
    // Import the services dynamically to avoid circular dependencies
    import('../services/heliusService').then(({ heliusService }) => {
      // Create a sample event
      const sampleEvent = heliusService.createSampleDAOEvent();
      console.log('Created sample event:', sampleEvent);

      // Dispatch a custom event
      document.dispatchEvent(new CustomEvent('manualChainEvent', { detail: sampleEvent }));
      console.log('Dispatched manual chain event');
    });
  };

  // Determine the title based on the filter
  const getTitle = () => {
    if (!filter) return 'Voice Moments from the Chain';

    switch (filter) {
      case 'channel':
        return `#${value} Channel`;
      case 'tag':
        return `#${value} Tag`;
      case 'chain':
        return `${value?.charAt(0).toUpperCase()}${value?.slice(1)} Chain`;
      default:
        return 'Voice Moments from the Chain';
    }
  };

  // Determine the feed props based on the filter
  const getFeedProps = () => {
    if (!filter || !value) return {};

    switch (filter) {
      case 'channel':
        return { channel: value };
      case 'tag':
        return { tag: value };
      case 'chain':
        return { chain: value };
      default:
        return {};
    }
  };

  return (
    <div className="w-full flex flex-col h-full md:container md:mx-auto md:max-w-3xl">
      {/* Header */}
      <div className="sticky top-0 bg-background/95 backdrop-blur-md z-10 px-2 py-3 sm:p-4 border-b border-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {filter && (
              <Link to="/chain-voice">
                <Button variant="ghost" size="icon">
                  <ArrowLeft className="h-5 w-5" />
                </Button>
              </Link>
            )}
            <h1 className="text-lg sm:text-xl font-bold">{getTitle()}</h1>
          </div>

          <div className="flex items-center space-x-1 sm:space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={triggerSampleEvent}
              className="text-xs px-2 sm:px-3"
            >
              Generate Event
            </Button>
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Info className="h-4 w-4 sm:h-5 sm:w-5" />
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>About Voice Moments from the Chain</DialogTitle>
                  <DialogDescription>
                    Voice Moments from the Chain is a real-time voice layer for Web3 activity.
                    It converts on-chain events like DAO votes, funding rounds, and more
                    into auto-generated voice posts.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <p>
                    These voice updates are posted directly to relevant public channels,
                    transcribed for the feed, playable like any normal voice post,
                    and you can reply to them to start conversations.
                  </p>
                  <h3 className="font-semibold">Examples of Voice Moments:</h3>
                  <ul className="list-disc pl-5 space-y-2">
                    <li>"A new Solana grant just funded Zeta Markets with $250,000."</li>
                    <li>"DeveloperDAO has passed a proposal to fund 5 hackathons in LATAM."</li>
                    <li>"Wallet 0xABC just swept 20 Mad Lads—$90,000 in volume."</li>
                    <li>"Warning: a major rugpull just drained $4.1M from the XYZ protocol."</li>
                    <li>"LayerZero is now live on Solana. First bridge transactions are flowing."</li>
                  </ul>
                  <p className="font-medium text-center mt-6">
                    "Web3 is speaking—are you listening?"
                  </p>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <ChainVoiceFeed {...getFeedProps()} />
      </div>
    </div>
  );
};

export default ChainVoiceMomentsPage;
