import React, { useState } from 'react';
import { useChannels } from '@/contexts/ChannelContext';
import { Button } from '@/components/ui/button';
import { Hash, Lock, Settings, UserPlus, Info, Users, Menu } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import VoiceMessage from './VoiceMessage';
import AudioRecorder from './AudioRecorder';
import { MediaFile } from './MediaUploader';
import { formatDistanceToNow } from 'date-fns';

interface ChannelViewProps {
  userAddress: string;
  onChannelSettings: (channelId: string) => void;
  onChannelInvite: (channelId: string) => void;
  onReply: (messageId: string) => void;
  onToggleSidebar?: () => void;
}

const ChannelView: React.FC<ChannelViewProps> = ({
  userAddress,
  onChannelSettings,
  onChannelInvite,
  onReply,
  onToggleSidebar
}) => {
  const { activeChannel, isUserModerator, addMessageToChannel } = useChannels();
  const [showInfo, setShowInfo] = useState(false);

  if (!activeChannel) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-6 text-center">
        <Hash size={48} className="text-muted-foreground mb-4" />
        <h2 className="text-xl font-semibold mb-2">No Channel Selected</h2>
        <p className="text-muted-foreground max-w-md mb-4">
          Select a channel from the sidebar or create a new one to get started.
        </p>
        {onToggleSidebar && (
          <Button
            variant="default"
            onClick={onToggleSidebar}
            className="flex items-center gap-2 bg-voicechain-purple hover:bg-voicechain-accent"
          >
            <Menu size={16} />
            Show Channels
          </Button>
        )}
      </div>
    );
  }

  const canModify = isUserModerator(activeChannel.id, userAddress);

  const handleRecordingComplete = (audioBlob: Blob, transcript: string, duration?: number, media?: MediaFile[]) => {
    // Create object URL for the audio blob
    const audioUrl = URL.createObjectURL(audioBlob);

    // If duration is provided directly, use it
    if (duration && isFinite(duration)) {
      console.log(`Using provided duration: ${duration}s`);
      console.log(`Media files attached: ${media?.length || 0}`);

      // Add the message to the channel
      addMessageToChannel(activeChannel.id, {
        id: Date.now().toString(),
        audioUrl,
        transcript,
        userAddress,
        timestamp: new Date(),
        duration: duration,
        replies: [],
        media: media
      });
      return;
    }

    console.log('Duration not provided, calculating from audio element');

    // Get accurate audio duration
    const audio = new Audio(audioUrl);

    // Create a promise to get the duration
    const getDuration = () => {
      return new Promise<number>((resolve) => {
        audio.addEventListener('loadedmetadata', () => {
          if (audio.duration !== Infinity) {
            resolve(audio.duration);
          } else {
            // Fallback if duration is Infinity
            audio.currentTime = 24 * 60 * 60; // Seek to 24 hours
            audio.addEventListener('timeupdate', function getDurationFromTimeUpdate() {
              if (audio.currentTime > 0) {
                resolve(audio.duration);
                audio.removeEventListener('timeupdate', getDurationFromTimeUpdate);
              }
            });
          }
        });
      });
    };

    // Get the duration and then create the message
    getDuration().then(actualDuration => {
      console.log(`Calculated duration: ${actualDuration}s`);
      console.log(`Media files attached: ${media?.length || 0}`);

      // Add the message to the channel
      addMessageToChannel(activeChannel.id, {
        id: Date.now().toString(),
        audioUrl,
        transcript,
        userAddress,
        timestamp: new Date(),
        duration: actualDuration,
        replies: [],
        media: media
      });
    });
  };

  return (
    <div className="flex flex-col h-full">
      {/* Channel Header */}
      <div className="sticky top-0 bg-background z-10 border-b border-border/50">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-2">
            {onToggleSidebar && (
              <Button
                variant="ghost"
                size="icon"
                onClick={onToggleSidebar}
                className="md:hidden mr-1 hover:bg-secondary/80 active:bg-secondary"
              >
                <Menu size={18} />
              </Button>
            )}

            {activeChannel.isPrivate ? (
              <Lock size={18} className="text-muted-foreground" />
            ) : (
              <Hash size={18} className="text-muted-foreground" />
            )}
            <h2 className="text-lg font-semibold truncate">{activeChannel.name}</h2>

            {activeChannel.tags.length > 0 && (
              <div className="hidden md:flex items-center gap-1 ml-2 overflow-x-auto">
                {activeChannel.tags.map(tag => (
                  <Badge key={tag} variant="secondary" className="text-xs whitespace-nowrap">
                    {tag}
                  </Badge>
                ))}
              </div>
            )}
          </div>

          <div className="flex items-center gap-1 md:gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="text-xs flex items-center gap-1 px-2 md:px-3"
              onClick={() => setShowInfo(!showInfo)}
            >
              <Info size={14} />
              <span className="hidden sm:inline">Info</span>
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="text-xs flex items-center gap-1 px-2 md:px-3"
              onClick={() => onChannelInvite(activeChannel.id)}
            >
              <UserPlus size={14} />
              <span className="hidden sm:inline">Invite</span>
            </Button>

            {canModify && (
              <Button
                variant="ghost"
                size="sm"
                className="text-xs flex items-center gap-1 px-2 md:px-3"
                onClick={() => onChannelSettings(activeChannel.id)}
              >
                <Settings size={14} />
                <span className="hidden sm:inline">Settings</span>
              </Button>
            )}
          </div>
        </div>

        {showInfo && (
          <div className="px-4 pb-4">
            <p className="text-sm text-muted-foreground mb-2">{activeChannel.description}</p>

            {/* Mobile-only tags display */}
            {activeChannel.tags.length > 0 && (
              <div className="flex md:hidden flex-wrap items-center gap-1 mb-2">
                {activeChannel.tags.map(tag => (
                  <Badge key={tag} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            )}

            <div className="flex flex-wrap items-center gap-4 text-xs text-muted-foreground">
              <span className="flex items-center gap-1">
                <Users size={12} />
                {activeChannel.members.length} members
              </span>
              <span>
                Created {formatDistanceToNow(activeChannel.createdAt, { addSuffix: true })}
              </span>
            </div>

            {activeChannel.rules && (
              <div className="mt-2 p-2 bg-secondary/50 rounded-md text-xs">
                <p className="font-medium mb-1">Channel Rules:</p>
                <p className="whitespace-pre-wrap">{activeChannel.rules}</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {activeChannel.messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full p-6 text-center">
            <Hash size={32} className="text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-1">No messages yet</h3>
            <p className="text-muted-foreground max-w-md">
              Be the first to send a voice message in this channel!
            </p>
          </div>
        ) : (
          activeChannel.messages.map(message => (
            <VoiceMessage
              key={message.id}
              {...message}
              onReply={onReply}
            />
          ))
        )}
      </div>

      {/* Voice Recorder */}
      <div className="p-4 border-t border-border/50">
        <AudioRecorder
          onRecordingComplete={handleRecordingComplete}
          placeholder={`Record a voice message in #${activeChannel.name}`}
        />
      </div>
    </div>
  );
};

export default ChannelView;
