import React, { createContext, useContext, useState, useRef, useEffect } from 'react';

interface AudioContextType {
  isPlaying: boolean;
  currentAudioId: string | null;
  play: (url: string, id: string) => void;
  pause: () => void;
  stop: () => void;
}

const AudioContext = createContext<AudioContextType | undefined>(undefined);

export const useAudio = () => {
  const context = useContext(AudioContext);
  if (!context) {
    throw new Error('useAudio must be used within an AudioProvider');
  }
  return context;
};

export const AudioProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentAudioId, setCurrentAudioId] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, []);

  const play = (url: string, id: string) => {
    // Stop any currently playing audio
    if (audioRef.current) {
      audioRef.current.pause();
    }

    // Create a new audio element
    const audio = new Audio(url);
    
    // Set up event listeners
    audio.addEventListener('ended', () => {
      setIsPlaying(false);
      setCurrentAudioId(null);
    });
    
    audio.addEventListener('error', (e) => {
      console.error('Audio playback error:', e);
      setIsPlaying(false);
      setCurrentAudioId(null);
    });

    // Start playing
    audio.play().then(() => {
      setIsPlaying(true);
      setCurrentAudioId(id);
      audioRef.current = audio;
    }).catch(error => {
      console.error('Error playing audio:', error);
      setIsPlaying(false);
      setCurrentAudioId(null);
    });
  };

  const pause = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  };

  const stop = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setIsPlaying(false);
      setCurrentAudioId(null);
    }
  };

  return (
    <AudioContext.Provider value={{ isPlaying, currentAudioId, play, pause, stop }}>
      {children}
    </AudioContext.Provider>
  );
};
