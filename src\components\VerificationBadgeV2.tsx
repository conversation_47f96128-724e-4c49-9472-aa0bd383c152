import React from 'react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { Check } from 'lucide-react';

// Define verification types
export type VerificationType =
  | 'owner'        // Dark purple - App owner
  | 'creator'      // Teal - Content creators
  | 'developer'    // Blue - Developers
  | 'community'    // Green - Community leaders
  | 'partner'      // Orange - Partners
  | 'investor'     // Gold - Investors
  | 'early'        // Pink - Early adopters
  | 'artist'       // Purple/Pink - Visual artists
  | 'musician'     // Blue/Purple - Musicians
  | 'journalist'   // Blue/Teal - Journalists
  | 'educator'     // Green/Blue - Educators
  | 'nonprofit'    // Green/Yellow - Nonprofit organizations
  | 'government'   // Blue/Gray - Government entities
  | 'celebrity'    // Gold/Orange - Public figures
  | 'custom';      // Custom color

interface VerificationBadgeProps {
  type: VerificationType;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  customColor?: string;
  showTooltip?: boolean;
}

const VerificationBadgeV2: React.FC<VerificationBadgeProps> = ({
  type,
  size = 'md',
  className,
  customColor,
  showTooltip = true
}) => {
  // Define badge colors
  const badgeConfig = {
    owner: {
      color: 'bg-voicechain-purple', // Dark purple for owner
      description: 'Audra Owner'
    },
    creator: {
      color: 'bg-blue-500',
      description: 'Verified Creator'
    },
    developer: {
      color: 'bg-blue-500',
      description: 'Verified Developer'
    },
    community: {
      color: 'bg-blue-500',
      description: 'Community Leader'
    },
    partner: {
      color: 'bg-blue-500',
      description: 'Official Partner'
    },
    investor: {
      color: 'bg-blue-500',
      description: 'Verified Investor'
    },
    early: {
      color: 'bg-blue-500',
      description: 'Early Adopter'
    },
    artist: {
      color: 'bg-blue-500',
      description: 'Verified Artist'
    },
    musician: {
      color: 'bg-blue-500',
      description: 'Verified Musician'
    },
    journalist: {
      color: 'bg-blue-500',
      description: 'Verified Journalist'
    },
    educator: {
      color: 'bg-blue-500',
      description: 'Verified Educator'
    },
    nonprofit: {
      color: 'bg-blue-500',
      description: 'Verified Nonprofit'
    },
    government: {
      color: 'bg-blue-500',
      description: 'Government Entity'
    },
    celebrity: {
      color: 'bg-blue-500',
      description: 'Public Figure'
    },
    custom: {
      color: customColor || 'bg-blue-500',
      description: 'Verified'
    }
  };

  // Size classes - smaller sizes for a more subtle badge
  const sizeClasses = {
    sm: 'w-3 h-3',
    md: 'w-3.5 h-3.5',
    lg: 'w-4 h-4'
  };

  // Get config for the current type
  const config = badgeConfig[type];

  // Badge element - designed to look like Twitter/Instagram verification badges
  const badge = (
    <div className={cn(
      'relative flex items-center justify-center rounded-full',
      config.color,
      sizeClasses[size],
      'ring-[1px] ring-white/50', // Subtle white outline
      'shadow-sm', // Add subtle shadow
      className
    )}>
      <Check className="text-white z-10 w-[60%] h-[60%] stroke-[3px]" />
    </div>
  );

  // Return with or without tooltip
  if (showTooltip) {
    return (
      <TooltipProvider>
        <Tooltip delayDuration={300}>
          <TooltipTrigger asChild>
            {badge}
          </TooltipTrigger>
          <TooltipContent className="px-3 py-1.5 text-xs">
            {config.description}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return badge;
};

export default VerificationBadgeV2;
