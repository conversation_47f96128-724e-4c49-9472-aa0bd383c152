
import React from 'react';
import VoiceMessageWithoutChannel from './VoiceMessageWithoutChannel';
import { VoiceMessageProps } from '@/types/voice-message';
import voiceMessageService from '@/services/voiceMessageService.js';
import { deleteVoiceMessageOptimized, togglePinMessageOptimized } from '@/services/optimizedVoiceMessageService';
import { toast } from '@/components/ui/sonner';
import { useNavigate } from 'react-router-dom';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import { supabase } from '@/integrations/supabase/client';

interface VoiceMessageListProps {
  messages: VoiceMessageProps[];
  onReply?: (parentId: string) => void;
  connectedAccount: string;
}

const VoiceMessageListWithoutChannel: React.FC<VoiceMessageListProps> = ({
  messages,
  onReply,
  connectedAccount
}) => {
  const navigate = useNavigate();
  const { incrementPostCount } = useProfiles();

  // Make sure messages is always an array
  const safeMessages = Array.isArray(messages) ? messages : [];

  // Handle delete
  const handleDelete = async (id: string) => {
    try {
      console.log("Deleting message:", id);

      // Call the optimized service to delete the message
      const success = await deleteVoiceMessageOptimized(id, connectedAccount);

      if (success) {
        toast.success('Message deleted successfully');
        // Optionally trigger a refresh of the message list
        setTimeout(() => window.location.reload(), 1000);
      } else {
        toast.error('Failed to delete message. You may not have permission.');
      }
    } catch (error) {
      console.error('Error deleting message:', error);
      toast.error('Error deleting message');
    }
  };

  // Handle pin
  const handlePin = async (id: string, isPinned: boolean) => {
    try {
      console.log(`${isPinned ? 'Unpinning' : 'Pinning'} message:`, id);

      // Call the optimized service to pin/unpin the message
      const success = await togglePinMessageOptimized(id, connectedAccount, isPinned);

      if (success) {
        toast.success(isPinned ? 'Message unpinned successfully' : 'Message pinned successfully');
        // Refresh to show the updated pin status
        setTimeout(() => window.location.reload(), 1000);
      } else {
        toast.error('Failed to pin message. You may not have permission.');
      }
    } catch (error) {
      console.error('Error pinning message:', error);
      toast.error('Error pinning message');
    }
  };

  // Update post count in profile context if needed
  React.useEffect(() => {
    if (connectedAccount && safeMessages.length > 0) {
      // Filter user posts (case-insensitive comparison)
      const userPosts = safeMessages.filter(message => 
        message.userAddress && 
        message.userAddress.toLowerCase() === connectedAccount.toLowerCase()
      );
      
      if (userPosts.length > 0 && typeof incrementPostCount === 'function') {
        incrementPostCount(connectedAccount, userPosts.length);
      }
    }
  }, [connectedAccount, safeMessages, incrementPostCount]);

  if (!safeMessages.length) {
    return (
      <div className="flex flex-col items-center justify-center py-10">
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-2">No messages yet</h3>
          <p className="text-sm text-muted-foreground">
            Be the first to add a voice message!
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {safeMessages.map((message) => (
        <VoiceMessageWithoutChannel
          key={message.id}
          {...message}
          onReply={onReply}
          onDelete={handleDelete}
          onPin={handlePin}
        />
      ))}
    </div>
  );
};

export default VoiceMessageListWithoutChannel;
