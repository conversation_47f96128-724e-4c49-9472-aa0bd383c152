import { useState } from 'react';
import { Button } from './ui/button';
import { AtSign } from 'lucide-react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { useToast } from './ui/use-toast';
import { summonUser } from '@/services/summonService';
import { useAuth } from '@/contexts/AuthContext';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Label } from './ui/label';
import { useProfiles } from '@/contexts/ProfileContext';
import { useNotifications } from '@/contexts/NotificationContext';

interface SummonButtonProps {
  messageId?: string;
}

export function SummonButton({ messageId }: SummonButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();
  const { profiles } = useProfiles();
  const { addNotification } = useNotifications();

  const handleSummon = async () => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please sign in to summon a user",
      });
      return;
    }

    if (!selectedUser) {
      toast({
        title: "User required",
        description: "Please select a user to summon",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);

    try {
      const result = await summonUser(
        user.id,
        selectedUser,
        messageId || null,
        'voice_response'
      );

      if (result.success) {
        toast({
          title: "Summon sent",
          description: "The user has been summoned for a voice response",
        });

        // Create notification for summon
        addNotification(
          'summon',
          user.id,
          selectedUser,
          messageId,
          {
            summonType: 'voice_response',
            context: 'voice_message'
          }
        );

        setIsOpen(false);
      } else {
        throw new Error("Failed to summon user");
      }
    } catch (error) {
      console.error('Error summoning user:', error);
      toast({
        title: "Error",
        description: "Failed to summon user. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm" className="flex items-center gap-1">
          <AtSign className="h-4 w-4" />
          <span>Summon</span>
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Summon a user</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="user">Select User</Label>
            <Select onValueChange={setSelectedUser} value={selectedUser}>
              <SelectTrigger>
                <SelectValue placeholder="Select a user" />
              </SelectTrigger>
              <SelectContent>
                {profiles.map((profile) => (
                  <SelectItem key={profile.id} value={profile.id}>
                    {profile.displayName || profile.username || profile.address.substring(0, 8)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <Button 
            onClick={handleSummon} 
            disabled={isLoading} 
            className="w-full"
          >
            {isLoading ? "Processing..." : "Summon User"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}