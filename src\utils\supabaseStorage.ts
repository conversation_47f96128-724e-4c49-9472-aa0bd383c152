
import { supabase } from '@/integrations/supabase/client';

/**
 * Ensure required storage buckets exist
 */
export async function ensureStorageBuckets(): Promise<void> {
  try {
    // Get list of buckets
    const { data: buckets, error } = await supabase.storage.listBuckets();

    if (error) {
      console.error('Error listing buckets:', error);
      // Continue execution even if we can't list buckets
      // The app should still function with existing buckets
      console.log('Continuing without creating buckets...');
      return;
    }

    const requiredBuckets = [
      {
        name: 'audio',
        public: true,
        fileSizeLimit: 50000000, // 50MB
        allowedMimeTypes: ['audio/webm', 'audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/ogg']
      },
      {
        name: 'profiles',
        public: true,
        fileSizeLimit: 5000000, // 5MB
        allowedMimeTypes: ['image/png', 'image/jpeg', 'image/gif', 'image/webp']
      }
    ];

    // Create missing buckets
    for (const bucket of requiredBuckets) {
      // Check if bucket already exists
      if (!buckets?.find(b => b.name === bucket.name)) {
        console.log(`Checking bucket: ${bucket.name}...`);

        try {
          // Try to create the bucket, but don't fail if we can't
          const { error } = await supabase.storage.createBucket(bucket.name, {
            public: bucket.public,
            fileSizeLimit: bucket.fileSizeLimit,
            allowedMimeTypes: bucket.allowedMimeTypes
          });

          if (error) {
            // Log the error but continue execution
            console.warn(`Note: Could not create ${bucket.name} bucket. This is expected if you don't have admin privileges.`);
            console.warn(`The app will attempt to use existing buckets.`);
          } else {
            console.log(`${bucket.name} bucket created successfully`);
          }
        } catch (bucketError) {
          // Catch any exceptions during bucket creation
          console.warn(`Note: Could not create ${bucket.name} bucket. This is expected if you don't have admin privileges.`);
          console.warn(`The app will attempt to use existing buckets.`);
        }
      } else {
        console.log(`${bucket.name} bucket already exists`);
      }
    }
  } catch (error) {
    // Catch any exceptions but allow the app to continue
    console.error('Error ensuring storage buckets:', error);
    console.log('Continuing without creating buckets...');
  }
}

/**
 * Run this on app initialization
 */
export function initializeSupabaseStorage(): void {
  // Try to ensure buckets exist, but don't block app initialization if it fails
  ensureStorageBuckets().catch(error => {
    console.error('Error during storage initialization:', error);
    console.log('App will continue without initializing storage buckets.');
  });
}
