import React, { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Users, Shield, AlertTriangle, BarChart3, ArrowRight } from 'lucide-react';
import { supabase } from '@/services/supabase';

interface DashboardStats {
  totalUsers: number;
  pendingVerifications: number;
  reportedContent: number;
  activeUsers: number;
  newUsersToday: number;
  totalPosts: number;
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    pendingVerifications: 0,
    reportedContent: 0,
    activeUsers: 0,
    newUsersToday: 0,
    totalPosts: 0,
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setIsLoading(true);

        // Get total users
        const { count: totalUsers } = await supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true });

        // Get pending verifications
        const { count: pendingVerifications } = await supabase
          .from('verification_applications')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'pending');

        // Get reported content
        const { count: reportedContent } = await supabase
          .from('content_reports')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'pending');

        // Get active users (users who logged in within the last 7 days)
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

        const { count: activeUsers } = await supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true })
          .gt('last_login', sevenDaysAgo.toISOString());

        // Get new users today
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const { count: newUsersToday } = await supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true })
          .gt('created_at', today.toISOString());

        // Get total posts
        const { count: totalPosts } = await supabase
          .from('voice_messages')
          .select('*', { count: 'exact', head: true });

        setStats({
          totalUsers: totalUsers || 0,
          pendingVerifications: pendingVerifications || 0,
          reportedContent: reportedContent || 0,
          activeUsers: activeUsers || 0,
          newUsersToday: newUsersToday || 0,
          totalPosts: totalPosts || 0,
        });
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
  }, []);

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Users
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">
                {isLoading ? '...' : stats.totalUsers.toLocaleString()}
              </div>
              <Users className="h-5 w-5 text-muted-foreground" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              {isLoading ? '...' : `${stats.newUsersToday} new today`}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Pending Verifications
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">
                {isLoading ? '...' : stats.pendingVerifications}
              </div>
              <Shield className="h-5 w-5 text-muted-foreground" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Requires your attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Reported Content
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">
                {isLoading ? '...' : stats.reportedContent}
              </div>
              <AlertTriangle className="h-5 w-5 text-muted-foreground" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Awaiting moderation
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Active Users
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">
                {isLoading ? '...' : stats.activeUsers.toLocaleString()}
              </div>
              <BarChart3 className="h-5 w-5 text-muted-foreground" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              In the last 7 days
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="verifications">
        <TabsList className="grid grid-cols-3 mb-6">
          <TabsTrigger value="verifications">Verifications</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
          <TabsTrigger value="activity">Recent Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="verifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Pending Verification Requests</CardTitle>
              <CardDescription>
                Review and approve verification requests from users
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <p>Loading verification requests...</p>
              ) : stats.pendingVerifications === 0 ? (
                <p className="text-muted-foreground">No pending verification requests</p>
              ) : (
                <div className="space-y-2">
                  <p>There are {stats.pendingVerifications} pending verification requests that require your attention.</p>
                  <Button asChild>
                    <Link to="/verifications">
                      View All Requests
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Content Reports</CardTitle>
              <CardDescription>
                Review reported content that may violate community guidelines
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <p>Loading content reports...</p>
              ) : stats.reportedContent === 0 ? (
                <p className="text-muted-foreground">No pending content reports</p>
              ) : (
                <div className="space-y-2">
                  <p>There are {stats.reportedContent} content reports that require moderation.</p>
                  <Button asChild>
                    <Link to="/content-moderation">
                      View All Reports
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Overview of recent platform activity
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium">New Users Today</h3>
                  <p className="text-2xl font-bold">{isLoading ? '...' : stats.newUsersToday}</p>
                </div>

                <div>
                  <h3 className="font-medium">Total Posts</h3>
                  <p className="text-2xl font-bold">{isLoading ? '...' : stats.totalPosts.toLocaleString()}</p>
                </div>

                <Button asChild variant="outline">
                  <Link to="/analytics">
                    View Detailed Analytics
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Dashboard;
