
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { VoiceMessageProps } from '@/types/voice-message';
import VoiceMessageList from '@/components/VoiceMessageList';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import { useAuth } from '@/contexts/AuthContext';
import { StoriesRing } from '@/components/StoriesRing';
import { SpacesList } from '@/components/SpacesList';
import { SpaceViewer } from '@/components/SpaceViewer';
import RecordedSpacesList from '@/components/RecordedSpacesList';

interface IndexProps {
  connectedAccount: string;
  messages: VoiceMessageProps[];
  onReply: (parentId: string) => void;
}

const Index: React.FC<IndexProps> = ({ connectedAccount, messages, onReply }) => {
  const [activeTab, setActiveTab] = useState('all');
  const [userPosts, setUserPosts] = useState<VoiceMessageProps[]>([]);
  const [selectedSpaceId, setSelectedSpaceId] = useState<string | null>(null);
  const { profiles, getUserPosts } = useProfiles();
  const { user } = useAuth();

  // Make sure messages is always an array
  const safeMessages = Array.isArray(messages) ? messages : [];
  
  // Get the user profile from the profiles object
  const userProfile = connectedAccount ?
    profiles[connectedAccount.toLowerCase()] : undefined;

  // Effect to get user's posts
  useEffect(() => {
    if (connectedAccount) {
      console.log('Fetching posts for connected account:', connectedAccount);
      
      // Try to get user posts from local storage first
      const localStoragePosts = getUserPosts(connectedAccount);
      
      if (localStoragePosts && localStoragePosts.length > 0) {
        console.log(`Found ${localStoragePosts.length} posts for user ${connectedAccount} in localStorage`);
        
        // Process any posts with string timestamps
        const processedPosts = localStoragePosts.map(post => ({
          ...post,
          timestamp: post.timestamp instanceof Date ? post.timestamp : new Date(post.timestamp)
        }));
        
        setUserPosts(processedPosts);
      } else {
        // Fall back to filtering from the messages prop
        console.log('No posts found in localStorage, filtering from messages prop');
        const filteredPosts = safeMessages.filter(message => {
          // Case insensitive comparison of addresses
          return message.userAddress && 
                 message.userAddress.toLowerCase() === connectedAccount.toLowerCase();
        });
        
        console.log(`Found ${filteredPosts.length} posts in messages prop`);
        setUserPosts(filteredPosts);
        
        // Save these posts to localStorage for future use
        if (filteredPosts.length > 0) {
          try {
            // Get existing posts
            const allPosts = JSON.parse(localStorage.getItem('all_posts') || '[]');
            
            // Add the filtered posts if they don't already exist
            let hasNewPosts = false;
            const existingIds = new Set(allPosts.map((p: any) => p.id));
            
            filteredPosts.forEach(post => {
              if (!existingIds.has(post.id)) {
                const postToSave = { ...post };
                // Convert Date to string for localStorage
                if (postToSave.timestamp instanceof Date) {
                  postToSave.timestamp = postToSave.timestamp.toISOString();
                }
                allPosts.push(postToSave);
                hasNewPosts = true;
              }
            });
            
            if (hasNewPosts) {
              localStorage.setItem('all_posts', JSON.stringify(allPosts));
              console.log(`Updated localStorage with new posts`);
            }
          } catch (error) {
            console.error('Error saving posts to localStorage:', error);
          }
        }
      }
    }
  }, [connectedAccount, safeMessages, getUserPosts]);

  // Effect to update userPosts when new messages are added
  useEffect(() => {
    if (connectedAccount && safeMessages.length > 0) {
      // Filter messages to get user's posts
      const userMessages = safeMessages.filter(message => {
        return message.userAddress &&
               message.userAddress.toLowerCase() === connectedAccount.toLowerCase();
      });

      // Check if we have new user posts that aren't in userPosts state
      const currentUserPostIds = new Set(userPosts.map(post => post.id));
      const newUserPosts = userMessages.filter(message => !currentUserPostIds.has(message.id));

      if (newUserPosts.length > 0) {
        console.log(`Found ${newUserPosts.length} new user posts, updating userPosts state`);

        // Merge new posts with existing user posts, sort by timestamp (newest first)
        const updatedUserPosts = [...userPosts, ...newUserPosts]
          .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

        setUserPosts(updatedUserPosts);
      }
    }
  }, [connectedAccount, safeMessages, userPosts]);

  // Get the list of deleted posts from localStorage
  const getDeletedPosts = (): string[] => {
    try {
      const deletedPostsKey = 'deleted_posts';
      const storedDeletedPosts = localStorage.getItem(deletedPostsKey);

      if (storedDeletedPosts) {
        return JSON.parse(storedDeletedPosts);
      }
    } catch (error) {
      console.error('Error getting deleted posts from localStorage:', error);
    }

    return [];
  };

  const deletedPosts = getDeletedPosts();

  // Filter messages based on active tab and exclude deleted posts
  const getFilteredMessages = () => {
    console.log(`Filtering messages for tab: ${activeTab}`);
    const messages = activeTab === 'all' ? safeMessages : userPosts;
    
    return messages.filter(message => {
      // Skip deleted posts
      if (deletedPosts.includes(message.id)) {
        return false;
      }
      
      // Show all posts in the 'all' tab
      if (activeTab === 'all') return true;
      
      // In the 'mine' tab, only show the user's own posts (case insensitive comparison)
      if (activeTab === 'mine' && connectedAccount) {
        const isUserPost = message.userAddress && 
          message.userAddress.toLowerCase() === connectedAccount.toLowerCase();
        return isUserPost;
      }
      
      return false;
    });
  };

  const filteredMessages = getFilteredMessages();
  console.log(`Filtered messages for ${activeTab} tab:`, filteredMessages.length);

  return (
    <div className="w-full">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Home</h1>
      </div>

      {/* Stories Ring */}
      <div className="mb-6">
        <div className="bg-red-100 border border-red-300 p-4 rounded-lg mb-2">
          <p className="text-sm text-red-800">
            DEBUG: connectedAccount = "{connectedAccount || 'null/undefined'}"
          </p>
          <p className="text-sm text-red-800">
            StoriesRing should be visible below this message
          </p>
        </div>
        <div className="border-4 border-blue-500 p-4 rounded-lg">
          <h2 className="text-lg font-bold text-blue-600 mb-4">STORIES RING CONTAINER</h2>
          <StoriesRing currentUserId={connectedAccount || 'demo-user'} />
        </div>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="all">All Posts</TabsTrigger>
          <TabsTrigger value="spaces">Live Spaces</TabsTrigger>
          <TabsTrigger value="recorded">Recorded Spaces</TabsTrigger>
          <TabsTrigger value="mine">My Posts</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          {filteredMessages.length > 0 ? (
            <VoiceMessageList
              messages={filteredMessages}
              onReply={onReply}
              connectedAccount={connectedAccount}
            />
          ) : (
            <div className="text-center py-10">
              <p className="text-muted-foreground">No voice messages yet. Be the first to share!</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="spaces" className="space-y-4">
          <div className="bg-muted/50 p-4 rounded-lg mb-2">
            <p className="text-sm">Debug: connectedAccount = "{connectedAccount || 'null/undefined'}"</p>
          </div>
          <SpacesList
            currentUserId={connectedAccount || 'demo-user'}
            onJoinSpace={(spaceId) => setSelectedSpaceId(spaceId)}
          />
        </TabsContent>

        <TabsContent value="recorded" className="space-y-4">
          <RecordedSpacesList />
        </TabsContent>

        <TabsContent value="mine" className="space-y-4">
          {filteredMessages.length > 0 ? (
            <VoiceMessageList
              messages={filteredMessages}
              onReply={onReply}
              connectedAccount={connectedAccount}
            />
          ) : (
            <div className="text-center py-10">
              <p className="text-muted-foreground">You haven't posted any voice messages yet.</p>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Space Viewer Modal */}
      {selectedSpaceId && connectedAccount && (
        <>
          {console.log('🔍 Index: Opening SpaceViewer with:', { selectedSpaceId, connectedAccount })}
          <SpaceViewer
            spaceId={selectedSpaceId}
            isOpen={!!selectedSpaceId}
            onClose={() => setSelectedSpaceId(null)}
            currentUserId={connectedAccount}
          />
        </>
      )}
    </div>
  );
};

export default Index;
