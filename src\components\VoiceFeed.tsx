
import React, { useState } from 'react';
import VoiceMessage, { VoiceMessageProps } from './VoiceMessage';
import { Separator } from '@/components/ui/separator';
import { Volume2, ChevronDown } from 'lucide-react';
import { Button } from './ui/button';

interface VoiceFeedProps {
  messages: VoiceMessageProps[];
  onReply?: (parentId: string) => void;
}

const VoiceFeed: React.FC<VoiceFeedProps> = ({ messages, onReply }) => {
  const [activeChannel, setActiveChannel] = useState('all');

  const channels = [
    { id: 'all', name: 'All' },
    { id: 'solana', name: 'Sol<PERSON>' },
    { id: 'ethereum', name: 'Ethereum' },
    { id: 'builders', name: 'Builders' },
    { id: 'following', name: 'Following' },
  ];

  if (messages.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-10">
        <div className="w-16 h-16 bg-secondary rounded-full flex items-center justify-center mb-4">
          <Volume2 size={24} className="text-voicechain-purple" />
        </div>
        <h3 className="text-lg font-semibold">No messages yet</h3>
        <p className="text-sm text-muted-foreground">
          Be the first to add a voice message!
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-1">
      <div className="sticky top-[60px] bg-voicechain-dark/95 backdrop-blur-md py-2 z-10">
        {/* Channel Selector */}
        <Button
          variant="ghost"
          className="flex items-center justify-between w-full mb-2 border border-border/40 rounded-md bg-secondary/30"
        >
          <span className="font-medium">{channels.find(c => c.id === activeChannel)?.name || 'All'}</span>
          <ChevronDown size={16} />
        </Button>

        <h2 className="text-lg font-bold mb-1">Voice Feed</h2>
        <Separator className="bg-border/50" />
      </div>
      <div className="space-y-4 pt-2">
        {messages.map((message) => (
          <VoiceMessage key={message.id} {...message} onReply={onReply} />
        ))}
      </div>
    </div>
  );
};

export default VoiceFeed;
