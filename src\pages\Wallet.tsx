import React, { useState } from 'react';
import WalletDisplay from '@/components/WalletDisplay';
import WalletFunding from '@/components/WalletFunding';
import TokenManager from '@/components/TokenManager';
import WalletSecurity from '@/components/WalletSecurity';
import TransactionNotifications from '@/components/TransactionNotifications';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useWallet } from '@/contexts/WalletContext';
import {
  ArrowUpRight,
  ArrowDownLeft,
  Clock,
  AlertCircle,
  Wallet as WalletIcon,
  ArrowDownToLine,
  Coins,
  Shield,
  Bell
} from 'lucide-react';

const Wallet: React.FC = () => {
  const { wallet, transactions } = useWallet();
  const [activeTab, setActiveTab] = useState('overview');

  // Calculate statistics
  const stats = {
    ethSent: transactions
      .filter(tx => tx.from === wallet?.address && tx.status === 'completed' && tx.currency === 'ETH')
      .reduce((sum, tx) => sum + parseFloat(tx.amount), 0)
      .toFixed(4),
    ethReceived: transactions
      .filter(tx => tx.to === wallet?.address && tx.status === 'completed' && tx.currency === 'ETH')
      .reduce((sum, tx) => sum + parseFloat(tx.amount), 0)
      .toFixed(4),
    solSent: transactions
      .filter(tx => tx.from === wallet?.address && tx.status === 'completed' && tx.currency === 'SOL')
      .reduce((sum, tx) => sum + parseFloat(tx.amount), 0)
      .toFixed(4),
    solReceived: transactions
      .filter(tx => tx.to === wallet?.address && tx.status === 'completed' && tx.currency === 'SOL')
      .reduce((sum, tx) => sum + parseFloat(tx.amount), 0)
      .toFixed(4),
    tipsSent: transactions
      .filter(tx => tx.from === wallet?.address && tx.type === 'tip' && tx.status === 'completed')
      .length,
    tipsReceived: transactions
      .filter(tx => tx.to === wallet?.address && tx.type === 'tip' && tx.status === 'completed')
      .length
  };

  return (
    <div className="container mx-auto max-w-3xl p-4">
      <h1 className="text-2xl font-bold mb-6">Wallet</h1>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
        <TabsList className="grid grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center gap-1">
            <WalletIcon className="h-4 w-4" />
            <span className="hidden sm:inline">Overview</span>
          </TabsTrigger>
          <TabsTrigger value="fund" className="flex items-center gap-1">
            <ArrowDownToLine className="h-4 w-4" />
            <span className="hidden sm:inline">Fund</span>
          </TabsTrigger>
          <TabsTrigger value="tokens" className="flex items-center gap-1">
            <Coins className="h-4 w-4" />
            <span className="hidden sm:inline">Tokens</span>
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-1">
            <Shield className="h-4 w-4" />
            <span className="hidden sm:inline">Security</span>
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-1">
            <Bell className="h-4 w-4" />
            <span className="hidden sm:inline">Alerts</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6 space-y-6">
          {/* Wallet Display */}
          <WalletDisplay />

          {/* Statistics */}
          {wallet && (
            <Card>
              <CardHeader>
                <CardTitle>Statistics</CardTitle>
                <CardDescription>Your wallet activity</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">ETH Sent</p>
                    <p className="text-xl font-bold">{stats.ethSent} ETH</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">ETH Received</p>
                    <p className="text-xl font-bold">{stats.ethReceived} ETH</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">SOL Sent</p>
                    <p className="text-xl font-bold">{stats.solSent} SOL</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">SOL Received</p>
                    <p className="text-xl font-bold">{stats.solReceived} SOL</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Tips Sent</p>
                    <p className="text-xl font-bold">{stats.tipsSent}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Tips Received</p>
                    <p className="text-xl font-bold">{stats.tipsReceived}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Transaction Activity */}
          {wallet && transactions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Transaction Activity</CardTitle>
                <CardDescription>Recent transactions</CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="all">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="all">All</TabsTrigger>
                    <TabsTrigger value="sent">Sent</TabsTrigger>
                    <TabsTrigger value="received">Received</TabsTrigger>
                  </TabsList>

                  <TabsContent value="all" className="pt-4 space-y-4">
                    {transactions.slice(0, 10).map(tx => (
                      <TransactionItem key={tx.id} transaction={tx} walletAddress={wallet.address} />
                    ))}
                  </TabsContent>

                  <TabsContent value="sent" className="pt-4 space-y-4">
                    {transactions
                      .filter(tx => tx.from === wallet.address)
                      .slice(0, 10)
                      .map(tx => (
                        <TransactionItem key={tx.id} transaction={tx} walletAddress={wallet.address} />
                      ))}
                  </TabsContent>

                  <TabsContent value="received" className="pt-4 space-y-4">
                    {transactions
                      .filter(tx => tx.to === wallet.address)
                      .slice(0, 10)
                      .map(tx => (
                        <TransactionItem key={tx.id} transaction={tx} walletAddress={wallet.address} />
                      ))}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          )}

          {/* Help Card */}
          <Card>
            <CardHeader>
              <CardTitle>About Your Wallet</CardTitle>
              <CardDescription>Important information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start gap-3 p-3 bg-yellow-500/10 rounded-md border border-yellow-500/20">
                <AlertCircle className="h-5 w-5 text-yellow-500 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-yellow-500">Security Notice</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    This is an in-app wallet for tipping purposes only. For security reasons, avoid storing large amounts of ETH in this wallet.
                  </p>
                </div>
              </div>

              <p className="text-sm text-muted-foreground">
                Your wallet is automatically created when you register for an account. You can use it to send and receive tips within the platform.
              </p>

              <p className="text-sm text-muted-foreground">
                The wallet is built on the Base network, an Ethereum Layer 2 solution that provides fast and low-cost transactions.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="fund" className="mt-6">
          <WalletFunding />
        </TabsContent>

        <TabsContent value="tokens" className="mt-6">
          <TokenManager />
        </TabsContent>

        <TabsContent value="security" className="mt-6">
          <WalletSecurity />
        </TabsContent>

        <TabsContent value="notifications" className="mt-6">
          <TransactionNotifications />
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Transaction Item Component
interface TransactionItemProps {
  transaction: any;
  walletAddress: string;
}

const TransactionItem: React.FC<TransactionItemProps> = ({ transaction, walletAddress }) => {
  const isSent = transaction.from === walletAddress;

  // Get transaction icon
  const getIcon = () => {
    if (transaction.status === 'pending') return <Clock className="h-5 w-5 text-yellow-500" />;
    if (transaction.status === 'failed') return <AlertCircle className="h-5 w-5 text-red-500" />;

    return isSent ?
      <ArrowUpRight className="h-5 w-5 text-red-500" /> :
      <ArrowDownLeft className="h-5 w-5 text-green-500" />;
  };

  // Format date
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleString();
  };

  return (
    <div className="flex items-start gap-3 p-3 bg-secondary/50 rounded-md">
      <div className="mt-1">{getIcon()}</div>
      <div className="flex-1">
        <div className="flex items-start justify-between">
          <div>
            <p className="text-sm font-medium">
              {isSent ? 'Sent to ' : 'Received from '}
              {isSent ? transaction.to.substring(0, 6) + '...' + transaction.to.substring(transaction.to.length - 4) :
                transaction.from.substring(0, 6) + '...' + transaction.from.substring(transaction.from.length - 4)}
            </p>
            <p className="text-xs text-muted-foreground">{formatDate(transaction.timestamp)}</p>
            {transaction.message && (
              <p className="text-xs mt-1 bg-secondary p-2 rounded-md">{transaction.message}</p>
            )}
          </div>
          <p className={`text-sm font-medium ${isSent ? 'text-red-500' : 'text-green-500'}`}>
            {isSent ? '-' : '+'}{transaction.amount} ETH
          </p>
        </div>
        <div className="flex items-center gap-2 mt-2">
          <p className={`text-xs px-2 py-0.5 rounded-full ${transaction.status === 'completed' ? 'bg-green-500/10 text-green-500' :
            transaction.status === 'pending' ? 'bg-yellow-500/10 text-yellow-500' :
              'bg-red-500/10 text-red-500'
            }`}>
            {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
          </p>
          <p className="text-xs text-muted-foreground">
            {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
          </p>
        </div>
      </div>
    </div>
  );
};

export default Wallet;
