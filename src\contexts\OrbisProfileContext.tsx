
// This file is deprecated and will be removed in a future version
// Use SimpleProfileContext.tsx instead

import React, { createContext, useState, useEffect } from 'react';
import { UserProfile } from '@/types/user-profile';

interface OrbisProfileContextType {
  profile: UserProfile | null;
  isLoading: boolean;
}

const OrbisProfileContext = createContext<OrbisProfileContextType>({
  profile: null,
  isLoading: false,
});

export const OrbisProfileProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // This provider is deprecated and is kept only for backward compatibility
  // All functionality has been moved to SimpleProfileContext

  return (
    <OrbisProfileContext.Provider
      value={{
        profile,
        isLoading,
      }}
    >
      {children}
    </OrbisProfileContext.Provider>
  );
};

export const useOrbisProfile = () => {
  console.warn('useOrbisProfile is deprecated, use useProfiles from SimpleProfileContext instead');
  return React.useContext(OrbisProfileContext);
};
