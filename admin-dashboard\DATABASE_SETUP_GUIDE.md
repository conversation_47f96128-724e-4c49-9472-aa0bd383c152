# Database Setup Guide for Audra Admin Dashboard

This guide will help you set up the necessary database tables, functions, and policies for the Audra Admin Dashboard.

## Overview of Issues

The errors you're seeing are related to:

1. **Missing Database Tables**: Some required tables don't exist in your Supabase database
2. **RLS Policy Recursion**: Infinite recursion in Row Level Security policies
3. **Missing Functions**: Required database functions are not defined
4. **Missing Columns**: Some tables are missing required columns

## Step 1: Run the Complete Database Setup Script

We've created a comprehensive setup script that will fix all these issues. Follow these steps:

1. Open the Supabase SQL Editor for your project
2. Copy the contents of the `complete_database_setup.sql` file
3. Paste it into the SQL Editor
4. Run the script

This script will:
- Create all necessary tables if they don't exist
- Fix RLS policies to prevent recursion
- Create required functions
- Add missing columns

## Step 2: Create Your First Super Admin

After running the setup script, you need to create your first super admin:

```sql
SELECT create_first_super_admin('your-user-id', 'your-email', 'Your Name');
```

Replace:
- `your-user-id` with your Supabase user ID (UUID from auth.users)
- `your-email` with your email address
- `Your Name` with your display name

To find your user ID:

1. Go to the Supabase Dashboard
2. Navigate to Authentication > Users
3. Find your user and copy the ID

## Step 3: Verify the Setup

After completing steps 1 and 2, refresh your admin dashboard. The errors should be resolved, and you should be able to:

- Access the admin dashboard
- View and manage admin profiles
- Use the audit logs feature
- Set up two-factor authentication

## Tables Created/Modified

The setup script creates or modifies the following tables:

1. **admin_profiles**: Stores admin user information
2. **admin_settings**: Stores global admin settings
3. **admin_mfa**: Stores two-factor authentication data
4. **verification**: Stores user verification data
5. **content_reports**: Stores content moderation reports
6. **audit_logs**: Stores admin action logs
7. **profiles**: Adds last_login column if missing

## Functions Created

The script creates several helper functions:

1. **create_first_super_admin**: Creates the first super admin
2. **is_admin**: Checks if a user is an admin
3. **is_super_admin**: Checks if a user is a super admin
4. **log_admin_action**: Logs admin actions
5. **get_audit_logs**: Retrieves audit logs with admin details
6. **generate_totp_secret**: Generates a TOTP secret for 2FA
7. **verify_totp_code**: Verifies a TOTP code
8. **regenerate_backup_codes**: Regenerates backup codes for 2FA
9. **disable_totp**: Disables 2FA for an admin

## RLS Policies

The script sets up proper Row Level Security (RLS) policies for all tables:

1. **admin_profiles**: Allows admins to view all profiles, but only super admins can create/delete
2. **admin_settings**: Allows admins to view settings, but only super admins can update
3. **content_reports**: Allows admins to view and update reports
4. **verification**: Allows admins to manage verification data
5. **audit_logs**: Allows admins to view logs, but only super admins can delete

## Troubleshooting

If you still encounter issues after running the setup script:

1. **Check for errors in the SQL console**: Look for any error messages when running the script
2. **Verify user permissions**: Make sure your user has the necessary permissions
3. **Check RLS policies**: Make sure RLS policies are properly applied
4. **Restart the application**: Sometimes a refresh is needed after database changes

## Next Steps

After setting up the database:

1. **Set up Two-Factor Authentication**: Navigate to the 2FA page to secure your admin account
2. **Configure Admin Settings**: Set up global settings for the admin dashboard
3. **Create Additional Admins**: Create more admin accounts as needed
4. **Review Audit Logs**: Check the audit logs to ensure everything is working

## Support

If you encounter any issues, please contact support with:
1. The specific error message you're seeing
2. The steps you've taken to resolve the issue
3. Any relevant logs or screenshots

---

This guide was created to help you set up the Audra Admin Dashboard database. If you have any questions or need further assistance, please don't hesitate to reach out.
