import { supabase } from '@/integrations/supabase/client';

export interface Reaction {
  id: string;
  messageId: string;
  userId: string;
  emoji: string;
  createdAt: string;
}

/**
 * Toggle a reaction on a message
 */
export async function toggleReaction(
  messageId: string,
  userId: string,
  emoji: string,
  messageOwnerId?: string
): Promise<{ success: boolean; action: 'added' | 'removed' }> {
  try {
    // Check if user already has this reaction
    const { data: existingReaction, error: fetchError } = await supabase
      .from('voice_reactions')
      .select('id')
      .eq('voice_message_id', messageId)
      .eq('profile_id', userId)
      .eq('emoji', emoji)
      .maybeSingle();

    if (fetchError) {
      console.error('Error checking existing reaction:', fetchError);
      return { success: false, action: 'removed' };
    }

    if (existingReaction) {
      // Remove existing reaction
      const { error: deleteError } = await supabase
        .from('voice_reactions')
        .delete()
        .eq('id', existingReaction.id);

      // Update reaction count for message owner after removal
      if (!deleteError && messageOwnerId) {
        try {
          const { syncUserReactionCount } = await import('@/services/reactionCountService');
          await syncUserReactionCount(messageOwnerId);
        } catch (error) {
          console.error('Error syncing reaction count after removal:', error);
        }
      }

      return { 
        success: !deleteError, 
        action: 'removed' 
      };
    } else {
      // Add new reaction
      const { error: insertError } = await supabase
        .from('voice_reactions')
        .insert({
          voice_message_id: messageId,
          profile_id: userId,
          emoji
        });

      if (!insertError) {
        // Update reaction count for message owner
        if (messageOwnerId) {
          try {
            const { syncUserReactionCount } = await import('@/services/reactionCountService');
            await syncUserReactionCount(messageOwnerId);
          } catch (error) {
            console.error('Error syncing reaction count:', error);
          }
        }

        // Create notification for the message owner (only if different user)
        if (messageOwnerId && messageOwnerId !== userId) {
          try {
            await supabase
              .from('notifications')
              .insert({
                type: 'reaction',
                from_address: userId,
                to_address: messageOwnerId,
                message_id: messageId,
                data: { emoji }
              });
          } catch (notificationError) {
            console.error('Error creating reaction notification:', notificationError);
          }
        }
      }

      return { 
        success: !insertError, 
        action: 'added' 
      };
    }
  } catch (error) {
    console.error('Error in toggleReaction:', error);
    return { success: false, action: 'removed' };
  }
}

/**
 * Get all reactions for a message
 */
export async function getReactions(messageId: string): Promise<Reaction[]> {
  try {
    const { data, error } = await supabase
      .from('voice_reactions')
      .select('*')
      .eq('voice_message_id', messageId)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Error fetching reactions:', error);
      return [];
    }

    return (data || []).map(row => ({
      id: row.id,
      messageId: row.voice_message_id,
      userId: row.profile_id,
      emoji: row.emoji,
      createdAt: row.created_at
    }));
  } catch (error) {
    console.error('Error in getReactions:', error);
    return [];
  }
}

/**
 * Get user's reaction for a specific message
 */
export async function getUserReaction(messageId: string, userId: string): Promise<string | null> {
  try {
    const { data, error } = await supabase
      .from('voice_reactions')
      .select('emoji')
      .eq('voice_message_id', messageId)
      .eq('profile_id', userId)
      .maybeSingle();

    if (error) {
      console.error('Error fetching user reaction:', error);
      return null;
    }

    return data?.emoji || null;
  } catch (error) {
    console.error('Error in getUserReaction:', error);
    return null;
  }
}