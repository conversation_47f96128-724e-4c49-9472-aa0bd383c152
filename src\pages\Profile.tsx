
import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Calendar } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Edit, Verified } from 'lucide-react';
import { Skeleton } from "@/components/ui/skeleton"
import { toast } from '@/components/ui/sonner';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import { UserProfile, UserProfileUpdate } from '@/types/user-profile';
import SimpleProfileEditModal from '@/components/SimpleProfileEditModal';
import { getCurrentUserAccount } from '@/utils/account';
import { forceSyncProfileFromSupabase } from '@/utils/profileSyncUtils';
import { supabase } from '@/integrations/supabase/client';
import VoiceMessageList from '@/components/VoiceMessageList';
import { VoiceMessageProps } from '@/types/voice-message';
import { getUserReactionCount } from '@/services/reactionCountService';

const ProfilePage: React.FC = () => {
  const { address } = useParams<{ address: string }>();
  const { getProfileByAddress, updateProfile, refreshProfile } = useProfiles();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [userPosts, setUserPosts] = useState<VoiceMessageProps[]>([]);
  const [postsLoading, setPostsLoading] = useState(false);
  const [realTimeStats, setRealTimeStats] = useState<{posts: number; reactions: number} | null>(null);

  // Effect to load profile data
  useEffect(() => {
    const loadProfile = async () => {
      if (!address) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      try {
        console.log(`Loading profile for address: ${address}`);
        
        // First try to force sync from Supabase
        try {
          const syncedProfile = await forceSyncProfileFromSupabase(address);
          if (syncedProfile) {
            console.log("Successfully synced profile from Supabase:", syncedProfile);
            setProfile(syncedProfile);
            setIsLoading(false);
            return;
          }
        } catch (syncError) {
          console.warn("Error syncing from Supabase, falling back to context:", syncError);
        }
        
        // Then try to get from context
        let userProfile = getProfileByAddress(address);
        
        if (userProfile) {
          console.log("Profile loaded from context:", userProfile);
          setProfile(userProfile);
          setIsLoading(false);
          return;
        }
        
        // If not in context, try to fetch from service
        try {
          // Import dynamically to avoid circular dependencies
          const simpleProfileService = await import('@/services/simpleProfileService');
          userProfile = await simpleProfileService.default.getProfileByAddress(address);
          
          if (userProfile) {
            console.log("Profile loaded from service:", userProfile);
            setProfile(userProfile);
            setIsLoading(false);
            return;
          }
        } catch (serviceError) {
          console.error("Error loading profile from service:", serviceError);
        }
        
        console.error("Profile not found for address:", address);
        toast.error("Profile not found");
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching profile:", error);
        toast.error("Failed to load profile");
        setIsLoading(false);
      }
    };

    loadProfile();
  }, [address, getProfileByAddress]);

  // Optimized function to fetch user posts and real-time stats
  const fetchUserPostsAndStats = async () => {
      if (!address) return;

      setPostsLoading(true);
      try {
        console.log('Fetching posts for address:', address);

        // First get the profile ID for better query performance
        let profileId = address;
        
        // Check if address is not already a UUID (profile ID)
        if (!address.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
          const { data: profileData } = await supabase
            .from('profiles')
            .select('id')
            .ilike('wallet_address', address)
            .single();
          
          if (profileData) {
            profileId = profileData.id;
          }
        }

        // Now fetch voice messages using the profile ID
        const { data: voiceMessages, error: messagesError, count } = await supabase
          .from('voice_messages')
          .select('*', { count: 'exact' })
          .eq('profile_id', profileId)
          .is('deleted_at', null)
          .order('created_at', { ascending: false })
          .limit(20); // Reduced limit for faster loading

        if (messagesError) {
          console.error('Error fetching user voice messages:', messagesError);
          setPostsLoading(false);
          return;
        }

        console.log(`Found ${count || 0} posts for user ${address}`);

        // Get reaction count for this user
        const reactionCount = await getUserReactionCount(address);

        // Update real-time stats
        setRealTimeStats({ posts: count || 0, reactions: reactionCount });

        if (voiceMessages && voiceMessages.length > 0) {
          // Convert to VoiceMessageProps format
          const formattedPosts = voiceMessages.map(message => ({
            id: message.id,
            userAddress: address,
            audioUrl: message.audio_url,
            transcript: message.transcript || '',
            timestamp: new Date(message.created_at),
            duration: message.audio_duration,
            isPinned: message.is_pinned,
            media: [],
            replies: []
          }));

          setUserPosts(formattedPosts);
        } else {
          setUserPosts([]);
        }
      } catch (error) {
        console.error('Error fetching user posts and stats:', error);
      } finally {
        setPostsLoading(false);
      }
    };

  // Effect to fetch user posts and real-time stats
  useEffect(() => {
    fetchUserPostsAndStats();
  }, [address]);

  const handleSaveProfile = async (update: UserProfileUpdate) => {
    if (!profile || !address) return;

    try {
      console.log("Saving profile with updates:", update);
      const updatedProfile = await updateProfile(profile.address, update);
      
      if (updatedProfile) {
        console.log("Profile updated successfully:", updatedProfile);
        setProfile(updatedProfile);
        toast.success("Profile updated successfully!");
        setIsEditModalOpen(false);
        
        // Refresh the profile to make sure we have the latest data
        setTimeout(async () => {
          const refreshed = await refreshProfile(address);
          if (refreshed) {
            setProfile(refreshed);
          }
          // Also refresh the posts and stats
          fetchUserPostsAndStats();
        }, 500);
      } else {
        throw new Error("Failed to update profile");
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to update profile");
    }
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="container mx-auto mt-8 p-4">
        <Card className="w-full max-w-3xl mx-auto">
          <CardContent className="p-8">
            <div className="flex items-center space-x-4 mb-6">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-[250px]" />
                <Skeleton className="h-4 w-[200px]" />
              </div>
            </div>
            <div className="space-y-4">
              <Skeleton className="h-4 w-[300px]" />
              <Skeleton className="h-4 w-[400px]" />
              <Skeleton className="h-4 w-[200px]" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render not found state
  if (!profile) {
    return (
      <div className="container mx-auto mt-8 p-4">
        <Card className="w-full max-w-3xl mx-auto">
          <CardContent className="p-8">
            <p>Profile not found</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Determine if edit button should be shown (only show for your own profile)
  const currentUserAccount = getCurrentUserAccount();
  const isOwnProfile = address && currentUserAccount && address.toLowerCase() === currentUserAccount.toLowerCase();

  return (
    <div className="container mx-auto mt-8 p-4">
      <Card className="w-full max-w-3xl mx-auto">
        <CardContent className="p-8">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <Avatar>
                <AvatarImage src={profile.profileImageUrl} alt={profile.displayName} />
                <AvatarFallback>{profile.displayName?.charAt(0)}</AvatarFallback>
              </Avatar>
              <div className="space-y-1">
                <div className="flex items-center space-x-2">
                  <h2 className="text-2xl font-semibold">{profile.displayName}</h2>
                  {profile.verification?.isVerified && (
                    <Verified className="w-5 h-5 text-blue-500" />
                  )}
                </div>
                <p className="text-gray-500">@{profile.username}</p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex mt-4 md:mt-0 space-x-2">
              {/* Only show edit button if this is the user's own profile */}
              {isOwnProfile && (
                <Button
                  onClick={() => setIsEditModalOpen(true)}
                  variant="outline"
                  className="flex items-center gap-1"
                >
                  <Edit size={16} />
                  Edit Profile
                </Button>
              )}
            </div>
          </div>

          <div className="mt-6">
            <h3 className="text-xl font-semibold mb-2">About</h3>
            <p>{profile.bio || 'No bio available.'}</p>
          </div>

          <div className="mt-6">
            <h3 className="text-xl font-semibold mb-2">Social Links</h3>
            <ul className="space-y-2">
              <li>
                <strong>Twitter:</strong>{' '}
                {profile.socialLinks?.twitter ? (
                  <a
                    href={`https://twitter.com/${profile.socialLinks.twitter}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:underline"
                  >
                    @{profile.socialLinks.twitter}
                  </a>
                ) : (
                  'Not available'
                )}
              </li>
              <li>
                <strong>GitHub:</strong>{' '}
                {profile.socialLinks?.github ? (
                  <a
                    href={`https://github.com/${profile.socialLinks.github}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:underline"
                  >
                    {profile.socialLinks.github}
                  </a>
                ) : (
                  'Not available'
                )}
              </li>
              <li>
                <strong>Website:</strong>{' '}
                {profile.socialLinks?.website ? (
                  <a
                    href={profile.socialLinks.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:underline"
                  >
                    {profile.socialLinks.website}
                  </a>
                ) : (
                  'Not available'
                )}
              </li>
            </ul>
          </div>

          <div className="mt-6">
            <h3 className="text-xl font-semibold mb-2">Stats</h3>
            <ul className="space-y-2">
              <li>
                <strong>Posts:</strong> {realTimeStats?.posts ?? profile.stats?.posts ?? 0}
              </li>
              <li>
                <strong>Reactions:</strong> {realTimeStats?.reactions ?? profile.stats?.likes ?? 0}
              </li>
              <li>
                <strong>Tips:</strong> {profile.stats?.tips || 0}
              </li>
              <li>
                <strong>Followers:</strong> {profile.stats?.followers || 0}
              </li>
              <li>
                <strong>Following:</strong> {profile.stats?.following || 0}
              </li>
            </ul>
          </div>

          <div className="mt-6 flex items-center space-x-2 text-gray-500">
            <Calendar className="w-4 h-4" />
            <span>Joined {profile.joinedDate ? new Date(profile.joinedDate).toLocaleDateString() : 'Unknown date'}</span>
          </div>
        </CardContent>
      </Card>

      {/* User Posts Section */}
      <Card className="w-full max-w-3xl mx-auto mt-6">
        <CardContent className="p-8">
          <h3 className="text-xl font-semibold mb-4">Posts</h3>
          {postsLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-20 w-full" />
            </div>
          ) : userPosts.length > 0 ? (
            <VoiceMessageList
              messages={userPosts}
              onReply={() => {}} // Profile page doesn't need reply functionality
              connectedAccount={getCurrentUserAccount()}
            />
          ) : (
            <div className="text-center py-10">
              <p className="text-muted-foreground">
                {isOwnProfile ? "You haven't posted any voice messages yet." : "This user hasn't posted any voice messages yet."}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      <SimpleProfileEditModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        profile={profile}
        onSave={handleSaveProfile}
      />
    </div>
  );
};

export default ProfilePage;
