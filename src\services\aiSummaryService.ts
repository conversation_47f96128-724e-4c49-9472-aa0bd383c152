
// This service handles AI-powered summaries for voice messages

// Interface for summary response
export interface SummaryResponse {
  summary: string;
  topics: string[];
  sentiment: 'positive' | 'neutral' | 'negative';
  keyPoints: string[];
  tldr: string;
}

/**
 * Generate a summary for a voice transcript
 * @param transcript The transcript text to summarize
 * @returns A promise that resolves to a SummaryResponse
 */
export const generateSummary = async (transcript: string): Promise<SummaryResponse> => {
  // For short transcripts, return a simple response
  if (transcript.length < 50) {
    return {
      summary: transcript,
      topics: [],
      sentiment: 'neutral',
      keyPoints: [transcript],
      tldr: transcript
    };
  }
  
  try {
    // In a real implementation, this would call an AI service API
    // For now, we'll simulate the API call with a timeout
    
    // For demo purposes, we'll generate a simple summary based on the transcript
    return await simulateAISummary(transcript);
  } catch (error) {
    console.error('Error generating summary:', error);
    throw new Error('Failed to generate summary');
  }
};

/**
 * Extract key topics from a transcript
 * @param transcript The transcript to analyze
 * @returns An array of topic strings
 */
export const extractTopics = async (transcript: string): Promise<string[]> => {
  // For short transcripts, return empty topics
  if (transcript.length < 50) {
    return [];
  }
  
  try {
    // In a real implementation, this would call an AI service API
    // For now, we'll simulate the API call
    
    // Simple topic extraction based on word frequency
    const words = transcript.toLowerCase().split(/\s+/);
    const stopWords = ['the', 'and', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'but', 'or', 'as', 'if', 'while', 'because', 'not', 'no', 'yes', 'this', 'that', 'these', 'those', 'it', 'they', 'them', 'their', 'i', 'me', 'my', 'mine', 'you', 'your', 'yours', 'he', 'him', 'his', 'she', 'her', 'hers', 'we', 'us', 'our', 'ours'];
    
    // Count word frequency
    const wordCounts: Record<string, number> = {};
    words.forEach(word => {
      if (word.length > 3 && !stopWords.includes(word)) {
        wordCounts[word] = (wordCounts[word] || 0) + 1;
      }
    });
    
    // Sort by frequency
    const sortedWords = Object.entries(wordCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(entry => entry[0]);
    
    return sortedWords;
  } catch (error) {
    console.error('Error extracting topics:', error);
    return [];
  }
};

/**
 * Generate hashtags from a transcript
 * @param transcript The transcript to analyze
 * @returns An array of hashtag strings
 */
export const generateHashtags = async (transcript: string): Promise<string[]> => {
  try {
    // Get topics first
    const topics = await extractTopics(transcript);
    
    // Convert topics to hashtags
    return topics.map(topic => `#${topic}`);
  } catch (error) {
    console.error('Error generating hashtags:', error);
    return [];
  }
};

/**
 * Simulate an AI summary generation
 * @param transcript The transcript to summarize
 * @returns A simulated AI summary response
 */
const simulateAISummary = (transcript: string): Promise<SummaryResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // Simple sentiment analysis
      const positiveWords = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'awesome', 'love', 'happy', 'excited', 'positive', 'success', 'successful', 'win', 'winning'];
      const negativeWords = ['bad', 'terrible', 'awful', 'horrible', 'poor', 'negative', 'sad', 'angry', 'upset', 'disappointed', 'fail', 'failure', 'lose', 'losing', 'problem', 'issue'];
      
      let positiveCount = 0;
      let negativeCount = 0;
      
      const words = transcript.toLowerCase().split(/\s+/);
      words.forEach(word => {
        if (positiveWords.includes(word)) positiveCount++;
        if (negativeWords.includes(word)) negativeCount++;
      });
      
      let sentiment: 'positive' | 'neutral' | 'negative' = 'neutral';
      if (positiveCount > negativeCount + 2) sentiment = 'positive';
      if (negativeCount > positiveCount + 2) sentiment = 'negative';
      
      // Generate key points (simple sentences from the transcript)
      const sentences = transcript.split(/[.!?]+/).filter(s => s.trim().length > 0);
      const keyPoints = sentences.length <= 3 
        ? sentences 
        : [sentences[0], sentences[Math.floor(sentences.length / 2)], sentences[sentences.length - 1]];
      
      // Generate TLDR (first 20% of the transcript)
      const tldr = transcript.length > 100 
        ? transcript.substring(0, Math.floor(transcript.length * 0.2)) + '...'
        : transcript;
      
      // Generate summary
      const summary = transcript.length > 200 
        ? transcript.substring(0, Math.floor(transcript.length * 0.6)) + '...'
        : transcript;
      
      // Extract topics
      const topics = extractTopicsSync(transcript);
      
      resolve({
        summary,
        topics,
        sentiment,
        keyPoints: keyPoints.map(p => p.trim()),
        tldr
      });
    }, 1000); // Simulate a 1-second API call
  });
};

/**
 * Synchronous version of topic extraction for the simulator
 */
const extractTopicsSync = (transcript: string): string[] => {
  const words = transcript.toLowerCase().split(/\s+/);
  const stopWords = ['the', 'and', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'but', 'or', 'as', 'if', 'while', 'because', 'not', 'no', 'yes', 'this', 'that', 'these', 'those', 'it', 'they', 'them', 'their', 'i', 'me', 'my', 'mine', 'you', 'your', 'yours', 'he', 'him', 'his', 'she', 'her', 'hers', 'we', 'us', 'our', 'ours'];
  
  // Count word frequency
  const wordCounts: Record<string, number> = {};
  words.forEach(word => {
    if (word.length > 3 && !stopWords.includes(word)) {
      wordCounts[word] = (wordCounts[word] || 0) + 1;
    }
  });
  
  // Sort by frequency
  const sortedWords = Object.entries(wordCounts)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5)
    .map(entry => entry[0]);
  
  return sortedWords;
};

export default {
  generateSummary,
  extractTopics,
  generateHashtags
};
