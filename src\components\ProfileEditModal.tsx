import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>T<PERSON>le, <PERSON><PERSON>Header, DialogFooter } from '@/components/ui/dialog';
import { Drawer, DrawerContent, DrawerHeader, DrawerFooter } from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { X, Upload, Twitter, Globe, Github } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { toast } from '@/components/ui/sonner';
import { UserProfile, UserProfileUpdate } from '@/types/user-profile';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import CustomAvatarImage from '@/components/CustomAvatarImage';
import ProfileCoverImage from '@/components/ProfileCoverImage';
import { supabase } from '@/integrations/supabase/client';
import { forceSyncProfileToSupabase } from '@/utils/profileSyncUtils';

interface ProfileEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  profile: UserProfile;
  onSave: (update: UserProfileUpdate) => void;
}

const ProfileEditModal: React.FC<ProfileEditModalProps> = ({
  isOpen,
  onClose,
  profile,
  onSave
}) => {
  // Debug logs
  console.log('ProfileEditModal rendered with isOpen:', isOpen);
  console.log('Profile data:', profile);
  const isMobile = useIsMobile();
  const [formData, setFormData] = useState<UserProfileUpdate & {
    profileImageFile?: File;
    coverImageFile?: File;
  }>({
    username: profile.username,
    displayName: profile.displayName,
    bio: profile.bio || '',
    profileImageUrl: profile.profileImageUrl || '',
    coverImageUrl: profile.coverImageUrl || '',
    socialLinks: {
      twitter: profile.socialLinks?.twitter || '',
      github: profile.socialLinks?.github || '',
      website: profile.socialLinks?.website || ''
    }
  });

  const [isUploading, setIsUploading] = useState(false);

  // Refs for file inputs
  const profileImageInputRef = useRef<HTMLInputElement>(null);
  const coverImageInputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    if (name.startsWith('social.')) {
      const socialType = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        socialLinks: {
          ...prev.socialLinks || {},
          [socialType]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleProfileImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Create a temporary preview using a data URL
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          // Use the data URL as a temporary preview
          const dataUrl = event.target.result as string;

          // Update form data with the data URL as a temporary preview
          setFormData(prev => ({
            ...prev,
            profileImageUrl: dataUrl,
            profileImageFile: file
          }));

          // Show success message
          toast.success('Profile image selected');
        }
      };
      reader.readAsDataURL(file);
    }
  };



  const handleCoverImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Create a temporary preview using a data URL
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          // Use the data URL as a temporary preview
          const dataUrl = event.target.result as string;

          // Update form data with the data URL as a temporary preview
          setFormData(prev => ({
            ...prev,
            coverImageUrl: dataUrl,
            coverImageFile: file
          }));

          // Show success message
          toast.success('Cover image selected');
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const uploadImageToSupabase = async (file: File, isProfileImage: boolean): Promise<string> => {
    try {
      // Show uploading toast
      toast.loading(`Uploading ${isProfileImage ? 'profile' : 'cover'} image to Supabase...`);

      // Import the Supabase storage service
      const { uploadProfileImage, uploadCoverImage } = await import('@/services/supabaseStorageService');

      // Upload to Supabase Storage based on image type
      let publicUrl: string;
      if (isProfileImage) {
        publicUrl = await uploadProfileImage(file, profile.address);
      } else {
        publicUrl = await uploadCoverImage(file, profile.address);
      }

      // Dismiss loading toast
      toast.dismiss();
      toast.success(`${isProfileImage ? 'Profile' : 'Cover'} image uploaded successfully`);

      // Return the public URL
      console.log(`Image uploaded to Supabase Storage: ${publicUrl}`);
      return publicUrl;
    } catch (error) {
      console.error('Error uploading image to Supabase:', error);
      toast.dismiss();
      toast.error(`Failed to upload ${isProfileImage ? 'profile' : 'cover'} image`);

      // Create a fallback URL using a data URL for immediate display
      try {
        const reader = new FileReader();
        const dataUrlPromise = new Promise<string>((resolve, reject) => {
          reader.onload = () => {
            if (reader.result) {
              resolve(reader.result as string);
            } else {
              reject(new Error('Failed to read file as data URL'));
            }
          };
          reader.onerror = () => reject(reader.error);
          reader.readAsDataURL(file);
        });

        // Return the data URL as a fallback
        const dataUrl = await dataUrlPromise;
        console.warn('Using data URL as fallback for immediate display');
        return dataUrl;
      } catch (fallbackError) {
        console.error('Error creating fallback data URL:', fallbackError);
        throw new Error('Failed to upload image');
      }
    }
  };

  const handleSubmit = async () => {
    try {
      setIsUploading(true);
      toast.loading('Updating profile...');

      // Validate the form data
      if (!formData.displayName || formData.displayName.trim() === '') {
        toast.dismiss();
        toast.error('Display name is required');
        setIsUploading(false);
        return;
      }

      if (!formData.username || formData.username.trim() === '') {
        toast.dismiss();
        toast.error('Username is required');
        setIsUploading(false);
        return;
      }

      // Create a deep copy of the form data to avoid reference issues
      const update: UserProfileUpdate = {
        displayName: formData.displayName.trim(),
        username: formData.username.trim(),
        bio: formData.bio?.trim() || '',
        socialLinks: { ...formData.socialLinks },
        profileImageUrl: formData.profileImageUrl,
        coverImageUrl: formData.coverImageUrl,
        // Ensure stats are fully defined to avoid TypeScript errors
        stats: {
          posts: profile.stats?.posts || 0,
          likes: profile.stats?.likes || 0,
          tips: profile.stats?.tips || 0
        }
      };

      // No need to store backup in localStorage - we'll save directly to Supabase

      // Process images in parallel for better performance
      const imagePromises: Promise<void>[] = [];

      // Upload profile image if changed
      if (formData.profileImageFile) {
        const profileImagePromise = (async () => {
          try {
            console.log('Uploading profile image to Supabase...');
            const profileImageUrl = await uploadImageToSupabase(formData.profileImageFile, true);
            console.log('Profile image uploaded successfully:', profileImageUrl);
            update.profileImageUrl = profileImageUrl;
          } catch (error) {
            console.error('Error uploading profile image:', error);
            // Don't throw here, we'll continue with the update
          }
        })();
        imagePromises.push(profileImagePromise);
      }

      // Upload cover image if changed
      if (formData.coverImageFile) {
        const coverImagePromise = (async () => {
          try {
            console.log('Uploading cover image to Supabase...');
            const coverImageUrl = await uploadImageToSupabase(formData.coverImageFile, false);
            console.log('Cover image uploaded successfully:', coverImageUrl);
            update.coverImageUrl = coverImageUrl;
          } catch (error) {
            console.error('Error uploading cover image:', error);
            // Don't throw here, we'll continue with the update
          }
        })();
        imagePromises.push(coverImagePromise);
      }

      // Wait for all image uploads to complete
      await Promise.all(imagePromises);

      console.log("ProfileEditModal - Updating profile with:", update);

      // Call the onSave function with the update data
      await onSave(update);

      // Store the updated profile in Supabase (permanent storage)
      try {
        const normalizedAddress = profile.address.toLowerCase();

        // Create an updated profile object for Supabase
        const updatedProfile = {
          ...profile,
          ...update,
          socialLinks: {
            ...profile.socialLinks,
            ...update.socialLinks
          }
        };

        console.log('Saving profile to Supabase for permanent storage');

        // Save to Supabase for permanent storage across devices
        console.log('Saving profile to Supabase using RPC (permanent storage)');

        // First check if this wallet address already has a profile in Supabase
        // If it does, we'll use its existing ID
        let existingProfileId = null;
        try {
          const { data: existingProfile } = await supabase
            .from('profiles')
            .select('id')
            .eq('wallet_address', normalizedAddress)
            .maybeSingle();

          if (existingProfile) {
            existingProfileId = existingProfile.id;
            console.log(`Found existing profile ID for ${normalizedAddress}: ${existingProfileId}`);
          }
        } catch (checkError) {
          console.warn('Error checking for existing profile:', checkError);
        }

        // Generate a UUID for the profile ID if we don't have an existing ID
        const generateUUID = () => {
          return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
          });
        };

        // Convert the profile to Supabase format
        const supabaseProfile = {
          id: existingProfileId || generateUUID(), // Use existing ID or generate a new UUID
          wallet_address: normalizedAddress,
          username: updatedProfile.username,
          display_name: updatedProfile.displayName,
          bio: updatedProfile.bio || '',
          avatar_url: updatedProfile.profileImageUrl || '',
          cover_image_url: updatedProfile.coverImageUrl || '',
          social_links: updatedProfile.socialLinks || {},
          post_count: updatedProfile.stats?.posts || 0,
          like_count: updatedProfile.stats?.likes || 0,
          tip_count: updatedProfile.stats?.tips || 0,
          is_verified: updatedProfile.verification?.isVerified || false,
          verification_type: updatedProfile.verification?.type || null,
          verified_at: updatedProfile.verification?.since?.toISOString() || null,
          updated_at: new Date().toISOString()
        };

        // Use direct upsert instead of RPC function
        const { data: rpcResult, error: rpcError } = await supabase
          .from('profiles')
          .upsert(supabaseProfile)
          .select()
          .single();

        if (rpcError) {
          console.error('Error saving profile using RPC:', rpcError);

          // Fall back to the force sync function
          // Make sure stats property is fully defined
          const profileWithFullStats = {
            ...updatedProfile,
            stats: {
              posts: updatedProfile.stats?.posts || 0,
              likes: updatedProfile.stats?.likes || 0,
              tips: updatedProfile.stats?.tips || 0
            }
          };

          // Force sync the profile to Supabase
          const success = await forceSyncProfileToSupabase(profileWithFullStats);

          if (success) {
            console.log('Successfully force synced profile to Supabase');
          } else {
            console.error('Failed to force sync profile to Supabase');
            toast.error('Failed to save profile to permanent storage. Your changes may not be available on other devices.');
          }
        } else {
          console.log('Successfully saved profile to Supabase using RPC:', rpcResult);

          // Even if the RPC was successful, force sync the profile to ensure it's saved
          const profileWithFullStats = {
            ...updatedProfile,
            stats: {
              posts: updatedProfile.stats?.posts || 0,
              likes: updatedProfile.stats?.likes || 0,
              tips: updatedProfile.stats?.tips || 0
            }
          };

          // Force sync in the background
          forceSyncProfileToSupabase(profileWithFullStats)
            .then(success => {
              if (success) {
                console.log('Successfully force synced profile to Supabase after RPC success');
              } else {
                console.error('Failed to force sync profile to Supabase after RPC success');
              }
            })
            .catch(error => {
              console.error('Error force syncing profile to Supabase after RPC success:', error);
            });
        }
      } catch (storageError) {
        console.error('Error saving profile to Supabase:', storageError);
        toast.error('Failed to save profile to permanent storage. Your changes may not be available on other devices.');
      }

      // Dismiss loading toast
      toast.dismiss();
      toast.success('Profile updated successfully!');

      // Close the modal
      onClose();

      // No need to force a page refresh - the profile is already updated in state
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.dismiss();
      toast.error("Failed to update profile");
    } finally {
      setIsUploading(false);
    }
  };

  // Function to handle removing profile image
  const removeProfileImage = () => {
    setFormData(prev => ({
      ...prev,
      profileImageUrl: '',
      profileImageFile: undefined
    }));
  };

  // Function to handle removing cover image
  const removeCoverImage = () => {
    setFormData(prev => ({
      ...prev,
      coverImageUrl: '',
      coverImageFile: undefined
    }));
  };



  if (isMobile) {
    // Log when the mobile version is rendered
    console.log('Rendering mobile version of ProfileEditModal, isOpen:', isOpen);

    return (
      <Drawer
        open={isOpen}
        onOpenChange={(open) => {
          console.log('Drawer onOpenChange triggered, new open state:', open);
          if (!open) onClose();
        }}
      >
        <DrawerContent className="px-4 py-6 max-h-[90vh] overflow-y-auto">
          <DrawerHeader className="p-0">
            <div className="flex justify-between items-center w-full mb-4">
              <h2 className="text-xl font-semibold">Edit Profile</h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  console.log('Mobile close button clicked');
                  onClose();
                }}
                type="button"
              >
                <X size={20} />
              </Button>
            </div>
          </DrawerHeader>

          <div className="space-y-6 overflow-y-auto pb-20">
            {/* Cover Image */}
            <div className="relative">
              <ProfileCoverImage
                src={formData.coverImageUrl || ''}
                alt="Add Cover Image"
                className="w-full h-32 bg-secondary rounded-lg overflow-hidden flex items-center justify-center"
                style={{
                  backgroundSize: 'cover',
                  backgroundPosition: 'center'
                }}
              />
              <div className="absolute bottom-2 right-2 flex space-x-2">
                {formData.coverImageUrl && (
                  <Button
                    variant="destructive"
                    size="icon"
                    className="h-8 w-8 rounded-full"
                    onClick={removeCoverImage}
                    type="button"
                  >
                    <X size={14} />
                  </Button>
                )}
                <Button
                  variant="secondary"
                  size="icon"
                  className="h-8 w-8 rounded-full"
                  onClick={() => coverImageInputRef.current?.click()}
                  type="button"
                >
                  <Upload size={14} />
                </Button>
                <input
                  ref={coverImageInputRef}
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleCoverImageChange}
                />
              </div>
            </div>

            {/* Profile Image */}
            <div className="flex justify-center -mt-12">
              <div className="relative">
                <Avatar className="h-24 w-24 border-4 border-background">
                  <CustomAvatarImage src={formData.profileImageUrl} alt="Profile" />
                  <AvatarFallback className="bg-voicechain-purple text-white text-xl">
                    {formData.displayName?.charAt(0) || profile.displayName.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div className="absolute -bottom-2 -right-2 flex space-x-1">
                  {formData.profileImageUrl && (
                    <Button
                      variant="destructive"
                      size="icon"
                      className="h-7 w-7 rounded-full"
                      onClick={removeProfileImage}
                      type="button"
                    >
                      <X size={12} />
                    </Button>
                  )}
                  <Button
                    variant="secondary"
                    size="icon"
                    className="h-7 w-7 rounded-full"
                    onClick={() => profileImageInputRef.current?.click()}
                    type="button"
                  >
                    <Upload size={12} />
                  </Button>
                  <input
                    ref={profileImageInputRef}
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleProfileImageChange}
                  />
                </div>
              </div>
            </div>

            {/* Form Fields */}
            <div className="space-y-4 mt-4">
              <div>
                <Label htmlFor="displayName">Display Name</Label>
                <Input
                  id="displayName"
                  name="displayName"
                  value={formData.displayName}
                  onChange={handleInputChange}
                  placeholder="Your display name"
                />
              </div>

              <div>
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  name="username"
                  value={formData.username}
                  onChange={handleInputChange}
                  placeholder="username"
                />
              </div>

              <div>
                <Label htmlFor="bio">Bio</Label>
                <Textarea
                  id="bio"
                  name="bio"
                  value={formData.bio}
                  onChange={handleInputChange}
                  placeholder="Tell us about yourself"
                  className="resize-none h-24"
                />
              </div>

              <div>
                <Label className="flex items-center gap-2" htmlFor="social.twitter">
                  <Twitter size={16} className="text-[#1DA1F2]" />
                  Twitter
                </Label>
                <Input
                  id="social.twitter"
                  name="social.twitter"
                  value={formData.socialLinks?.twitter || ''}
                  onChange={handleInputChange}
                  placeholder="@username"
                />
              </div>

              <div>
                <Label className="flex items-center gap-2" htmlFor="social.github">
                  <Github size={16} />
                  GitHub
                </Label>
                <Input
                  id="social.github"
                  name="social.github"
                  value={formData.socialLinks?.github || ''}
                  onChange={handleInputChange}
                  placeholder="username"
                />
              </div>

              <div>
                <Label className="flex items-center gap-2" htmlFor="social.website">
                  <Globe size={16} className="text-blue-500" />
                  Website
                </Label>
                <Input
                  id="social.website"
                  name="social.website"
                  value={formData.socialLinks?.website || ''}
                  onChange={handleInputChange}
                  placeholder="https://yourwebsite.com"
                />
              </div>
            </div>
          </div>

          <DrawerFooter className="px-0 pt-4 pb-0 sticky bottom-0 bg-background">
            <Button
              onClick={handleSubmit}
              className="w-full bg-voicechain-purple hover:bg-voicechain-accent"
              disabled={isUploading}
              type="button"
            >
              {isUploading ? 'Saving...' : 'Save Profile'}
            </Button>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    );
  }

  // Log when the desktop version is rendered
  console.log('Rendering desktop version of ProfileEditModal, isOpen:', isOpen);

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        console.log('Dialog onOpenChange triggered, new open state:', open);
        if (!open) onClose();
      }}
    >
      <DialogContent className="sm:max-w-md p-6 max-h-[90vh] overflow-y-auto">
        <DialogHeader className="p-0">
          <div className="flex justify-between items-center w-full mb-4">
            <DialogTitle className="text-xl font-semibold">Edit Profile</DialogTitle>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => {
                console.log('Close button clicked');
                onClose();
              }}
              type="button"
            >
              <X size={20} />
            </Button>
          </div>
        </DialogHeader>

        <div className="space-y-6 overflow-y-auto pr-2">
          {/* Cover Image */}
          <div className="relative">
            <ProfileCoverImage
              src={formData.coverImageUrl || ''}
              alt="Add Cover Image"
              className="w-full h-32 bg-secondary rounded-lg overflow-hidden flex items-center justify-center"
              style={{
                backgroundSize: 'cover',
                backgroundPosition: 'center'
              }}
            />
            <div className="absolute bottom-2 right-2 flex space-x-2">
              {formData.coverImageUrl && (
                <Button
                  variant="destructive"
                  size="icon"
                  className="h-8 w-8 rounded-full"
                  onClick={removeCoverImage}
                  type="button"
                >
                  <X size={14} />
                </Button>
              )}
              <Button
                variant="secondary"
                size="icon"
                className="h-8 w-8 rounded-full"
                onClick={() => coverImageInputRef.current?.click()}
                type="button"
              >
                <Upload size={14} />
              </Button>
              <input
                ref={coverImageInputRef}
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleCoverImageChange}
              />
            </div>
          </div>

          {/* Profile Image */}
          <div className="flex justify-center -mt-12">
            <div className="relative">
              <Avatar className="h-24 w-24 border-4 border-background">
                <CustomAvatarImage src={formData.profileImageUrl} alt="Profile" />
                <AvatarFallback className="bg-voicechain-purple text-white text-xl">
                  {formData.displayName?.charAt(0) || profile.displayName.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div className="absolute -bottom-2 -right-2 flex space-x-1">
                {formData.profileImageUrl && (
                  <Button
                    variant="destructive"
                    size="icon"
                    className="h-7 w-7 rounded-full"
                    onClick={removeProfileImage}
                    type="button"
                  >
                    <X size={12} />
                  </Button>
                )}
                <Button
                  variant="secondary"
                  size="icon"
                  className="h-7 w-7 rounded-full"
                  onClick={() => profileImageInputRef.current?.click()}
                  type="button"
                >
                  <Upload size={12} />
                </Button>
                <input
                  ref={profileImageInputRef}
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleProfileImageChange}
                />
              </div>
            </div>
          </div>

          {/* Form Fields */}
          <div className="space-y-4 mt-4">
            <div>
              <Label htmlFor="displayName">Display Name</Label>
              <Input
                id="displayName"
                name="displayName"
                value={formData.displayName}
                onChange={handleInputChange}
                placeholder="Your display name"
              />
            </div>

            <div>
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                placeholder="username"
              />
            </div>

            <div>
              <Label htmlFor="bio">Bio</Label>
              <Textarea
                id="bio"
                name="bio"
                value={formData.bio}
                onChange={handleInputChange}
                placeholder="Tell us about yourself"
                className="resize-none h-24"
              />
            </div>

            <div>
              <Label className="flex items-center gap-2" htmlFor="social.twitter">
                <Twitter size={16} className="text-[#1DA1F2]" />
                Twitter
              </Label>
              <Input
                id="social.twitter"
                name="social.twitter"
                value={formData.socialLinks?.twitter || ''}
                onChange={handleInputChange}
                placeholder="@username"
              />
            </div>

            <div>
              <Label className="flex items-center gap-2" htmlFor="social.github">
                <Github size={16} />
                GitHub
              </Label>
              <Input
                id="social.github"
                name="social.github"
                value={formData.socialLinks?.github || ''}
                onChange={handleInputChange}
                placeholder="username"
              />
            </div>

            <div>
              <Label className="flex items-center gap-2" htmlFor="social.website">
                <Globe size={16} className="text-blue-500" />
                Website
              </Label>
              <Input
                id="social.website"
                name="social.website"
                value={formData.socialLinks?.website || ''}
                onChange={handleInputChange}
                placeholder="https://yourwebsite.com"
              />
            </div>
          </div>
        </div>

        <DialogFooter className="mt-6">
          <Button
            onClick={handleSubmit}
            className="w-full bg-voicechain-purple hover:bg-voicechain-accent"
            disabled={isUploading}
            type="button"
          >
            {isUploading ? 'Saving...' : 'Save Profile'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ProfileEditModal;
