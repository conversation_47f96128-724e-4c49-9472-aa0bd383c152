import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useAuth } from '@/contexts/AuthContext';
import { useJournals } from '@/contexts/JournalContext';
import { journalService } from '@/services/journalService';
import { JournalEntry } from '@/types/journal';

export const JournalDebugger: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { journals, isLoading, addJournal, refreshJournals } = useJournals();
  const [testTitle, setTestTitle] = useState('Test Journal Entry');
  const [testTranscript, setTestTranscript] = useState('This is a test journal entry for debugging.');
  const [isPrivate, setIsPrivate] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string[]>([]);

  const addDebugInfo = (info: string) => {
    setDebugInfo(prev => [...prev, `${new Date().toLocaleTimeString()}: ${info}`]);
  };

  const testJournalCreation = async () => {
    if (!user?.id) {
      addDebugInfo('❌ No user ID available');
      return;
    }

    addDebugInfo('📝 Testing journal creation...');
    addDebugInfo(`User ID: ${user.id}`);
    addDebugInfo(`Title: ${testTitle}`);
    addDebugInfo(`Private: ${isPrivate}`);

    try {
      const testJournal: JournalEntry = {
        id: crypto.randomUUID(),
        title: testTitle,
        description: 'Test description',
        audioUrl: 'https://example.com/test-audio.mp3',
        transcript: testTranscript,
        createdAt: new Date(),
        userAddress: user.id,
        isPrivate: isPrivate,
        isLocked: false,
        isUnlocked: true,
        duration: 30,
        unlockCondition: {
          type: 'time',
          value: new Date().toISOString()
        }
      };

      await addJournal(testJournal);
      addDebugInfo('✅ Journal creation successful');
    } catch (error) {
      addDebugInfo(`❌ Journal creation failed: ${(error as Error).message}`);
    }
  };

  const testDirectDatabaseCall = async () => {
    if (!user?.id) {
      addDebugInfo('❌ No user ID available');
      return;
    }

    addDebugInfo('🔍 Testing direct database call...');

    try {
      const journalId = await journalService.createJournal(
        user.id,
        'Direct DB Test Journal',
        'https://example.com/direct-test.mp3',
        'This is a direct database test',
        45,
        false,
        undefined,
        undefined,
        [],
        isPrivate
      );

      addDebugInfo(`✅ Direct database call successful: ${journalId}`);
    } catch (error) {
      addDebugInfo(`❌ Direct database call failed: ${(error as Error).message}`);
    }
  };

  const testJournalRetrieval = async () => {
    if (!user?.id) {
      addDebugInfo('❌ No user ID available');
      return;
    }

    addDebugInfo('📖 Testing journal retrieval...');

    try {
      const userJournals = await journalService.getUserJournals(user.id);
      addDebugInfo(`✅ Retrieved ${userJournals.length} journals from database`);
      
      if (userJournals.length > 0) {
        addDebugInfo(`Latest journal: ${userJournals[0].title}`);
      }
    } catch (error) {
      addDebugInfo(`❌ Journal retrieval failed: ${(error as Error).message}`);
    }
  };

  const clearDebugInfo = () => {
    setDebugInfo([]);
  };

  if (!isAuthenticated) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Journal Debugger</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Please log in to use the journal debugger.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Journal Debugger</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="testTitle">Test Journal Title</Label>
            <Input
              id="testTitle"
              value={testTitle}
              onChange={(e) => setTestTitle(e.target.value)}
              placeholder="Enter test title"
            />
          </div>
          <div>
            <Label htmlFor="testTranscript">Test Transcript</Label>
            <Input
              id="testTranscript"
              value={testTranscript}
              onChange={(e) => setTestTranscript(e.target.value)}
              placeholder="Enter test transcript"
            />
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="isPrivate"
            checked={isPrivate}
            onCheckedChange={setIsPrivate}
          />
          <Label htmlFor="isPrivate">Private Journal</Label>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button onClick={testJournalCreation} variant="outline">
            Test Journal Creation
          </Button>
          <Button onClick={testDirectDatabaseCall} variant="outline">
            Test Direct DB Call
          </Button>
          <Button onClick={testJournalRetrieval} variant="outline">
            Test Journal Retrieval
          </Button>
          <Button onClick={refreshJournals} variant="outline">
            Refresh Journals
          </Button>
          <Button onClick={clearDebugInfo} variant="destructive">
            Clear Debug
          </Button>
        </div>

        <div className="space-y-2">
          <h3 className="font-semibold">Current State:</h3>
          <div className="text-sm space-y-1">
            <p><strong>User ID:</strong> {user?.id || 'None'}</p>
            <p><strong>Journals Loaded:</strong> {journals.length}</p>
            <p><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
            {journals.length > 0 && (
              <div className="ml-4 text-xs">
                <p>Latest: {journals[0]?.title}</p>
                <p>Private: {journals[0]?.isPrivate ? 'Yes' : 'No'}</p>
                <p>Created: {journals[0]?.createdAt?.toLocaleString()}</p>
              </div>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="font-semibold">Debug Log:</h3>
          <div className="bg-gray-100 p-3 rounded max-h-64 overflow-y-auto text-sm font-mono">
            {debugInfo.length === 0 ? (
              <p className="text-gray-500">No debug info yet. Click a test button above.</p>
            ) : (
              debugInfo.map((info, index) => (
                <div key={index} className="mb-1">
                  {info}
                </div>
              ))
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default JournalDebugger;
