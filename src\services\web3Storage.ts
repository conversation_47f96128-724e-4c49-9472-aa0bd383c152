/**
 * Web3Storage service for IPFS uploads
 * This service handles uploading files to IPFS via Web3.Storage
 */

// Note: You'll need to install web3.storage with: npm install web3.storage
import { Web3Storage } from 'web3.storage';
import { WEB3_STORAGE_TOKEN } from '@/utils/env';

/**
 * Get a Web3Storage client instance
 */
export function getWeb3Client(): Web3Storage {
  if (!WEB3_STORAGE_TOKEN) {
    console.warn('Web3Storage token not found. Please set NEXT_PUBLIC_WEB3_STORAGE_TOKEN in your environment variables.');
  }

  return new Web3Storage({ token: WEB3_STORAGE_TOKEN });
}

/**
 * Upload a file to IPFS via Web3.Storage
 * @param file The file to upload
 * @returns IPFS URI in the format ipfs://{CID}/{filename}
 */
export async function uploadToIPFS(file: File): Promise<string> {
  try {
    const client = getWeb3Client();

    // Upload the file to IPFS
    const cid = await client.put([file]);

    // Return the IPFS URI
    return `ipfs://${cid}/${file.name}`;
  } catch (error) {
    console.error('Error uploading to IPFS:', error);
    throw error;
  }
}

/**
 * Upload a blob to IPFS via Web3.Storage
 * @param blob The blob to upload
 * @param fileName The name to give the file
 * @returns IPFS URI in the format ipfs://{CID}/{filename}
 */
export async function uploadBlobToIPFS(blob: Blob, fileName: string): Promise<string> {
  try {
    // Convert blob to File object
    const file = new File([blob], fileName, { type: blob.type });

    // Upload to IPFS
    return await uploadToIPFS(file);
  } catch (error) {
    console.error('Error uploading blob to IPFS:', error);
    throw error;
  }
}

/**
 * Get a gateway URL for an IPFS URI
 * @param ipfsUri IPFS URI in the format ipfs://{CID}/{filename}
 * @returns HTTP URL for accessing the content
 */
export function getIPFSGatewayUrl(ipfsUri: string): string {
  // Remove ipfs:// prefix
  const ipfsPath = ipfsUri.replace('ipfs://', '');

  // Split into CID and filename
  const [cid, ...pathParts] = ipfsPath.split('/');
  const path = pathParts.join('/');

  // Use a public gateway
  return `https://${cid}.ipfs.dweb.link/${path}`;
}

/**
 * Check if a string is an IPFS URI
 * @param uri The URI to check
 * @returns True if the URI is an IPFS URI
 */
export function isIPFSUri(uri: string): boolean {
  return uri.startsWith('ipfs://');
}

/**
 * Convert an IPFS URI to a gateway URL if needed
 * @param uri The URI to convert
 * @returns HTTP URL for accessing the content
 */
export function resolveIPFSUri(uri: string): string {
  if (isIPFSUri(uri)) {
    return getIPFSGatewayUrl(uri);
  }
  return uri;
}
