import React, { useState, useEffect } from 'react';
import { useChannels } from '@/contexts/ChannelContext';
import CreateChannelModal from '@/components/CreateChannelModal';
import JoinChannelModal from '@/components/JoinChannelModal';
import ChannelInviteModal from '@/components/ChannelInviteModal';
import ChannelSettings from '@/components/ChannelSettings';
import ChannelGuide from '@/components/ChannelGuide';
import ModernChannelList from '@/components/ModernChannelList';
import ModernChannelView from '@/components/ModernChannelView';
import { Button } from '@/components/ui/button';
import { Menu, X, HelpCircle } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';

interface ModernChannelsProps {
  userAddress?: string;
}

const ModernChannels: React.FC<ModernChannelsProps> = ({ userAddress = '' }) => {
  const { activeChannel, getChannelById, channels } = useChannels();
  const isMobile = useIsMobile();

  // EMERGENCY FIX: Track active channel locally
  const [localActiveChannel, setLocalActiveChannel] = useState<string | null>(null);

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isJoinModalOpen, setIsJoinModalOpen] = useState(false);
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [isGuideModalOpen, setIsGuideModalOpen] = useState(false);
  const [selectedChannelId, setSelectedChannelId] = useState<string | null>(null);
  const [showSidebar, setShowSidebar] = useState(!isMobile);

  // Update sidebar visibility when screen size changes
  useEffect(() => {
    setShowSidebar(!isMobile);
  }, [isMobile]);

  // Close sidebar when a channel is selected on mobile
  useEffect(() => {
    if (isMobile && activeChannel) {
      setShowSidebar(false);
    }
  }, [activeChannel, isMobile]);

  const handleChannelSettings = (channelId: string) => {
    setSelectedChannelId(channelId);
    setIsSettingsModalOpen(true);
  };

  const handleChannelInvite = (channelId: string) => {
    setSelectedChannelId(channelId);
    setIsInviteModalOpen(true);
  };

  const toggleSidebar = () => {
    setShowSidebar(!showSidebar);
  };

  // Show onboarding guide for first-time users
  useEffect(() => {
    const hasSeenGuide = localStorage.getItem('channels-guide-seen');
    if (!hasSeenGuide) {
      setTimeout(() => {
        setIsGuideModalOpen(true);
        localStorage.setItem('channels-guide-seen', 'true');
      }, 1000);
    }
  }, []);

  return (
    <div className="h-screen flex flex-col bg-gradient-to-br from-background via-background to-secondary/10">
      {/* Top Header - Mobile Only */}
      {isMobile && (
        <div className="flex items-center justify-between p-4 border-b border-border/50 bg-card/80 backdrop-blur-sm">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleSidebar}
              className="h-9 w-9"
            >
              <Menu size={20} />
            </Button>
            <div>
              <h1 className="text-lg font-bold text-foreground">
                {activeChannel ? getChannelById(activeChannel)?.name : 'Channels'}
              </h1>
              <p className="text-xs text-muted-foreground">
                {activeChannel ? 'Voice community' : 'Select a channel'}
              </p>
            </div>
          </div>
          
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsGuideModalOpen(true)}
            className="h-9 w-9"
          >
            <HelpCircle size={18} />
          </Button>
        </div>
      )}

      <div className="flex flex-1 relative overflow-hidden">
        {/* Mobile Overlay */}
        {isMobile && showSidebar && (
          <div
            className="fixed inset-0 bg-black/50 z-10"
            onClick={toggleSidebar}
          />
        )}

        {/* Sidebar */}
        <div
          className={`
            ${showSidebar ? 'translate-x-0' : '-translate-x-full'}
            ${isMobile ? 'absolute z-20 h-full shadow-2xl' : 'relative'}
            transition-transform duration-300 ease-in-out
            bg-card/95 backdrop-blur-sm border-r border-border/50 w-full md:w-80 lg:w-96
          `}
        >
          {/* Desktop Header */}
          {!isMobile && (
            <div className="flex items-center justify-between p-6 border-b border-border/50 bg-gradient-to-r from-voicechain-purple/5 to-voicechain-accent/5">
              <div>
                <h1 className="text-2xl font-bold text-foreground">Channels</h1>
                <p className="text-sm text-muted-foreground">Voice communities await</p>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsGuideModalOpen(true)}
                className="h-9 w-9 hover:bg-secondary/50"
              >
                <HelpCircle size={18} />
              </Button>
            </div>
          )}

          <ModernChannelList
            userAddress={userAddress}
            onCreateChannel={() => setIsCreateModalOpen(true)}
            onJoinChannel={() => setIsJoinModalOpen(true)}
          />
        </div>

        {/* Main Content */}
        <div className="flex-1 min-w-0">
          {/* EMERGENCY DEBUG */}
          <div className="p-2 bg-red-100 border-b text-xs">
            <strong>🚨 EMERGENCY DEBUG:</strong> activeChannel: {activeChannel || 'null'} |
            localActiveChannel: {localActiveChannel || 'null'} |
            Available channels: {channels?.length || 0} |
            <button
              onClick={() => {
                console.log('🚨 FORCE REFRESH');
                window.location.reload();
              }}
              className="ml-2 px-2 py-1 bg-red-500 text-white rounded text-xs"
            >
              Force Refresh
            </button>
            {channels?.length > 0 && (
              <button
                onClick={() => {
                  const firstChannel = channels[0];
                  console.log('🚨 FORCE ENTER FIRST CHANNEL:', firstChannel);
                  console.log('🚨 SETTING LOCAL ACTIVE CHANNEL TO ID:', firstChannel.id);
                  setLocalActiveChannel(firstChannel.id);
                }}
                className="ml-2 px-2 py-1 bg-blue-500 text-white rounded text-xs"
              >
                Force Enter: {channels[0]?.name}
              </button>
            )}
          </div>

          <ModernChannelView
            userAddress={userAddress}
            onChannelSettings={handleChannelSettings}
            onChannelInvite={handleChannelInvite}
            forceChannelId={localActiveChannel}
          />
        </div>
      </div>

      {/* Modals */}
      <CreateChannelModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        userAddress={userAddress}
      />

      <JoinChannelModal
        isOpen={isJoinModalOpen}
        onClose={() => setIsJoinModalOpen(false)}
      />

      <ChannelInviteModal
        isOpen={isInviteModalOpen}
        onClose={() => setIsInviteModalOpen(false)}
        channelId={selectedChannelId}
      />

      <ChannelSettings
        isOpen={isSettingsModalOpen}
        onClose={() => setIsSettingsModalOpen(false)}
        channelId={selectedChannelId}
      />

      <ChannelGuide
        isOpen={isGuideModalOpen}
        onClose={() => setIsGuideModalOpen(false)}
      />
    </div>
  );
};

export default ModernChannels;
