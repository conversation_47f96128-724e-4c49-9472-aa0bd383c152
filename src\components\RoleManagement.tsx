import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { supabase } from '@/lib/supabase';
import { toast } from '@/components/ui/sonner';
import RoleBadge, { type Role } from './RoleBadge';
import { Ban, VolumeX } from 'lucide-react';

interface ChannelMember {
  id: string;
  profile_id: string;
  role: Role;
  joined_at: string;
  is_banned: boolean;
  is_muted: boolean;
  profile?: {
    wallet_address: string;
    display_name?: string;
  };
}

interface RoleManagementProps {
  channelId: string;
  userAddress: string;
  userRole: string;
  onClose?: () => void;
}

const RoleManagement: React.FC<RoleManagementProps> = ({
  channelId,
  userAddress,
  userRole,
  onClose
}) => {
  const [members, setMembers] = useState<ChannelMember[]>([]);
  const [loading, setLoading] = useState(true);

  const roleHierarchy = {
    owner: 5,
    admin: 4,
    moderator: 3,
    member: 2,
    guest: 1
  };

  useEffect(() => {
    loadMembers();
  }, [channelId]);

  const loadMembers = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('channel_members')
        .select(`
          *,
          profile:profiles(wallet_address, display_name)
        `)
        .eq('channel_id', channelId)
        .order('joined_at', { ascending: false });

      if (error) {
        console.error('Error loading members:', error);
        toast('Failed to load channel members');
        return;
      }

      setMembers(data || []);
    } catch (error) {
      console.error('Error loading members:', error);
      toast('Failed to load channel members');
    } finally {
      setLoading(false);
    }
  };

  const canManageRole = (targetRole: string) => {
    const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
    const targetLevel = roleHierarchy[targetRole as keyof typeof roleHierarchy] || 0;
    return userLevel > targetLevel;
  };

  const handleRoleChange = async (memberId: string, newRole: string) => {
    try {
      const { error } = await supabase
        .from('channel_members')
        .update({ 
          role: newRole,
          role_assigned_at: new Date().toISOString()
        })
        .eq('id', memberId);

      if (error) {
        console.error('Error updating role:', error);
        toast('Failed to update member role');
        return;
      }
      
      toast(`Member role updated to ${newRole}`);
      loadMembers();
    } catch (error) {
      console.error('Error updating role:', error);
      toast('Failed to update member role');
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffHours < 24) return `${diffHours}h ago`;
    return `${diffDays}d ago`;
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-16 bg-secondary rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold">Role Management</h2>
          <p className="text-muted-foreground">Manage channel members and their permissions</p>
        </div>
        <Badge variant="outline" className="text-sm">
          {members.length} members
        </Badge>
      </div>

      <div className="space-y-4">
        {members.map((member) => {
          const canManage = canManageRole(member.role);

          return (
            <Card key={member.id} className={member.is_banned ? 'opacity-50' : ''}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-10 w-10">
                      <AvatarFallback>
                        {member.profile?.wallet_address?.slice(2, 4).toUpperCase() || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">
                          {member.profile?.display_name || 
                           `${member.profile?.wallet_address?.slice(0, 6)}...${member.profile?.wallet_address?.slice(-4)}`}
                        </span>
                        <RoleBadge role={member.role} />
                        {member.is_banned && (
                          <Badge variant="destructive" className="text-xs">
                            <Ban size={10} className="mr-1" />
                            Banned
                          </Badge>
                        )}
                        {member.is_muted && (
                          <Badge variant="secondary" className="text-xs">
                            <VolumeX size={10} className="mr-1" />
                            Muted
                          </Badge>
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Joined {formatTimeAgo(member.joined_at)}
                      </div>
                    </div>
                  </div>

                  {canManage && (
                    <Select
                      value={member.role}
                      onValueChange={(newRole) => handleRoleChange(member.id, newRole)}
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.keys(roleHierarchy).map((role) => (
                          <SelectItem 
                            key={role} 
                            value={role}
                            disabled={!canManageRole(role)}
                          >
                            {role.charAt(0).toUpperCase() + role.slice(1)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default RoleManagement;
