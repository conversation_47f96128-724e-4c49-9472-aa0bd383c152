@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Safe area support for mobile devices */
@supports (padding: max(0px)) {
  .safe-area-top {
    padding-top: max(12px, env(safe-area-inset-top));
  }

  .safe-area-bottom {
    padding-bottom: max(12px, env(safe-area-inset-bottom));
  }

  .safe-area-left {
    padding-left: max(12px, env(safe-area-inset-left));
  }

  .safe-area-right {
    padding-right: max(12px, env(safe-area-inset-right));
  }

  /* Full height with safe areas */
  .safe-height {
    height: 100vh;
    height: 100dvh;
  }
}

/* Prevent overscroll bounce on iOS */
body {
  overscroll-behavior: none;
  -webkit-overflow-scrolling: touch;
}

/* Ensure proper touch behavior */
* {
  -webkit-tap-highlight-color: transparent;
}

/* Prevent zoom on input focus for mobile */
@media screen and (max-width: 768px) {
  input, textarea, select {
    font-size: 16px !important;
    transform-origin: left top;
    transform: scale(1);
  }

  /* Prevent zoom on focus */
  input:focus, textarea:focus, select:focus {
    font-size: 16px !important;
  }
}

/* Additional mobile optimizations */
@media screen and (max-width: 768px) {
  /* Prevent horizontal scroll */
  html, body {
    overflow-x: hidden;
    width: 100%;
    position: relative;
  }

  /* Ensure viewport stays fixed */
  .mobile-container {
    width: 100vw;
    max-width: 100vw;
    overflow-x: hidden;
  }

  /* Force proper button sizing on mobile */
  .mobile-container button {
    max-width: calc(100vw - 64px);
    box-sizing: border-box;
  }

  /* Ensure proper padding for all mobile content */
  .mobile-chat-content {
    padding-right: max(32px, env(safe-area-inset-right) + 24px) !important;
    box-sizing: border-box;
  }
}

@layer base {
  :root {
    /* Light mode variables */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 98%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 255 80% 75%;
    --primary-foreground: 240 100% 99%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 255 70% 65%;
    --accent-foreground: 240 100% 99%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 255 80% 75%;

    --radius: 0.75rem;
  }

  .dark {
    /* Dark mode variables */
    --background: 240 20% 12%;
    --foreground: 210 40% 98%;

    --card: 240 15% 14%;
    --card-foreground: 210 40% 98%;

    --popover: 240 15% 14%;
    --popover-foreground: 210 40% 98%;

    --primary: 255 80% 75%;
    --primary-foreground: 240 100% 99%;

    --secondary: 240 10% 18%;
    --secondary-foreground: 240 100% 99%;

    --muted: 240 15% 25%;
    --muted-foreground: 240 5% 75%;

    --accent: 255 70% 65%;
    --accent-foreground: 240 100% 99%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 240 15% 20%;
    --input: 240 15% 20%;
    --ring: 255 80% 75%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', sans-serif;
  }
}

/* Custom utilities */
@layer utilities {
  .waveform-bar {
    @apply w-1 bg-voicechain-purple rounded-full mx-[2px] transform transition-all;
  }

  /* Verification badge flower shape */
  .flower-badge {
    @apply rounded-full;
    position: relative;
    clip-path: path('M50,0 C60,35 90,40 100,50 C90,60 60,65 50,100 C40,65 10,60 0,50 C10,40 40,35 50,0');
  }

  /* Floating emoji animations - similar to Telegram reactions */
@keyframes float-up {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(0.8);
  }
  30% {
    opacity: 1;
    transform: translate(-50%, -80px) scale(1.2);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -120px) scale(0.8);
  }
}

.animate-float-up {
  animation: float-up 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* PWA and Safe Area Styles */
/* Default fallback values for older browsers */
.pb-safe {
  padding-bottom: 20px; /* Fallback */
  padding-bottom: env(safe-area-inset-bottom);
}

.pb-safe-bottom {
  padding-bottom: calc(6rem + 20px); /* Fallback */
  padding-bottom: calc(6rem + env(safe-area-inset-bottom));
}

.bottom-safe-offset {
  bottom: calc(5rem + 20px); /* Fallback */
  bottom: calc(5rem + env(safe-area-inset-bottom));
}

.h-safe-bottom {
  height: 20px; /* Fallback */
  height: env(safe-area-inset-bottom);
}

.h-safe-top {
  height: 44px; /* Fallback for iOS status bar */
  height: env(safe-area-inset-top);
  min-height: 24px; /* Minimum for Android */
}

.pt-safe-top {
  padding-top: 44px; /* Fallback */
  padding-top: env(safe-area-inset-top);
}

.mt-safe-top {
  margin-top: 44px; /* Fallback */
  margin-top: env(safe-area-inset-top);
}

/* Enhanced support check */
@supports (padding: env(safe-area-inset-top)) {
  .pb-safe {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .pb-safe-bottom {
    padding-bottom: calc(6rem + env(safe-area-inset-bottom));
  }

  .bottom-safe-offset {
    bottom: calc(5rem + env(safe-area-inset-bottom));
  }

  .h-safe-bottom {
    height: env(safe-area-inset-bottom);
  }

  .h-safe-top {
    height: env(safe-area-inset-top);
  }

  .pt-safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .mt-safe-top {
    margin-top: env(safe-area-inset-top);
  }
}

/* PWA Display Mode Styles */
@media (display-mode: standalone) {
  body {
    /* Hide scrollbars in standalone mode for cleaner look */
    -webkit-overflow-scrolling: touch;
  }

  /* Ensure proper spacing for PWA */
  .pwa-safe-area {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* PWA Modal Fixes */
  [data-vaul-drawer] {
    margin-bottom: env(safe-area-inset-bottom, 0px) !important;
    padding-bottom: max(1rem, env(safe-area-inset-bottom)) !important;
  }

  /* PWA Audio Fixes */
  audio {
    /* Prevent audio from being cut off */
    -webkit-playsinline: true;
    playsinline: true;
  }

  /* PWA Dialog Fixes */
  [role="dialog"] {
    margin-bottom: env(safe-area-inset-bottom, 0px);
  }
}

/* iOS PWA specific styles */
@media (display-mode: standalone) and (-webkit-touch-callout: none) {
  /* iOS specific PWA styles */
  body {
    -webkit-user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
  }

  /* Prevent zoom on input focus */
  input, textarea, select {
    font-size: 16px !important;
  }
}

/* Android PWA specific styles */
@media (display-mode: standalone) and (not (-webkit-touch-callout: none)) {
  /* Android specific PWA styles */
  body {
    overscroll-behavior: none;
  }
}

/* Splash screen styles for PWA */
.pwa-splash {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: hsl(var(--background));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 1;
  transition: opacity 0.3s ease-out;
}

.pwa-splash.fade-out {
  opacity: 0;
  pointer-events: none;
}

/* PWA install button styles */
.pwa-install-button {
  background: hsl(var(--primary));
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  color: hsl(var(--primary-foreground));
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px hsl(var(--primary) / 0.3);
}

.pwa-install-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px hsl(var(--primary) / 0.4);
}

.pwa-install-button:active {
  transform: translateY(0);
}

/* Notification styles for PWA */
.pwa-notification {
  position: fixed;
  top: env(safe-area-inset-top, 20px);
  left: 50%;
  transform: translateX(-50%);
  background: hsl(var(--popover));
  color: hsl(var(--popover-foreground));
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  z-index: 10000;
  backdrop-filter: blur(10px);
  border: 1px solid hsl(var(--border));
}

/* Hide scrollbars for horizontal scrolling */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
}