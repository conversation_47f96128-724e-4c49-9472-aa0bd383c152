import { toast } from '@/components/ui/sonner';
import walletService, { WalletData } from './walletService';
import walletManager from './walletManager';
import { supabase } from '@/integrations/supabase/client';

// User interface
export interface User {
  id: string;
  email: string;
  username: string;
  displayName: string;
  bio?: string;
  profilePicture?: string;
  bannerPicture?: string;
  walletAddress: string;
  wallet?: WalletData;
  isEmailVerified: boolean;
  createdAt: Date;
  lastLoginAt?: Date;
}

// Registration data interface
export interface RegistrationData {
  email: string;
  username: string;
  displayName: string;
  password: string;
  bio?: string;
}

// Login data interface
export interface LoginData {
  email: string;
  password: string;
  rememberMe?: boolean;
}

// Password reset data interface
export interface PasswordResetData {
  email: string;
}

// New password data interface
export interface NewPasswordData {
  token: string;
  password: string;
  confirmPassword: string;
}

// Authentication response interface
export interface AuthResponse {
  user: User;
  token: string;
  wallet: WalletData;
}

// Local storage keys
const AUTH_TOKEN_KEY = 'voicechain_auth_token';
const USER_KEY = 'voicechain_user';

// Register a new user
export const register = async (data: RegistrationData): Promise<AuthResponse> => {
  // Validate input
  if (!data.email || !data.username || !data.displayName || !data.password) {
    throw new Error('All fields are required');
  }

  try {
    // Register with Supabase
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: data.email,
      password: data.password,
      options: {
        data: {
          username: data.username,
          display_name: data.displayName,
          bio: data.bio || ''
        }
      }
    });

    if (authError) {
      console.error('Error registering with Supabase:', authError);
      throw new Error(authError.message);
    }

    if (!authData.user) {
      throw new Error('Failed to create user');
    }

    // Get the user's wallet from the wallet manager
    // This ensures the same user always gets the same wallet address on any device
    const wallet = walletManager.getWallet(authData.user.id);
    
    console.log(`New user ${data.username} registered with wallet address: ${wallet.address}`);

    // Store wallet data in local storage with user ID as key for persistence
    localStorage.setItem(`wallet_${authData.user.id}`, JSON.stringify(wallet));

    // Create user profile in Supabase profiles table
    const { error: profileError } = await supabase
      .from('profiles')
      .insert({
        id: authData.user.id,
        username: data.username,
        display_name: data.displayName,
        bio: data.bio || '',
        wallet_address: wallet.address,
        avatar_url: '',
        is_verified: false, // Ensure new users are not verified by default
        verification_type: null, // No verification type for new users
        created_at: new Date().toISOString()
      });

    if (profileError) {
      console.error('Error creating profile in Supabase:', profileError);
      // Continue anyway, as the auth user was created
    }

    // Create user object
    const user: User = {
      id: authData.user.id,
      email: data.email,
      username: data.username,
      displayName: data.displayName,
      bio: data.bio || '',
      walletAddress: wallet.address,
      wallet: wallet,
      isEmailVerified: false,
      createdAt: new Date(),
      lastLoginAt: new Date()
    };

    // Store user in local storage
    localStorage.setItem(USER_KEY, JSON.stringify(user));

    // Return auth response
    return {
      user,
      token: authData.session?.access_token || '',
      wallet
    };
  } catch (error) {
    console.error('Error registering user:', error);
    throw error;
  }
};

// Login user
export const login = async (data: LoginData): Promise<AuthResponse> => {
  // Validate input
  if (!data.email || !data.password) {
    throw new Error('Email and password are required');
  }

  try {
    // Login with Supabase
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: data.email,
      password: data.password
    });

    if (authError) {
      console.error('Error logging in with Supabase:', authError);
      throw new Error(authError.message);
    }

    if (!authData.user) {
      throw new Error('User not found');
    }

    // Get user profile from Supabase
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single();

    if (profileError) {
      console.error('Error fetching profile from Supabase:', profileError);
      throw new Error('Failed to fetch user profile');
    }

    // Get the user's wallet from the wallet manager
    // This ensures the same user always gets the same wallet address on any device
    const wallet = walletManager.getWallet(authData.user.id);

    console.log(`Login: User ID ${authData.user.id} has wallet address ${wallet.address}`);

    // Always update the profile with the deterministic wallet address to ensure consistency
    try {
      console.log('Updating profile with deterministic wallet address');

      // Update the profile with the deterministic wallet address
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ wallet_address: wallet.address })
        .eq('id', authData.user.id);

      if (updateError) {
        console.error('Error updating wallet address in profile:', updateError);
      } else {
        console.log(`Successfully updated wallet address to ${wallet.address} for user ${authData.user.id}`);
      }
    } catch (walletUpdateError) {
      console.error('Exception updating wallet address:', walletUpdateError);
    }

    // Store wallet data in local storage with user ID as key for persistence
    localStorage.setItem(`wallet_${authData.user.id}`, JSON.stringify(wallet));

    // Create user object
    const user: User = {
      id: authData.user.id,
      email: authData.user.email || data.email,
      username: profileData.username || authData.user.user_metadata.username,
      displayName: profileData.display_name || authData.user.user_metadata.display_name,
      bio: profileData.bio || authData.user.user_metadata.bio || '',
      profilePicture: profileData.avatar_url || '',
      walletAddress: profileData.wallet_address || wallet.address,
      wallet: wallet,
      isEmailVerified: authData.user.email_confirmed_at !== null,
      createdAt: new Date(profileData.created_at || authData.user.created_at),
      lastLoginAt: new Date()
    };

    // Store user in local storage
    localStorage.setItem(USER_KEY, JSON.stringify(user));

    // Return auth response
    return {
      user,
      token: authData.session.access_token,
      wallet
    };
  } catch (error) {
    console.error('Error logging in:', error);
    throw error;
  }
};

// Logout user
export const logout = async (): Promise<void> => {
  try {
    // Sign out from Supabase
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error('Error signing out from Supabase:', error);
    }
  } catch (error) {
    console.error('Error in logout:', error);
  } finally {
    // Remove user from local storage
    localStorage.removeItem(AUTH_TOKEN_KEY);
    localStorage.removeItem(USER_KEY);
  }
};

// Check if user is authenticated
export const isAuthenticated = async (): Promise<boolean> => {
  try {
    const { data } = await supabase.auth.getSession();
    return !!data.session;
  } catch (error) {
    console.error('Error checking authentication:', error);
    return false;
  }
};

// Get current user
export const getCurrentUser = async (): Promise<User | null> => {
  try {
    // First check local storage for cached user
    const userJson = localStorage.getItem(USER_KEY);
    if (userJson) {
      return JSON.parse(userJson);
    }

    // If not in local storage, get from Supabase
    const { data: authData } = await supabase.auth.getUser();
    if (!authData.user) {
      return null;
    }

    // Get user profile from Supabase
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single();

    if (profileError) {
      console.error('Error fetching profile from Supabase:', profileError);
      return null;
    }

    // Check if this user is the owner based on verification status
    const isOwner = profileData.is_verified && profileData.verification_type === 'owner';

    // Store the owner status in localStorage for the wallet manager to use
    if (isOwner) {
      localStorage.setItem('is_owner', 'true');
    } else {
      localStorage.removeItem('is_owner');
    }

    // Get the user's wallet from the wallet manager
    // This ensures the same user always gets the same wallet address on any device
    const wallet = walletManager.getWallet(authData.user.id);

    console.log(`GetCurrentUser: User ID ${authData.user.id} has wallet address ${wallet.address}`);

    // Always update the profile with the deterministic wallet address to ensure consistency
    try {
      console.log('Updating profile with deterministic wallet address');

      // Update the profile with the deterministic wallet address
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ wallet_address: wallet.address })
        .eq('id', authData.user.id);

      if (updateError) {
        console.error('Error updating wallet address in profile:', updateError);
      } else {
        console.log(`Successfully updated wallet address to ${wallet.address} for user ${authData.user.id}`);
      }
    } catch (walletUpdateError) {
      console.error('Exception updating wallet address:', walletUpdateError);
    }

    // Store wallet data in local storage with user ID as key for persistence
    localStorage.setItem(`wallet_${authData.user.id}`, JSON.stringify(wallet));

    // Create user object
    const user: User = {
      id: authData.user.id,
      email: authData.user.email || '',
      username: profileData.username || authData.user.user_metadata.username,
      displayName: profileData.display_name || authData.user.user_metadata.display_name,
      bio: profileData.bio || authData.user.user_metadata.bio || '',
      profilePicture: profileData.avatar_url || '',
      walletAddress: profileData.wallet_address || wallet.address,
      wallet: wallet,
      isEmailVerified: authData.user.email_confirmed_at !== null,
      createdAt: new Date(profileData.created_at || authData.user.created_at),
      lastLoginAt: new Date()
    };

    // Cache user in local storage
    localStorage.setItem(USER_KEY, JSON.stringify(user));

    return user;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
};

// Get current user wallet
export const getCurrentUserWallet = async (): Promise<WalletData | null> => {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return null;
    }

    // Get the user's wallet from the wallet manager
    // This ensures the same user always gets the same wallet address on any device
    const wallet = walletManager.getWallet(user.id);

    // Store wallet data in local storage with user ID as key for persistence
    localStorage.setItem(`wallet_${user.id}`, JSON.stringify(wallet));

    // Check if the wallet address matches the one in the user object
    if (user.walletAddress && user.walletAddress !== wallet.address) {
      console.warn('User wallet address mismatch. Updating to deterministic wallet.');

      // Update the user's wallet address in Supabase
      await supabase
        .from('profiles')
        .update({ wallet_address: wallet.address })
        .eq('id', user.id);
    }

    return wallet;
  } catch (error) {
    console.error('Error getting current user wallet:', error);
    return null;
  }
};

// Request password reset
export const requestPasswordReset = async (data: PasswordResetData): Promise<void> => {
  // Validate input
  if (!data.email) {
    throw new Error('Email is required');
  }

  try {
    // Request password reset with Supabase
    const { error } = await supabase.auth.resetPasswordForEmail(data.email, {
      redirectTo: window.location.origin + '/reset-password'
    });

    if (error) {
      console.error('Error requesting password reset with Supabase:', error);
      throw new Error(error.message);
    }

    // For security, don't reveal if the email exists or not
    console.log(`Password reset email sent to ${data.email}`);
  } catch (error) {
    console.error('Error requesting password reset:', error);
    throw error;
  }
};

// Reset password
export const resetPassword = async (data: NewPasswordData): Promise<void> => {
  // Validate input
  if (!data.password || !data.confirmPassword) {
    throw new Error('All fields are required');
  }

  if (data.password !== data.confirmPassword) {
    throw new Error('Passwords do not match');
  }

  try {
    // Update password with Supabase
    const { error } = await supabase.auth.updateUser({
      password: data.password
    });

    if (error) {
      console.error('Error resetting password with Supabase:', error);
      throw new Error(error.message);
    }

    console.log('Password reset successful');
  } catch (error) {
    console.error('Error resetting password:', error);
    throw error;
  }
};

// Update user profile
export const updateProfile = async (userId: string, updates: Partial<User>): Promise<User> => {
  try {
    // Get current user
    const currentUser = await getCurrentUser();
    if (!currentUser || currentUser.id !== userId) {
      throw new Error('User not found or unauthorized');
    }

    // Update user metadata in Supabase Auth
    const { error: authError } = await supabase.auth.updateUser({
      data: {
        username: updates.username,
        display_name: updates.displayName,
        bio: updates.bio
      }
    });

    if (authError) {
      console.error('Error updating user metadata in Supabase Auth:', authError);
      // Continue anyway, as we can still update the profile
    }

    // Update user profile in Supabase
    const { error: profileError } = await supabase
      .from('profiles')
      .update({
        username: updates.username,
        display_name: updates.displayName,
        bio: updates.bio,
        avatar_url: updates.profilePicture
      })
      .eq('id', userId);

    if (profileError) {
      console.error('Error updating profile in Supabase:', profileError);
      throw new Error('Failed to update profile in database');
    }

    // Update user object
    const updatedUser: User = {
      ...currentUser,
      ...updates,
      // Don't allow updating these fields
      id: currentUser.id,
      email: currentUser.email,
      walletAddress: currentUser.walletAddress,
      isEmailVerified: currentUser.isEmailVerified,
      createdAt: currentUser.createdAt
    };

    // Update user in local storage
    localStorage.setItem(USER_KEY, JSON.stringify(updatedUser));

    return updatedUser;
  } catch (error) {
    console.error('Error updating profile:', error);
    throw error;
  }
};

export default {
  register,
  login,
  logout,
  isAuthenticated,
  getCurrentUser,
  getCurrentUserWallet,
  requestPasswordReset,
  resetPassword,
  updateProfile
};
