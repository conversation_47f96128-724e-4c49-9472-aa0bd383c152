
import React from 'react';
import VoiceMessageWithoutChannel from './VoiceMessageWithoutChannel';
import { VoiceMessageProps } from '@/types/voice-message';
import voiceMessageService from '@/services/voiceMessageService.js';
import { toast } from '@/components/ui/sonner';
import { useNavigate } from 'react-router-dom';
import { useProfiles } from '@/contexts/SimpleProfileContext';

interface VoiceMessageListProps {
  messages: VoiceMessageProps[];
  onReply?: (parentId: string) => void;
  connectedAccount: string;
}

const VoiceMessageList: React.FC<VoiceMessageListProps> = ({
  messages,
  onReply,
  connectedAccount
}) => {
  const navigate = useNavigate();
  const { incrementPostCount } = useProfiles();

  // Make sure messages is always an array
  const safeMessages = Array.isArray(messages) ? messages : [];

  // Handle delete post
  const handleDeletePost = (postId: string) => {
    try {
      console.log("Deleting post:", postId);

      // Call the service to delete the message in Supabase
      voiceMessageService.deleteVoiceMessage(postId, connectedAccount)
        .then(success => {
          if (!success) {
            console.error('Server-side deletion failed');
            toast.error('Failed to delete message');
          }
        })
        .catch(error => {
          console.error('Error in server-side deletion:', error);
          toast.error('Failed to delete message');
        });

      // Show success message
      toast.success('Message deleted');

      // Remove the post from the DOM immediately
      const postElement = document.getElementById(`post-${postId}`);
      if (postElement) {
        postElement.style.opacity = '0';
        setTimeout(() => {
          postElement.style.display = 'none';
        }, 300);
      }

      // Also update localStorage for immediate UI reflection
      try {
        // Get all saved posts
        const allPosts = JSON.parse(localStorage.getItem('all_posts') || '[]');
        
        // Filter out the deleted post
        const updatedPosts = allPosts.filter((post: any) => post.id !== postId);
        
        // Save back to localStorage
        localStorage.setItem('all_posts', JSON.stringify(updatedPosts));
        
        // Also add to deleted_posts
        const deletedPosts = JSON.parse(localStorage.getItem('deleted_posts') || '[]');
        if (!deletedPosts.includes(postId)) {
          deletedPosts.push(postId);
          localStorage.setItem('deleted_posts', JSON.stringify(deletedPosts));
        }
      } catch (error) {
        console.error('Error updating localStorage:', error);
      }

      // If we're on the post detail page, go back to home
      if (window.location.pathname.includes('/post/')) {
        navigate('/');
      }
    } catch (error) {
      console.error('Error deleting message:', error);
      toast.error('Failed to delete message');
    }
  };

  // Handle pin post  
  const handlePinPost = (postId: string, isPinned: boolean) => {
    try {
      // Call the service to pin/unpin the post
      voiceMessageService.togglePinMessage(postId, connectedAccount, isPinned).then(success => {

        if (success) {
          toast.success(isPinned ? 'Post pinned to your profile' : 'Post unpinned successfully');
          
          // Save to localStorage for immediate UI reflection
          try {
            const pinnedPosts = JSON.parse(localStorage.getItem(`pinned_posts_${connectedAccount}`) || '[]');
            if (isPinned) {
              // If pinning, add to the array
              if (!pinnedPosts.includes(postId)) {
                pinnedPosts.push(postId);
              }
            } else {
              // If unpinning, remove from the array
              const index = pinnedPosts.indexOf(postId);
              if (index > -1) {
                pinnedPosts.splice(index, 1);
              }
            }
            localStorage.setItem(`pinned_posts_${connectedAccount}`, JSON.stringify(pinnedPosts));
            
            // Also update the message in all_posts
            const allPosts = JSON.parse(localStorage.getItem('all_posts') || '[]');
            const updatedPosts = allPosts.map((post: any) => {
              if (post.id === postId) {
                return { ...post, isPinned };
              }
              return post;
            });
            localStorage.setItem('all_posts', JSON.stringify(updatedPosts));
          } catch (error) {
            console.error('Error updating localStorage:', error);
          }
        } else {
          toast.error('Failed to update pin status');
        }
      }).catch(error => {
        console.error('Error updating pin status:', error);
        toast.error('Failed to update pin status');
      });
    } catch (error) {
      console.error('Error updating pin status:', error);
      toast.error('Failed to update pin status');
    }
  };

  // Save posts to localStorage for user profile access
  React.useEffect(() => {
    if (connectedAccount && safeMessages.length > 0) {
      try {
        // First, get existing posts from localStorage
        const existingPosts = JSON.parse(localStorage.getItem('all_posts') || '[]');
        
        // Create a map of existing posts by ID for quick lookup
        const existingPostsMap = new Map();
        existingPosts.forEach((post: any) => {
          existingPostsMap.set(post.id, post);
        });
        
        // Add new messages that aren't already in existingPosts
        let hasNewPosts = false;
        safeMessages.forEach(message => {
          // Make sure message has the connectedAccount set as userAddress if it matches
          if (message.userAddress && message.userAddress.toLowerCase() === connectedAccount.toLowerCase()) {
            const messageToStore = { ...message };
            
            // Convert Date objects to strings for localStorage
            if (messageToStore.timestamp instanceof Date) {
              messageToStore.timestamp = messageToStore.timestamp.toISOString();
            }
            
            if (!existingPostsMap.has(messageToStore.id)) {
              existingPosts.push(messageToStore);
              hasNewPosts = true;
            }
          }
        });
        
        // Only update localStorage if there are new posts
        if (hasNewPosts) {
          localStorage.setItem('all_posts', JSON.stringify(existingPosts));
          console.log(`Updated localStorage with ${existingPosts.length} posts`);
          
          // Update post count in the profile context
          if (typeof incrementPostCount === 'function') {
            const userPosts = existingPosts.filter((post: any) => 
              post.userAddress && post.userAddress.toLowerCase() === connectedAccount.toLowerCase()
            );
            incrementPostCount(connectedAccount, userPosts.length);
          }
        }
      } catch (error) {
        console.error('Error saving posts to localStorage:', error);
      }
    }
  }, [connectedAccount, safeMessages, incrementPostCount]);

  if (!safeMessages.length) {
    return (
      <div className="text-center py-10">
        <p className="text-muted-foreground">No voice messages to display.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {safeMessages.map((message) => (
        <VoiceMessageWithoutChannel
          key={message.id}
          {...message}
          onReply={onReply}
          onDelete={handleDeletePost}
          onPin={handlePinPost}
        />
      ))}
    </div>
  );
};

export default VoiceMessageList;
