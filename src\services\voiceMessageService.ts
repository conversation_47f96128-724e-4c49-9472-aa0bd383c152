
import { v4 as uuidv4 } from 'uuid';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/sonner';
import { VoiceMessageProps } from '@/types/voice-message';
import audioStorageService from './audioStorageService';

/**
 * Filter out deleted posts from a list
 * @param posts - The list of posts to filter
 * @returns - Filtered list without deleted posts
 */
export function filterDeletedPosts(posts: any[]): any[] {
  if (!posts || !Array.isArray(posts)) {
    return [];
  }

  // Filter out any posts that have been marked as deleted in the database
  return posts.filter(post => !post.deleted_at);
}

/**
 * Normalize a user address for database consistency
 * @param userAddress - The user wallet address
 * @returns - The normalized profile ID
 */
async function getNormalizedProfileId(userAddress: string): Promise<string> {
  try {
    // Ensure userAddress is in the correct format for the database
    const addressStr = typeof userAddress === 'string' ? userAddress : String(userAddress);
    let profileId = addressStr.toLowerCase();

    // Check if the address is an Ethereum address
    if (profileId.startsWith('0x')) {
      console.log('Converting Ethereum address to UUID format for database compatibility');

      // Try to find the user's UUID from profiles table
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .ilike('wallet_address', profileId)
        .maybeSingle();

      if (profileError) {
        console.error('Error finding profile by wallet address:', profileError);
      }

      if (profileData && profileData.id) {
        // Use the UUID from the profile
        profileId = profileData.id;
        console.log(`Found matching profile ID: ${profileId}`);
      } else {
        // If we can't find a matching profile, create one
        console.log('No matching profile found for wallet address, creating new profile');
        
        // Generate a UUID for the new profile
        const newProfileId = uuidv4();
        
        // Insert with explicit UUID
        const { data: newProfile, error: createError } = await supabase
          .from('profiles')
          .insert({
            id: newProfileId,
            wallet_address: profileId,
            username: `user_${profileId.substring(2, 8)}`,
            display_name: `User ${profileId.substring(2, 8)}`,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();
          
        if (createError) {
          console.error('Error creating profile:', createError);
        } else if (newProfile) {
          profileId = newProfile.id;
          console.log(`Created new profile with ID: ${profileId}`);
        }
      }
    }

    return profileId;
  } catch (error) {
    console.error('Error normalizing profile ID:', error);
    return typeof userAddress === 'string' ? userAddress.toLowerCase() : String(userAddress).toLowerCase();
  }
}

/**
 * Save a voice message to Supabase
 * @param audioUrl - The audio URL
 * @param transcript - The transcript
 * @param userAddress - The user wallet address
 * @param duration - The duration of the audio
 * @param media - Optional media attachments
 * @param customId - Optional custom ID for the message
 * @returns - The saved voice message
 */
export async function saveVoiceMessage(
  audioUrl: string,
  transcript: string,
  userAddress: string,
  duration: number,
  media: { url: string; type: 'image' | 'video'; id: string }[] = [],
  customId: string | null = null
): Promise<VoiceMessageProps> {
  try {
    console.log('Saving voice message:', { audioUrl, transcript, userAddress, duration });

    // Use the provided custom ID or generate a new UUID
    const messageId = customId || uuidv4();
    
    // Get normalized profile ID
    const addressStr = typeof userAddress === 'string' ? userAddress : String(userAddress);
    const profileId = await getNormalizedProfileId(addressStr);

    // Create message object for database
    const message = {
      id: messageId,
      audio_url: audioUrl,
      transcript: transcript || '',
      profile_id: profileId,
      audio_duration: duration,
      created_at: new Date().toISOString(),
      is_pinned: false
    };

    // Store in Supabase
    console.log('Storing message in Supabase:', message);
    const { error } = await supabase
      .from('voice_messages')
      .insert(message);

    if (error) {
      console.error('Error saving voice message to Supabase:', error);
      throw new Error(`Failed to save voice message: ${error.message}`);
    }

    // If there's media, store it in the database too
    if (media && media.length > 0) {
      const mediaItems = media.map(item => ({
        id: item.id || uuidv4(),
        voice_message_id: messageId,
        url: item.url,
        type: item.type,
        created_at: new Date().toISOString()
      }));

      const { error: mediaError } = await supabase
        .from('voice_message_media')
        .insert(mediaItems);

      if (mediaError) {
        console.warn('Error storing media attachments:', mediaError);
      }
    }

    // Update the profile's post count
    try {
      const { data: profileData } = await supabase
        .from('profiles')
        .select('post_count')
        .eq('id', profileId)
        .single();

      if (profileData) {
        const currentCount = profileData.post_count || 0;
        
        await supabase
          .from('profiles')
          .update({ post_count: currentCount + 1 })
          .eq('id', profileId);
          
        console.log(`Updated post count for user ${profileId} to ${currentCount + 1}`);
      }
    } catch (countError) {
      console.error('Error updating post count:', countError);
    }

    // Return the voice message object for the UI
    const result: VoiceMessageProps = {
      id: messageId,
      userAddress: profileId,
      audioUrl,
      transcript,
      timestamp: new Date(),
      duration,
      isPinned: false,
      media: media || [],
      replies: []
    };

    return result;
  } catch (error) {
    console.error('Error saving voice message:', error);
    toast.error('Failed to save your voice message');
    throw error;
  }
}

/**
 * Get voice messages for a user
 * @param userAddress - The user wallet address
 * @returns - The user's voice messages
 */
export async function getUserVoiceMessages(userAddress: string): Promise<VoiceMessageProps[]> {
  try {
    // Get normalized profile ID
    const addressStr = typeof userAddress === 'string' ? userAddress : String(userAddress);
    const profileId = await getNormalizedProfileId(addressStr);

    // Get messages from database
    const { data: messages, error } = await supabase
      .from('voice_messages')
      .select('*')
      .eq('profile_id', profileId)
      .is('deleted_at', null)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error getting user voice messages:', error);
      throw error;
    }

    if (!messages || messages.length === 0) {
      return [];
    }

    // Convert database messages to VoiceMessageProps
    const voiceMessages: VoiceMessageProps[] = await Promise.all(
      messages.map(async (message) => {
        // Get media for this message
        const { data: mediaData } = await supabase
          .from('voice_message_media')
          .select('*')
          .eq('voice_message_id', message.id);

        // Get replies for this message
        const { data: repliesData } = await supabase
          .from('voice_messages')
          .select('*')
          .eq('parent_id', message.id)
          .is('deleted_at', null)
          .order('created_at', { ascending: true });

        // Convert replies recursively
        const replies: VoiceMessageProps[] = repliesData 
          ? await Promise.all(repliesData.map(async (reply) => {
              // Get media for this reply
              const { data: replyMediaData } = await supabase
                .from('voice_message_media')
                .select('*')
                .eq('voice_message_id', reply.id);

              return {
                id: reply.id,
                userAddress: reply.profile_id,
                audioUrl: reply.audio_url,
                transcript: reply.transcript || '',
                timestamp: new Date(reply.created_at),
                duration: reply.audio_duration,
                isPinned: reply.is_pinned,
                media: replyMediaData?.map(m => ({
                  id: m.id,
                  url: m.url,
                  type: m.type as 'image' | 'video'
                })) || [],
                replies: [],
                parentId: message.id,
                isReply: true
              };
            }))
          : [];

        return {
          id: message.id,
          userAddress: message.profile_id,
          audioUrl: message.audio_url,
          transcript: message.transcript || '',
          timestamp: new Date(message.created_at),
          duration: message.audio_duration,
          isPinned: message.is_pinned,
          media: mediaData?.map(m => ({
            id: m.id,
            url: m.url,
            type: m.type as 'image' | 'video'
          })) || [],
          replies,
          channelId: message.channel_id
        };
      })
    );

    return voiceMessages;
  } catch (error) {
    console.error('Error getting user voice messages:', error);
    return [];
  }
}

/**
 * Delete a voice message
 * @param messageId - The ID of the message to delete
 * @param userAddress - The user wallet address (for authorization)
 * @returns - Whether the deletion was successful
 */
export async function deleteVoiceMessage(messageId: string, userAddress: string): Promise<boolean> {
  try {
    console.log(`Deleting voice message ${messageId} by user ${userAddress}`);

    // Ensure we have valid inputs
    if (!messageId || !userAddress) {
      console.error('Invalid inputs for deleteVoiceMessage:', { messageId, userAddress });
      return false;
    }

    // Get normalized profile ID
    const addressStr = typeof userAddress === 'string' ? userAddress : String(userAddress);
    const profileId = await getNormalizedProfileId(addressStr);

    // First check if the message exists and if it's already deleted
    const { data: messageData, error: messageError } = await supabase
      .from('voice_messages')
      .select('profile_id, deleted_at')
      .eq('id', messageId)
      .maybeSingle();

    if (messageError) {
      console.error('Error checking message:', messageError);
      return false;
    }

    // If message not found, consider it already deleted
    if (!messageData) {
      console.log('Message not found, considering it already deleted');
      return true;
    }

    // If message is already deleted, return success
    if (messageData.deleted_at) {
      console.log('Message already marked as deleted');
      return true;
    }

    // Check if user is the owner or the admin
    const isOwner = messageData.profile_id === profileId;
    const isAdmin = profileId === '2cd24c86-d3c5-4406-a92c-1f0892495e0a'; // Owner's UUID

    if (!isOwner && !isAdmin) {
      console.error('User is not authorized to delete this message');
      return false;
    }

    // Soft delete the message by setting deleted_at
    const { error: deleteError } = await supabase
      .from('voice_messages')
      .update({ deleted_at: new Date().toISOString() })
      .eq('id', messageId);

    if (deleteError) {
      console.error('Error deleting voice message:', deleteError);
      return false;
    }

    // Decrement the post count if the user is the owner
    if (isOwner) {
      try {
        const { data: profileData } = await supabase
          .from('profiles')
          .select('post_count')
          .eq('id', profileId)
          .single();
  
        if (profileData && profileData.post_count > 0) {
          await supabase
            .from('profiles')
            .update({ post_count: profileData.post_count - 1 })
            .eq('id', profileId);
            
          console.log(`Updated post count for user ${profileId} to ${profileData.post_count - 1}`);
        }
      } catch (countError) {
        console.error('Error updating post count:', countError);
      }
    }

    console.log('Message deleted successfully');
    return true;
  } catch (error) {
    console.error('Error deleting voice message:', error);
    return false;
  }
}

/**
 * Toggle pin status of a voice message
 * @param messageId - The ID of the message to pin/unpin
 * @param userAddress - The user wallet address (for authorization)
 * @param isPinned - Whether to pin or unpin the message
 * @returns - Whether the operation was successful
 */
export async function togglePinMessage(messageId: string, userAddress: string, isPinned: boolean): Promise<boolean> {
  try {
    console.log(`${isPinned ? 'Unpinning' : 'Pinning'} voice message ${messageId} by user ${userAddress}`);

    // Get normalized profile ID
    const addressStr = typeof userAddress === 'string' ? userAddress : String(userAddress);
    const profileId = await getNormalizedProfileId(addressStr);

    // First check if the user is the owner of the message
    const { data: messageData, error: messageError } = await supabase
      .from('voice_messages')
      .select('profile_id')
      .eq('id', messageId)
      .maybeSingle();

    if (messageError) {
      console.error('Error checking message ownership:', messageError);
      return false;
    }

    // If message not found, return false
    if (!messageData) {
      console.log('Message not found');
      return false;
    }

    // Check if user is the owner
    const isOwner = messageData.profile_id === profileId;

    if (!isOwner) {
      console.error('User is not authorized to pin/unpin this message');
      return false;
    }

    // If we're pinning, first unpin all other messages
    if (!isPinned) {
      // Call the Supabase function to unpin all user messages
      await supabase.rpc('unpin_all_user_messages', { user_id: profileId });
    }

    // Update the pin status
    const { error: updateError } = await supabase
      .from('voice_messages')
      .update({ is_pinned: !isPinned })
      .eq('id', messageId);

    if (updateError) {
      console.error('Error updating pin status:', updateError);
      return false;
    }

    console.log('Pin status updated successfully');
    return true;
  } catch (error) {
    console.error('Error toggling pin status:', error);
    return false;
  }
}

/**
 * Save a reply to a voice message
 * @param audioUrl - The audio URL
 * @param transcript - The transcript
 * @param userAddress - The user wallet address
 * @param duration - The duration of the audio
 * @param parentId - The ID of the parent message
 * @param media - Optional media attachments
 * @returns - The saved reply
 */
export async function saveReply(
  audioUrl: string,
  transcript: string,
  userAddress: string,
  duration: number,
  parentId: string,
  media: { url: string; type: 'image' | 'video'; id: string }[] = []
): Promise<VoiceMessageProps> {
  try {
    console.log('Saving reply:', { audioUrl, transcript, userAddress, duration, parentId });

    const messageId = uuidv4();
    const addressStr = typeof userAddress === 'string' ? userAddress : String(userAddress);
    const profileId = await getNormalizedProfileId(addressStr);

    // Create reply object for database
    const reply = {
      id: messageId,
      audio_url: audioUrl,
      transcript: transcript || '',
      profile_id: profileId,
      audio_duration: duration,
      parent_id: parentId,
      created_at: new Date().toISOString(),
      is_pinned: false
    };

    // Store in Supabase
    const { error } = await supabase
      .from('voice_messages')
      .insert(reply);

    if (error) {
      console.error('Error saving reply to Supabase:', error);
      throw new Error(`Failed to save reply: ${error.message}`);
    }

    // If there's media, store it in the database too
    if (media && media.length > 0) {
      const mediaItems = media.map(item => ({
        id: item.id || uuidv4(),
        voice_message_id: messageId,
        url: item.url,
        type: item.type,
        created_at: new Date().toISOString()
      }));

      const { error: mediaError } = await supabase
        .from('voice_message_media')
        .insert(mediaItems);

      if (mediaError) {
        console.warn('Error storing media attachments:', mediaError);
      }
    }

    // Return the reply object for the UI
    const result: VoiceMessageProps = {
      id: messageId,
      userAddress: profileId,
      audioUrl,
      transcript,
      timestamp: new Date(),
      duration,
      isPinned: false,
      media: media || [],
      replies: [],
      parentId,
      isReply: true
    };

    return result;
  } catch (error) {
    console.error('Error saving reply:', error);
    toast.error('Failed to save your reply');
    throw error;
  }
}

// Get all voice messages
export async function getAllVoiceMessages(): Promise<VoiceMessageProps[]> {
  try {
    // Get messages from database
    const { data: messages, error } = await supabase
      .from('voice_messages')
      .select('*')
      .is('deleted_at', null)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error getting all voice messages:', error);
      throw error;
    }

    if (!messages || messages.length === 0) {
      return [];
    }

    // Convert database messages to VoiceMessageProps
    const voiceMessages: VoiceMessageProps[] = await Promise.all(
      messages.map(async (message) => {
        // Get media for this message
        const { data: mediaData } = await supabase
          .from('voice_message_media')
          .select('*')
          .eq('voice_message_id', message.id);

        // Get replies for this message
        const { data: repliesData } = await supabase
          .from('voice_messages')
          .select('*')
          .eq('parent_id', message.id)
          .is('deleted_at', null)
          .order('created_at', { ascending: true });

        // Convert replies recursively
        const replies: VoiceMessageProps[] = repliesData 
          ? await Promise.all(repliesData.map(async (reply) => {
              // Get media for this reply
              const { data: replyMediaData } = await supabase
                .from('voice_message_media')
                .select('*')
                .eq('voice_message_id', reply.id);

              return {
                id: reply.id,
                userAddress: reply.profile_id,
                audioUrl: reply.audio_url,
                transcript: reply.transcript || '',
                timestamp: new Date(reply.created_at),
                duration: reply.audio_duration,
                isPinned: reply.is_pinned,
                media: replyMediaData?.map(m => ({
                  id: m.id,
                  url: m.url,
                  type: m.type as 'image' | 'video'
                })) || [],
                replies: [],
                parentId: message.id,
                isReply: true
              };
            }))
          : [];

        return {
          id: message.id,
          userAddress: message.profile_id,
          audioUrl: message.audio_url,
          transcript: message.transcript || '',
          timestamp: new Date(message.created_at),
          duration: message.audio_duration,
          isPinned: message.is_pinned,
          media: mediaData?.map(m => ({
            id: m.id,
            url: m.url,
            type: m.type as 'image' | 'video'
          })) || [],
          replies,
          channelId: message.channel_id
        };
      })
    );

    return voiceMessages;
  } catch (error) {
    console.error('Error getting all voice messages:', error);
    return [];
  }
}

/**
 * Save a summon message
 * @param audioUrl - The audio URL
 * @param transcript - The transcript
 * @param userAddress - The user wallet address
 * @param duration - The duration of the audio
 * @param media - Optional media attachments
 * @returns - The saved summon message
 */
export async function saveSummon(
  audioUrl: string,
  transcript: string,
  userAddress: string,
  duration: number,
  media: { url: string; type: 'image' | 'video'; id: string }[] = []
): Promise<VoiceMessageProps> {
  try {
    console.log('Saving summon:', { audioUrl, transcript, userAddress, duration });

    const messageId = uuidv4();
    const addressStr = typeof userAddress === 'string' ? userAddress : String(userAddress);
    const profileId = await getNormalizedProfileId(addressStr);

    // Create summon object for database
    const summon = {
      id: messageId,
      audio_url: audioUrl,
      transcript: transcript || '',
      profile_id: profileId,
      audio_duration: duration,
      created_at: new Date().toISOString(),
      is_pinned: false,
      message_type: 'summon'
    };

    // Store in Supabase
    const { error } = await supabase
      .from('voice_messages')
      .insert(summon);

    if (error) {
      console.error('Error saving summon to Supabase:', error);
      throw new Error(`Failed to save summon: ${error.message}`);
    }

    // If there's media, store it in the database too
    if (media && media.length > 0) {
      const mediaItems = media.map(item => ({
        id: item.id || uuidv4(),
        voice_message_id: messageId,
        url: item.url,
        type: item.type,
        created_at: new Date().toISOString()
      }));

      const { error: mediaError } = await supabase
        .from('voice_message_media')
        .insert(mediaItems);

      if (mediaError) {
        console.warn('Error storing media attachments:', mediaError);
      }
    }

    // Return the summon object for the UI
    const result: VoiceMessageProps = {
      id: messageId,
      userAddress: profileId,
      audioUrl,
      transcript,
      timestamp: new Date(),
      duration,
      isPinned: false,
      media: media || [],
      replies: []
    };

    return result;
  } catch (error) {
    console.error('Error saving summon:', error);
    toast.error('Failed to save your summon');
    throw error;
  }
}

export default {
  filterDeletedPosts,
  saveVoiceMessage,
  saveReply,
  saveSummon,
  getUserVoiceMessages,
  getAllVoiceMessages,
  deleteVoiceMessage,
  togglePinMessage
};
