
import React, { useState } from 'react';
import { useChannels } from '@/contexts/ChannelContext';
import { toast } from '@/components/ui/sonner';
import { Dialog, DialogContent, DialogTitle, DialogDescription, DialogFooter, DialogHeader } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { X, Plus, Hash, Upload, Palette, Users, Shield, DollarSign, Vote, Search } from 'lucide-react';

interface CreateChannelModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CreateChannelModal: React.FC<CreateChannelModalProps> = ({
  isOpen,
  onClose
}) => {
  const { createChannel } = useChannels();

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isPrivate: false,
    tags: [] as string[],
    currentTag: '',
    coverImageUrl: '',
    enableGovernance: true,
    enableTokens: true,
    primaryColor: '#8B5CF6'
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Validate form
    if (!formData.name.trim()) {
      toast('Please enter a channel name');
      setIsSubmitting(false);
      return;
    }

    if (!formData.description.trim()) {
      toast('Please enter a channel description');
      setIsSubmitting(false);
      return;
    }

    // Create the channel
    try {
      console.log("Creating channel with data:", formData);
      await createChannel(
        formData.name.trim(),
        formData.description.trim(),
        formData.isPrivate,
        formData.tags
      );

      console.log("Channel created successfully");
      toast("Channel created successfully! 🎉");

      // Reset form
      setFormData({
        name: '',
        description: '',
        isPrivate: false,
        tags: [],
        currentTag: '',
        coverImageUrl: '',
        enableGovernance: true,
        enableTokens: true,
        primaryColor: '#8B5CF6'
      });

      onClose();
    } catch (error) {
      console.error("Error creating channel:", error);
      toast("Failed to create channel. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Direct state update to fix cursor jumping issue
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleToggleChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, isPrivate: checked }));
  };

  const handleAddTag = () => {
    if (!formData.currentTag.trim()) return;

    // Check if tag already exists
    if (formData.tags.includes(formData.currentTag.trim())) {
      toast('Tag already exists');
      return;
    }

    setFormData(prev => ({
      ...prev,
      tags: [...prev.tags, prev.currentTag.trim()],
      currentTag: ''
    }));

    // Focus back on the tag input
    setTimeout(() => {
      const tagInput = document.getElementById('tag-input');
      if (tagInput) {
        tagInput.focus();
      }
    }, 10);
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  // If not open, don't render anything
  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-hidden flex flex-col">
        <form onSubmit={handleSubmit} className="flex flex-col h-full">
          <div className="flex justify-between items-center mb-4">
            <DialogTitle className="text-xl font-semibold">Create a Voice Channel</DialogTitle>
            <Button variant="ghost" size="icon" onClick={onClose} type="button">
              <X size={20} />
            </Button>
          </div>

          <DialogDescription className="text-sm text-muted-foreground mb-6">
            Create a voice-first channel with governance, tokens, and custom features
          </DialogDescription>

          {/* Scrollable Content */}
          <div className="flex-1 overflow-y-auto pr-2 space-y-6"
               style={{ maxHeight: 'calc(90vh - 200px)' }}>

            {/* Channel Name */}
            <div className="space-y-2">
              <Label htmlFor="channel-name" className="flex items-center gap-1">
                <Hash size={16} />
                Channel Name
              </Label>
              <Input
                id="channel-name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="e.g., announcements"
                maxLength={50}
                className="dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                autoFocus
              />
              <p className="text-xs text-muted-foreground">
                {formData.name.length}/50 characters
              </p>
            </div>

            {/* Channel Description */}
            <div className="space-y-2">
              <Label htmlFor="channel-description">Description</Label>
              <Textarea
                id="channel-description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                placeholder="What is this channel about?"
                maxLength={200}
                rows={3}
                className="dark:bg-gray-800 dark:border-gray-700 dark:text-white resize-none"
              />
              <p className="text-xs text-muted-foreground">
                {formData.description.length}/200 characters
              </p>
            </div>

            {/* Channel Tags */}
            <div className="space-y-2">
              <Label>Tags</Label>
              <div className="flex flex-wrap gap-2 mb-2">
                {formData.tags.map(tag => (
                  <span
                    key={tag}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium dark:bg-gray-700 bg-gray-200"
                  >
                    {tag}
                    <button
                      type="button"
                      className="ml-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                      onClick={() => handleRemoveTag(tag)}
                    >
                      ✕
                    </button>
                  </span>
                ))}
              </div>

              <div className="flex gap-2">
                <Input
                  id="tag-input"
                  name="currentTag"
                  value={formData.currentTag}
                  onChange={handleChange}
                  placeholder="Add a tag"
                  className="flex-1 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  onKeyDown={e => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddTag();
                    }
                  }}
                />
                <Button
                  type="button"
                  variant="outline"
                  className="dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  onClick={handleAddTag}
                  disabled={!formData.currentTag.trim()}
                >
                  <Plus size={16} />
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                Press Enter or click + to add a tag
              </p>
            </div>

            {/* Private Channel Switch */}
            <div className="flex items-center justify-between">
              <div>
                <span className="font-medium">Private Channel</span>
                <p className="text-xs text-muted-foreground">
                  Private channels are only visible to members
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="private-channel"
                  checked={formData.isPrivate}
                  onCheckedChange={handleToggleChange}
                />
                <Label htmlFor="private-channel" className="sr-only">Private Channel</Label>
              </div>
            </div>

            {/* Channel Features Preview */}
            <Card className="bg-gradient-to-r from-voicechain-purple/5 to-voicechain-accent/5 border-voicechain-purple/20">
              <CardContent className="p-4">
                <h4 className="font-semibold mb-3 flex items-center gap-2">
                  <Badge className="bg-voicechain-purple">NEW</Badge>
                  Your Channel Will Include:
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center gap-2">
                    <Hash size={16} className="text-blue-500" />
                    <span>6 Organized Sections</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users size={16} className="text-green-500" />
                    <span>Role Management</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <DollarSign size={16} className="text-yellow-500" />
                    <span>Token Tipping & Rewards</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Vote size={16} className="text-purple-500" />
                    <span>DAO Governance</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Palette size={16} className="text-pink-500" />
                    <span>Custom Themes</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Search size={16} className="text-orange-500" />
                    <span>Voice Search & Discovery</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Advanced Settings */}
            <div className="space-y-4">
              <h4 className="font-semibold text-sm">Advanced Settings</h4>

              {/* Cover Image URL */}
              <div className="space-y-2">
                <Label htmlFor="cover-image" className="flex items-center gap-1">
                  <Upload size={16} />
                  Cover Image URL (Optional)
                </Label>
                <Input
                  id="cover-image"
                  name="coverImageUrl"
                  value={formData.coverImageUrl}
                  onChange={handleChange}
                  placeholder="https://example.com/image.jpg"
                  className="w-full"
                />
                <p className="text-xs text-muted-foreground">
                  Add a banner image for your channel
                </p>
              </div>

              {/* Primary Color */}
              <div className="space-y-2">
                <Label htmlFor="primary-color" className="flex items-center gap-1">
                  <Palette size={16} />
                  Primary Color
                </Label>
                <div className="flex items-center gap-3">
                  <Input
                    id="primary-color"
                    name="primaryColor"
                    type="color"
                    value={formData.primaryColor}
                    onChange={handleChange}
                    className="w-16 h-10 p-1 rounded"
                  />
                  <Input
                    value={formData.primaryColor}
                    onChange={(e) => setFormData(prev => ({ ...prev, primaryColor: e.target.value }))}
                    placeholder="#8B5CF6"
                    className="flex-1"
                  />
                </div>
              </div>

              {/* Feature Toggles */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <span className="font-medium flex items-center gap-2">
                      <Vote size={16} />
                      Enable Governance
                    </span>
                    <p className="text-xs text-muted-foreground">
                      Allow members to create and vote on proposals
                    </p>
                  </div>
                  <Switch
                    checked={formData.enableGovernance}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, enableGovernance: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <span className="font-medium flex items-center gap-2">
                      <DollarSign size={16} />
                      Enable Token Features
                    </span>
                    <p className="text-xs text-muted-foreground">
                      Enable tipping, rewards, and treasury
                    </p>
                  </div>
                  <Switch
                    checked={formData.enableTokens}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, enableTokens: checked }))}
                  />
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="mt-6 pt-4 border-t">
            <Button
              type="submit"
              className="w-full bg-voicechain-purple hover:bg-voicechain-accent"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Creating...' : 'Create Channel'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateChannelModal;
