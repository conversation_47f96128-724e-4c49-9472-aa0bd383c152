/**
 * PWA-specific audio utilities to handle playback issues
 */

import { isPWA } from './pwa';

// Check if we're in PWA mode
const isInPWA = (): boolean => {
  return isPWA() || window.matchMedia('(display-mode: standalone)').matches;
};

// Enhanced audio play function for PWA
export const playAudioInPWA = async (audioElement: HTMLAudioElement): Promise<boolean> => {
  try {
    console.log('🎵 PWA Audio: Attempting to play audio...');
    
    // Ensure audio is properly loaded
    if (audioElement.readyState < 2) {
      console.log('🎵 PWA Audio: Waiting for audio to load...');
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Audio load timeout'));
        }, 5000);
        
        const handleCanPlay = () => {
          clearTimeout(timeout);
          audioElement.removeEventListener('canplay', handleCanPlay);
          audioElement.removeEventListener('error', handleError);
          resolve(true);
        };
        
        const handleError = () => {
          clearTimeout(timeout);
          audioElement.removeEventListener('canplay', handleCanPlay);
          audioElement.removeEventListener('error', handleError);
          reject(new Error('Audio load error'));
        };
        
        audioElement.addEventListener('canplay', handleCanPlay);
        audioElement.addEventListener('error', handleError);
        audioElement.load();
      });
    }
    
    // PWA-specific audio settings
    if (isInPWA()) {
      audioElement.setAttribute('playsinline', 'true');
      audioElement.setAttribute('webkit-playsinline', 'true');
      audioElement.preload = 'auto';
    }
    
    // Try to play
    await audioElement.play();
    console.log('🎵 PWA Audio: Playback started successfully');
    return true;
    
  } catch (error) {
    console.error('🎵 PWA Audio: Playback failed:', error);
    
    // Try alternative approach for PWA
    try {
      console.log('🎵 PWA Audio: Trying alternative playback method...');
      audioElement.currentTime = 0;
      audioElement.muted = false;
      await audioElement.play();
      console.log('🎵 PWA Audio: Alternative playback successful');
      return true;
    } catch (retryError) {
      console.error('🎵 PWA Audio: Alternative playback also failed:', retryError);
      return false;
    }
  }
};

// Create audio element with PWA-optimized settings
export const createPWAAudioElement = (src: string): HTMLAudioElement => {
  const audio = new Audio(src);
  
  // PWA-specific attributes
  if (isInPWA()) {
    audio.setAttribute('playsinline', 'true');
    audio.setAttribute('webkit-playsinline', 'true');
    audio.preload = 'auto';
    audio.crossOrigin = 'anonymous';
  }
  
  return audio;
};

// Auto-play audio for transcription in PWA
export const autoPlayForTranscription = async (audioUrl: string): Promise<void> => {
  return new Promise((resolve) => {
    const audio = createPWAAudioElement(audioUrl);
    
    // Set low volume for background transcription
    audio.volume = 0.1;
    
    const playForTranscription = async () => {
      try {
        console.log('🎵 PWA: Auto-playing for transcription...');
        
        // Play audio briefly
        await playAudioInPWA(audio);
        
        // Stop after a short time to trigger transcription
        setTimeout(() => {
          audio.pause();
          audio.currentTime = 0;
          console.log('🎵 PWA: Transcription trigger complete');
          resolve();
        }, 300); // Play for 300ms
        
      } catch (error) {
        console.log('🎵 PWA: Auto-play blocked, proceeding anyway:', error);
        resolve();
      }
    };
    
    // Try to play after a short delay
    setTimeout(playForTranscription, 100);
  });
};

// Fix audio context issues in PWA
export const fixAudioContextInPWA = (): void => {
  if (!isInPWA()) return;
  
  // Resume audio context if suspended (common in PWA)
  const resumeAudioContext = () => {
    if (typeof window !== 'undefined' && 'AudioContext' in window) {
      const AudioContext = window.AudioContext || (window as any).webkitAudioContext;
      if (AudioContext) {
        const context = new AudioContext();
        if (context.state === 'suspended') {
          context.resume().then(() => {
            console.log('🎵 PWA: Audio context resumed');
          }).catch((error) => {
            console.warn('🎵 PWA: Failed to resume audio context:', error);
          });
        }
      }
    }
  };
  
  // Resume on user interaction
  const handleUserInteraction = () => {
    resumeAudioContext();
    document.removeEventListener('touchstart', handleUserInteraction);
    document.removeEventListener('click', handleUserInteraction);
  };
  
  document.addEventListener('touchstart', handleUserInteraction, { once: true });
  document.addEventListener('click', handleUserInteraction, { once: true });
};

// Initialize PWA audio fixes
export const initializePWAAudio = (): void => {
  if (!isInPWA()) return;
  
  console.log('🎵 PWA: Initializing audio fixes...');
  
  // Fix audio context issues
  fixAudioContextInPWA();
  
  // Add PWA-specific audio styles
  const style = document.createElement('style');
  style.textContent = `
    /* PWA Audio Fixes */
    audio {
      -webkit-playsinline: true !important;
      playsinline: true !important;
    }
    
    /* Prevent audio controls from being cut off */
    audio::-webkit-media-controls-panel {
      margin-bottom: env(safe-area-inset-bottom, 0px);
    }
  `;
  document.head.appendChild(style);
  
  console.log('🎵 PWA: Audio fixes initialized');
};

// Check if audio is supported in current PWA environment
export const isAudioSupportedInPWA = (): boolean => {
  try {
    const audio = new Audio();
    return !!(audio.canPlayType && audio.canPlayType('audio/webm').replace(/no/, ''));
  } catch (error) {
    console.warn('🎵 PWA: Audio support check failed:', error);
    return false;
  }
};

// Get optimal audio format for PWA
export const getOptimalAudioFormat = (): string => {
  const audio = new Audio();
  
  // Check supported formats in order of preference
  const formats = [
    { type: 'audio/webm; codecs="opus"', mime: 'audio/webm' },
    { type: 'audio/mp4; codecs="mp4a.40.2"', mime: 'audio/mp4' },
    { type: 'audio/mpeg', mime: 'audio/mpeg' },
    { type: 'audio/wav', mime: 'audio/wav' }
  ];
  
  for (const format of formats) {
    if (audio.canPlayType(format.type) !== '') {
      console.log('🎵 PWA: Using audio format:', format.mime);
      return format.mime;
    }
  }
  
  console.log('🎵 PWA: Falling back to default audio format');
  return 'audio/webm';
};
