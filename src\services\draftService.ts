
import { supabase } from '@/integrations/supabase/client';
import { MediaFile } from '@/types/voice-message';
import { v4 as uuidv4 } from 'uuid';

// Draft types
export type DraftType = 'voice_message' | 'journal';

export interface Draft {
  id: string;
  userId: string;
  type: DraftType;
  title?: string;
  audioUrl?: string;
  transcript?: string;
  audioDuration?: number;
  media?: MediaFile[];
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

interface DbDraft {
  id: string;
  user_id: string;
  type: DraftType;
  title: string | null;
  audio_url: string | null;
  transcript: string | null;
  audio_duration: number | null;
  media: any[] | null;
  created_at: string;
  updated_at: string;
  metadata: Record<string, any> | null;
}

/**
 * Service for managing draft voice messages and journals
 */
class DraftService {
  /**
   * Get all drafts for the current user
   */
  public async getDrafts(userId: string, type?: DraftType): Promise<Draft[]> {
    try {
      if (!userId) {
        throw new Error('User ID is required');
      }
      
      const normalizedUserId = userId.toLowerCase();
      
      // Build query
      let query = supabase
        .from('drafts')
        .select('*')
        .eq('user_id', normalizedUserId);
        
      if (type) {
        query = query.eq('type', type);
      }
      
      // Run query
      const { data, error } = await query.order('updated_at', { ascending: false });
      
      if (error) {
        console.error('Error fetching drafts from database:', error);
        return [];
      }
      
      if (!data || data.length === 0) {
        return [];
      }
      
      // Map database results to Draft interface
      return data.map(this.mapDbDraftToDraft);
    } catch (error) {
      console.error('Error in getDrafts:', error);
      return [];
    }
  }

  /**
   * Map database draft to application draft
   */
  private mapDbDraftToDraft(dbDraft: DbDraft): Draft {
    return {
      id: dbDraft.id,
      userId: dbDraft.user_id,
      type: dbDraft.type,
      title: dbDraft.title || undefined,
      audioUrl: dbDraft.audio_url || undefined,
      transcript: dbDraft.transcript || undefined,
      audioDuration: dbDraft.audio_duration || undefined,
      media: dbDraft.media || [],
      createdAt: new Date(dbDraft.created_at),
      updatedAt: new Date(dbDraft.updated_at),
      metadata: dbDraft.metadata || {}
    };
  }

  /**
   * Map application draft to database draft
   */
  private mapDraftToDbDraft(draft: Omit<Draft, 'id' | 'createdAt' | 'updatedAt'>): Omit<DbDraft, 'id' | 'created_at' | 'updated_at'> {
    return {
      user_id: draft.userId,
      type: draft.type,
      title: draft.title || null,
      audio_url: draft.audioUrl || null,
      transcript: draft.transcript || null,
      audio_duration: draft.audioDuration || null,
      media: draft.media || [],
      metadata: draft.metadata || {}
    };
  }

  /**
   * Save a draft
   */
  public async saveDraft(draft: Omit<Draft, 'id' | 'createdAt' | 'updatedAt'>): Promise<Draft> {
    try {
      const now = new Date();
      const draftId = crypto.randomUUID();
      
      const newDraft: Draft = {
        ...draft,
        id: draftId,
        createdAt: now,
        updatedAt: now
      };
      
      // Convert to database format
      const dbDraft = {
        id: newDraft.id,
        user_id: newDraft.userId.toLowerCase(),
        type: newDraft.type,
        title: newDraft.title || null,
        audio_url: newDraft.audioUrl || null,
        transcript: newDraft.transcript || null,
        audio_duration: newDraft.audioDuration || null,
        media: newDraft.media || [],
        created_at: now.toISOString(),
        updated_at: now.toISOString(),
        metadata: newDraft.metadata || {}
      };
      
      // Save to database
      const { error } = await supabase
        .from('drafts')
        .insert(dbDraft);
        
      if (error) {
        console.error('Error saving draft to database:', error);
        throw error;
      }
      
      return newDraft;
    } catch (error) {
      console.error('Error in saveDraft:', error);
      throw error;
    }
  }

  /**
   * Update a draft
   */
  public async updateDraft(id: string, userId: string, updates: Partial<Omit<Draft, 'id' | 'userId' | 'createdAt' | 'updatedAt'>>): Promise<Draft | null> {
    try {
      // Get the draft first to make sure it exists
      const drafts = await this.getDrafts(userId);
      const draft = drafts.find(d => d.id === id);
      
      if (!draft) {
        console.error('Draft not found:', id);
        return null;
      }
      
      // Update the draft
      const updatedDraft: Draft = {
        ...draft,
        ...updates,
        updatedAt: new Date()
      };
      
      // Save to database
      const { error } = await supabase
        .from('drafts')
        .update({
          type: updatedDraft.type,
          title: updatedDraft.title || null,
          audio_url: updatedDraft.audioUrl || null,
          transcript: updatedDraft.transcript || null,
          audio_duration: updatedDraft.audioDuration || null,
          media: updatedDraft.media || [],
          updated_at: updatedDraft.updatedAt.toISOString(),
          metadata: updatedDraft.metadata || {}
        })
        .eq('id', id)
        .eq('user_id', userId.toLowerCase());
        
      if (error) {
        console.error('Error updating draft in database:', error);
        throw error;
      }
      
      return updatedDraft;
    } catch (error) {
      console.error('Error in updateDraft:', error);
      return null;
    }
  }

  /**
   * Delete a draft
   */
  public async deleteDraft(id: string, userId: string): Promise<boolean> {
    try {
      // Delete from database
      const { error } = await supabase
        .from('drafts')
        .delete()
        .eq('id', id)
        .eq('user_id', userId.toLowerCase());
        
      if (error) {
        console.error('Error deleting draft from database:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Error in deleteDraft:', error);
      return false;
    }
  }
}

// Export a singleton instance
export const draftService = new DraftService();
export default draftService;
