-- Critical profile fix: Ensure the current user has a proper profile
-- Get the current authenticated user and fix their profile

-- First, create/update a profile for the current user
INSERT INTO profiles (
  id,
  wallet_address,
  username,
  display_name,
  bio,
  avatar_url,
  cover_image_url,
  social_links,
  created_at,
  updated_at
) VALUES (
  '0f59ebfe-9b88-48ca-a0e4-66a3f533d843',
  '0f59ebfe-9b88-48ca-a0e4-66a3f533d843',
  'user_0f59ebfe',
  'Your Name',
  'Welcome to my profile!',
  '',
  '',
  '{}',
  NOW(),
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  wallet_address = EXCLUDED.wallet_address,
  username = COALESCE(profiles.username, EXCLUDED.username),
  display_name = COALESCE(profiles.display_name, EXCLUDED.display_name),
  updated_at = NOW();