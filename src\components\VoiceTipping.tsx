import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { supabase } from '@/lib/supabase';
import { toast } from '@/components/ui/sonner';
import { 
  DollarSign, 
  Heart, 
  Zap, 
  Gift,
  Coins,
  TrendingUp,
  Eye,
  EyeOff
} from 'lucide-react';

interface Token {
  id: string;
  symbol: string;
  name: string;
  decimals: number;
  is_stablecoin: boolean;
  price_usd: number;
  icon_url?: string;
}

interface VoiceTippingProps {
  voiceMessageId?: string;
  topicId?: string;
  channelId: string;
  recipientAddress: string;
  recipientName?: string;
  onTipSent?: (amount: number, token: string) => void;
}

const VoiceTipping: React.FC<VoiceTippingProps> = ({
  voiceMessageId,
  topicId,
  channelId,
  recipientAddress,
  recipientName,
  onTipSent
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [tokens, setTokens] = useState<Token[]>([]);
  const [selectedToken, setSelectedToken] = useState<Token | null>(null);
  const [amount, setAmount] = useState('');
  const [tipMessage, setTipMessage] = useState('');
  const [isAnonymous, setIsAnonymous] = useState(false);
  const [loading, setLoading] = useState(false);
  const [usdValue, setUsdValue] = useState(0);

  // Quick tip amounts
  const quickAmounts = [1, 5, 10, 25, 50, 100];

  useEffect(() => {
    loadTokens();
  }, []);

  useEffect(() => {
    if (selectedToken && amount) {
      const numAmount = parseFloat(amount);
      if (!isNaN(numAmount)) {
        setUsdValue(numAmount * selectedToken.price_usd);
      }
    } else {
      setUsdValue(0);
    }
  }, [selectedToken, amount]);

  const loadTokens = async () => {
    try {
      const { data, error } = await supabase
        .from('tokens')
        .select('*')
        .eq('is_enabled', true)
        .order('is_stablecoin', { ascending: false });

      if (error) {
        console.error('Error loading tokens:', error);
        return;
      }

      setTokens(data || []);
      if (data && data.length > 0) {
        // Default to USDC if available, otherwise first token
        const defaultToken = data.find(t => t.symbol === 'USDC') || data[0];
        setSelectedToken(defaultToken);
      }
    } catch (error) {
      console.error('Error loading tokens:', error);
    }
  };

  const handleQuickAmount = (quickAmount: number) => {
    setAmount(quickAmount.toString());
  };

  const handleSendTip = async () => {
    if (!selectedToken || !amount || parseFloat(amount) <= 0) {
      toast('Please select a token and enter a valid amount');
      return;
    }

    try {
      setLoading(true);

      // Get current user profile
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('wallet_address', 'current_user_address') // This should be the actual user address
        .single();

      if (profileError) {
        toast('Please connect your wallet first');
        return;
      }

      // Get recipient profile
      const { data: recipientData, error: recipientError } = await supabase
        .from('profiles')
        .select('id')
        .eq('wallet_address', recipientAddress)
        .single();

      if (recipientError) {
        toast('Recipient profile not found');
        return;
      }

      // Create tip record
      const tipData = {
        from_profile_id: profileData.id,
        to_profile_id: recipientData.id,
        voice_message_id: voiceMessageId,
        topic_id: topicId,
        channel_id: channelId,
        token_id: selectedToken.id,
        amount: parseFloat(amount),
        amount_usd: usdValue,
        tip_message: tipMessage || null,
        is_anonymous: isAnonymous,
        blockchain_status: 'pending'
      };

      const { error: tipError } = await supabase
        .from('voice_tips')
        .insert(tipData);

      if (tipError) {
        console.error('Error creating tip:', tipError);
        toast('Failed to send tip');
        return;
      }

      // Here you would integrate with actual blockchain transaction
      // For now, we'll simulate a successful transaction
      toast(`Successfully sent ${amount} ${selectedToken.symbol} tip! 🎉`);
      
      onTipSent?.(parseFloat(amount), selectedToken.symbol);
      setIsOpen(false);
      setAmount('');
      setTipMessage('');
      setIsAnonymous(false);

    } catch (error) {
      console.error('Error sending tip:', error);
      toast('Failed to send tip');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm" className="text-green-600 hover:text-green-700 hover:bg-green-50">
          <DollarSign size={16} className="mr-1" />
          Tip
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Gift className="text-green-600" size={20} />
            Send Voice Tip
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Recipient Info */}
          <Card className="bg-secondary/20">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                  <Heart size={20} className="text-white" />
                </div>
                <div>
                  <p className="font-medium">Tipping</p>
                  <p className="text-sm text-muted-foreground">
                    {recipientName || `${recipientAddress.slice(0, 6)}...${recipientAddress.slice(-4)}`}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Token Selection */}
          <div>
            <Label className="text-sm font-medium mb-2 block">Select Token</Label>
            <Select
              value={selectedToken?.id || ''}
              onValueChange={(tokenId) => {
                const token = tokens.find(t => t.id === tokenId);
                setSelectedToken(token || null);
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Choose token" />
              </SelectTrigger>
              <SelectContent>
                {tokens.map((token) => (
                  <SelectItem key={token.id} value={token.id}>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{token.symbol}</span>
                      <span className="text-muted-foreground">({token.name})</span>
                      {token.is_stablecoin && (
                        <Badge variant="secondary" className="text-xs">Stable</Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Quick Amount Buttons */}
          <div>
            <Label className="text-sm font-medium mb-2 block">Quick Amounts</Label>
            <div className="grid grid-cols-3 gap-2">
              {quickAmounts.map((quickAmount) => (
                <Button
                  key={quickAmount}
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAmount(quickAmount)}
                  className="text-xs"
                >
                  {quickAmount} {selectedToken?.symbol || ''}
                </Button>
              ))}
            </div>
          </div>

          {/* Custom Amount */}
          <div>
            <Label className="text-sm font-medium mb-2 block">Custom Amount</Label>
            <div className="relative">
              <Input
                type="number"
                placeholder="0.00"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="pr-16"
                step="0.01"
                min="0"
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-muted-foreground">
                {selectedToken?.symbol}
              </div>
            </div>
            {usdValue > 0 && (
              <p className="text-xs text-muted-foreground mt-1">
                ≈ ${usdValue.toFixed(2)} USD
              </p>
            )}
          </div>

          {/* Tip Message */}
          <div>
            <Label className="text-sm font-medium mb-2 block">Message (Optional)</Label>
            <Textarea
              placeholder="Add a nice message with your tip..."
              value={tipMessage}
              onChange={(e) => setTipMessage(e.target.value)}
              rows={3}
              maxLength={200}
            />
            <p className="text-xs text-muted-foreground mt-1">
              {tipMessage.length}/200 characters
            </p>
          </div>

          {/* Anonymous Option */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Label htmlFor="anonymous" className="text-sm font-medium">
                Send Anonymously
              </Label>
              {isAnonymous ? (
                <EyeOff size={14} className="text-muted-foreground" />
              ) : (
                <Eye size={14} className="text-muted-foreground" />
              )}
            </div>
            <Switch
              id="anonymous"
              checked={isAnonymous}
              onCheckedChange={setIsAnonymous}
            />
          </div>

          {/* Send Button */}
          <div className="flex gap-2">
            <Button
              onClick={handleSendTip}
              disabled={loading || !selectedToken || !amount || parseFloat(amount) <= 0}
              className="flex-1 bg-green-600 hover:bg-green-700"
            >
              {loading ? (
                <>
                  <Zap size={16} className="mr-2 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Gift size={16} className="mr-2" />
                  Send Tip
                </>
              )}
            </Button>
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={loading}
            >
              Cancel
            </Button>
          </div>

          {/* Tip Info */}
          <div className="text-xs text-muted-foreground bg-secondary/20 p-3 rounded-lg">
            <p className="flex items-center gap-1 mb-1">
              <TrendingUp size={12} />
              Tips support creators and show appreciation for quality content
            </p>
            <p>• Tips are recorded on-chain for transparency</p>
            <p>• Recipients can withdraw to their wallet anytime</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default VoiceTipping;
