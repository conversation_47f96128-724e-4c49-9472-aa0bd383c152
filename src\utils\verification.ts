
import { VerificationType } from '@/components/VerificationBadge';
import { VERIFIED_ADDRESSES } from '@/utils/verification-data';

/**
 * Check if an address is verified and get its verification type
 * @param address Ethereum address to check
 * @returns Verification type or null if not verified
 */
export function getVerificationType(address: string): VerificationType | null {
  if (!address) return null;

  const normalizedAddress = address.toLowerCase();
  return VERIFIED_ADDRESSES[normalizedAddress] || null;
}

/**
 * Check if an address is verified
 * @param address Ethereum address to check
 * @returns Boolean indicating if the address is verified
 */
export function isVerified(address: string): boolean {
  return getVerificationType(address) !== null;
}

/**
 * Get verification description based on type
 * @param type Verification type
 * @returns Description of the verification type
 */
export function getVerificationDescription(type: VerificationType): string {
  const descriptions: Record<VerificationType, string> = {
    owner: 'App Owner',
    creator: 'Verified Creator',
    developer: 'Verified Developer',
    community: 'Community Leader',
    partner: 'Official Partner',
    investor: 'Verified Investor',
    early: 'Early Adopter',
    artist: 'Verified Artist',
    musician: 'Verified Musician',
    journalist: 'Verified Journalist',
    educator: 'Verified Educator',
    nonprofit: 'Verified Nonprofit',
    government: 'Government Entity',
    celebrity: 'Public Figure',
    custom: 'Verified'
  };

  return descriptions[type] || 'Verified';
}

/**
 * Get detailed verification description based on type
 * @param type Verification type
 * @returns Detailed description of the verification type
 */
export function getDetailedVerificationDescription(type: VerificationType): string {
  const descriptions: Record<VerificationType, string> = {
    owner: 'This account is owned by the creator of Audra.',
    creator: 'This account belongs to a verified content creator who regularly contributes high-quality voice content.',
    developer: 'This account belongs to a verified developer who contributes to the Audra platform.',
    community: 'This account belongs to a recognized community leader who helps grow and moderate the Audra community.',
    partner: 'This account represents an official partner organization that collaborates with Audra.',
    investor: 'This account belongs to a verified investor in the Audra platform.',
    early: 'This account belongs to an early adopter who joined during Audra\'s initial launch phase.',
    artist: 'This account belongs to a verified visual artist who creates original artwork and visual content.',
    musician: 'This account belongs to a verified musician who creates and shares original music.',
    journalist: 'This account belongs to a verified journalist or news organization that reports on relevant topics.',
    educator: 'This account belongs to a verified educator, teacher, or educational institution.',
    nonprofit: 'This account represents a verified nonprofit organization with a charitable mission.',
    government: 'This account represents an official government entity, department, or official.',
    celebrity: 'This account belongs to a public figure with significant recognition in their field.',
    custom: 'This account has been verified by the Audra team.'
  };

  return descriptions[type] || 'This account has been verified by the Audra team.';
}
