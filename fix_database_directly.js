import { supabase } from './src/lib/supabase.js';

async function fixDatabase() {
  console.log('🔧 Fixing database schema...');

  try {
    // Add invite_code column to voice_chats table
    const { error: error1 } = await supabase
      .from('voice_chats')
      .select('invite_code')
      .limit(1);

    if (error1 && error1.message.includes('invite_code')) {
      console.log('Adding invite_code column...');
      // Column doesn't exist, we need to add it via raw SQL
      // Since we can't run DDL directly, let's check if we can create a chat first
    }

    // Test creating a simple chat to see what's missing
    console.log('Testing chat creation...');
    const { data: testChat, error: createError } = await supabase
      .from('voice_chats')
      .insert({
        name: 'Test Chat',
        type: 'direct',
        creator_id: 'test-user-id'
      })
      .select()
      .single();

    if (createError) {
      console.log('❌ Chat creation error:', createError.message);
      
      if (createError.message.includes('invite_code')) {
        console.log('✅ Confirmed: invite_code column is missing');
        console.log('📋 Please run this SQL in Supabase SQL Editor:');
        console.log(`
ALTER TABLE public.voice_chats ADD COLUMN IF NOT EXISTS invite_code TEXT UNIQUE;
CREATE INDEX IF NOT EXISTS idx_voice_chats_invite_code ON public.voice_chats(invite_code);

CREATE TABLE IF NOT EXISTS public.group_invites (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
  chat_id TEXT NOT NULL REFERENCES public.voice_chats(id) ON DELETE CASCADE,
  invite_code TEXT NOT NULL UNIQUE,
  created_by TEXT NOT NULL,
  max_uses INTEGER DEFAULT NULL,
  current_uses INTEGER DEFAULT 0,
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
        `);
      }
    } else {
      console.log('✅ Chat creation works! Cleaning up test chat...');
      await supabase.from('voice_chats').delete().eq('id', testChat.id);
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

fixDatabase();
