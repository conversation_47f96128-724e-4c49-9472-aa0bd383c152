import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Download, FileDown } from 'lucide-react';
import { supabase } from '@/services/supabase';
import { exportData } from '@/utils/exportData';

interface AnalyticsData {
  userGrowth: {
    labels: string[];
    data: number[];
  };
  contentCreation: {
    labels: string[];
    data: number[];
  };
  engagement: {
    labels: string[];
    data: number[];
  };
  userRetention: {
    percentage: number;
    weeklyData: {
      labels: string[];
      data: number[];
    };
  };
  contentDistribution: {
    labels: string[];
    data: number[];
  };
  deviceUsage: {
    labels: string[];
    data: number[];
  };
}

const Analytics: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [dateRange, setDateRange] = useState<'7d' | '30d' | '90d' | '1y' | 'all'>('30d');
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>({
    userGrowth: {
      labels: [],
      data: []
    },
    contentCreation: {
      labels: [],
      data: []
    },
    engagement: {
      labels: [],
      data: []
    },
    userRetention: {
      percentage: 0,
      weeklyData: {
        labels: [],
        data: []
      }
    },
    contentDistribution: {
      labels: [],
      data: []
    },
    deviceUsage: {
      labels: [],
      data: []
    }
  });

  useEffect(() => {
    fetchAnalyticsData();
  }, [dateRange]);

  const fetchAnalyticsData = async () => {
    try {
      setIsLoading(true);

      // Calculate date range based on selection
      let startDate = new Date();
      let intervalType: 'day' | 'month' = 'day';
      let intervalCount = 7;
      let formatString: 'short' | 'numeric' = 'short';

      switch (dateRange) {
        case '7d':
          startDate.setDate(startDate.getDate() - 7);
          intervalType = 'day';
          intervalCount = 7;
          break;
        case '30d':
          startDate.setDate(startDate.getDate() - 30);
          intervalType = 'day';
          intervalCount = 30;
          break;
        case '90d':
          startDate.setDate(startDate.getDate() - 90);
          intervalType = 'month';
          intervalCount = 3;
          break;
        case '1y':
          startDate.setFullYear(startDate.getFullYear() - 1);
          intervalType = 'month';
          intervalCount = 12;
          break;
        case 'all':
          startDate = new Date(2020, 0, 1); // Assuming platform started in 2020
          intervalType = 'month';
          intervalCount = 12; // Show last 12 months for "all" view
          break;
      }

      // Generate labels and date ranges
      const labels = [];
      const dateRanges = [];

      if (intervalType === 'day') {
        // Daily intervals
        for (let i = intervalCount - 1; i >= 0; i--) {
          const date = new Date();
          date.setDate(date.getDate() - i);

          // Format as "Jan 1" or just day number depending on range
          const label = dateRange === '7d'
            ? date.toLocaleDateString('default', { weekday: 'short' })
            : date.toLocaleDateString('default', { month: 'short', day: 'numeric' });

          labels.push(label);
          dateRanges.push(date.toISOString().split('T')[0]); // YYYY-MM-DD format
        }
      } else {
        // Monthly intervals
        for (let i = intervalCount - 1; i >= 0; i--) {
          const date = new Date();
          date.setMonth(date.getMonth() - i);

          labels.push(date.toLocaleString('default', { month: 'short', year: dateRange === 'all' ? '2-digit' : undefined }));
          dateRanges.push(date.toISOString().substring(0, 7)); // YYYY-MM format
        }
      }

      // Fetch user growth data
      const userGrowthData = [];
      for (let i = 0; i < dateRanges.length; i++) {
        const currentDate = dateRanges[i];
        let startDateStr, endDateStr;

        if (intervalType === 'day') {
          // For daily intervals
          startDateStr = `${currentDate}T00:00:00`;

          // If it's the last day, use current time as end
          if (i === dateRanges.length - 1) {
            endDateStr = new Date().toISOString();
          } else {
            // Otherwise use the next day at midnight
            const nextDay = new Date(currentDate);
            nextDay.setDate(nextDay.getDate() + 1);
            endDateStr = nextDay.toISOString().split('T')[0] + 'T00:00:00';
          }
        } else {
          // For monthly intervals
          startDateStr = `${currentDate}-01T00:00:00`;

          // If it's the current month, use current date as end
          if (i === dateRanges.length - 1 &&
            currentDate === new Date().toISOString().substring(0, 7)) {
            endDateStr = new Date().toISOString();
          } else {
            // Otherwise use the first day of next month
            const [year, month] = currentDate.split('-').map(Number);
            const nextMonth = month === 12 ? 1 : month + 1;
            const nextYear = month === 12 ? year + 1 : year;
            endDateStr = `${nextYear}-${nextMonth.toString().padStart(2, '0')}-01T00:00:00`;
          }
        }

        const { count, error } = await supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true })
          .gte('created_at', startDateStr)
          .lt('created_at', endDateStr);

        if (error) {
          console.error('Error fetching user growth data:', error);
          userGrowthData.push(0);
        } else {
          userGrowthData.push(count || 0);
        }
      }

      // Fetch content creation data - voice messages
      const contentCreationData = [];
      for (let i = 0; i < dateRanges.length; i++) {
        const currentDate = dateRanges[i];
        let startDateStr, endDateStr;

        if (intervalType === 'day') {
          // For daily intervals
          startDateStr = `${currentDate}T00:00:00`;

          // If it's the last day, use current time as end
          if (i === dateRanges.length - 1) {
            endDateStr = new Date().toISOString();
          } else {
            // Otherwise use the next day at midnight
            const nextDay = new Date(currentDate);
            nextDay.setDate(nextDay.getDate() + 1);
            endDateStr = nextDay.toISOString().split('T')[0] + 'T00:00:00';
          }
        } else {
          // For monthly intervals
          startDateStr = `${currentDate}-01T00:00:00`;

          // If it's the current month, use current date as end
          if (i === dateRanges.length - 1 &&
            currentDate === new Date().toISOString().substring(0, 7)) {
            endDateStr = new Date().toISOString();
          } else {
            // Otherwise use the first day of next month
            const [year, month] = currentDate.split('-').map(Number);
            const nextMonth = month === 12 ? 1 : month + 1;
            const nextYear = month === 12 ? year + 1 : year;
            endDateStr = `${nextYear}-${nextMonth.toString().padStart(2, '0')}-01T00:00:00`;
          }
        }

        const { count, error } = await supabase
          .from('voice_messages')
          .select('*', { count: 'exact', head: true })
          .gte('created_at', startDateStr)
          .lt('created_at', endDateStr);

        if (error) {
          console.error('Error fetching content creation data:', error);
          contentCreationData.push(0);
        } else {
          contentCreationData.push(count || 0);
        }
      }

      // Fetch engagement data
      const { data: likesCount, error: likesError } = await supabase
        .from('likes')
        .select('*', { count: 'exact', head: true });

      const { data: commentsCount, error: commentsError } = await supabase
        .from('comments')
        .select('*', { count: 'exact', head: true });

      const { data: sharesCount, error: sharesError } = await supabase
        .from('shares')
        .select('*', { count: 'exact', head: true });

      const { data: tipsCount, error: tipsError } = await supabase
        .from('tips')
        .select('*', { count: 'exact', head: true });

      const { data: reactionsCount, error: reactionsError } = await supabase
        .from('reactions')
        .select('*', { count: 'exact', head: true });

      // Calculate user retention
      // Get users who signed up in the last 5 weeks
      const fiveWeeksAgo = new Date();
      fiveWeeksAgo.setDate(fiveWeeksAgo.getDate() - 35);

      // Try to fetch profiles with last_login, but handle the case where the column doesn't exist
      let newUsers: Array<{ id: string, created_at: string, last_login?: string }> = [];
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('id, created_at, last_login')
          .gte('created_at', fiveWeeksAgo.toISOString());

        if (error) {
          console.error('Error fetching new users with last_login:', error);
          // If the error is about missing column, try without it
          if (error.message.includes('last_login does not exist')) {
            const { data: fallbackData, error: fallbackError } = await supabase
              .from('profiles')
              .select('id, created_at')
              .gte('created_at', fiveWeeksAgo.toISOString());

            if (fallbackError) {
              console.error('Error fetching new users without last_login:', fallbackError);
            } else {
              // Add a mock last_login for each user (for demo purposes)
              newUsers = fallbackData?.map(user => ({
                ...user,
                last_login: user.created_at // Use created_at as a fallback for last_login
              })) || [];
            }
          }
        } else {
          newUsers = data || [];
        }
      } catch (error) {
        console.error('Error fetching new users:', error);
      }

      // Calculate retention by week
      const weekLabels = [];
      const weekData = [];
      const totalNewUsers = newUsers?.length || 0;

      if (totalNewUsers > 0) {
        for (let i = 0; i < 5; i++) {
          const weekStart = new Date();
          weekStart.setDate(weekStart.getDate() - (7 * (i + 1)));

          weekLabels.unshift(`Week ${i + 1}`);

          // Count users who logged in during this week
          const activeUsers = newUsers?.filter(user => {
            if (!user.last_login) return false;

            const lastLogin = new Date(user.last_login);
            return lastLogin >= weekStart;
          }).length || 0;

          // Calculate retention percentage
          const retentionPercentage = Math.round((activeUsers / totalNewUsers) * 100);
          weekData.unshift(retentionPercentage);
        }
      } else {
        // Fallback if no new users
        weekLabels.push('Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5');
        weekData.push(0, 0, 0, 0, 0);
      }

      // Fetch content distribution data
      const { count: voiceMessagesCount, error: voiceMessagesError } = await supabase
        .from('voice_messages')
        .select('*', { count: 'exact', head: true });

      // Fetch device usage data (this would typically come from analytics service)
      // For now, we'll use estimated data based on industry standards
      const deviceLabels = ['Mobile', 'Desktop', 'Tablet'];
      const deviceData = [70, 25, 5]; // Estimated percentages

      // Compile all data
      const analyticsData: AnalyticsData = {
        userGrowth: {
          labels: labels,
          data: userGrowthData
        },
        contentCreation: {
          labels: labels,
          data: contentCreationData
        },
        engagement: {
          labels: ['Likes', 'Comments', 'Shares', 'Tips', 'Reactions'],
          data: [
            likesCount?.length || 0,
            commentsCount?.length || 0,
            sharesCount?.length || 0,
            tipsCount?.length || 0,
            reactionsCount?.length || 0
          ]
        },
        userRetention: {
          percentage: weekData[weekData.length - 1] || 0,
          weeklyData: {
            labels: weekLabels,
            data: weekData
          }
        },
        contentDistribution: {
          labels: ['Voice Messages', 'Comments', 'Reactions', 'Tips'],
          data: [
            voiceMessagesCount || 0,
            commentsCount?.length || 0,
            reactionsCount?.length || 0,
            tipsCount?.length || 0
          ]
        },
        deviceUsage: {
          labels: deviceLabels,
          data: deviceData
        }
      };

      setAnalyticsData(analyticsData);
    } catch (error) {
      console.error('Error fetching analytics data:', error);

      // Fallback to mock data if there's an error
      const mockData: AnalyticsData = {
        userGrowth: {
          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
          data: [12, 19, 27, 35, 48, 62, 78]
        },
        contentCreation: {
          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
          data: [45, 72, 98, 124, 156, 187, 210]
        },
        engagement: {
          labels: ['Likes', 'Comments', 'Shares', 'Tips', 'Reactions'],
          data: [320, 180, 90, 45, 210]
        },
        userRetention: {
          percentage: 68,
          weeklyData: {
            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5'],
            data: [100, 82, 75, 70, 68]
          }
        },
        contentDistribution: {
          labels: ['Voice Messages', 'Comments', 'Reactions', 'Tips'],
          data: [65, 20, 10, 5]
        },
        deviceUsage: {
          labels: ['Mobile', 'Desktop', 'Tablet'],
          data: [72, 23, 5]
        }
      };

      setAnalyticsData(mockData);
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to render a simple bar chart
  const renderBarChart = (labels: string[], data: number[], height: number = 200) => {
    const maxValue = Math.max(...data);

    return (
      <div className="w-full" style={{ height: `${height}px` }}>
        <div className="flex h-full items-end">
          {labels.map((label, index) => (
            <div key={index} className="flex-1 flex flex-col items-center">
              <div
                className="w-full max-w-[40px] bg-purple-500 rounded-t"
                style={{
                  height: `${(data[index] / maxValue) * (height - 40)}px`,
                  opacity: 0.7 + (0.3 * (data[index] / maxValue))
                }}
              ></div>
              <div className="text-xs mt-2 text-center">{label}</div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Helper function to render a simple line chart
  const renderLineChart = (labels: string[], data: number[], height: number = 200) => {
    const maxValue = Math.max(...data);
    const points = labels.map((_, i) => {
      const x = (i / (labels.length - 1)) * 100;
      const y = 100 - ((data[i] / maxValue) * 100);
      return `${x},${y}`;
    }).join(' ');

    return (
      <div className="w-full" style={{ height: `${height}px` }}>
        <svg width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none">
          <polyline
            points={points}
            fill="none"
            stroke="#a855f7"
            strokeWidth="2"
          />
          {data.map((_, i) => {
            const x = (i / (labels.length - 1)) * 100;
            const y = 100 - ((data[i] / maxValue) * 100);
            return (
              <circle
                key={i}
                cx={x}
                cy={y}
                r="2"
                fill="#a855f7"
              />
            );
          })}
        </svg>
        <div className="flex justify-between mt-2">
          {labels.map((label, index) => (
            <div key={index} className="text-xs">{label}</div>
          ))}
        </div>
      </div>
    );
  };

  // Helper function to render a simple pie chart
  const renderPieChart = (labels: string[], data: number[]) => {
    const total = data.reduce((acc, val) => acc + val, 0);
    let cumulativePercentage = 0;

    const colors = ['#a855f7', '#d946ef', '#ec4899', '#f43f5e', '#f97316'];

    return (
      <div className="flex items-center justify-center">
        <div className="relative w-40 h-40">
          <svg viewBox="0 0 100 100" className="w-full h-full">
            {data.map((value, index) => {
              const percentage = (value / total) * 100;
              const startAngle = cumulativePercentage * 3.6; // 3.6 = 360 / 100
              cumulativePercentage += percentage;
              const endAngle = cumulativePercentage * 3.6;

              // Convert angles to radians and calculate x,y coordinates
              const startRad = (startAngle - 90) * Math.PI / 180;
              const endRad = (endAngle - 90) * Math.PI / 180;

              const x1 = 50 + 50 * Math.cos(startRad);
              const y1 = 50 + 50 * Math.sin(startRad);
              const x2 = 50 + 50 * Math.cos(endRad);
              const y2 = 50 + 50 * Math.sin(endRad);

              const largeArcFlag = percentage > 50 ? 1 : 0;

              const pathData = [
                `M 50 50`,
                `L ${x1} ${y1}`,
                `A 50 50 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                `Z`
              ].join(' ');

              return (
                <path
                  key={index}
                  d={pathData}
                  fill={colors[index % colors.length]}
                  stroke="#fff"
                  strokeWidth="1"
                />
              );
            })}
          </svg>
        </div>
        <div className="ml-8">
          {labels.map((label, index) => (
            <div key={index} className="flex items-center mb-2">
              <div
                className="w-3 h-3 mr-2"
                style={{ backgroundColor: colors[index % colors.length] }}
              ></div>
              <span className="text-sm">{label} ({Math.round((data[index] / total) * 100)}%)</span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const handleExportData = (type: 'csv' | 'json') => {
    // Prepare data for export
    const exportableData = {
      userGrowth: analyticsData.userGrowth.labels.map((label, index) => ({
        date: label,
        newUsers: analyticsData.userGrowth.data[index]
      })),
      contentCreation: analyticsData.contentCreation.labels.map((label, index) => ({
        date: label,
        newContent: analyticsData.contentCreation.data[index]
      })),
      engagement: analyticsData.engagement.labels.map((label, index) => ({
        type: label,
        count: analyticsData.engagement.data[index]
      })),
      userRetention: {
        overallRetention: analyticsData.userRetention.percentage,
        weeklyData: analyticsData.userRetention.weeklyData.labels.map((label, index) => ({
          week: label,
          retentionRate: analyticsData.userRetention.weeklyData.data[index]
        }))
      },
      contentDistribution: analyticsData.contentDistribution.labels.map((label, index) => ({
        type: label,
        count: analyticsData.contentDistribution.data[index]
      })),
      deviceUsage: analyticsData.deviceUsage.labels.map((label, index) => ({
        device: label,
        percentage: analyticsData.deviceUsage.data[index]
      }))
    };

    // Export data based on type
    exportData(
      [exportableData],
      type,
      `analytics_${activeTab}_${dateRange}`
    );
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Analytics Dashboard</CardTitle>
          <CardDescription>
            Platform usage and performance metrics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row justify-between items-center mb-6">
            <div className="flex flex-col sm:flex-row gap-2 mt-4 md:mt-0">
              <div className="inline-flex rounded-md shadow-sm">
                <Button
                  variant={dateRange === '7d' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setDateRange('7d')}
                  className="rounded-l-md rounded-r-none"
                >
                  7D
                </Button>
                <Button
                  variant={dateRange === '30d' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setDateRange('30d')}
                  className="rounded-none border-l-0 border-r-0"
                >
                  30D
                </Button>
                <Button
                  variant={dateRange === '90d' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setDateRange('90d')}
                  className="rounded-none border-r-0"
                >
                  90D
                </Button>
                <Button
                  variant={dateRange === '1y' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setDateRange('1y')}
                  className="rounded-none border-r-0"
                >
                  1Y
                </Button>
                <Button
                  variant={dateRange === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setDateRange('all')}
                  className="rounded-r-md rounded-l-none"
                >
                  All
                </Button>
              </div>

              <div className="inline-flex rounded-md shadow-sm">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleExportData('csv')}
                  className="rounded-l-md rounded-r-none"
                >
                  <FileDown className="h-4 w-4 mr-1" />
                  CSV
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleExportData('json')}
                  className="rounded-l-none rounded-r-md"
                >
                  <Download className="h-4 w-4 mr-1" />
                  JSON
                </Button>
              </div>
            </div>
          </div>

          <Tabs defaultValue="overview" onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-4 mb-6">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="users">Users</TabsTrigger>
              <TabsTrigger value="content">Content</TabsTrigger>
              <TabsTrigger value="engagement">Engagement</TabsTrigger>
            </TabsList>
            <TabsContent value="overview" className="space-y-6">
              {isLoading ? (
                <div className="text-center py-8">
                  <p>Loading analytics data...</p>
                </div>
              ) : (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg">User Growth</CardTitle>
                        <CardDescription>New users over time</CardDescription>
                      </CardHeader>
                      <CardContent>
                        {renderLineChart(analyticsData.userGrowth.labels, analyticsData.userGrowth.data)}
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg">Content Creation</CardTitle>
                        <CardDescription>Voice messages posted over time</CardDescription>
                      </CardHeader>
                      <CardContent>
                        {renderBarChart(analyticsData.contentCreation.labels, analyticsData.contentCreation.data)}
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg">Content Distribution</CardTitle>
                        <CardDescription>Types of content on the platform</CardDescription>
                      </CardHeader>
                      <CardContent>
                        {renderPieChart(analyticsData.contentDistribution.labels, analyticsData.contentDistribution.data)}
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg">Device Usage</CardTitle>
                        <CardDescription>Platform access by device type</CardDescription>
                      </CardHeader>
                      <CardContent>
                        {renderPieChart(analyticsData.deviceUsage.labels, analyticsData.deviceUsage.data)}
                      </CardContent>
                    </Card>
                  </div>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">User Retention</CardTitle>
                      <CardDescription>Weekly retention rate: {analyticsData.userRetention.percentage}%</CardDescription>
                    </CardHeader>
                    <CardContent>
                      {renderLineChart(
                        analyticsData.userRetention.weeklyData.labels,
                        analyticsData.userRetention.weeklyData.data
                      )}
                    </CardContent>
                  </Card>
                </>
              )}
            </TabsContent>

            <TabsContent value="users" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">User Demographics</CardTitle>
                  <CardDescription>Detailed user statistics</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-center py-4">User demographics data will be implemented here.</p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="content" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Content Metrics</CardTitle>
                  <CardDescription>Detailed content statistics</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-center py-4">Content metrics data will be implemented here.</p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="engagement" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Engagement Metrics</CardTitle>
                  <CardDescription>User interaction statistics</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-center py-4">Engagement metrics data will be implemented here.</p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default Analytics;
