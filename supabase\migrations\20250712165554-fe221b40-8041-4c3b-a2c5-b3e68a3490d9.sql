-- Fix profile for current user with unique username
-- First check current profiles
DO $$
DECLARE
    user_exists BOOLEAN;
    unique_username TEXT;
BEGIN
    -- Check if user already has a profile
    SELECT EXISTS(SELECT 1 FROM profiles WHERE id = '0f59ebfe-9b88-48ca-a0e4-66a3f533d843' OR wallet_address = '0f59ebfe-9b88-48ca-a0e4-66a3f533d843') INTO user_exists;
    
    IF NOT user_exists THEN
        -- Generate a unique username
        unique_username := 'user_' || substr(replace('0f59ebfe-9b88-48ca-a0e4-66a3f533d843', '-', ''), 1, 8);
        
        -- Make sure it's unique
        WHILE EXISTS(SELECT 1 FROM profiles WHERE username = unique_username) LOOP
            unique_username := unique_username || '_' || floor(random() * 1000)::text;
        END LOOP;
        
        -- Insert the profile
        INSERT INTO profiles (
            id,
            wallet_address,
            username,
            display_name,
            bio,
            avatar_url,
            cover_image_url,
            social_links,
            created_at,
            updated_at
        ) VALUES (
            '0f59ebfe-9b88-48ca-a0e4-66a3f533d843',
            '0f59ebfe-9b88-48ca-a0e4-66a3f533d843',
            unique_username,
            'Your Name',
            'Welcome to my profile!',
            '',
            '',
            '{}',
            NOW(),
            NOW()
        );
        
        RAISE NOTICE 'Created profile with username: %', unique_username;
    ELSE
        RAISE NOTICE 'Profile already exists for user';
    END IF;
END $$;