export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON><PERSON> | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      admin_mfa: {
        Row: {
          admin_id: string | null
          backup_codes: <PERSON><PERSON>
          created_at: string | null
          id: string
          totp_secret: string
          updated_at: string | null
        }
        Insert: {
          admin_id?: string | null
          backup_codes: J<PERSON>
          created_at?: string | null
          id?: string
          totp_secret: string
          updated_at?: string | null
        }
        Update: {
          admin_id?: string | null
          backup_codes?: J<PERSON>
          created_at?: string | null
          id?: string
          totp_secret?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "admin_mfa_admin_id_fkey"
            columns: ["admin_id"]
            isOneToOne: true
            referencedRelation: "admin_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      admin_profiles: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          display_name: string | null
          email: string
          id: string
          last_login: string | null
          role: string
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          display_name?: string | null
          email: string
          id: string
          last_login?: string | null
          role?: string
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          display_name?: string | null
          email?: string
          id?: string
          last_login?: string | null
          role?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      admin_settings: {
        Row: {
          created_at: string | null
          id: string
          settings: Json
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id: string
          settings?: Json
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          settings?: Json
          updated_at?: string | null
        }
        Relationships: []
      }
      audit_logs: {
        Row: {
          action: string
          admin_id: string | null
          created_at: string | null
          details: Json | null
          entity_id: string
          entity_type: string
          id: string
          ip_address: string | null
          user_agent: string | null
        }
        Insert: {
          action: string
          admin_id?: string | null
          created_at?: string | null
          details?: Json | null
          entity_id: string
          entity_type: string
          id?: string
          ip_address?: string | null
          user_agent?: string | null
        }
        Update: {
          action?: string
          admin_id?: string | null
          created_at?: string | null
          details?: Json | null
          entity_id?: string
          entity_type?: string
          id?: string
          ip_address?: string | null
          user_agent?: string | null
        }
        Relationships: []
      }
      chain_voice_posts: {
        Row: {
          audio_duration: number
          audio_url: string
          event_type: string
          id: string
          metadata: Json | null
          source_chain: string
          tags: string[] | null
          timestamp: string
          title: string
          transcription: string | null
        }
        Insert: {
          audio_duration: number
          audio_url: string
          event_type: string
          id?: string
          metadata?: Json | null
          source_chain: string
          tags?: string[] | null
          timestamp?: string
          title: string
          transcription?: string | null
        }
        Update: {
          audio_duration?: number
          audio_url?: string
          event_type?: string
          id?: string
          metadata?: Json | null
          source_chain?: string
          tags?: string[] | null
          timestamp?: string
          title?: string
          transcription?: string | null
        }
        Relationships: []
      }
      channel_invites: {
        Row: {
          channel_id: string
          code: string
          created_at: string
          created_by: string
          expires_at: string | null
          id: string
          max_uses: number | null
          uses: number
        }
        Insert: {
          channel_id: string
          code: string
          created_at?: string
          created_by: string
          expires_at?: string | null
          id?: string
          max_uses?: number | null
          uses?: number
        }
        Update: {
          channel_id?: string
          code?: string
          created_at?: string
          created_by?: string
          expires_at?: string | null
          id?: string
          max_uses?: number | null
          uses?: number
        }
        Relationships: [
          {
            foreignKeyName: "channel_invites_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
        ]
      }
      channel_members: {
        Row: {
          ban_reason: string | null
          banned_at: string | null
          banned_by: string | null
          channel_id: string
          id: string
          is_banned: boolean | null
          is_muted: boolean | null
          joined_at: string
          last_activity: string | null
          muted_by: string | null
          muted_until: string | null
          permissions: Json | null
          profile_id: string
          role: string
          role_assigned_at: string | null
          role_assigned_by: string | null
        }
        Insert: {
          ban_reason?: string | null
          banned_at?: string | null
          banned_by?: string | null
          channel_id: string
          id?: string
          is_banned?: boolean | null
          is_muted?: boolean | null
          joined_at?: string
          last_activity?: string | null
          muted_by?: string | null
          muted_until?: string | null
          permissions?: Json | null
          profile_id: string
          role?: string
          role_assigned_at?: string | null
          role_assigned_by?: string | null
        }
        Update: {
          ban_reason?: string | null
          banned_at?: string | null
          banned_by?: string | null
          channel_id?: string
          id?: string
          is_banned?: boolean | null
          is_muted?: boolean | null
          joined_at?: string
          last_activity?: string | null
          muted_by?: string | null
          muted_until?: string | null
          permissions?: Json | null
          profile_id?: string
          role?: string
          role_assigned_at?: string | null
          role_assigned_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "channel_members_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
        ]
      }
      channel_sections: {
        Row: {
          channel_id: string
          created_at: string | null
          description: string | null
          id: string
          is_enabled: boolean | null
          name: string
          section_type: string
          settings: Json | null
          updated_at: string | null
        }
        Insert: {
          channel_id: string
          created_at?: string | null
          description?: string | null
          id?: string
          is_enabled?: boolean | null
          name: string
          section_type: string
          settings?: Json | null
          updated_at?: string | null
        }
        Update: {
          channel_id?: string
          created_at?: string | null
          description?: string | null
          id?: string
          is_enabled?: boolean | null
          name?: string
          section_type?: string
          settings?: Json | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "channel_sections_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
        ]
      }
      channel_themes: {
        Row: {
          accent_color: string | null
          background_type: string | null
          background_value: string | null
          border_radius: string | null
          channel_id: string
          created_at: string | null
          created_by: string | null
          custom_css: string | null
          font_family: string | null
          id: string
          is_active: boolean | null
          primary_color: string | null
          secondary_color: string | null
          theme_name: string
          updated_at: string | null
        }
        Insert: {
          accent_color?: string | null
          background_type?: string | null
          background_value?: string | null
          border_radius?: string | null
          channel_id: string
          created_at?: string | null
          created_by?: string | null
          custom_css?: string | null
          font_family?: string | null
          id?: string
          is_active?: boolean | null
          primary_color?: string | null
          secondary_color?: string | null
          theme_name: string
          updated_at?: string | null
        }
        Update: {
          accent_color?: string | null
          background_type?: string | null
          background_value?: string | null
          border_radius?: string | null
          channel_id?: string
          created_at?: string | null
          created_by?: string | null
          custom_css?: string | null
          font_family?: string | null
          id?: string
          is_active?: boolean | null
          primary_color?: string | null
          secondary_color?: string | null
          theme_name?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "channel_themes_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
        ]
      }
      channel_treasury: {
        Row: {
          balance: number | null
          channel_id: string
          created_at: string | null
          id: string
          last_updated: string | null
          token_id: string
          total_allocations: number | null
          total_contributions: number | null
        }
        Insert: {
          balance?: number | null
          channel_id: string
          created_at?: string | null
          id?: string
          last_updated?: string | null
          token_id: string
          total_allocations?: number | null
          total_contributions?: number | null
        }
        Update: {
          balance?: number | null
          channel_id?: string
          created_at?: string | null
          id?: string
          last_updated?: string | null
          token_id?: string
          total_allocations?: number | null
          total_contributions?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "channel_treasury_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "channel_treasury_token_id_fkey"
            columns: ["token_id"]
            isOneToOne: false
            referencedRelation: "tokens"
            referencedColumns: ["id"]
          },
        ]
      }
      channels: {
        Row: {
          cover_image_url: string | null
          created_at: string
          created_by: string | null
          description: string | null
          id: string
          is_private: boolean
          name: string
          rules: string | null
          tags: string[] | null
          updated_at: string
        }
        Insert: {
          cover_image_url?: string | null
          created_at?: string
          created_by?: string | null
          description?: string | null
          id?: string
          is_private?: boolean
          name: string
          rules?: string | null
          tags?: string[] | null
          updated_at?: string
        }
        Update: {
          cover_image_url?: string | null
          created_at?: string
          created_by?: string | null
          description?: string | null
          id?: string
          is_private?: boolean
          name?: string
          rules?: string | null
          tags?: string[] | null
          updated_at?: string
        }
        Relationships: []
      }
      comments: {
        Row: {
          content: string
          created_at: string | null
          id: string
          post_id: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          content: string
          created_at?: string | null
          id?: string
          post_id: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          content?: string
          created_at?: string | null
          id?: string
          post_id?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      content_reports: {
        Row: {
          content_id: string
          content_type: string
          created_at: string | null
          details: string | null
          id: string
          reason: string
          reported_at: string | null
          reported_user_id: string | null
          reporter_id: string | null
          resolved_at: string | null
          status: string
          updated_at: string | null
        }
        Insert: {
          content_id: string
          content_type: string
          created_at?: string | null
          details?: string | null
          id?: string
          reason: string
          reported_at?: string | null
          reported_user_id?: string | null
          reporter_id?: string | null
          resolved_at?: string | null
          status?: string
          updated_at?: string | null
        }
        Update: {
          content_id?: string
          content_type?: string
          created_at?: string | null
          details?: string | null
          id?: string
          reason?: string
          reported_at?: string | null
          reported_user_id?: string | null
          reporter_id?: string | null
          resolved_at?: string | null
          status?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      cover_image_url: {
        Row: {
          created_at: string
          id: number
        }
        Insert: {
          created_at?: string
          id?: number
        }
        Update: {
          created_at?: string
          id?: number
        }
        Relationships: []
      }
      creator_earnings: {
        Row: {
          channel_id: string | null
          created_at: string | null
          engagement_score: number | null
          id: string
          period_end: string
          period_start: string
          profile_id: string
          rewards_earned_amount: number | null
          rewards_earned_count: number | null
          rewards_earned_usd: number | null
          tips_received_amount: number | null
          tips_received_count: number | null
          tips_received_usd: number | null
          top_content_ids: string[] | null
          top_tippers: Json | null
          updated_at: string | null
          voice_posts_count: number | null
          voice_replies_count: number | null
        }
        Insert: {
          channel_id?: string | null
          created_at?: string | null
          engagement_score?: number | null
          id?: string
          period_end: string
          period_start: string
          profile_id: string
          rewards_earned_amount?: number | null
          rewards_earned_count?: number | null
          rewards_earned_usd?: number | null
          tips_received_amount?: number | null
          tips_received_count?: number | null
          tips_received_usd?: number | null
          top_content_ids?: string[] | null
          top_tippers?: Json | null
          updated_at?: string | null
          voice_posts_count?: number | null
          voice_replies_count?: number | null
        }
        Update: {
          channel_id?: string | null
          created_at?: string | null
          engagement_score?: number | null
          id?: string
          period_end?: string
          period_start?: string
          profile_id?: string
          rewards_earned_amount?: number | null
          rewards_earned_count?: number | null
          rewards_earned_usd?: number | null
          tips_received_amount?: number | null
          tips_received_count?: number | null
          tips_received_usd?: number | null
          top_content_ids?: string[] | null
          top_tippers?: Json | null
          updated_at?: string | null
          voice_posts_count?: number | null
          voice_replies_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "creator_earnings_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
        ]
      }
      deleted_messages: {
        Row: {
          audio_duration: number | null
          audio_url: string | null
          created_at: string | null
          deleted_at: string | null
          id: string
          media: Json | null
          original_id: string
          parent_id: string | null
          profile_id: string
          transcript: string | null
        }
        Insert: {
          audio_duration?: number | null
          audio_url?: string | null
          created_at?: string | null
          deleted_at?: string | null
          id?: string
          media?: Json | null
          original_id: string
          parent_id?: string | null
          profile_id: string
          transcript?: string | null
        }
        Update: {
          audio_duration?: number | null
          audio_url?: string | null
          created_at?: string | null
          deleted_at?: string | null
          id?: string
          media?: Json | null
          original_id?: string
          parent_id?: string | null
          profile_id?: string
          transcript?: string | null
        }
        Relationships: []
      }
      drafts: {
        Row: {
          audio_duration: number | null
          audio_url: string | null
          created_at: string
          id: string
          media: Json | null
          metadata: Json | null
          title: string | null
          transcript: string | null
          type: string
          updated_at: string
          user_id: string
        }
        Insert: {
          audio_duration?: number | null
          audio_url?: string | null
          created_at?: string
          id?: string
          media?: Json | null
          metadata?: Json | null
          title?: string | null
          transcript?: string | null
          type: string
          updated_at?: string
          user_id: string
        }
        Update: {
          audio_duration?: number | null
          audio_url?: string | null
          created_at?: string
          id?: string
          media?: Json | null
          metadata?: Json | null
          title?: string | null
          transcript?: string | null
          type?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      drop_unlocks: {
        Row: {
          drop_id: string
          id: string
          payment_amount: number | null
          payment_token: string | null
          profile_id: string
          transaction_hash: string | null
          unlock_method: string
          unlocked_at: string | null
        }
        Insert: {
          drop_id: string
          id?: string
          payment_amount?: number | null
          payment_token?: string | null
          profile_id: string
          transaction_hash?: string | null
          unlock_method: string
          unlocked_at?: string | null
        }
        Update: {
          drop_id?: string
          id?: string
          payment_amount?: number | null
          payment_token?: string | null
          profile_id?: string
          transaction_hash?: string | null
          unlock_method?: string
          unlocked_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "drop_unlocks_drop_id_fkey"
            columns: ["drop_id"]
            isOneToOne: false
            referencedRelation: "drops_releases"
            referencedColumns: ["id"]
          },
        ]
      }
      drops_releases: {
        Row: {
          audio_duration: number
          audio_url: string
          channel_id: string
          content_type: string
          cover_image_url: string | null
          created_at: string | null
          creator_profile_id: string
          current_unlocks: number | null
          description: string | null
          id: string
          is_exclusive: boolean | null
          max_unlocks: number | null
          release_type: string
          section_id: string | null
          tags: string[] | null
          title: string
          unlock_price: number | null
          unlock_time: string | null
          unlock_token: string | null
          updated_at: string | null
        }
        Insert: {
          audio_duration: number
          audio_url: string
          channel_id: string
          content_type: string
          cover_image_url?: string | null
          created_at?: string | null
          creator_profile_id: string
          current_unlocks?: number | null
          description?: string | null
          id?: string
          is_exclusive?: boolean | null
          max_unlocks?: number | null
          release_type?: string
          section_id?: string | null
          tags?: string[] | null
          title: string
          unlock_price?: number | null
          unlock_time?: string | null
          unlock_token?: string | null
          updated_at?: string | null
        }
        Update: {
          audio_duration?: number
          audio_url?: string
          channel_id?: string
          content_type?: string
          cover_image_url?: string | null
          created_at?: string | null
          creator_profile_id?: string
          current_unlocks?: number | null
          description?: string | null
          id?: string
          is_exclusive?: boolean | null
          max_unlocks?: number | null
          release_type?: string
          section_id?: string | null
          tags?: string[] | null
          title?: string
          unlock_price?: number | null
          unlock_time?: string | null
          unlock_token?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "drops_releases_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "drops_releases_section_id_fkey"
            columns: ["section_id"]
            isOneToOne: false
            referencedRelation: "channel_sections"
            referencedColumns: ["id"]
          },
        ]
      }
      engagement_rewards: {
        Row: {
          activity_id: string | null
          activity_type: string | null
          amount: number
          amount_usd: number | null
          channel_id: string | null
          claimed_at: string | null
          created_at: string | null
          engagement_score: number | null
          expires_at: string | null
          id: string
          is_claimed: boolean | null
          metadata: Json | null
          multiplier: number | null
          profile_id: string
          quality_score: number | null
          reward_type: string
          streak_count: number | null
          token_id: string
        }
        Insert: {
          activity_id?: string | null
          activity_type?: string | null
          amount: number
          amount_usd?: number | null
          channel_id?: string | null
          claimed_at?: string | null
          created_at?: string | null
          engagement_score?: number | null
          expires_at?: string | null
          id?: string
          is_claimed?: boolean | null
          metadata?: Json | null
          multiplier?: number | null
          profile_id: string
          quality_score?: number | null
          reward_type: string
          streak_count?: number | null
          token_id: string
        }
        Update: {
          activity_id?: string | null
          activity_type?: string | null
          amount?: number
          amount_usd?: number | null
          channel_id?: string | null
          claimed_at?: string | null
          created_at?: string | null
          engagement_score?: number | null
          expires_at?: string | null
          id?: string
          is_claimed?: boolean | null
          metadata?: Json | null
          multiplier?: number | null
          profile_id?: string
          quality_score?: number | null
          reward_type?: string
          streak_count?: number | null
          token_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "engagement_rewards_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "engagement_rewards_token_id_fkey"
            columns: ["token_id"]
            isOneToOne: false
            referencedRelation: "tokens"
            referencedColumns: ["id"]
          },
        ]
      }
      engagement_streaks: {
        Row: {
          channel_id: string | null
          created_at: string | null
          current_streak: number | null
          id: string
          last_activity_date: string | null
          longest_streak: number | null
          profile_id: string
          streak_multiplier: number | null
          streak_start_date: string | null
          total_active_days: number | null
          updated_at: string | null
        }
        Insert: {
          channel_id?: string | null
          created_at?: string | null
          current_streak?: number | null
          id?: string
          last_activity_date?: string | null
          longest_streak?: number | null
          profile_id: string
          streak_multiplier?: number | null
          streak_start_date?: string | null
          total_active_days?: number | null
          updated_at?: string | null
        }
        Update: {
          channel_id?: string | null
          created_at?: string | null
          current_streak?: number | null
          id?: string
          last_activity_date?: string | null
          longest_streak?: number | null
          profile_id?: string
          streak_multiplier?: number | null
          streak_start_date?: string | null
          total_active_days?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "engagement_streaks_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
        ]
      }
      follows: {
        Row: {
          created_at: string
          follower_id: string
          following_id: string
          id: number
        }
        Insert: {
          created_at?: string
          follower_id: string
          following_id: string
          id?: never
        }
        Update: {
          created_at?: string
          follower_id?: string
          following_id?: string
          id?: never
        }
        Relationships: []
      }
      journal_media: {
        Row: {
          created_at: string
          id: string
          journal_id: string
          type: string
          url: string
        }
        Insert: {
          created_at?: string
          id: string
          journal_id: string
          type: string
          url: string
        }
        Update: {
          created_at?: string
          id?: string
          journal_id?: string
          type?: string
          url?: string
        }
        Relationships: [
          {
            foreignKeyName: "journal_media_journal_id_fkey"
            columns: ["journal_id"]
            isOneToOne: false
            referencedRelation: "journals"
            referencedColumns: ["id"]
          },
        ]
      }
      journal_tips: {
        Row: {
          amount: number
          created_at: string
          currency: string
          from_user_id: string
          id: string
          journal_id: string
          to_user_id: string
          updated_at: string
        }
        Insert: {
          amount: number
          created_at?: string
          currency?: string
          from_user_id: string
          id?: string
          journal_id: string
          to_user_id: string
          updated_at?: string
        }
        Update: {
          amount?: number
          created_at?: string
          currency?: string
          from_user_id?: string
          id?: string
          journal_id?: string
          to_user_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "journal_tips_journal_id_fkey"
            columns: ["journal_id"]
            isOneToOne: false
            referencedRelation: "journals"
            referencedColumns: ["id"]
          },
        ]
      }
      journals: {
        Row: {
          audio_duration: number | null
          audio_url: string
          created_at: string
          id: string
          is_locked: boolean | null
          is_published: boolean | null
          profile_id: string
          scheduled_for: string | null
          title: string
          transcript: string | null
          unlock_condition: Json | null
          updated_at: string | null
        }
        Insert: {
          audio_duration?: number | null
          audio_url: string
          created_at?: string
          id: string
          is_locked?: boolean | null
          is_published?: boolean | null
          profile_id: string
          scheduled_for?: string | null
          title: string
          transcript?: string | null
          unlock_condition?: Json | null
          updated_at?: string | null
        }
        Update: {
          audio_duration?: number | null
          audio_url?: string
          created_at?: string
          id?: string
          is_locked?: boolean | null
          is_published?: boolean | null
          profile_id?: string
          scheduled_for?: string | null
          title?: string
          transcript?: string | null
          unlock_condition?: Json | null
          updated_at?: string | null
        }
        Relationships: []
      }
      like_count: {
        Row: {
          created_at: string
          id: number
        }
        Insert: {
          created_at?: string
          id?: number
        }
        Update: {
          created_at?: string
          id?: number
        }
        Relationships: []
      }
      likes: {
        Row: {
          created_at: string
          id: string
          profile_id: string
          voice_message_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          profile_id: string
          voice_message_id: string
        }
        Update: {
          created_at?: string
          id?: string
          profile_id?: string
          voice_message_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "likes_voice_message_id_fkey"
            columns: ["voice_message_id"]
            isOneToOne: false
            referencedRelation: "voice_messages"
            referencedColumns: ["id"]
          },
        ]
      }
      live_streams: {
        Row: {
          actual_start: string | null
          audience_can_request_mic: boolean | null
          channel_id: string
          chat_enabled: boolean | null
          created_at: string | null
          description: string | null
          ended_at: string | null
          host_profile_id: string
          id: string
          is_recorded: boolean | null
          max_participants: number | null
          recording_url: string | null
          scheduled_start: string | null
          section_id: string | null
          settings: Json | null
          status: string
          title: string
          updated_at: string | null
        }
        Insert: {
          actual_start?: string | null
          audience_can_request_mic?: boolean | null
          channel_id: string
          chat_enabled?: boolean | null
          created_at?: string | null
          description?: string | null
          ended_at?: string | null
          host_profile_id: string
          id?: string
          is_recorded?: boolean | null
          max_participants?: number | null
          recording_url?: string | null
          scheduled_start?: string | null
          section_id?: string | null
          settings?: Json | null
          status?: string
          title: string
          updated_at?: string | null
        }
        Update: {
          actual_start?: string | null
          audience_can_request_mic?: boolean | null
          channel_id?: string
          chat_enabled?: boolean | null
          created_at?: string | null
          description?: string | null
          ended_at?: string | null
          host_profile_id?: string
          id?: string
          is_recorded?: boolean | null
          max_participants?: number | null
          recording_url?: string | null
          scheduled_start?: string | null
          section_id?: string | null
          settings?: Json | null
          status?: string
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "live_streams_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "live_streams_section_id_fkey"
            columns: ["section_id"]
            isOneToOne: false
            referencedRelation: "channel_sections"
            referencedColumns: ["id"]
          },
        ]
      }
      member_participation: {
        Row: {
          badges: string[] | null
          channel_id: string
          created_at: string | null
          id: string
          last_activity: string | null
          participation_score: number | null
          profile_id: string
          reactions_given: number | null
          reactions_received: number | null
          tips_given: number | null
          tips_received: number | null
          total_voice_duration: number | null
          updated_at: string | null
          voice_messages_count: number | null
        }
        Insert: {
          badges?: string[] | null
          channel_id: string
          created_at?: string | null
          id?: string
          last_activity?: string | null
          participation_score?: number | null
          profile_id: string
          reactions_given?: number | null
          reactions_received?: number | null
          tips_given?: number | null
          tips_received?: number | null
          total_voice_duration?: number | null
          updated_at?: string | null
          voice_messages_count?: number | null
        }
        Update: {
          badges?: string[] | null
          channel_id?: string
          created_at?: string | null
          id?: string
          last_activity?: string | null
          participation_score?: number | null
          profile_id?: string
          reactions_given?: number | null
          reactions_received?: number | null
          tips_given?: number | null
          tips_received?: number | null
          total_voice_duration?: number | null
          updated_at?: string | null
          voice_messages_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "member_participation_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
        ]
      }
      members_lounge: {
        Row: {
          allow_recordings: boolean | null
          channel_id: string
          created_at: string | null
          description: string | null
          id: string
          is_private: boolean | null
          leaderboard_enabled: boolean | null
          name: string
          section_id: string | null
          settings: Json | null
          updated_at: string | null
        }
        Insert: {
          allow_recordings?: boolean | null
          channel_id: string
          created_at?: string | null
          description?: string | null
          id?: string
          is_private?: boolean | null
          leaderboard_enabled?: boolean | null
          name?: string
          section_id?: string | null
          settings?: Json | null
          updated_at?: string | null
        }
        Update: {
          allow_recordings?: boolean | null
          channel_id?: string
          created_at?: string | null
          description?: string | null
          id?: string
          is_private?: boolean | null
          leaderboard_enabled?: boolean | null
          name?: string
          section_id?: string | null
          settings?: Json | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "members_lounge_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "members_lounge_section_id_fkey"
            columns: ["section_id"]
            isOneToOne: false
            referencedRelation: "channel_sections"
            referencedColumns: ["id"]
          },
        ]
      }
      moderation_actions: {
        Row: {
          action_type: string
          channel_id: string
          created_at: string | null
          duration_minutes: number | null
          id: string
          metadata: Json | null
          moderator_profile_id: string
          reason: string | null
          target_content_id: string | null
          target_content_type: string | null
          target_profile_id: string | null
        }
        Insert: {
          action_type: string
          channel_id: string
          created_at?: string | null
          duration_minutes?: number | null
          id?: string
          metadata?: Json | null
          moderator_profile_id: string
          reason?: string | null
          target_content_id?: string | null
          target_content_type?: string | null
          target_profile_id?: string | null
        }
        Update: {
          action_type?: string
          channel_id?: string
          created_at?: string | null
          duration_minutes?: number | null
          id?: string
          metadata?: Json | null
          moderator_profile_id?: string
          reason?: string | null
          target_content_id?: string | null
          target_content_type?: string | null
          target_profile_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "moderation_actions_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          created_at: string
          data: Json | null
          from_address: string | null
          id: string
          message_id: string | null
          read: boolean
          to_address: string
          type: string
        }
        Insert: {
          created_at?: string
          data?: Json | null
          from_address?: string | null
          id?: string
          message_id?: string | null
          read?: boolean
          to_address: string
          type: string
        }
        Update: {
          created_at?: string
          data?: Json | null
          from_address?: string | null
          id?: string
          message_id?: string | null
          read?: boolean
          to_address?: string
          type?: string
        }
        Relationships: []
      }
      post_count: {
        Row: {
          created_at: string
          id: number
        }
        Insert: {
          created_at?: string
          id?: number
        }
        Update: {
          created_at?: string
          id?: number
        }
        Relationships: []
      }
      post_reactions: {
        Row: {
          created_at: string
          id: string
          post_id: string
          reaction_type: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id: string
          post_id: string
          reaction_type: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          post_id?: string
          reaction_type?: string
          user_id?: string
        }
        Relationships: []
      }
      profile_stats: {
        Row: {
          followers_count: number
          following_count: number
          profile_id: string
          updated_at: string
        }
        Insert: {
          followers_count?: number
          following_count?: number
          profile_id: string
          updated_at?: string
        }
        Update: {
          followers_count?: number
          following_count?: number
          profile_id?: string
          updated_at?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          avatar_url: string | null
          bio: string | null
          cover_image_url: string | null
          created_at: string
          display_name: string | null
          flag_reason: string | null
          flagged_at: string | null
          id: string
          is_flagged: boolean | null
          is_verified: boolean | null
          last_login: string | null
          like_count: number | null
          post_count: number | null
          profile_image_url: string | null
          social_links: Json | null
          tip_count: number | null
          updated_at: string
          username: string | null
          verification_type: string | null
          verified_at: string | null
          wallet_address: string | null
        }
        Insert: {
          avatar_url?: string | null
          bio?: string | null
          cover_image_url?: string | null
          created_at?: string
          display_name?: string | null
          flag_reason?: string | null
          flagged_at?: string | null
          id?: string
          is_flagged?: boolean | null
          is_verified?: boolean | null
          last_login?: string | null
          like_count?: number | null
          post_count?: number | null
          profile_image_url?: string | null
          social_links?: Json | null
          tip_count?: number | null
          updated_at?: string
          username?: string | null
          verification_type?: string | null
          verified_at?: string | null
          wallet_address?: string | null
        }
        Update: {
          avatar_url?: string | null
          bio?: string | null
          cover_image_url?: string | null
          created_at?: string
          display_name?: string | null
          flag_reason?: string | null
          flagged_at?: string | null
          id?: string
          is_flagged?: boolean | null
          is_verified?: boolean | null
          last_login?: string | null
          like_count?: number | null
          post_count?: number | null
          profile_image_url?: string | null
          social_links?: Json | null
          tip_count?: number | null
          updated_at?: string
          username?: string | null
          verification_type?: string | null
          verified_at?: string | null
          wallet_address?: string | null
        }
        Relationships: []
      }
      proposals: {
        Row: {
          audio_duration: number | null
          audio_proposal_url: string | null
          channel_id: string
          created_at: string | null
          description: string | null
          executed_at: string | null
          executed_by: string | null
          execution_data: Json | null
          id: string
          min_approval_percentage: number | null
          min_participation: number | null
          proposal_type: string
          proposer_profile_id: string
          status: string
          title: string
          transcript: string | null
          updated_at: string | null
          voting_end: string
          voting_start: string | null
          voting_type: string
        }
        Insert: {
          audio_duration?: number | null
          audio_proposal_url?: string | null
          channel_id: string
          created_at?: string | null
          description?: string | null
          executed_at?: string | null
          executed_by?: string | null
          execution_data?: Json | null
          id?: string
          min_approval_percentage?: number | null
          min_participation?: number | null
          proposal_type: string
          proposer_profile_id: string
          status?: string
          title: string
          transcript?: string | null
          updated_at?: string | null
          voting_end: string
          voting_start?: string | null
          voting_type?: string
        }
        Update: {
          audio_duration?: number | null
          audio_proposal_url?: string | null
          channel_id?: string
          created_at?: string | null
          description?: string | null
          executed_at?: string | null
          executed_by?: string | null
          execution_data?: Json | null
          id?: string
          min_approval_percentage?: number | null
          min_participation?: number | null
          proposal_type?: string
          proposer_profile_id?: string
          status?: string
          title?: string
          transcript?: string | null
          updated_at?: string | null
          voting_end?: string
          voting_start?: string | null
          voting_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "proposals_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
        ]
      }
      reactions: {
        Row: {
          created_at: string | null
          id: string
          post_id: string
          reaction_type: string
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          post_id: string
          reaction_type: string
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          post_id?: string
          reaction_type?: string
          user_id?: string | null
        }
        Relationships: []
      }
      recorded_spaces: {
        Row: {
          created_at: string | null
          description: string | null
          duration_minutes: number | null
          ended_at: string | null
          host_profile_id: string
          id: string
          is_public: boolean | null
          participant_count: number | null
          recording_url: string | null
          space_id: string
          tags: string[] | null
          thumbnail_url: string | null
          title: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          duration_minutes?: number | null
          ended_at?: string | null
          host_profile_id: string
          id?: string
          is_public?: boolean | null
          participant_count?: number | null
          recording_url?: string | null
          space_id: string
          tags?: string[] | null
          thumbnail_url?: string | null
          title: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          duration_minutes?: number | null
          ended_at?: string | null
          host_profile_id?: string
          id?: string
          is_public?: boolean | null
          participant_count?: number | null
          recording_url?: string | null
          space_id?: string
          tags?: string[] | null
          thumbnail_url?: string | null
          title?: string
        }
        Relationships: []
      }
      reposts: {
        Row: {
          created_at: string
          id: string
          post_id: string
          post_type: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          post_id: string
          post_type: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          post_id?: string
          post_type?: string
          user_id?: string
        }
        Relationships: []
      }
      role_permissions: {
        Row: {
          created_at: string | null
          id: string
          is_default: boolean | null
          permission_description: string | null
          permission_name: string
          role_name: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_default?: boolean | null
          permission_description?: string | null
          permission_name: string
          role_name: string
        }
        Update: {
          created_at?: string | null
          id?: string
          is_default?: boolean | null
          permission_description?: string | null
          permission_name?: string
          role_name?: string
        }
        Relationships: []
      }
      shares: {
        Row: {
          created_at: string | null
          id: string
          post_id: string
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          post_id: string
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          post_id?: string
          user_id?: string | null
        }
        Relationships: []
      }
      social_links: {
        Row: {
          created_at: string
          id: number
        }
        Insert: {
          created_at?: string
          id?: number
        }
        Update: {
          created_at?: string
          id?: number
        }
        Relationships: []
      }
      story_comments: {
        Row: {
          comment: string
          created_at: string | null
          id: string
          story_id: string | null
          user_id: string
        }
        Insert: {
          comment: string
          created_at?: string | null
          id?: string
          story_id?: string | null
          user_id: string
        }
        Update: {
          comment?: string
          created_at?: string | null
          id?: string
          story_id?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "story_comments_story_id_fkey"
            columns: ["story_id"]
            isOneToOne: false
            referencedRelation: "voice_stories"
            referencedColumns: ["id"]
          },
        ]
      }
      story_highlights: {
        Row: {
          cover_image_url: string | null
          created_at: string | null
          highlight_description: string | null
          highlight_name: string
          id: string
          profile_id: string
          story_id: string | null
        }
        Insert: {
          cover_image_url?: string | null
          created_at?: string | null
          highlight_description?: string | null
          highlight_name: string
          id?: string
          profile_id: string
          story_id?: string | null
        }
        Update: {
          cover_image_url?: string | null
          created_at?: string | null
          highlight_description?: string | null
          highlight_name?: string
          id?: string
          profile_id?: string
          story_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "story_highlights_story_id_fkey"
            columns: ["story_id"]
            isOneToOne: false
            referencedRelation: "voice_stories"
            referencedColumns: ["id"]
          },
        ]
      }
      story_likes: {
        Row: {
          created_at: string | null
          id: string
          story_id: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          story_id?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          story_id?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "story_likes_story_id_fkey"
            columns: ["story_id"]
            isOneToOne: false
            referencedRelation: "voice_stories"
            referencedColumns: ["id"]
          },
        ]
      }
      story_mentions: {
        Row: {
          created_at: string | null
          id: string
          mentioned_by_id: string
          mentioned_user_id: string
          story_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          mentioned_by_id: string
          mentioned_user_id: string
          story_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          mentioned_by_id?: string
          mentioned_user_id?: string
          story_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "story_mentions_story_id_fkey"
            columns: ["story_id"]
            isOneToOne: false
            referencedRelation: "voice_stories"
            referencedColumns: ["id"]
          },
        ]
      }
      story_replies: {
        Row: {
          audio_duration: number
          audio_url: string
          created_at: string | null
          id: string
          profile_id: string
          story_id: string | null
          transcript: string | null
        }
        Insert: {
          audio_duration: number
          audio_url: string
          created_at?: string | null
          id?: string
          profile_id: string
          story_id?: string | null
          transcript?: string | null
        }
        Update: {
          audio_duration?: number
          audio_url?: string
          created_at?: string | null
          id?: string
          profile_id?: string
          story_id?: string | null
          transcript?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "story_replies_story_id_fkey"
            columns: ["story_id"]
            isOneToOne: false
            referencedRelation: "voice_stories"
            referencedColumns: ["id"]
          },
        ]
      }
      story_views: {
        Row: {
          id: string
          story_id: string | null
          view_duration: number | null
          viewed_at: string | null
          viewer_id: string
        }
        Insert: {
          id?: string
          story_id?: string | null
          view_duration?: number | null
          viewed_at?: string | null
          viewer_id: string
        }
        Update: {
          id?: string
          story_id?: string | null
          view_duration?: number | null
          viewed_at?: string | null
          viewer_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "story_views_story_id_fkey"
            columns: ["story_id"]
            isOneToOne: false
            referencedRelation: "voice_stories"
            referencedColumns: ["id"]
          },
        ]
      }
      stream_chat: {
        Row: {
          created_at: string | null
          id: string
          message: string
          message_type: string | null
          profile_id: string
          stream_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          message: string
          message_type?: string | null
          profile_id: string
          stream_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          message?: string
          message_type?: string | null
          profile_id?: string
          stream_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "stream_chat_stream_id_fkey"
            columns: ["stream_id"]
            isOneToOne: false
            referencedRelation: "live_streams"
            referencedColumns: ["id"]
          },
        ]
      }
      stream_participants: {
        Row: {
          id: string
          is_muted: boolean | null
          joined_at: string | null
          left_at: string | null
          mic_granted_at: string | null
          mic_requested_at: string | null
          profile_id: string
          role: string
          stream_id: string
        }
        Insert: {
          id?: string
          is_muted?: boolean | null
          joined_at?: string | null
          left_at?: string | null
          mic_granted_at?: string | null
          mic_requested_at?: string | null
          profile_id: string
          role?: string
          stream_id: string
        }
        Update: {
          id?: string
          is_muted?: boolean | null
          joined_at?: string | null
          left_at?: string | null
          mic_granted_at?: string | null
          mic_requested_at?: string | null
          profile_id?: string
          role?: string
          stream_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "stream_participants_stream_id_fkey"
            columns: ["stream_id"]
            isOneToOne: false
            referencedRelation: "live_streams"
            referencedColumns: ["id"]
          },
        ]
      }
      tip_balances: {
        Row: {
          created_at: string
          eth_balance: number
          id: string
          sol_balance: number
          total_received_eth: number
          total_received_sol: number
          total_received_usdc: number
          total_sent_eth: number
          total_sent_sol: number
          total_sent_usdc: number
          updated_at: string
          usdc_balance: number
          user_id: string | null
        }
        Insert: {
          created_at?: string
          eth_balance?: number
          id?: string
          sol_balance?: number
          total_received_eth?: number
          total_received_sol?: number
          total_received_usdc?: number
          total_sent_eth?: number
          total_sent_sol?: number
          total_sent_usdc?: number
          updated_at?: string
          usdc_balance?: number
          user_id?: string | null
        }
        Update: {
          created_at?: string
          eth_balance?: number
          id?: string
          sol_balance?: number
          total_received_eth?: number
          total_received_sol?: number
          total_received_usdc?: number
          total_sent_eth?: number
          total_sent_sol?: number
          total_sent_usdc?: number
          updated_at?: string
          usdc_balance?: number
          user_id?: string | null
        }
        Relationships: []
      }
      tip_count: {
        Row: {
          created_at: string
          id: number
        }
        Insert: {
          created_at?: string
          id?: number
        }
        Update: {
          created_at?: string
          id?: number
        }
        Relationships: []
      }
      tips: {
        Row: {
          amount: number
          created_at: string
          currency: string
          id: string
          receiver_id: string
          sender_id: string
          voice_message_id: string
        }
        Insert: {
          amount: number
          created_at?: string
          currency: string
          id?: string
          receiver_id: string
          sender_id: string
          voice_message_id: string
        }
        Update: {
          amount?: number
          created_at?: string
          currency?: string
          id?: string
          receiver_id?: string
          sender_id?: string
          voice_message_id?: string
        }
        Relationships: []
      }
      tokens: {
        Row: {
          chain_id: number | null
          contract_address: string | null
          created_at: string | null
          decimals: number | null
          icon_url: string | null
          id: string
          is_enabled: boolean | null
          is_stablecoin: boolean | null
          last_price_update: string | null
          name: string
          price_usd: number | null
          symbol: string
        }
        Insert: {
          chain_id?: number | null
          contract_address?: string | null
          created_at?: string | null
          decimals?: number | null
          icon_url?: string | null
          id?: string
          is_enabled?: boolean | null
          is_stablecoin?: boolean | null
          last_price_update?: string | null
          name: string
          price_usd?: number | null
          symbol: string
        }
        Update: {
          chain_id?: number | null
          contract_address?: string | null
          created_at?: string | null
          decimals?: number | null
          icon_url?: string | null
          id?: string
          is_enabled?: boolean | null
          is_stablecoin?: boolean | null
          last_price_update?: string | null
          name?: string
          price_usd?: number | null
          symbol?: string
        }
        Relationships: []
      }
      topics: {
        Row: {
          channel_id: string
          created_at: string | null
          creator_profile_id: string
          description: string | null
          hashtags: string[] | null
          id: string
          is_locked: boolean | null
          is_pinned: boolean | null
          last_reply_at: string | null
          reply_count: number | null
          section_id: string | null
          title: string
          updated_at: string | null
        }
        Insert: {
          channel_id: string
          created_at?: string | null
          creator_profile_id: string
          description?: string | null
          hashtags?: string[] | null
          id?: string
          is_locked?: boolean | null
          is_pinned?: boolean | null
          last_reply_at?: string | null
          reply_count?: number | null
          section_id?: string | null
          title: string
          updated_at?: string | null
        }
        Update: {
          channel_id?: string
          created_at?: string | null
          creator_profile_id?: string
          description?: string | null
          hashtags?: string[] | null
          id?: string
          is_locked?: boolean | null
          is_pinned?: boolean | null
          last_reply_at?: string | null
          reply_count?: number | null
          section_id?: string | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "topics_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "topics_section_id_fkey"
            columns: ["section_id"]
            isOneToOne: false
            referencedRelation: "channel_sections"
            referencedColumns: ["id"]
          },
        ]
      }
      treasury_transactions: {
        Row: {
          amount: number
          amount_usd: number | null
          channel_id: string
          confirmed_at: string | null
          created_at: string | null
          description: string | null
          id: string
          profile_id: string
          proposal_id: string | null
          status: string
          token_id: string
          transaction_hash: string | null
          transaction_type: string
        }
        Insert: {
          amount: number
          amount_usd?: number | null
          channel_id: string
          confirmed_at?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          profile_id: string
          proposal_id?: string | null
          status?: string
          token_id: string
          transaction_hash?: string | null
          transaction_type: string
        }
        Update: {
          amount?: number
          amount_usd?: number | null
          channel_id?: string
          confirmed_at?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          profile_id?: string
          proposal_id?: string | null
          status?: string
          token_id?: string
          transaction_hash?: string | null
          transaction_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "treasury_transactions_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "treasury_transactions_proposal_id_fkey"
            columns: ["proposal_id"]
            isOneToOne: false
            referencedRelation: "proposals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "treasury_transactions_token_id_fkey"
            columns: ["token_id"]
            isOneToOne: false
            referencedRelation: "tokens"
            referencedColumns: ["id"]
          },
        ]
      }
      trending_content: {
        Row: {
          channel_id: string | null
          content_id: string
          content_type: string
          created_at: string | null
          engagement_count: number | null
          id: string
          metadata: Json | null
          profile_id: string | null
          share_count: number | null
          time_period: string | null
          trend_score: number | null
          trending_date: string | null
          updated_at: string | null
          view_count: number | null
        }
        Insert: {
          channel_id?: string | null
          content_id: string
          content_type: string
          created_at?: string | null
          engagement_count?: number | null
          id?: string
          metadata?: Json | null
          profile_id?: string | null
          share_count?: number | null
          time_period?: string | null
          trend_score?: number | null
          trending_date?: string | null
          updated_at?: string | null
          view_count?: number | null
        }
        Update: {
          channel_id?: string | null
          content_id?: string
          content_type?: string
          created_at?: string | null
          engagement_count?: number | null
          id?: string
          metadata?: Json | null
          profile_id?: string | null
          share_count?: number | null
          time_period?: string | null
          trend_score?: number | null
          trending_date?: string | null
          updated_at?: string | null
          view_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "trending_content_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
        ]
      }
      user_balances: {
        Row: {
          balance: number | null
          created_at: string | null
          earned_balance: number | null
          id: string
          last_activity: string | null
          pending_balance: number | null
          profile_id: string
          token_id: string
          total_earned: number | null
          total_spent: number | null
          updated_at: string | null
        }
        Insert: {
          balance?: number | null
          created_at?: string | null
          earned_balance?: number | null
          id?: string
          last_activity?: string | null
          pending_balance?: number | null
          profile_id: string
          token_id: string
          total_earned?: number | null
          total_spent?: number | null
          updated_at?: string | null
        }
        Update: {
          balance?: number | null
          created_at?: string | null
          earned_balance?: number | null
          id?: string
          last_activity?: string | null
          pending_balance?: number | null
          profile_id?: string
          token_id?: string
          total_earned?: number | null
          total_spent?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_balances_token_id_fkey"
            columns: ["token_id"]
            isOneToOne: false
            referencedRelation: "tokens"
            referencedColumns: ["id"]
          },
        ]
      }
      user_mutes: {
        Row: {
          created_at: string
          id: string
          target_id: string
          type: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          target_id: string
          type: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          target_id?: string
          type?: string
          user_id?: string
        }
        Relationships: []
      }
      user_settings: {
        Row: {
          created_at: string | null
          id: string
          key: string
          updated_at: string | null
          user_id: string
          value: Json
        }
        Insert: {
          created_at?: string | null
          id?: string
          key: string
          updated_at?: string | null
          user_id: string
          value: Json
        }
        Update: {
          created_at?: string | null
          id?: string
          key?: string
          updated_at?: string | null
          user_id?: string
          value?: Json
        }
        Relationships: []
      }
      verification: {
        Row: {
          created_at: string | null
          id: string
          updated_at: string | null
          user_id: string
          verification_type: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          updated_at?: string | null
          user_id: string
          verification_type: string
        }
        Update: {
          created_at?: string | null
          id?: string
          updated_at?: string | null
          user_id?: string
          verification_type?: string
        }
        Relationships: []
      }
      verification_applications: {
        Row: {
          admin_notes: string | null
          id: string
          reason: string
          reviewed_at: string | null
          reviewed_by: string | null
          social_proof: string
          status: string
          submitted_at: string
          type: string
          user_address: string
        }
        Insert: {
          admin_notes?: string | null
          id: string
          reason: string
          reviewed_at?: string | null
          reviewed_by?: string | null
          social_proof: string
          status?: string
          submitted_at?: string
          type: string
          user_address: string
        }
        Update: {
          admin_notes?: string | null
          id?: string
          reason?: string
          reviewed_at?: string | null
          reviewed_by?: string | null
          social_proof?: string
          status?: string
          submitted_at?: string
          type?: string
          user_address?: string
        }
        Relationships: [
          {
            foreignKeyName: "verification_applications_reviewed_by_fkey"
            columns: ["reviewed_by"]
            isOneToOne: false
            referencedRelation: "admin_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      verification_type: {
        Row: {
          created_at: string
          id: number
        }
        Insert: {
          created_at?: string
          id?: number
        }
        Update: {
          created_at?: string
          id?: number
        }
        Relationships: []
      }
      voice_badges: {
        Row: {
          badge_description: string | null
          badge_image_url: string | null
          badge_name: string
          badge_rarity: string | null
          badge_type: string
          blockchain_address: string | null
          channel_id: string | null
          earned_at: string | null
          earned_for: string | null
          expires_at: string | null
          id: string
          is_poap: boolean | null
          metadata: Json | null
          poap_token_id: string | null
          profile_id: string
          proposal_id: string | null
          voice_message_id: string | null
        }
        Insert: {
          badge_description?: string | null
          badge_image_url?: string | null
          badge_name: string
          badge_rarity?: string | null
          badge_type: string
          blockchain_address?: string | null
          channel_id?: string | null
          earned_at?: string | null
          earned_for?: string | null
          expires_at?: string | null
          id?: string
          is_poap?: boolean | null
          metadata?: Json | null
          poap_token_id?: string | null
          profile_id: string
          proposal_id?: string | null
          voice_message_id?: string | null
        }
        Update: {
          badge_description?: string | null
          badge_image_url?: string | null
          badge_name?: string
          badge_rarity?: string | null
          badge_type?: string
          blockchain_address?: string | null
          channel_id?: string | null
          earned_at?: string | null
          earned_for?: string | null
          expires_at?: string | null
          id?: string
          is_poap?: boolean | null
          metadata?: Json | null
          poap_token_id?: string | null
          profile_id?: string
          proposal_id?: string | null
          voice_message_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "voice_badges_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "voice_badges_proposal_id_fkey"
            columns: ["proposal_id"]
            isOneToOne: false
            referencedRelation: "proposals"
            referencedColumns: ["id"]
          },
        ]
      }
      voice_highlights: {
        Row: {
          channel_id: string | null
          created_at: string | null
          description: string | null
          duration: number
          expires_at: string | null
          highlight_type: string | null
          id: string
          is_featured: boolean | null
          like_count: number | null
          profile_id: string
          tags: string[] | null
          thumbnail_url: string | null
          title: string | null
          updated_at: string | null
          view_count: number | null
          voice_message_id: string | null
        }
        Insert: {
          channel_id?: string | null
          created_at?: string | null
          description?: string | null
          duration: number
          expires_at?: string | null
          highlight_type?: string | null
          id?: string
          is_featured?: boolean | null
          like_count?: number | null
          profile_id: string
          tags?: string[] | null
          thumbnail_url?: string | null
          title?: string | null
          updated_at?: string | null
          view_count?: number | null
          voice_message_id?: string | null
        }
        Update: {
          channel_id?: string | null
          created_at?: string | null
          description?: string | null
          duration?: number
          expires_at?: string | null
          highlight_type?: string | null
          id?: string
          is_featured?: boolean | null
          like_count?: number | null
          profile_id?: string
          tags?: string[] | null
          thumbnail_url?: string | null
          title?: string | null
          updated_at?: string | null
          view_count?: number | null
          voice_message_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "voice_highlights_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
        ]
      }
      voice_journals: {
        Row: {
          audio_duration: number
          audio_url: string
          channel_id: string | null
          created_at: string | null
          entry_date: string | null
          id: string
          is_public: boolean | null
          is_soulbound: boolean | null
          location: string | null
          mood: string | null
          profile_id: string
          section_id: string | null
          tags: string[] | null
          title: string | null
          transcript: string | null
          updated_at: string | null
          weather: string | null
        }
        Insert: {
          audio_duration: number
          audio_url: string
          channel_id?: string | null
          created_at?: string | null
          entry_date?: string | null
          id?: string
          is_public?: boolean | null
          is_soulbound?: boolean | null
          location?: string | null
          mood?: string | null
          profile_id: string
          section_id?: string | null
          tags?: string[] | null
          title?: string | null
          transcript?: string | null
          updated_at?: string | null
          weather?: string | null
        }
        Update: {
          audio_duration?: number
          audio_url?: string
          channel_id?: string | null
          created_at?: string | null
          entry_date?: string | null
          id?: string
          is_public?: boolean | null
          is_soulbound?: boolean | null
          location?: string | null
          mood?: string | null
          profile_id?: string
          section_id?: string | null
          tags?: string[] | null
          title?: string | null
          transcript?: string | null
          updated_at?: string | null
          weather?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "voice_journals_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "voice_journals_section_id_fkey"
            columns: ["section_id"]
            isOneToOne: false
            referencedRelation: "channel_sections"
            referencedColumns: ["id"]
          },
        ]
      }
      voice_message_media: {
        Row: {
          created_at: string
          id: string
          type: string
          url: string
          voice_message_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          type: string
          url: string
          voice_message_id: string
        }
        Update: {
          created_at?: string
          id?: string
          type?: string
          url?: string
          voice_message_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "voice_message_media_voice_message_id_fkey"
            columns: ["voice_message_id"]
            isOneToOne: false
            referencedRelation: "voice_messages"
            referencedColumns: ["id"]
          },
        ]
      }
      voice_messages: {
        Row: {
          audio_duration: number
          audio_url: string
          channel_id: string | null
          created_at: string
          deleted_at: string | null
          id: string
          is_pinned: boolean | null
          is_removed: boolean | null
          message_type: string | null
          parent_id: string | null
          profile_id: string
          removed_at: string | null
          removed_reason: string | null
          section_id: string | null
          topic_id: string | null
          transcript: string | null
          updated_at: string
        }
        Insert: {
          audio_duration: number
          audio_url: string
          channel_id?: string | null
          created_at?: string
          deleted_at?: string | null
          id?: string
          is_pinned?: boolean | null
          is_removed?: boolean | null
          message_type?: string | null
          parent_id?: string | null
          profile_id: string
          removed_at?: string | null
          removed_reason?: string | null
          section_id?: string | null
          topic_id?: string | null
          transcript?: string | null
          updated_at?: string
        }
        Update: {
          audio_duration?: number
          audio_url?: string
          channel_id?: string | null
          created_at?: string
          deleted_at?: string | null
          id?: string
          is_pinned?: boolean | null
          is_removed?: boolean | null
          message_type?: string | null
          parent_id?: string | null
          profile_id?: string
          removed_at?: string | null
          removed_reason?: string | null
          section_id?: string | null
          topic_id?: string | null
          transcript?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "voice_messages_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "voice_messages_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "voice_messages"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "voice_messages_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "voice_messages_section_id_fkey"
            columns: ["section_id"]
            isOneToOne: false
            referencedRelation: "channel_sections"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "voice_messages_topic_id_fkey"
            columns: ["topic_id"]
            isOneToOne: false
            referencedRelation: "topics"
            referencedColumns: ["id"]
          },
        ]
      }
      voice_metadata: {
        Row: {
          content: Json | null
          created_at: string | null
          deleted_at: string | null
          id: string
          updated_at: string | null
        }
        Insert: {
          content?: Json | null
          created_at?: string | null
          deleted_at?: string | null
          id: string
          updated_at?: string | null
        }
        Update: {
          content?: Json | null
          created_at?: string | null
          deleted_at?: string | null
          id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      voice_reactions: {
        Row: {
          created_at: string
          emoji: string
          id: string
          profile_id: string
          voice_message_id: string
        }
        Insert: {
          created_at?: string
          emoji: string
          id?: string
          profile_id: string
          voice_message_id: string
        }
        Update: {
          created_at?: string
          emoji?: string
          id?: string
          profile_id?: string
          voice_message_id?: string
        }
        Relationships: []
      }
      voice_stories: {
        Row: {
          allow_replies: boolean | null
          audio_duration: number
          audio_url: string
          background_color: string | null
          background_image_url: string | null
          created_at: string | null
          expires_at: string | null
          id: string
          is_public: boolean | null
          poll_options: Json | null
          poll_question: string | null
          profile_id: string
          reply_count: number | null
          story_type: string | null
          tags: string[] | null
          title: string | null
          transcript: string | null
          view_count: number | null
          viewers_can_reply: boolean | null
        }
        Insert: {
          allow_replies?: boolean | null
          audio_duration: number
          audio_url: string
          background_color?: string | null
          background_image_url?: string | null
          created_at?: string | null
          expires_at?: string | null
          id?: string
          is_public?: boolean | null
          poll_options?: Json | null
          poll_question?: string | null
          profile_id: string
          reply_count?: number | null
          story_type?: string | null
          tags?: string[] | null
          title?: string | null
          transcript?: string | null
          view_count?: number | null
          viewers_can_reply?: boolean | null
        }
        Update: {
          allow_replies?: boolean | null
          audio_duration?: number
          audio_url?: string
          background_color?: string | null
          background_image_url?: string | null
          created_at?: string | null
          expires_at?: string | null
          id?: string
          is_public?: boolean | null
          poll_options?: Json | null
          poll_question?: string | null
          profile_id?: string
          reply_count?: number | null
          story_type?: string | null
          tags?: string[] | null
          title?: string | null
          transcript?: string | null
          view_count?: number | null
          viewers_can_reply?: boolean | null
        }
        Relationships: []
      }
      voice_tips: {
        Row: {
          amount: number
          amount_usd: number | null
          blockchain_status: string | null
          channel_id: string | null
          confirmed_at: string | null
          created_at: string | null
          from_profile_id: string
          id: string
          is_anonymous: boolean | null
          tip_message: string | null
          to_profile_id: string
          token_id: string
          topic_id: string | null
          transaction_hash: string | null
          voice_message_id: string | null
        }
        Insert: {
          amount: number
          amount_usd?: number | null
          blockchain_status?: string | null
          channel_id?: string | null
          confirmed_at?: string | null
          created_at?: string | null
          from_profile_id: string
          id?: string
          is_anonymous?: boolean | null
          tip_message?: string | null
          to_profile_id: string
          token_id: string
          topic_id?: string | null
          transaction_hash?: string | null
          voice_message_id?: string | null
        }
        Update: {
          amount?: number
          amount_usd?: number | null
          blockchain_status?: string | null
          channel_id?: string | null
          confirmed_at?: string | null
          created_at?: string | null
          from_profile_id?: string
          id?: string
          is_anonymous?: boolean | null
          tip_message?: string | null
          to_profile_id?: string
          token_id?: string
          topic_id?: string | null
          transaction_hash?: string | null
          voice_message_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "voice_tips_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "voice_tips_token_id_fkey"
            columns: ["token_id"]
            isOneToOne: false
            referencedRelation: "tokens"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "voice_tips_topic_id_fkey"
            columns: ["topic_id"]
            isOneToOne: false
            referencedRelation: "topics"
            referencedColumns: ["id"]
          },
        ]
      }
      voice_transcripts: {
        Row: {
          channel_id: string
          confidence_score: number | null
          created_at: string | null
          id: string
          keywords: string[] | null
          language_code: string | null
          profile_id: string
          search_vector: unknown | null
          sentiment_score: number | null
          topic_id: string | null
          transcript_source: string | null
          transcript_text: string
          updated_at: string | null
          voice_message_id: string | null
        }
        Insert: {
          channel_id: string
          confidence_score?: number | null
          created_at?: string | null
          id?: string
          keywords?: string[] | null
          language_code?: string | null
          profile_id: string
          search_vector?: unknown | null
          sentiment_score?: number | null
          topic_id?: string | null
          transcript_source?: string | null
          transcript_text: string
          updated_at?: string | null
          voice_message_id?: string | null
        }
        Update: {
          channel_id?: string
          confidence_score?: number | null
          created_at?: string | null
          id?: string
          keywords?: string[] | null
          language_code?: string | null
          profile_id?: string
          search_vector?: unknown | null
          sentiment_score?: number | null
          topic_id?: string | null
          transcript_source?: string | null
          transcript_text?: string
          updated_at?: string | null
          voice_message_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "voice_transcripts_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "voice_transcripts_topic_id_fkey"
            columns: ["topic_id"]
            isOneToOne: false
            referencedRelation: "topics"
            referencedColumns: ["id"]
          },
        ]
      }
      votes: {
        Row: {
          created_at: string | null
          id: string
          proposal_id: string
          voice_comment_duration: number | null
          voice_comment_url: string | null
          vote_choice: string
          vote_reason: string | null
          vote_weight: number | null
          voter_profile_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          proposal_id: string
          voice_comment_duration?: number | null
          voice_comment_url?: string | null
          vote_choice: string
          vote_reason?: string | null
          vote_weight?: number | null
          voter_profile_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          proposal_id?: string
          voice_comment_duration?: number | null
          voice_comment_url?: string | null
          vote_choice?: string
          vote_reason?: string | null
          vote_weight?: number | null
          voter_profile_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "votes_proposal_id_fkey"
            columns: ["proposal_id"]
            isOneToOne: false
            referencedRelation: "proposals"
            referencedColumns: ["id"]
          },
        ]
      }
      wallet_data: {
        Row: {
          created_at: string | null
          id: string
          updated_at: string | null
          user_id: string
          wallet_data: Json
        }
        Insert: {
          created_at?: string | null
          id?: string
          updated_at?: string | null
          user_id: string
          wallet_data: Json
        }
        Update: {
          created_at?: string | null
          id?: string
          updated_at?: string | null
          user_id?: string
          wallet_data?: Json
        }
        Relationships: []
      }
      wallet_security: {
        Row: {
          created_at: string | null
          has_backup: boolean
          id: string
          is_locked: boolean
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          has_backup?: boolean
          id?: string
          is_locked?: boolean
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          has_backup?: boolean
          id?: string
          is_locked?: boolean
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      wallet_tokens: {
        Row: {
          address: string
          chain_id: number
          created_at: string | null
          decimals: number
          id: string
          logo_url: string | null
          name: string
          symbol: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          address: string
          chain_id?: number
          created_at?: string | null
          decimals?: number
          id?: string
          logo_url?: string | null
          name: string
          symbol: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          address?: string
          chain_id?: number
          created_at?: string | null
          decimals?: number
          id?: string
          logo_url?: string | null
          name?: string
          symbol?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      wallet_transactions: {
        Row: {
          amount: string
          chain_id: number
          from_address: string
          id: string
          message: string | null
          status: string
          timestamp: string | null
          to_address: string
          token_address: string | null
          token_decimals: number | null
          token_symbol: string | null
          tx_hash: string | null
          type: string
          user_id: string
        }
        Insert: {
          amount: string
          chain_id?: number
          from_address: string
          id?: string
          message?: string | null
          status: string
          timestamp?: string | null
          to_address: string
          token_address?: string | null
          token_decimals?: number | null
          token_symbol?: string | null
          tx_hash?: string | null
          type: string
          user_id: string
        }
        Update: {
          amount?: string
          chain_id?: number
          from_address?: string
          id?: string
          message?: string | null
          status?: string
          timestamp?: string | null
          to_address?: string
          token_address?: string | null
          token_decimals?: number | null
          token_symbol?: string | null
          tx_hash?: string | null
          type?: string
          user_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      add_voice_reaction: {
        Args: {
          reaction_id: string
          message_id: string
          user_address: string
          reaction_type: string
          reaction_time: string
        }
        Returns: boolean
      }
      approve_verification: {
        Args: { application_id: string; admin_notes?: string }
        Returns: boolean
      }
      create_admin: {
        Args: {
          p_user_id: string
          p_email: string
          p_role: string
          p_display_name?: string
        }
        Returns: string
      }
      create_first_super_admin: {
        Args: { p_user_id: string; p_email: string; p_display_name?: string }
        Returns: string
      }
      create_storage_bucket: {
        Args: { bucket_name: string; is_public?: boolean }
        Returns: undefined
      }
      deduct_tip_balance: {
        Args: { p_user_id: string; p_amount: number; p_currency: string }
        Returns: boolean
      }
      delete_post: {
        Args: { post_id: string }
        Returns: boolean
      }
      delete_post_bypass_rls: {
        Args: { post_id: string }
        Returns: undefined
      }
      delete_voice_message: {
        Args: { message_id: string; user_address: string }
        Returns: boolean
      }
      directly_update_verification_status: {
        Args: {
          p_user_address: string
          p_verification_type: string
          p_is_verified?: boolean
          p_verified_at?: string
        }
        Returns: Json
      }
      disable_totp: {
        Args: { p_admin_id: string }
        Returns: boolean
      }
      exec_sql: {
        Args: { sql_query: string }
        Returns: undefined
      }
      execute_sql: {
        Args: { sql_query: string }
        Returns: undefined
      }
      generate_backup_code: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      generate_backup_codes: {
        Args: { p_count?: number }
        Returns: Json
      }
      generate_totp_secret: {
        Args: { p_admin_id: string; p_admin_email: string }
        Returns: Json
      }
      generate_uuid: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_accessible_journals: {
        Args: { user_id_param: string }
        Returns: {
          id: string
          profile_id: string
          title: string
          transcript: string
          audio_url: string
          audio_duration: number
          is_locked: boolean
          is_published: boolean
          scheduled_for: string
          created_at: string
          updated_at: string
          unlock_condition: Json
        }[]
      }
      get_active_stories: {
        Args: { viewer_id_param?: string }
        Returns: {
          id: string
          profile_id: string
          audio_url: string
          audio_duration: number
          transcript: string
          background_image_url: string
          background_color: string
          story_type: string
          title: string
          tags: string[]
          poll_question: string
          poll_options: Json
          created_at: string
          expires_at: string
          view_count: number
          reply_count: number
          has_viewed: boolean
        }[]
      }
      get_audit_logs: {
        Args: {
          p_limit?: number
          p_offset?: number
          p_action?: string
          p_entity_type?: string
          p_admin_id?: string
          p_start_date?: string
          p_end_date?: string
        }
        Returns: {
          id: string
          admin_id: string
          admin_email: string
          admin_role: string
          action: string
          entity_type: string
          entity_id: string
          details: Json
          ip_address: string
          user_agent: string
          created_at: string
        }[]
      }
      get_follower_count: {
        Args: { user_address: string }
        Returns: number
      }
      get_following_count: {
        Args: { user_address: string }
        Returns: number
      }
      get_live_streams: {
        Args: { p_channel_id?: string }
        Returns: {
          id: string
          title: string
          description: string
          host_profile_id: string
          channel_id: string
          status: string
          scheduled_start: string
          actual_start: string
          ended_at: string
          participant_count: number
          speaker_count: number
          created_at: string
        }[]
      }
      get_message_voice_reactions: {
        Args: { message_id_param: string } | { voice_message_id_param: string }
        Returns: {
          id: string
          voice_message_id: string
          profile_id: string
          emoji: string
          created_at: string
        }[]
      }
      get_muted_posts: {
        Args: { user_id_param: string }
        Returns: {
          id: string
          target_id: string
          created_at: string
        }[]
      }
      get_muted_users: {
        Args: { user_id_param: string }
        Returns: {
          id: string
          target_id: string
          created_at: string
        }[]
      }
      get_or_create_profile: {
        Args: { wallet_addr: string }
        Returns: {
          id: string
          wallet_address: string
          username: string
          display_name: string
          bio: string
          profile_image_url: string
          cover_image_url: string
          social_links: Json
          created_at: string
          updated_at: string
        }[]
      }
      get_or_create_user_profile: {
        Args: { p_user_id: string }
        Returns: {
          id: string
          wallet_address: string
          username: string
          display_name: string
          bio: string
          avatar_url: string
          cover_image_url: string
          social_links: Json
          created_at: string
          updated_at: string
        }[]
      }
      get_pinned_message: {
        Args: { p_profile_id: string }
        Returns: {
          audio_duration: number
          audio_url: string
          channel_id: string | null
          created_at: string
          deleted_at: string | null
          id: string
          is_pinned: boolean | null
          is_removed: boolean | null
          message_type: string | null
          parent_id: string | null
          profile_id: string
          removed_at: string | null
          removed_reason: string | null
          section_id: string | null
          topic_id: string | null
          transcript: string | null
          updated_at: string
        }[]
      }
      get_post_reactions: {
        Args: { post_id_param: string }
        Returns: {
          id: string
          post_id: string
          user_id: string
          reaction_type: string
          created_at: string
        }[]
      }
      get_post_reposts: {
        Args: { post_id_param: string; post_type_param: string }
        Returns: {
          id: string
          user_id: string
          post_id: string
          post_type: string
          created_at: string
        }[]
      }
      get_private_stories: {
        Args: { viewer_id_param?: string }
        Returns: {
          id: string
          profile_id: string
          audio_url: string
          audio_duration: number
          transcript: string
          background_image_url: string
          background_color: string
          story_type: string
          title: string
          tags: string[]
          poll_question: string
          poll_options: Json
          created_at: string
          expires_at: string
          view_count: number
          reply_count: number
          has_viewed: boolean
        }[]
      }
      get_profile_by_address: {
        Args: { address_param: string }
        Returns: {
          avatar_url: string | null
          bio: string | null
          cover_image_url: string | null
          created_at: string
          display_name: string | null
          flag_reason: string | null
          flagged_at: string | null
          id: string
          is_flagged: boolean | null
          is_verified: boolean | null
          last_login: string | null
          like_count: number | null
          post_count: number | null
          profile_image_url: string | null
          social_links: Json | null
          tip_count: number | null
          updated_at: string
          username: string | null
          verification_type: string | null
          verified_at: string | null
          wallet_address: string | null
        }[]
      }
      get_profile_by_wallet_address: {
        Args: { p_wallet_address: string }
        Returns: Json
      }
      get_reposts_with_messages: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          user_id: string
          post_id: string
          post_type: string
          created_at: string
          original_message: Json
        }[]
      }
      get_user_journals: {
        Args: { user_id_param: string }
        Returns: {
          id: string
          profile_id: string
          title: string
          transcript: string
          audio_url: string
          audio_duration: number
          is_locked: boolean
          scheduled_for: string
          created_at: string
          updated_at: string
        }[]
      }
      get_user_journals_fixed: {
        Args: { user_id_param: string }
        Returns: {
          id: string
          title: string
          content: string
          user_id: string
          created_at: string
          updated_at: string
          is_public: boolean
          unlock_date: string
          unlock_condition: string
        }[]
      }
      get_user_journals_text: {
        Args: { user_id_param: string }
        Returns: {
          id: string
          profile_id: string
          title: string
          transcript: string
          audio_url: string
          audio_duration: number
          is_locked: boolean
          scheduled_for: string
          created_at: string
          updated_at: string
        }[]
      }
      get_user_pinned_message: {
        Args: { user_id: string }
        Returns: {
          audio_duration: number
          audio_url: string
          channel_id: string | null
          created_at: string
          deleted_at: string | null
          id: string
          is_pinned: boolean | null
          is_removed: boolean | null
          message_type: string | null
          parent_id: string | null
          profile_id: string
          removed_at: string | null
          removed_reason: string | null
          section_id: string | null
          topic_id: string | null
          transcript: string | null
          updated_at: string
        }[]
      }
      get_user_reposts: {
        Args: { user_id_param: string }
        Returns: {
          id: string
          user_id: string
          post_id: string
          post_type: string
          created_at: string
        }[]
      }
      get_user_tip_balance: {
        Args: { p_user_id: string }
        Returns: {
          eth_balance: number
          sol_balance: number
          usdc_balance: number
          total_received_eth: number
          total_received_sol: number
          total_received_usdc: number
        }[]
      }
      get_user_voice_messages: {
        Args: { user_id_param: string }
        Returns: {
          id: string
          profile_id: string
          channel_id: string
          parent_id: string
          transcript: string
          audio_url: string
          audio_duration: number
          created_at: string
          updated_at: string
          is_pinned: boolean
          deleted_at: string
        }[]
      }
      get_user_voice_messages_text: {
        Args: { user_id_param: string }
        Returns: {
          id: string
          profile_id: string
          channel_id: string
          parent_id: string
          transcript: string
          audio_url: string
          audio_duration: number
          created_at: string
          updated_at: string
          is_pinned: boolean
          deleted_at: string
        }[]
      }
      get_voice_message_media: {
        Args:
          | Record<PropertyKey, never>
          | { message_id: string }
          | { message_id_param: string }
        Returns: {
          id: string
          voice_message_id: string
          url: string
          type: string
          created_at: string
        }[]
      }
      grant_mic_access: {
        Args: { p_stream_id: string; p_profile_id: string; p_host_id: string }
        Returns: boolean
      }
      has_user_reposted: {
        Args: {
          user_id_param: string
          post_id_param: string
          post_type_param: string
        }
        Returns: boolean
      }
      increment_story_view_count: {
        Args: { story_id_param: string; viewer_id_param: string }
        Returns: undefined
      }
      insert_voice_message_media: {
        Args: { media_items: Json }
        Returns: undefined
      }
      is_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_following: {
        Args: { follower_address: string; following_address: string }
        Returns: boolean
      }
      is_super_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      join_live_stream: {
        Args: { p_stream_id: string; p_profile_id: string; p_role?: string }
        Returns: boolean
      }
      leave_live_stream: {
        Args: { p_stream_id: string; p_profile_id: string }
        Returns: boolean
      }
      log_admin_action: {
        Args:
          | {
              p_action: string
              p_entity_type: string
              p_entity_id: string
              p_details?: Json
            }
          | {
              p_action: string
              p_table_name: string
              p_record_id: string
              p_details?: string
            }
        Returns: string
      }
      pin_voice_message: {
        Args: { p_message_id: string; p_profile_id: string }
        Returns: undefined
      }
      refresh_schema_cache: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      regenerate_backup_codes: {
        Args: { p_admin_id: string }
        Returns: Json
      }
      report_content: {
        Args: {
          p_content_id: string
          p_content_type: string
          p_reason: string
          p_details: string
          p_reported_user_id: string
        }
        Returns: string
      }
      request_mic_access: {
        Args: { p_stream_id: string; p_profile_id: string }
        Returns: boolean
      }
      soft_delete_voice_message: {
        Args: { message_id: string; delete_time: string }
        Returns: undefined
      }
      unpin_all_user_messages: {
        Args: Record<PropertyKey, never> | { user_id: string }
        Returns: undefined
      }
      update_admin_last_login: {
        Args: { p_admin_id: string }
        Returns: boolean
      }
      update_follow_counts: {
        Args: { follower_id: string; following_id: string; is_follow: boolean }
        Returns: undefined
      }
      update_message_pin_status: {
        Args: { message_id: string; pin_status: boolean }
        Returns: undefined
      }
      update_profile_by_address: {
        Args: {
          p_wallet_address: string
          p_username: string
          p_display_name: string
          p_bio: string
          p_avatar_url: string
          p_cover_image_url: string
          p_social_links: Json
        }
        Returns: Json
      }
      update_profile_with_wallet: {
        Args: {
          p_wallet_address: string
          p_display_name: string
          p_username: string
          p_bio: string
          p_avatar_url: string
          p_cover_image_url: string
          p_social_links: Json
        }
        Returns: undefined
      }
      update_verification_status: {
        Args: {
          user_address: string
          verification_type: string
          is_verified: boolean
          verified_at?: string
        }
        Returns: Json
      }
      upsert_profile: {
        Args: { profile_data: Json }
        Returns: Json
      }
      upsert_profile_with_id: {
        Args: {
          p_id: string
          p_wallet_address: string
          p_username: string
          p_display_name: string
          p_bio: string
          p_avatar_url: string
          p_cover_image_url: string
          p_social_links?: Json
        }
        Returns: Json
      }
      verify_totp_code: {
        Args: { p_admin_id: string; p_code: string }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
