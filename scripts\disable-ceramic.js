/**
 * <PERSON><PERSON><PERSON> to disable Ceramic by replacing imports in all files
 * This script will find all imports of ceramicClient.ts and replace them with disabledCeramicClient.ts
 *
 * Run this script with:
 * node scripts/disable-ceramic.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current file path
const __filename = fileURLToPath(import.meta.url);
// Get the current directory
const __dirname = path.dirname(__filename);

// Directories to search
const directories = [
  'src/components',
  'src/contexts',
  'src/services',
  'src/utils',
  'src/hooks',
  'src/pages'
];

// Files to ignore
const ignoreFiles = [
  'ceramic-shim.ts',
  'ceramic-client.ts',
  'disabledCeramicClient.ts'
];

// Function to replace imports in a file
function replaceImportsInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');

    // Check if the file imports ceramicClient
    if (content.includes("from './ceramicClient'") ||
      content.includes("from '../services/ceramicClient'") ||
      content.includes("from '@/services/ceramicClient'")) {

      console.log(`Found Ceramic import in: ${filePath}`);

      // Replace the imports
      let newContent = content
        .replace(/from ['"]\.\/ceramicClient['"]/g, "from './disabledCeramicClient'")
        .replace(/from ['"]\.\.\/services\/ceramicClient['"]/g, "from '../services/disabledCeramicClient'")
        .replace(/from ['"]@\/services\/ceramicClient['"]/g, "from '@/services/disabledCeramicClient'");

      // Write the updated content back to the file
      fs.writeFileSync(filePath, newContent, 'utf8');
      console.log(`Updated imports in: ${filePath}`);

      return true;
    }

    return false;
  } catch (error) {
    console.error(`Error processing file ${filePath}:`, error);
    return false;
  }
}

// Function to recursively search directories
function searchAndReplaceInDirectory(dir) {
  const files = fs.readdirSync(dir);

  const results = [];

  for (const file of files) {
    const filePath = path.join(dir, file);

    // Skip ignored files
    if (ignoreFiles.includes(file)) {
      continue;
    }

    const stats = fs.statSync(filePath);

    if (stats.isDirectory()) {
      // Recursively search subdirectories
      results.push(...searchAndReplaceInDirectory(filePath));
    } else if (stats.isFile() &&
      (file.endsWith('.js') || file.endsWith('.jsx') ||
        file.endsWith('.ts') || file.endsWith('.tsx'))) {
      // Process JavaScript/TypeScript files
      if (replaceImportsInFile(filePath)) {
        results.push(filePath);
      }
    }
  }

  return results;
}

// Main function
function main() {
  console.log('Searching for and replacing Ceramic imports...');

  const results = [];

  for (const dir of directories) {
    try {
      const dirPath = path.join(__dirname, '..', dir);
      if (fs.existsSync(dirPath)) {
        results.push(...searchAndReplaceInDirectory(dirPath));
      }
    } catch (error) {
      console.error(`Error searching directory ${dir}:`, error);
    }
  }

  console.log('\nUpdated Ceramic imports in the following files:');
  results.forEach(file => console.log(`- ${file}`));
  console.log(`\nTotal files updated: ${results.length}`);
}

// Run the script
main();
