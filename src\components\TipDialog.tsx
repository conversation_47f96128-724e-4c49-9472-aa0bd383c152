import React, { useState } from 'react';
import { useWallet } from '@/contexts/WalletContext';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from '@/components/ui/sonner';
import { DollarSign, Loader2 } from 'lucide-react';
import walletService from '@/services/walletService';

interface TipDialogProps {
  isOpen: boolean;
  onClose: () => void;
  recipientAddress: string;
  recipientName?: string;
}

const TipDialog: React.FC<TipDialogProps> = ({
  isOpen,
  onClose,
  recipientAddress,
  recipientName = 'this creator'
}) => {
  const { wallet, isLoading, sendTip } = useWallet();
  const [amount, setAmount] = useState<string>('0.01');
  const [message, setMessage] = useState<string>('');
  const [isSending, setIsSending] = useState<boolean>(false);
  
  // Predefined tip amounts
  const tipAmounts = ['0.001', '0.01', '0.05', '0.1'];
  
  // Handle tip submission
  const handleSendTip = async () => {
    if (!wallet) {
      toast.error('No wallet available');
      return;
    }
    
    // Validate amount
    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }
    
    // Check if amount is greater than balance
    if (amountNum > parseFloat(wallet.balance)) {
      toast.error('Insufficient balance');
      return;
    }
    
    try {
      setIsSending(true);
      
      // Send the tip
      const transaction = await sendTip(recipientAddress, amount, message);
      
      if (transaction && transaction.status === 'completed') {
        // Close the dialog on success
        onClose();
      }
    } catch (error) {
      console.error('Error sending tip:', error);
      toast.error('Error sending tip');
    } finally {
      setIsSending(false);
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Send a Tip</DialogTitle>
          <DialogDescription>
            Support {recipientName} by sending ETH directly to their wallet.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          {/* Recipient */}
          <div className="space-y-2">
            <Label>Recipient</Label>
            <Input 
              value={walletService.formatAddress(recipientAddress)} 
              readOnly 
              className="font-mono text-sm"
            />
          </div>
          
          {/* Amount */}
          <div className="space-y-2">
            <Label>Amount (ETH)</Label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="pl-9"
                step="0.001"
                min="0.001"
              />
            </div>
            
            {/* Quick amount buttons */}
            <div className="flex flex-wrap gap-2 mt-2">
              {tipAmounts.map((amt) => (
                <Button
                  key={amt}
                  variant="outline"
                  size="sm"
                  onClick={() => setAmount(amt)}
                  className={amount === amt ? 'bg-voicechain-purple text-white' : ''}
                >
                  {amt} ETH
                </Button>
              ))}
            </div>
            
            {/* Balance display */}
            <p className="text-xs text-muted-foreground">
              Your balance: {wallet?.balance || '0'} ETH
            </p>
          </div>
          
          {/* Message */}
          <div className="space-y-2">
            <Label>Message (optional)</Label>
            <Textarea
              placeholder="Add a message with your tip..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              className="resize-none"
              rows={3}
            />
          </div>
        </div>
        
        <DialogFooter className="flex space-x-2 justify-end">
          <Button variant="outline" onClick={onClose} disabled={isSending}>
            Cancel
          </Button>
          <Button 
            onClick={handleSendTip} 
            disabled={!wallet || isLoading || isSending || parseFloat(amount) <= 0 || parseFloat(amount) > parseFloat(wallet?.balance || '0')}
            className="bg-voicechain-purple hover:bg-voicechain-accent"
          >
            {isSending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Sending...
              </>
            ) : (
              'Send Tip'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TipDialog;
