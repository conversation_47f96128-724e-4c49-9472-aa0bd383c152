import { supabase } from '@/integrations/supabase/client';
import { v4 as uuidv4 } from 'uuid';

/**
 * Summon a user for a response
 */
export async function summonUser(
  fromUserId: string,
  toUserId: string,
  messageId: string | null,
  context?: string
): Promise<{ success: boolean; summonId?: string }> {
  try {
    // Create a summon record
    const summonId = uuidv4();
    const { error: summonError } = await supabase
      .from('summons')
      .insert({
        id: summonId,
        from_user_id: fromUserId,
        to_user_id: toUserId,
        message_id: messageId,
        context,
        created_at: new Date().toISOString(),
        status: 'pending'
      });
      
    if (summonError) {
      console.error('Error creating summon:', summonError);
      return { success: false };
    }
    
    // Create notification for the summoned user
    const { error: notificationError } = await supabase
      .from('notifications')
      .insert({
        id: uuidv4(),
        type: 'summon',
        from_address: fromUserId,
        to_address: toUserId,
        message_id: messageId,
        data: { context, summonId },
        read: false,
        created_at: new Date().toISOString()
      });
      
    if (notificationError) {
      console.error('Error creating summon notification:', notificationError);
    }
    
    return { success: true, summonId };
  } catch (error) {
    console.error('Error in summonUser:', error);
    return { success: false };
  }
}

/**
 * Respond to a summon
 */
export async function respondToSummon(
  summonId: string,
  responseMessageId: string
): Promise<{ success: boolean }> {
  try {
    // Update the summon status
    const { error: updateError } = await supabase
      .from('summons')
      .update({
        status: 'responded',
        response_message_id: responseMessageId,
        responded_at: new Date().toISOString()
      })
      .eq('id', summonId);
      
    if (updateError) {
      console.error('Error updating summon:', updateError);
      return { success: false };
    }
    
    return { success: true };
  } catch (error) {
    console.error('Error in respondToSummon:', error);
    return { success: false };
  }
}