
import React from 'react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { Check } from 'lucide-react';

// Define verification types
export type VerificationType =
  | 'owner'        // Dark purple - App owner
  | 'creator'      // Teal - Content creators
  | 'developer'    // Blue - Developers
  | 'community'    // Green - Community leaders
  | 'partner'      // Orange - Partners
  | 'investor'     // Gold - Investors
  | 'early'        // Pink - Early adopters
  | 'artist'       // Purple/Pink - Visual artists
  | 'musician'     // Blue/Purple - Musicians
  | 'journalist'   // Blue/Teal - Journalists
  | 'educator'     // Green/Blue - Educators
  | 'nonprofit'    // Green/Yellow - Nonprofit organizations
  | 'government'   // Blue/Gray - Government entities
  | 'celebrity'    // Gold/Orange - Public figures
  | 'custom';      // Custom color

interface VerificationBadgeProps {
  type: VerificationType;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  customColor?: string;
  showTooltip?: boolean;
}

const VerificationBadge: React.FC<VerificationBadgeProps> = ({
  type,
  size = 'md',
  className,
  customColor,
  showTooltip = true
}) => {
  // Define badge colors
  const badgeConfig = {
    owner: {
      color: 'bg-voicechain-purple', // Dark purple for owner
      description: 'Audra Owner'
    },
    creator: {
      color: 'bg-gradient-to-r from-teal-500 to-blue-500',
      description: 'Verified Creator'
    },
    developer: {
      color: 'bg-gradient-to-r from-blue-600 to-indigo-600',
      description: 'Verified Developer'
    },
    community: {
      color: 'bg-gradient-to-r from-green-500 to-emerald-500',
      description: 'Community Leader'
    },
    partner: {
      color: 'bg-gradient-to-r from-orange-500 to-amber-500',
      description: 'Official Partner'
    },
    investor: {
      color: 'bg-gradient-to-r from-yellow-400 to-amber-600',
      description: 'Verified Investor'
    },
    early: {
      color: 'bg-gradient-to-r from-pink-500 to-rose-500',
      description: 'Early Adopter'
    },
    artist: {
      color: 'bg-gradient-to-r from-purple-500 to-pink-500',
      description: 'Verified Artist'
    },
    musician: {
      color: 'bg-gradient-to-r from-blue-500 to-purple-600',
      description: 'Verified Musician'
    },
    journalist: {
      color: 'bg-gradient-to-r from-blue-500 to-teal-500',
      description: 'Verified Journalist'
    },
    educator: {
      color: 'bg-gradient-to-r from-green-500 to-blue-500',
      description: 'Verified Educator'
    },
    nonprofit: {
      color: 'bg-gradient-to-r from-green-500 to-yellow-500',
      description: 'Verified Nonprofit'
    },
    government: {
      color: 'bg-gradient-to-r from-blue-600 to-gray-500',
      description: 'Government Entity'
    },
    celebrity: {
      color: 'bg-gradient-to-r from-yellow-500 to-orange-500',
      description: 'Public Figure'
    },
    custom: {
      color: customColor || 'bg-gradient-to-r from-blue-500 to-purple-500',
      description: 'Verified'
    }
  };

  // Size classes - reduced sizes for a more subtle badge
  const sizeClasses = {
    sm: 'w-3.5 h-3.5',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  };

  // Get config for the current type
  const config = badgeConfig[type];

  // Badge element - redesigned to look more like Twitter/Instagram verification badges
  const badge = (
    <div className={cn(
      'relative flex items-center justify-center rounded-full',
      config.color,
      sizeClasses[size],
      'ring-[1.5px] ring-white',  // Add white outline
      className
    )}>
      <Check className="text-white z-10 w-[55%] h-[55%] stroke-[3px]" />
    </div>
  );

  // Return with or without tooltip
  if (showTooltip) {
    return (
      <TooltipProvider>
        <Tooltip delayDuration={300}>
          <TooltipTrigger asChild>
            {badge}
          </TooltipTrigger>
          <TooltipContent className="px-3 py-1.5 text-xs">
            {config.description}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return badge;
};

export default VerificationBadge;
