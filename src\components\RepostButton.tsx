
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Repeat2, Loader2 } from 'lucide-react';
import { toast } from '@/components/ui/sonner';
import { supabase } from '@/integrations/supabase/client';
import { useNotifications } from '@/contexts/NotificationContext';

interface RepostButtonProps {
  postId: string;
  currentUserId: string;
  originalUserId: string;
  postType: 'voice_message' | 'journal';
  onRepost?: () => void;
  size?: 'default' | 'sm' | 'lg' | 'icon';
  variant?: 'default' | 'outline' | 'ghost';
  className?: string;
  iconOnly?: boolean;
}

const RepostButton: React.FC<RepostButtonProps> = ({
  postId,
  currentUserId,
  originalUserId,
  postType,
  onRepost,
  size = 'sm',
  variant = 'ghost',
  className = '',
  iconOnly = false
}) => {
  const [isReposted, setIsReposted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [repostCount, setRepostCount] = useState(0);
  const { addNotification } = useNotifications();

  // Check if already reposted
  useEffect(() => {
    const checkRepostStatus = async () => {
      try {
        // Convert wallet address to profile ID with multiple strategies
        let profileData = null;

        // Strategy 1: Exact case-sensitive match
        const { data: exactMatch } = await supabase
          .from('profiles')
          .select('id')
          .eq('wallet_address', currentUserId)
          .single();

        if (exactMatch) {
          profileData = exactMatch;
        } else {
          // Strategy 2: Case-insensitive match
          const { data: caseInsensitiveMatch } = await supabase
            .from('profiles')
            .select('id')
            .ilike('wallet_address', currentUserId)
            .single();

          if (caseInsensitiveMatch) {
            profileData = caseInsensitiveMatch;
          } else {
            // Strategy 3: Check if currentUserId is already a UUID (profile ID)
            const { data: uuidMatch } = await supabase
              .from('profiles')
              .select('id')
              .eq('id', currentUserId)
              .single();

            if (uuidMatch) {
              profileData = uuidMatch;
            }
          }
        }

        if (!profileData) {
          // Try to create a profile if one doesn't exist
          try {
            const { data: newProfile, error: createError } = await supabase
              .from('profiles')
              .insert({
                id: crypto.randomUUID(),
                wallet_address: currentUserId,
                display_name: `User ${currentUserId.slice(0, 6)}`,
                username: `user_${currentUserId.slice(2, 8).toLowerCase()}`,
                created_at: new Date().toISOString()
              })
              .select('id')
              .single();

            if (createError) {
              console.error('RepostButton: Error creating profile:', createError);
              return;
            }

            profileData = newProfile;
          } catch (createProfileError) {
            console.error('RepostButton: Error in profile creation:', createProfileError);
            return;
          }
        }

        const { data, error } = await supabase
          .from('reposts')
          .select('*')
          .eq('user_id', profileData.id)
          .eq('post_id', postId)
          .eq('post_type', postType);

        if (error) {
          console.error('RepostButton: Error checking repost status:', error);
          return;
        }

        setIsReposted(data && data.length > 0);
      } catch (error) {
        console.error('RepostButton: Error in checkRepostStatus:', error);
      }
    };

    // Get repost count
    const getRepostCount = async () => {
      try {
        const { count, error } = await supabase
          .from('reposts')
          .select('*', { count: 'exact', head: true })
          .eq('post_id', postId)
          .eq('post_type', postType);

        if (error) {
          console.error('Error getting repost count:', error);
          return;
        }

        setRepostCount(count || 0);
      } catch (error) {
        console.error('Error in getRepostCount:', error);
      }
    };

    if (currentUserId && postId) {
      checkRepostStatus();
      getRepostCount();
    }
  }, [currentUserId, postId, postType]);

  // Handle repost
  const handleRepost = async (e?: React.MouseEvent) => {
    // Stop event propagation to prevent navigation to post detail
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }

    if (!currentUserId) {
      toast.error('Please connect your wallet to repost');
      return;
    }



    // Remove the check that prevents reposting your own post
    // This allows users to repost their own content

    setIsLoading(true);

    try {
      // Convert wallet address to profile ID with multiple strategies
      let profileData = null;

      // Strategy 1: Exact case-sensitive match
      const { data: exactMatch } = await supabase
        .from('profiles')
        .select('id')
        .eq('wallet_address', currentUserId)
        .single();

      if (exactMatch) {
        profileData = exactMatch;
      } else {
        // Strategy 2: Case-insensitive match
        const { data: caseInsensitiveMatch } = await supabase
          .from('profiles')
          .select('id')
          .ilike('wallet_address', currentUserId)
          .single();

        if (caseInsensitiveMatch) {
          profileData = caseInsensitiveMatch;
        } else {
          // Strategy 3: Check if currentUserId is already a UUID (profile ID)
          const { data: uuidMatch } = await supabase
            .from('profiles')
            .select('id')
            .eq('id', currentUserId)
            .single();

          if (uuidMatch) {
            profileData = uuidMatch;
          }
        }
      }

      if (!profileData) {
        // Try to create a profile if one doesn't exist
        try {
          const { data: newProfile, error: createError } = await supabase
            .from('profiles')
            .insert({
              id: crypto.randomUUID(),
              wallet_address: currentUserId,
              display_name: `User ${currentUserId.slice(0, 6)}`,
              username: `user_${currentUserId.slice(2, 8).toLowerCase()}`,
              created_at: new Date().toISOString()
            })
            .select('id')
            .single();

          if (createError) {
            console.error('RepostButton: Error creating profile:', createError);
            toast.error('Failed to create your profile. Please try again.');
            return;
          }

          profileData = newProfile;
          toast.success('Profile created successfully!');
        } catch (createProfileError) {
          console.error('RepostButton: Error in profile creation:', createProfileError);
          toast.error('Failed to find or create your profile. Please make sure you are logged in.');
          return;
        }
      }

      if (isReposted) {
        // Remove repost
        const { error } = await supabase
          .from('reposts')
          .delete()
          .eq('user_id', profileData.id)
          .eq('post_id', postId)
          .eq('post_type', postType);

        if (error) {
          console.error('Error removing repost:', error);
          toast.error('Failed to remove repost');
          return;
        }

        setIsReposted(false);
        setRepostCount(prev => Math.max(0, prev - 1));
        toast.success('Repost removed');
      } else {
        // Add repost
        const { error } = await supabase
          .from('reposts')
          .insert({
            id: crypto.randomUUID(),
            user_id: profileData.id,
            post_id: postId,
            post_type: postType,
            created_at: new Date().toISOString()
          });

        if (error) {
          console.error('Error adding repost:', error);
          toast.error('Failed to repost');
          return;
        }

        setIsReposted(true);
        setRepostCount(prev => prev + 1);
        toast.success('Post reposted to your profile');

        // Create notification
        if (originalUserId !== currentUserId) {
          addNotification(
            'repost',
            currentUserId,
            originalUserId,
            postId,
            { postType }
          );
        }

        // Call onRepost callback if provided
        if (onRepost) {
          onRepost();
        }
      }
    } catch (error) {
      console.error('Error in handleRepost:', error);
      toast.error('An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      className={`flex items-center gap-1 ${isReposted ? 'text-green-500' : ''} ${className}`}
      onClick={(e) => handleRepost(e)}
      disabled={isLoading}
    >
      {isLoading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <Repeat2 className={`h-4 w-4 ${isReposted ? 'fill-green-500' : ''}`} />
      )}
      {!iconOnly && (
        <span className="text-xs">
          {isReposted ? 'Reposted' : 'Repost'}
          {repostCount > 0 && ` (${repostCount})`}
        </span>
      )}
    </Button>
  );
};

export default RepostButton;
