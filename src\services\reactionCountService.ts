import { supabase } from '@/integrations/supabase/client';

/**
 * Get the total number of reactions received by a user on their voice messages
 */
export async function getUserReactionCount(userAddress: string): Promise<number> {
  try {
    // Get all voice messages for this user
    const { data: userMessages, error: messagesError } = await supabase
      .from('voice_messages')
      .select('id')
      .or(`profile_id.eq.${userAddress},profile_id.in.(select id from profiles where wallet_address.ilike.${userAddress})`)
      .is('deleted_at', null);

    if (messagesError) {
      console.error('Error fetching user messages for reaction count:', messagesError);
      return 0;
    }

    if (!userMessages || userMessages.length === 0) {
      return 0;
    }

    const messageIds = userMessages.map(msg => msg.id);

    // Count reactions on all their voice messages
    const { count: voiceReactionCount, error: voiceError } = await supabase
      .from('voice_reactions')
      .select('*', { count: 'exact', head: true })
      .in('voice_message_id', messageIds);

    if (voiceError) {
      console.error('Error counting voice reactions:', voiceError);
      return 0;
    }

    // Also count reactions from post_reactions table
    const { count: postReactionCount, error: postError } = await supabase
      .from('post_reactions')
      .select('*', { count: 'exact', head: true })
      .in('post_id', messageIds);

    if (postError) {
      console.error('Error counting post reactions:', postError);
      return 0;
    }

    const totalReactions = (voiceReactionCount || 0) + (postReactionCount || 0);
    console.log(`User ${userAddress} has received ${totalReactions} total reactions`);
    
    return totalReactions;
  } catch (error) {
    console.error('Error in getUserReactionCount:', error);
    return 0;
  }
}

/**
 * Update profile reaction count in the database
 */
export async function updateProfileReactionCount(userAddress: string, reactionCount: number): Promise<void> {
  try {
    const { error } = await supabase
      .from('profiles')
      .update({ like_count: reactionCount }) // Using like_count column to store reaction count
      .or(`id.eq.${userAddress},wallet_address.ilike.${userAddress}`);

    if (error) {
      console.error('Error updating profile reaction count:', error);
    }
  } catch (error) {
    console.error('Error in updateProfileReactionCount:', error);
  }
}

/**
 * Sync reaction count for a user (fetch and update in profile)
 */
export async function syncUserReactionCount(userAddress: string): Promise<number> {
  const reactionCount = await getUserReactionCount(userAddress);
  await updateProfileReactionCount(userAddress, reactionCount);
  return reactionCount;
}