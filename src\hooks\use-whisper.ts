
import { useState, useCallback, useEffect } from 'react';
import { getStorageService } from '@/services/storageServiceFactory';
import { supabase } from '@/lib/supabase';

interface WhisperOptions {
  apiKey?: string;
  language?: string;
  onTranscriptionProgress?: (text: string) => void;
  onTranscriptionComplete?: (text: string) => void;
}

interface WhisperResult {
  transcribing: boolean;
  transcript: string;
  startTranscription: (audioBlob: Blob) => Promise<string>;
  stopTranscription: () => void;
}

/**
 * A hook for using speech-to-text transcription with Web Speech API fallback
 */
export const useWhisper = (options: WhisperOptions = {}): WhisperResult => {
  const [transcribing, setTranscribing] = useState(false);
  const [transcript, setTranscript] = useState('');

  // Function to transcribe audio using Web Speech API
  const transcribeWithWebSpeech = async (audioBlob: Blob): Promise<string> => {
    return new Promise((resolve) => {
      // Check if browser supports SpeechRecognition
      if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        console.error('Speech recognition not supported in this browser');
        resolve(`Voice message recorded at ${new Date().toLocaleTimeString()}`);
        return;
      }

      try {
        console.log('Starting Web Speech API transcription');
        // Create a URL from the blob
        const audioURL = URL.createObjectURL(audioBlob);
        
        // Create an audio element
        const audio = new Audio(audioURL);
        
        // Setup transcription variables
        let finalTranscript = '';
        let recognitionStarted = false;
        
        // @ts-ignore - TypeScript doesn't know about SpeechRecognition
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        const recognition = new SpeechRecognition();

        // Configure recognition
        recognition.lang = 'en-US';
        recognition.continuous = true;
        recognition.interimResults = true;
        recognition.maxAlternatives = 1;
        
        // Handle recognition results
        recognition.onresult = (event: any) => {
          let interimTranscript = '';

          for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;

            if (event.results[i].isFinal) {
              finalTranscript += transcript + ' ';
              console.log('Final transcript updated:', finalTranscript);

              // Call progress callback if provided
              if (options.onTranscriptionProgress) {
                options.onTranscriptionProgress(finalTranscript.trim());
              }
            } else {
              interimTranscript += transcript;
            }
          }

          // Log progress for debugging
          if (interimTranscript) {
            console.log('Interim transcript:', interimTranscript);
          }
        };

        // Handle recognition end
        recognition.onend = () => {
          console.log('Recognition ended with final transcript:', finalTranscript);
          
          if (finalTranscript.trim()) {
            console.log('Returning final transcript:', finalTranscript.trim());
            resolve(finalTranscript.trim());
          } else {
            console.log('No transcription detected, using fallback');
            // Try direct audio file analysis via server (mock implementation)
            const mockTranscript = "Voice recording transcription";
            resolve(mockTranscript);
          }
          
          // Stop audio playback if it's still playing
          audio.pause();
          audio.currentTime = 0;
          
          // Release the object URL
          URL.revokeObjectURL(audioURL);
        };

        // Handle recognition errors
        recognition.onerror = (event: any) => {
          console.error('Speech recognition error:', event.error);
          // Try server-side transcription
          uploadForServerTranscription(audioBlob).then(serverTranscript => {
            if (serverTranscript) {
              resolve(serverTranscript);
            } else {
              resolve(`Voice message recorded at ${new Date().toLocaleTimeString()}`);
            }
          });
        };

        // Load audio metadata to determine length
        audio.addEventListener('loadedmetadata', () => {
          console.log('Audio duration:', audio.duration, 'seconds');
          
          // Only proceed if audio is valid
          if (isNaN(audio.duration) || audio.duration <= 0.1) {
            console.warn('Audio duration too short or invalid');
            resolve(`Voice message recorded at ${new Date().toLocaleTimeString()}`);
            return;
          }
          
          // Start recognition
          try {
            console.log('Starting recognition...');
            recognition.start();
            recognitionStarted = true;
            
            // Play at low volume to avoid feedback
            audio.volume = 0.1;
            audio.play().catch(err => {
              console.error('Error playing audio:', err);
              // Try alternate approach if audio playback fails
              alternateRecognition();
            });
            
            // Stop when audio ends
            audio.onended = () => {
              console.log('Audio playback ended');
              if (recognitionStarted) {
                try {
                  recognition.stop();
                } catch (e) {
                  console.error('Error stopping recognition:', e);
                }
              }
            };
            
            // Set a timeout as a backup
            setTimeout(() => {
              if (recognitionStarted) {
                try {
                  console.log('Stopping recognition via timeout');
                  recognition.stop();
                } catch (e) {
                  console.error('Error stopping recognition:', e);
                }
              }
            }, (audio.duration * 1000) + 2000); // Add 2 seconds buffer
          } catch (recognitionError) {
            console.error('Error starting recognition:', recognitionError);
            alternateRecognition();
          }
        });
        
        // Handle audio loading errors
        audio.addEventListener('error', (e) => {
          console.error('Audio element error:', e);
          alternateRecognition();
        });
        
        // Alternate approach without audio playback
        function alternateRecognition() {
          console.log('Using alternate recognition approach');
          
          if (!recognitionStarted) {
            try {
              recognition.start();
              recognitionStarted = true;
              
              // Stop after a reasonable amount of time (increased for PWA)
              setTimeout(() => {
                if (recognitionStarted) {
                  try {
                    recognition.stop();
                  } catch (e) {
                    console.error('Error stopping direct recognition:', e);
                  }
                }
              }, 15000); // 15 seconds timeout (increased for PWA)
            } catch (startError) {
              console.error('Error starting alternate recognition:', startError);
              // Try server-side transcription
              uploadForServerTranscription(audioBlob).then(serverTranscript => {
                if (serverTranscript) {
                  resolve(serverTranscript);
                } else {
                  resolve(`Voice message recorded at ${new Date().toLocaleTimeString()}`);
                }
              });
            }
          }
        }
      } catch (error) {
        console.error('Error in Web Speech API:', error);
        // Try server-side transcription
        uploadForServerTranscription(audioBlob).then(serverTranscript => {
          if (serverTranscript) {
            resolve(serverTranscript);
          } else {
            resolve(`Voice message recorded at ${new Date().toLocaleTimeString()}`);
          }
        });
      }
    });
  };

  // Upload audio to Supabase and try to transcribe there
  const uploadForServerTranscription = async (audioBlob: Blob): Promise<string | null> => {
    try {
      const userId = localStorage.getItem('connectedAccount') || 'anonymous';
      const fileName = `transcriptions/${userId}/${Date.now()}.webm`;
      
      // Upload to Supabase storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('audio')
        .upload(fileName, audioBlob, {
          contentType: 'audio/webm',
          cacheControl: '3600'
        });
        
      if (uploadError) {
        console.error('Error uploading audio for transcription:', uploadError);
        return null;
      }
      
      // Get the public URL
      const { data: urlData } = supabase.storage
        .from('audio')
        .getPublicUrl(fileName);
        
      // Store the audio URL in the database
      const { data: dbData, error: dbError } = await supabase
        .from('voice_messages')
        .insert({
          profile_id: userId,
          audio_url: urlData.publicUrl,
          audio_duration: 0, // We don't know the duration yet
          transcript: 'Processing transcription...',
          created_at: new Date().toISOString()
        })
        .select();
        
      if (dbError) {
        console.error('Error storing audio metadata:', dbError);
      }
      
      console.log('Audio uploaded for server transcription', urlData.publicUrl);
      
      // In a real implementation, you would call a Supabase Edge Function to transcribe
      // For now, we'll just return null and rely on the client-side transcription
      return null;
    } catch (error) {
      console.error('Error in server transcription:', error);
      return null;
    }
  };

  // Use the browser's supported transcription format
  const getAudioFileType = () => {
    // Check for browser support of various audio formats
    const audio = document.createElement('audio');
    if (audio.canPlayType('audio/webm')) {
      return 'webm';
    } else if (audio.canPlayType('audio/mp4')) {
      return 'mp4';
    } else {
      return 'wav';
    }
  };

  // Permanently store the audio in Supabase
  const storeAudioInSupabase = async (audioBlob: Blob, transcript: string): Promise<string | null> => {
    try {
      const userId = localStorage.getItem('connectedAccount') || 'anonymous';
      const fileName = `recordings/${userId}/${Date.now()}.webm`;
      
      // Upload to Supabase storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('audio')
        .upload(fileName, audioBlob, {
          contentType: 'audio/webm',
          cacheControl: '3600'
        });
        
      if (uploadError) {
        console.error('Error uploading audio recording:', uploadError);
        return null;
      }
      
      // Get the public URL
      const { data: urlData } = supabase.storage
        .from('audio')
        .getPublicUrl(fileName);
        
      return urlData.publicUrl;
    } catch (error) {
      console.error('Error storing audio in Supabase:', error);
      return null;
    }
  };

  const startTranscription = useCallback(async (audioBlob: Blob): Promise<string> => {
    try {
      setTranscribing(true);
      setTranscript('');
      console.log('Starting transcription for audio blob:', { size: audioBlob.size, type: audioBlob.type });

      // Try to transcribe with Web Speech API
      const text = await transcribeWithWebSpeech(audioBlob);
      console.log('Transcription result:', text);
      
      // Store the audio permanently in Supabase
      const audioUrl = await storeAudioInSupabase(audioBlob, text);
      if (audioUrl) {
        console.log('Audio stored permanently at:', audioUrl);
      }
      
      // Update the transcript in the database if we have a message ID
      // This would normally be handled by your message creation logic

      // Update state with the transcribed text
      setTranscript(text);

      // Call the completion callback if provided
      if (options.onTranscriptionComplete) {
        options.onTranscriptionComplete(text);
      }

      setTranscribing(false);
      return text;
    } catch (error) {
      console.error('Error in transcription:', error);
      const fallbackText = `Voice message recorded at ${new Date().toLocaleTimeString()}`;
      setTranscript(fallbackText);
      setTranscribing(false);
      return fallbackText;
    }
  }, [options]);

  const stopTranscription = useCallback(() => {
    setTranscribing(false);
  }, []);

  // Cleanup function to handle any resources that need to be released
  useEffect(() => {
    return () => {
      // Cleanup any resources if needed
    };
  }, []);

  return {
    transcribing,
    transcript,
    startTranscription,
    stopTranscription
  };
};

export default useWhisper;
