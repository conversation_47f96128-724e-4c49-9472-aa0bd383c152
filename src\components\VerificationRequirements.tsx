import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, CheckCircle, AlertCircle, Users, Code, BookOpen, Building, Coins, Calendar } from 'lucide-react';
import { VerificationType } from './VerificationBadge';

interface VerificationRequirementsProps {
  selectedType?: VerificationType;
}

const VerificationRequirements: React.FC<VerificationRequirementsProps> = ({ selectedType }) => {
  const requirements = [
    {
      type: 'creator',
      title: 'Content Creator',
      icon: <Users className="h-5 w-5 text-teal-500" />,
      description: 'For active content creators with a significant following',
      criteria: [
        'Have at least 1,000 followers on a major social media platform',
        'Regularly create and publish original content',
        'Have been active for at least 3 months',
        'Provide links to your social media profiles and content',
        'Have a complete profile with a real photo and bio'
      ],
      examples: [
        'YouTubers with over 1,000 subscribers',
        'Podcasters with established shows',
        'Social media influencers',
        'Writers with published work',
        'Musicians with released music'
      ]
    },
    {
      type: 'developer',
      title: 'Developer',
      icon: <Code className="h-5 w-5 text-blue-500" />,
      description: 'For active developers contributing to blockchain or web3 projects',
      criteria: [
        'Have contributed to at least one open-source project',
        'Have a GitHub account with activity',
        'Demonstrate knowledge of blockchain or web3 technologies',
        'Provide links to your work or GitHub profile',
        'Have a complete profile with a real photo and bio'
      ],
      examples: [
        'Blockchain developers',
        'Smart contract developers',
        'Web3 frontend developers',
        'Open-source contributors',
        'Technical writers in the blockchain space'
      ]
    },
    {
      type: 'community',
      title: 'Community Leader',
      icon: <Users className="h-5 w-5 text-green-500" />,
      description: 'For active community leaders and moderators',
      criteria: [
        'Lead or moderate a community with at least 500 members',
        'Have been active in the community for at least 6 months',
        'Demonstrate positive community engagement',
        'Provide links to the communities you lead or moderate',
        'Have a complete profile with a real photo and bio'
      ],
      examples: [
        'Discord server moderators',
        'Telegram group admins',
        'Forum moderators',
        'Community managers',
        'Event organizers'
      ]
    },
    {
      type: 'partner',
      title: 'Official Partner',
      icon: <Building className="h-5 w-5 text-orange-500" />,
      description: 'For organizations and businesses partnered with Audra',
      criteria: [
        'Have an official partnership agreement with Audra',
        'Represent a legitimate business or organization',
        'Provide proof of your organization\'s identity',
        'Have a complete profile with your organization\'s logo and description',
        'Provide contact information for verification'
      ],
      examples: [
        'Technology partners',
        'Media partners',
        'Educational institutions',
        'Non-profit organizations',
        'Corporate partners'
      ]
    },
    {
      type: 'investor',
      title: 'Investor',
      icon: <Coins className="h-5 w-5 text-amber-500" />,
      description: 'For investors in Audra or related projects',
      criteria: [
        'Have invested in Audra or related projects',
        'Provide proof of investment (will be kept confidential)',
        'Have a complete profile with a real photo and bio',
        'Maintain ethical investment practices',
        'Agree to our investor code of conduct'
      ],
      examples: [
        'Angel investors',
        'Venture capital firms',
        'Individual investors',
        'Investment DAOs',
        'Strategic partners'
      ]
    },
    {
      type: 'early',
      title: 'Early Adopter',
      icon: <Calendar className="h-5 w-5 text-pink-500" />,
      description: 'For early users who joined during Audra\'s initial launch',
      criteria: [
        'Have joined Audra during the first 3 months of launch',
        'Have been an active user since joining',
        'Have contributed positively to the platform',
        'Have a complete profile with a real photo and bio',
        'Have invited at least 3 other users to the platform'
      ],
      examples: [
        'Beta testers',
        'First 1,000 users',
        'Early community members',
        'Initial content creators',
        'Launch event participants'
      ]
    }
  ];

  // If a specific type is selected, only show that one
  const displayRequirements = selectedType
    ? requirements.filter(req => req.type === selectedType)
    : requirements;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5 text-primary" />
          Verification Requirements
        </CardTitle>
        <CardDescription>
          Each verification type has specific requirements. Review them before applying.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Accordion type="single" collapsible className="w-full">
          {displayRequirements.map((req) => (
            <AccordionItem key={req.type} value={req.type}>
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center gap-2">
                  {req.icon}
                  <span>{req.title}</span>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="space-y-4 pt-2">
                  <p className="text-sm text-muted-foreground">{req.description}</p>

                  <div>
                    <h4 className="text-sm font-medium mb-2 flex items-center">
                      <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
                      Requirements
                    </h4>
                    <ul className="text-sm space-y-1 ml-6 list-disc">
                      {req.criteria.map((criterion, i) => (
                        <li key={i}>{criterion}</li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-2 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1 text-blue-500" />
                      Examples
                    </h4>
                    <ul className="text-sm space-y-1 ml-6 list-disc">
                      {req.examples.map((example, i) => (
                        <li key={i}>{example}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </CardContent>
    </Card>
  );
};

export default VerificationRequirements;
