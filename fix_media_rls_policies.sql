-- Fix Media Persistence - RLS Policies and Storage Setup
-- Run this SQL in your Supabase SQL Editor

-- 1. Create voice_message_media table if it doesn't exist
CREATE TABLE IF NOT EXISTS voice_message_media (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
  voice_message_id TEXT NOT NULL,
  url TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('image', 'video')),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 2. Enable RLS on voice_message_media table
ALTER TABLE voice_message_media ENABLE ROW LEVEL SECURITY;

-- 3. Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Users can view all voice message media" ON voice_message_media;
DROP POLICY IF EXISTS "Users can insert voice message media" ON voice_message_media;
DROP POLICY IF EXISTS "Users can delete voice message media" ON voice_message_media;
DROP POLICY IF EXISTS "Users can update voice message media" ON voice_message_media;

-- 4. Create permissive RLS policies for voice_message_media
CREATE POLICY "Allow all operations on voice_message_media" 
ON voice_message_media 
FOR ALL 
USING (true) 
WITH CHECK (true);

-- 5. Create index for better performance
CREATE INDEX IF NOT EXISTS voice_message_media_voice_message_id_idx 
ON voice_message_media(voice_message_id);

-- 6. Create storage bucket policies for media bucket
-- First, let's make sure the media bucket exists
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'media', 
  'media', 
  true, 
  20971520, -- 20MB
  ARRAY['image/png', 'image/jpeg', 'image/gif', 'image/webp', 'video/mp4', 'video/webm', 'video/quicktime']
)
ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 20971520,
  allowed_mime_types = ARRAY['image/png', 'image/jpeg', 'image/gif', 'image/webp', 'video/mp4', 'video/webm', 'video/quicktime'];

-- 7. Drop existing storage policies if they exist
DROP POLICY IF EXISTS "Allow public uploads to media bucket" ON storage.objects;
DROP POLICY IF EXISTS "Allow public access to media bucket" ON storage.objects;
DROP POLICY IF EXISTS "Allow public deletes from media bucket" ON storage.objects;

-- 8. Create storage policies for media bucket
CREATE POLICY "Allow public uploads to media bucket"
ON storage.objects
FOR INSERT
WITH CHECK (bucket_id = 'media');

CREATE POLICY "Allow public access to media bucket"
ON storage.objects
FOR SELECT
USING (bucket_id = 'media');

CREATE POLICY "Allow public deletes from media bucket"
ON storage.objects
FOR DELETE
USING (bucket_id = 'media');

CREATE POLICY "Allow public updates to media bucket"
ON storage.objects
FOR UPDATE
USING (bucket_id = 'media')
WITH CHECK (bucket_id = 'media');

-- 9. Also ensure audio bucket has proper policies
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'audio', 
  'audio', 
  true, 
  52428800, -- 50MB
  ARRAY['audio/webm', 'audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/ogg']
)
ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 52428800,
  allowed_mime_types = ARRAY['audio/webm', 'audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/ogg'];

-- 10. Create storage policies for audio bucket
CREATE POLICY "Allow public uploads to audio bucket"
ON storage.objects
FOR INSERT
WITH CHECK (bucket_id = 'audio');

CREATE POLICY "Allow public access to audio bucket"
ON storage.objects
FOR SELECT
USING (bucket_id = 'audio');

CREATE POLICY "Allow public deletes from audio bucket"
ON storage.objects
FOR DELETE
USING (bucket_id = 'audio');

CREATE POLICY "Allow public updates to audio bucket"
ON storage.objects
FOR UPDATE
USING (bucket_id = 'audio')
WITH CHECK (bucket_id = 'audio');

-- 11. Ensure profiles bucket has proper policies
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'profiles', 
  'profiles', 
  true, 
  5242880, -- 5MB
  ARRAY['image/png', 'image/jpeg', 'image/gif', 'image/webp']
)
ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 5242880,
  allowed_mime_types = ARRAY['image/png', 'image/jpeg', 'image/gif', 'image/webp'];

-- 12. Create storage policies for profiles bucket
CREATE POLICY "Allow public uploads to profiles bucket"
ON storage.objects
FOR INSERT
WITH CHECK (bucket_id = 'profiles');

CREATE POLICY "Allow public access to profiles bucket"
ON storage.objects
FOR SELECT
USING (bucket_id = 'profiles');

CREATE POLICY "Allow public deletes from profiles bucket"
ON storage.objects
FOR DELETE
USING (bucket_id = 'profiles');

CREATE POLICY "Allow public updates to profiles bucket"
ON storage.objects
FOR UPDATE
USING (bucket_id = 'profiles')
WITH CHECK (bucket_id = 'profiles');

-- 13. Grant necessary permissions
GRANT ALL ON voice_message_media TO anon;
GRANT ALL ON voice_message_media TO authenticated;

-- 14. Test the setup
SELECT 'Media persistence fix completed successfully!' as status;

-- 15. Show current bucket configuration
SELECT 
  name,
  public,
  file_size_limit,
  allowed_mime_types
FROM storage.buckets 
WHERE name IN ('media', 'audio', 'profiles');

-- 16. Show current policies
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE tablename IN ('voice_message_media', 'objects')
ORDER BY tablename, policyname;
