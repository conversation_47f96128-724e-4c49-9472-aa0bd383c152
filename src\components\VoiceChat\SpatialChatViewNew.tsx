import React from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Globe, Headphones, Mic, MoreHorizontal, Trash2 } from 'lucide-react';
import { VoiceChatMessage } from '@/services/voiceChatService';
import AudioPlayer from '@/components/AudioPlayer';
import MessageReactions from '@/components/MessageReactions';

interface SpatialChatViewProps {
  messages: VoiceChatMessage[];
  participants: any[];
  onSendMessage: (content: string) => void;
  onSendVoice: () => void;
  currentUserId: string;
  onDeleteMessage: (messageId: string, deleteType: 'for-me' | 'for-everyone') => void;
}

const SpatialChatView: React.FC<SpatialChatViewProps> = ({
  messages,
  participants,
  onSendMessage,
  onSendVoice,
  currentUserId,
  onDeleteMessage
}) => {
  return (
    <ScrollArea className="flex-1 p-4 relative max-h-full overflow-y-auto">
      {/* Beautiful 3D Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-cyan-900/30 to-purple-900/20 pointer-events-none">
        {/* Floating Particles */}
        {Array.from({ length: 50 }).map((_, i) => (
          <div
            key={`particle-${i}`}
            className="absolute w-1 h-1 bg-cyan-400 rounded-full opacity-40 animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 3}s`,
              transform: `perspective(1000px) translateZ(${Math.random() * 100}px)`
            }}
          />
        ))}

        {/* 3D Grid Effect */}
        <div className="absolute inset-0 opacity-5">
          {Array.from({ length: 20 }).map((_, i) => (
            <div
              key={`grid-${i}`}
              className="absolute border-t border-cyan-400"
              style={{
                top: `${i * 5}%`,
                width: '100%',
                transform: `perspective(1000px) rotateX(${i * 1}deg) translateZ(${i * 10}px)`
              }}
            />
          ))}
        </div>
      </div>

      {/* Messages Content */}
        <div className="space-y-4">
          {messages.length === 0 ? (
            <div className="text-center text-sm text-muted-foreground">
              <div className="text-4xl mb-2">🌍</div>
              <p>No messages yet. Start the spatial conversation! 🎤</p>
            </div>
          ) : (
            messages.map((message, index) => {
              const isVoiceMessage = message.message_type === 'voice';

              return (
                <div
                  key={message.id}
                  className="group flex gap-3 hover:bg-muted/50 rounded-lg p-3 transition-all duration-300 relative border-l-4 border-l-cyan-500 bg-cyan-50/30 dark:bg-cyan-950/30 backdrop-blur-sm"
                  style={{
                    transform: `perspective(1000px) translateZ(${index * 3}px)`,
                    animation: `slideInSpatial 0.6s ease-out ${index * 0.1}s both`,
                    boxShadow: '0 8px 32px rgba(6, 182, 212, 0.1)'
                  }}
                >
                  {/* Spatial Chat Type Indicator */}
                  <div className="absolute top-2 right-2 opacity-60">
                    <Globe className="h-3 w-3 text-cyan-500" />
                  </div>

                  {/* 3D Floating Particles around message */}
                  <div className="absolute inset-0 pointer-events-none overflow-hidden">
                    {Array.from({ length: 3 }).map((_, i) => (
                      <div
                        key={`msg-particle-${i}`}
                        className="absolute w-1 h-1 bg-cyan-400 rounded-full opacity-30 animate-bounce"
                        style={{
                          left: `${10 + Math.random() * 80}%`,
                          top: `${10 + Math.random() * 80}%`,
                          animationDelay: `${Math.random() * 2}s`,
                          animationDuration: `${2 + Math.random() * 2}s`
                        }}
                      />
                    ))}
                  </div>

                  {/* Avatar */}
                  <Avatar className="w-8 h-8 ring-2 ring-cyan-400/30">
                    <AvatarImage src={message.sender_profile?.avatar_url} />
                    <AvatarFallback className="bg-gradient-to-br from-cyan-500 to-blue-500 text-white text-xs">
                      {message.sender_profile?.display_name?.slice(0, 2) || 'U'}
                    </AvatarFallback>
                  </Avatar>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-cyan-700 dark:text-cyan-300">
                          {message.sender_profile?.display_name || 'Unknown User'}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {new Date(message.created_at).toLocaleTimeString()}
                        </span>
                        {isVoiceMessage && (
                          <div className="flex items-center gap-1">
                            <Mic className="h-3 w-3 text-cyan-500" />
                            <span className="text-xs text-cyan-500">Spatial</span>
                          </div>
                        )}
                      </div>

                      {/* Message Actions */}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity">
                            <MoreHorizontal className="h-3 w-3" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => onDeleteMessage(message.id, 'for-me')}
                            className="text-orange-600 hover:text-orange-700"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete for me
                          </DropdownMenuItem>
                          {(message.sender_id === currentUserId) && (
                            <DropdownMenuItem
                              onClick={() => onDeleteMessage(message.id, 'for-everyone')}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete for everyone
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>

                    {/* Text Message */}
                    {message.message_type === 'text' && (
                      <div className="text-sm text-foreground leading-relaxed">
                        {message.content}
                      </div>
                    )}

                    {/* Voice Message */}
                    {isVoiceMessage && (
                      <div className="p-3 rounded-lg max-w-sm bg-cyan-50 dark:bg-cyan-900/20 backdrop-blur-sm border border-cyan-200/30">
                        {/* Spatial Audio Indicator */}
                        <div className="mb-2 flex items-center gap-2 text-xs">
                          <Headphones className="h-3 w-3 text-cyan-500" />
                          <span className="text-cyan-600 dark:text-cyan-400">Spatial Audio</span>
                        </div>

                        {/* 3D Audio Visualizer */}
                        <div className="flex items-center justify-center gap-1 py-2 mb-2">
                          {Array.from({ length: 16 }).map((_, i) => (
                            <div
                              key={i}
                              className="w-0.5 bg-gradient-to-t from-cyan-500 to-blue-400 rounded-full animate-pulse"
                              style={{
                                height: `${6 + Math.random() * 12}px`,
                                animationDelay: `${i * 0.1}s`,
                                animationDuration: `${1 + Math.random()}s`
                              }}
                            />
                          ))}
                        </div>

                        {message.voice_url ? (
                          <AudioPlayer
                            src={message.voice_url}
                            className="w-full"
                          />
                        ) : (
                          <div className="flex items-center gap-2 text-muted-foreground">
                            <Mic className="h-4 w-4" />
                            <span className="text-sm">Voice message unavailable</span>
                          </div>
                        )}

                        <div className="mt-2 flex items-center justify-between text-xs text-muted-foreground">
                          <span>
                            Duration: {message.voice_duration ? `${Math.round(message.voice_duration)}s` : 'Unknown'}
                          </span>
                          {message.voice_transcript && (
                            <span className="text-cyan-500">Transcribed</span>
                          )}
                        </div>

                        {message.voice_transcript && (
                          <div className="mt-2 p-2 bg-background rounded border-l-2 border-cyan-500">
                            <p className="text-xs text-muted-foreground italic">
                              "{message.voice_transcript}"
                            </p>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Message Reactions - Using the same component as Wave chat */}
                    <div className="mt-2">
                      <MessageReactions
                        messageId={message.id}
                        userId={currentUserId}
                      />
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>

      {/* CSS Animations */}
      <style>{`
        @keyframes slideInSpatial {
          from {
            opacity: 0;
            transform: perspective(1000px) translateZ(-100px) translateY(30px) rotateX(10deg);
          }
          to {
            opacity: 1;
            transform: perspective(1000px) translateZ(0px) translateY(0px) rotateX(0deg);
          }
        }
      `}</style>
    </ScrollArea>
  );
};

export default SpatialChatView;
