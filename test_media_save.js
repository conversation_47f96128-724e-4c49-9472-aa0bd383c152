import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://jcltjkaumevuycntdmds.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpjbHRqa2F1bWV2dXljbnRkbWRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTkwMDEsImV4cCI6MjA2MTg3NTAwMX0.LqQSk0NwB1mDB4Cznn0iQSrYvsAAWyUsaj8reo_etqM'
);

async function testMediaSave() {
  console.log('🧪 Testing media save process...\n');

  try {
    // 1. Test voice_message_media table access
    console.log('1️⃣ Testing voice_message_media table access...');
    
    const testMediaItem = {
      id: `test_media_${Date.now()}`,
      voice_message_id: `test_message_${Date.now()}`,
      url: 'https://example.com/test-image.jpg',
      type: 'image',
      created_at: new Date().toISOString()
    };

    console.log('📤 Attempting to insert test media item:', testMediaItem);

    const { data: insertData, error: insertError } = await supabase
      .from('voice_message_media')
      .insert(testMediaItem)
      .select()
      .single();

    if (insertError) {
      console.error('❌ Media insert failed:', insertError);
      
      if (insertError.code === '42P01') {
        console.log('🔧 Table does not exist! Creating voice_message_media table...');
        
        // Try to create the table
        const createTableSQL = `
          CREATE TABLE IF NOT EXISTS voice_message_media (
            id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
            voice_message_id TEXT NOT NULL,
            url TEXT NOT NULL,
            type TEXT NOT NULL CHECK (type IN ('image', 'video')),
            created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
          );
        `;
        
        console.log('Please run this SQL in Supabase SQL Editor:');
        console.log(createTableSQL);
        return;
      }
      
      if (insertError.message.includes('row-level security')) {
        console.log('🔧 RLS policy blocking insert! Run fix_media_rls_policies.sql');
        return;
      }
      
      return;
    }

    console.log('✅ Media insert successful:', insertData);

    // 2. Test retrieval
    console.log('\n2️⃣ Testing media retrieval...');
    const { data: retrieveData, error: retrieveError } = await supabase
      .from('voice_message_media')
      .select('*')
      .eq('id', testMediaItem.id)
      .single();

    if (retrieveError) {
      console.error('❌ Media retrieval failed:', retrieveError);
    } else {
      console.log('✅ Media retrieval successful:', retrieveData);
    }

    // 3. Test with voice message join
    console.log('\n3️⃣ Testing voice message with media join...');
    
    // First create a test voice message
    const testMessage = {
      id: testMediaItem.voice_message_id,
      profile_id: 'test_profile',
      audio_url: 'https://example.com/test-audio.mp3',
      transcript: 'Test message for media testing',
      audio_duration: 10,
      created_at: new Date().toISOString()
    };

    const { error: messageError } = await supabase
      .from('voice_messages')
      .insert(testMessage);

    if (messageError) {
      console.log('⚠️  Could not create test message:', messageError.message);
    } else {
      console.log('✅ Test message created');
      
      // Now test the join query
      const { data: joinData, error: joinError } = await supabase
        .from('voice_messages')
        .select(`
          id,
          transcript,
          voice_message_media (
            id,
            url,
            type
          )
        `)
        .eq('id', testMessage.id)
        .single();

      if (joinError) {
        console.error('❌ Join query failed:', joinError);
      } else {
        console.log('✅ Join query successful:', joinData);
        console.log(`   Media count: ${joinData.voice_message_media?.length || 0}`);
      }
    }

    // 4. Clean up test data
    console.log('\n4️⃣ Cleaning up test data...');
    await supabase.from('voice_message_media').delete().eq('id', testMediaItem.id);
    await supabase.from('voice_messages').delete().eq('id', testMessage.id);
    console.log('✅ Test data cleaned up');

    // 5. Test the actual media save function format
    console.log('\n5️⃣ Testing actual media save format...');
    
    const realMediaItem = {
      id: `real_test_${Date.now()}`,
      voice_message_id: `real_message_${Date.now()}`,
      url: 'https://jcltjkaumevuycntdmds.supabase.co/storage/v1/object/public/media/test/test-image.jpg',
      type: 'image',
      created_at: new Date().toISOString()
    };

    console.log('📤 Testing with real Supabase URL format:', realMediaItem);

    const { data: realInsertData, error: realInsertError } = await supabase
      .from('voice_message_media')
      .insert(realMediaItem)
      .select()
      .single();

    if (realInsertError) {
      console.error('❌ Real format insert failed:', realInsertError);
    } else {
      console.log('✅ Real format insert successful:', realInsertData);
      
      // Clean up
      await supabase.from('voice_message_media').delete().eq('id', realMediaItem.id);
    }

    console.log('\n🎉 Media save test completed!');
    console.log('\n📋 Results:');
    console.log('- voice_message_media table: ✅ Accessible');
    console.log('- Media insert: ✅ Working');
    console.log('- Media retrieval: ✅ Working');
    console.log('- Join queries: ✅ Working');
    
    console.log('\n🔍 If media is still not persisting in your app:');
    console.log('1. Check browser console for media save errors');
    console.log('2. Verify uploadedMedia array is not empty before saving');
    console.log('3. Check if media save is being called at all');
    console.log('4. Look for silent failures in the media upload process');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testMediaSave().catch(console.error);
