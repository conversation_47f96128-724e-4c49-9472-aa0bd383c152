/**
 * Disabled Ceramic Client
 * This is a stub implementation that doesn't actually connect to Ceramic
 * It's used to prevent errors when Ceramic is not available
 *
 * IMPORTANT: This file is a temporary solution to prevent errors while we migrate away from Ceramic.
 * All functionality should be moved to Supabase instead.
 */

import { supabase } from '@/integrations/supabase/client';
import { v4 as uuidv4 } from 'uuid';

// Define the VoicePostMetadata interface to match what's used in the real client
export interface VoicePostMetadata {
  audioUrl: string;
  transcript: string;
  userAddress: string;
  timestamp: string;
  duration: number;
  media?: MediaItem[];
  [key: string]: any;
}

// Define the MediaItem interface to match what's used in the real client
export interface MediaItem {
  url: string;
  type: string;
  width?: number;
  height?: number;
  [key: string]: any;
}

// Mock CeramicClient class
export class CeramicClient {
  _apiUrl: string;
  did: any;

  constructor(apiUrl: string) {
    this._apiUrl = apiUrl;
    console.log('Created disabled Ceramic client (stub implementation)');
  }

  // Add any methods that might be called
  async loadStream() {
    console.warn('Ceramic is disabled - loadStream called but will return null');
    return null;
  }

  async createDocument() {
    console.warn('Ceramic is disabled - createDocument called but will return null');
    return null;
  }

  async loadDocument() {
    console.warn('Ceramic is disabled - loadDocument called but will return null');
    return null;
  }
}

// Mock TileDocument class
export class TileDocument {
  static async create(ceramic: any, content: any, options: any) {
    console.warn('Ceramic is disabled - TileDocument.create called');
    console.log('Saving content to Supabase instead:', content);

    try {
      // Generate a unique ID for the document
      const docId = uuidv4();

      // Save the content to Supabase
      const { error } = await supabase
        .from('voice_metadata')
        .insert({
          id: docId,
          content: content,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (error) {
        console.error('Error saving to Supabase:', error);
      }

      return {
        id: {
          toString: () => docId
        },
        content: content
      };
    } catch (error) {
      console.error('Error in TileDocument.create:', error);
      return {
        id: {
          toString: () => 'error-ceramic-doc-id'
        },
        content: content
      };
    }
  }

  static async load(ceramic: any, docId: string) {
    console.warn('Ceramic is disabled - TileDocument.load called');
    console.log('Loading content from Supabase instead for docId:', docId);

    try {
      // Load the content from Supabase
      const { data, error } = await supabase
        .from('voice_metadata')
        .select('content')
        .eq('id', docId)
        .maybeSingle();

      if (error) {
        console.error('Error loading from Supabase:', error);
        return {
          id: {
            toString: () => docId
          },
          content: {}
        };
      }

      return {
        id: {
          toString: () => docId
        },
        content: data?.content || {}
      };
    } catch (error) {
      console.error('Error in TileDocument.load:', error);
      return {
        id: {
          toString: () => docId
        },
        content: {}
      };
    }
  }

  async update(content: any) {
    console.warn('Ceramic is disabled - TileDocument.update called');
    console.log('Updating content in Supabase instead:', content);

    try {
      const docId = this.id.toString();

      // Update the content in Supabase
      const { error } = await supabase
        .from('voice_metadata')
        .update({
          content: content,
          updated_at: new Date().toISOString()
        })
        .eq('id', docId);

      if (error) {
        console.error('Error updating in Supabase:', error);
      }

      return {
        id: this.id,
        content: content
      };
    } catch (error) {
      console.error('Error in TileDocument.update:', error);
      return this;
    }
  }

  id: {
    toString: () => string;
  };

  content: any;

  constructor() {
    this.id = {
      toString: () => 'mock-ceramic-doc-id'
    };
    this.content = {};
  }
}

// Create a disabled ceramic client
export const ceramic = new CeramicClient('disabled');

/**
 * Authenticate with Ceramic using a DID
 * This is a stub implementation that doesn't actually connect to Ceramic
 * @returns The disabled Ceramic client
 */
export async function authenticateDID(): Promise<CeramicClient> {
  console.warn('Ceramic is disabled - authenticateDID called but will return a stub implementation');
  return ceramic;
}

/**
 * Create a voice post document in Ceramic
 * This is a stub implementation that saves to Supabase instead
 * @param metadata The metadata for the voice post
 * @returns A document ID from Supabase
 */
export async function publishVoicePost(metadata: VoicePostMetadata): Promise<string> {
  console.warn('Ceramic is disabled - publishVoicePost called');
  console.log('Saving voice post to Supabase instead:', metadata);

  try {
    // Generate a unique ID for the document
    const docId = uuidv4();

    // Save the metadata to Supabase
    const { error } = await supabase
      .from('voice_metadata')
      .insert({
        id: docId,
        content: metadata,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (error) {
      console.error('Error saving voice post to Supabase:', error);
      return 'error-ceramic-doc-id';
    }

    return docId;
  } catch (error) {
    console.error('Error in publishVoicePost:', error);
    return 'error-ceramic-doc-id';
  }
}

/**
 * Get a voice post document from Ceramic
 * This is a stub implementation that loads from Supabase instead
 * @param documentId The document ID
 * @returns The voice post metadata from Supabase
 */
export async function getVoicePost(documentId: string): Promise<VoicePostMetadata> {
  console.warn('Ceramic is disabled - getVoicePost called');
  console.log('Loading voice post from Supabase instead for documentId:', documentId);

  try {
    // Load the metadata from Supabase
    const { data, error } = await supabase
      .from('voice_metadata')
      .select('content')
      .eq('id', documentId)
      .maybeSingle();

    if (error) {
      console.error('Error loading voice post from Supabase:', error);
      return {
        audioUrl: '',
        transcript: '',
        userAddress: '',
        timestamp: new Date().toISOString(),
        duration: 0
      };
    }

    return data?.content as VoicePostMetadata || {
      audioUrl: '',
      transcript: '',
      userAddress: '',
      timestamp: new Date().toISOString(),
      duration: 0
    };
  } catch (error) {
    console.error('Error in getVoicePost:', error);
    return {
      audioUrl: '',
      transcript: '',
      userAddress: '',
      timestamp: new Date().toISOString(),
      duration: 0
    };
  }
}

/**
 * Update a voice post document in Ceramic
 * This is a stub implementation that updates in Supabase instead
 * @param documentId The document ID
 * @param metadata The updated metadata
 * @returns The document ID
 */
export async function updateVoicePost(documentId: string, metadata: VoicePostMetadata): Promise<string> {
  console.warn('Ceramic is disabled - updateVoicePost called');
  console.log('Updating voice post in Supabase instead for documentId:', documentId);

  try {
    // Update the metadata in Supabase
    const { error } = await supabase
      .from('voice_metadata')
      .update({
        content: metadata,
        updated_at: new Date().toISOString()
      })
      .eq('id', documentId);

    if (error) {
      console.error('Error updating voice post in Supabase:', error);
      return documentId;
    }

    return documentId;
  } catch (error) {
    console.error('Error in updateVoicePost:', error);
    return documentId;
  }
}

// Export interfaces for compatibility
export interface VoicePostMetadata {
  audioUrl: string;
  transcript?: string;
  userAddress: string;
  timestamp: string;
  duration: number;
  media?: MediaItem[];
  parentId?: string;
  channelId?: string;
  isPinned?: boolean;
  encrypted?: boolean;
  [key: string]: any;
}

export interface MediaItem {
  id?: string;
  url: string;
  type: string;
  width?: number;
  height?: number;
  [key: string]: any;
}
