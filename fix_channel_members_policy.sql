-- Drop existing policies on channel_members table
DROP POLICY IF EXISTS "Users can view channels they are members of" ON public.channel_members;
DROP POLICY IF EXISTS "Users can join public channels" ON public.channel_members;
DROP POLICY IF EXISTS "Users can leave channels" ON public.channel_members;
DROP POLICY IF EXISTS "Channel owners can manage members" ON public.channel_members;

-- Create new policies without recursion
CREATE POLICY "Users can view all channel members"
ON public.channel_members
FOR SELECT
USING (true);

CREATE POLICY "Users can join public channels"
ON public.channel_members
FOR INSERT
WITH CHECK (
  -- Check if the channel is public using a direct query instead of a recursive policy
  EXISTS (
    SELECT 1 FROM public.channels c
    WHERE c.id = channel_id
    AND c.is_public = true
  )
  -- Or if the user is the one being added
  OR profile_id = auth.uid()
);

CREATE POLICY "Users can leave channels"
ON public.channel_members
FOR DELETE
USING (profile_id = auth.uid());

CREATE POLICY "Channel owners can manage members"
ON public.channel_members
FOR ALL
USING (
  -- Check if the user is the channel owner using a direct query
  EXISTS (
    SELECT 1 FROM public.channels c
    WHERE c.id = channel_id
    AND c.owner_id = auth.uid()
  )
);
