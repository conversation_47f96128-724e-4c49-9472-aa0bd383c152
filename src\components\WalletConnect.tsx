
import React, { useState, useEffect } from 'react';
import { ethers } from 'ethers';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/sonner';

interface WalletConnectProps {
  onConnect: (account: string) => void;
}

const WalletConnect: React.FC<WalletConnectProps> = ({ onConnect }) => {
  const [account, setAccount] = useState<string>('');
  const [isConnecting, setIsConnecting] = useState(false);

  // Check if MetaMask is installed
  const checkIfWalletIsConnected = async () => {
    try {
      const { ethereum } = window as any;
      if (!ethereum) {
        console.log('Make sure you have MetaMask installed!');
        return;
      }

      // Check if we're authorized to access the user's wallet
      const accounts = await ethereum.request({ method: 'eth_accounts' });
      if (accounts.length !== 0) {
        const account = accounts[0];
        console.log('Found an authorized account:', account);
        setAccount(account);
        onConnect(account);
      }
    } catch (error) {
      console.error(error);
    }
  };

  // Connect wallet handler
  const connectWallet = async () => {
    try {
      setIsConnecting(true);
      const { ethereum } = window as any;
      if (!ethereum) {
        toast('Please install MetaMask to connect your wallet');
        return;
      }

      const accounts = await ethereum.request({ method: 'eth_requestAccounts' });
      console.log('Connected', accounts[0]);
      setAccount(accounts[0]);
      onConnect(accounts[0]);
      toast('Wallet connected successfully!');
    } catch (error: any) {
      console.error(error);
      toast('Error connecting wallet: ' + (error.message || 'Unknown error'));
    } finally {
      setIsConnecting(false);
    }
  };

  // Only check wallet connection if explicitly requested
  // This prevents automatic MetaMask popups
  useEffect(() => {
    // Check if we should auto-connect (controlled by a setting)
    const shouldAutoConnect = localStorage.getItem('autoConnectWallet') === 'true';

    if (shouldAutoConnect) {
      checkIfWalletIsConnected();
    } else {
      console.log('Auto wallet connection is disabled. User must click Connect Wallet button.');
    }
  }, []);

  return (
    <div className="flex flex-col items-center justify-center">
      {account ? (
        <div className="flex items-center gap-2 bg-secondary px-4 py-2 rounded-full">
          <div className="h-3 w-3 rounded-full bg-green-400"></div>
          <span className="text-sm font-medium">
            {`${account.substring(0, 6)}...${account.substring(account.length - 4)}`}
          </span>
        </div>
      ) : (
        <Button
          onClick={connectWallet}
          disabled={isConnecting}
          className="bg-voicechain-accent hover:bg-voicechain-purple text-white font-semibold rounded-full px-6"
        >
          {isConnecting ? 'Connecting...' : 'Connect Wallet'}
        </Button>
      )}
    </div>
  );
};

export default WalletConnect;
