
/**
 * Profile Image Helper
 * Provides utilities for storing and retrieving profile images
 * using Supabase Storage for persistence across devices
 */

import { supabase } from '@/integrations/supabase/client';

/**
 * Store a profile image in Supabase Storage
 * @param key The key to store the image under
 * @param dataUrl The data URL of the image
 * @param metadata Additional metadata to store
 * @returns Promise that resolves to the public URL of the stored image
 */
export const storeProfileImageInSupabase = async (
  key: string,
  dataUrl: string,
  metadata: Record<string, any> = {}
): Promise<string | null> => {
  try {
    // Convert data URL to blob
    const response = await fetch(dataUrl);
    const blob = await response.blob();
    
    // Store in memory cache for immediate access
    if (typeof window !== 'undefined') {
      if (!(window as any).__profileImageCache) {
        (window as any).__profileImageCache = {};
      }
      (window as any).__profileImageCache[key] = dataUrl;
    }

    // Store in Supabase Storage
    const filePath = `profiles/${metadata.userAddress}/${key}.jpg`;
    const { data, error } = await supabase
      .storage
      .from('profile_images')
      .upload(filePath, blob, {
        contentType: 'image/jpeg',
        upsert: true
      });

    if (error) {
      console.error('Error storing image in Supabase Storage:', error);
      return null;
    }

    // Get public URL
    const { data: publicUrlData } = supabase
      .storage
      .from('profile_images')
      .getPublicUrl(filePath);

    // Store the mapping in Supabase
    try {
      await supabase
        .from('profile_image_mappings')
        .upsert({
          user_address: metadata.userAddress,
          image_type: metadata.imageType || 'profile',
          image_key: key,
          image_url: publicUrlData.publicUrl,
          created_at: new Date().toISOString()
        }, {
          onConflict: 'user_address,image_type'
        });
    } catch (mappingError) {
      console.warn('Error storing image mapping in Supabase:', mappingError);
    }

    return publicUrlData.publicUrl;
  } catch (error) {
    console.error('Error in storeProfileImageInSupabase:', error);
    return null;
  }
};

/**
 * Get a profile image from Supabase Storage
 * @param userAddress The user address
 * @param imageType The type of image (profile or cover)
 * @returns Promise that resolves to the public URL of the image
 */
export const getProfileImageFromSupabase = async (
  userAddress: string,
  imageType: 'profile' | 'cover' = 'profile'
): Promise<string | null> => {
  try {
    // Check if we have a cached version in memory
    const cachedImages = (window as any).__profileImageCache || {};
    const cacheKey = `${userAddress}-${imageType}`;
    if (cachedImages[cacheKey]) {
      console.log(`Image retrieved from memory cache for ${userAddress}`);
      return cachedImages[cacheKey];
    }

    // Try to get from Supabase
    const { data, error } = await supabase
      .from('profile_image_mappings')
      .select('image_url')
      .eq('user_address', userAddress)
      .eq('image_type', imageType)
      .single();

    if (error) {
      console.warn(`No image found in Supabase for ${userAddress} (${imageType})`);
      return null;
    }

    if (data) {
      // Cache in memory for faster access
      if (typeof window !== 'undefined') {
        if (!(window as any).__profileImageCache) {
          (window as any).__profileImageCache = {};
        }
        (window as any).__profileImageCache[cacheKey] = data.image_url;
      }

      return data.image_url;
    }

    return null;
  } catch (error) {
    console.error('Error in getProfileImageFromSupabase:', error);
    return null;
  }
};

/**
 * Check if a URL is a Supabase Storage URL
 * @param url The URL to check
 * @returns True if the URL is a Supabase Storage URL
 */
export const isSupabaseUrl = (url: string): boolean => {
  return url.includes('supabase.co/storage/v1');
};

/**
 * Get a displayable URL for an image
 * @param url The URL to get a displayable URL for
 * @returns A displayable URL for the image
 */
export const getDisplayableImageUrl = async (url: string): Promise<string> => {
  if (!url) return '';

  // For Supabase URLs, they're already public and displayable
  return url;
};

/**
 * Store profile image mapping in Supabase
 * This helps track which profiles are using which image URLs
 * @param profileId The ID of the profile
 * @param imageType The type of image (profile or cover)
 * @param imageUrl The image URL
 */
export const storeProfileImageMapping = async (
  profileId: string,
  imageType: 'profile' | 'cover',
  imageUrl: string
): Promise<void> => {
  try {
    const { error } = await supabase
      .from('profile_image_mappings')
      .upsert({
        user_address: profileId,
        image_type: imageType,
        image_url: imageUrl,
        created_at: new Date().toISOString()
      }, {
        onConflict: 'user_address,image_type'
      });
    
    if (error) {
      console.error('Error storing profile image mapping in Supabase:', error);
    } else {
      console.log(`Stored ${imageType} image mapping for profile ${profileId}: ${imageUrl}`);
    }
  } catch (error) {
    console.error('Error storing profile image mapping:', error);
  }
};
