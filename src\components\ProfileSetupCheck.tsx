import React, { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/sonner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Upload, User } from 'lucide-react';

interface ProfileSetupCheckProps {
  children: React.ReactNode;
  onProfileReady?: (profileId: string) => void;
}

export const ProfileSetupCheck: React.FC<ProfileSetupCheckProps> = ({ 
  children, 
  onProfileReady 
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasProfile, setHasProfile] = useState(false);
  const [profileId, setProfileId] = useState<string | null>(null);
  const [showSetup, setShowSetup] = useState(false);
  const [username, setUsername] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [bio, setBio] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  useEffect(() => {
    checkProfileStatus();
  }, []);

  const checkProfileStatus = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        setIsLoading(false);
        return;
      }

      console.log('Checking/creating profile for user:', user.id);

      // Use the universal function to get or create profile
      const { data: profileData, error } = await supabase
        .rpc('get_or_create_user_profile', { p_user_id: user.id });

      if (error) {
        console.error('Error getting/creating profile:', error);
        setShowSetup(true);
        // Pre-fill with user data
        setDisplayName(user.user_metadata?.full_name || user.email?.split('@')[0] || 'User');
        setUsername(`user_${user.id.substring(0, 8)}`);
      } else if (profileData && profileData.length > 0) {
        console.log('Profile ready:', profileData[0]);
        setHasProfile(true);
        setProfileId(profileData[0].id);
        onProfileReady?.(profileData[0].id);
      } else {
        console.log('No profile found, showing setup');
        setShowSetup(true);
        // Pre-fill with user data
        setDisplayName(user.user_metadata?.full_name || user.email?.split('@')[0] || 'User');
        setUsername(`user_${user.id.substring(0, 8)}`);
      }
    } catch (error) {
      console.error('Error in profile check:', error);
      setShowSetup(true);
    } finally {
      setIsLoading(false);
    }
  };

  const createProfile = async () => {
    try {
      setIsCreating(true);
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      // Generate unique username if needed
      let finalUsername = username || `user_${user.id.substring(0, 8)}`;
      
      // Check if username exists and make it unique
      const { data: existingUser } = await supabase
        .from('profiles')
        .select('username')
        .eq('username', finalUsername)
        .maybeSingle();

      if (existingUser) {
        finalUsername = `${finalUsername}_${Math.floor(Math.random() * 1000)}`;
      }

      // Create the profile
      const { error } = await supabase
        .from('profiles')
        .upsert({
          id: user.id,
          wallet_address: user.id,
          username: finalUsername,
          display_name: displayName || 'User',
          bio: bio || '',
          avatar_url: '',
          cover_image_url: '',
          social_links: {},
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (error) {
        console.error('Error creating profile:', error);
        toast("Error creating profile: " + error.message);
        return;
      }

      toast("Profile created successfully!");

      setHasProfile(true);
      setProfileId(user.id);
      setShowSetup(false);
      onProfileReady?.(user.id);
    } catch (error) {
      console.error('Error creating profile:', error);
      toast("Failed to create profile. Please try again.");
    } finally {
      setIsCreating(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (showSetup) {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Complete Your Profile
            </CardTitle>
            <CardDescription>
              Set up your profile to start using spaces and other features.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="displayName">Display Name</Label>
              <Input
                id="displayName"
                value={displayName}
                onChange={(e) => setDisplayName(e.target.value)}
                placeholder="Your display name"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                value={username}
                onChange={(e) => setUsername(e.target.value.toLowerCase().replace(/[^a-z0-9_]/g, ''))}
                placeholder="username"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="bio">Bio (Optional)</Label>
              <Textarea
                id="bio"
                value={bio}
                onChange={(e) => setBio(e.target.value)}
                placeholder="Tell us about yourself..."
                rows={3}
              />
            </div>
            
            <Button 
              onClick={createProfile} 
              disabled={isCreating || !displayName.trim()}
              className="w-full"
            >
              {isCreating ? 'Creating Profile...' : 'Complete Profile'}
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (hasProfile && profileId) {
    return <>{children}</>;
  }

  return (
    <div className="flex items-center justify-center min-h-screen">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Profile Setup Required</CardTitle>
          <CardDescription>
            You need to set up your profile to continue.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => setShowSetup(true)} className="w-full">
            Set Up Profile
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};