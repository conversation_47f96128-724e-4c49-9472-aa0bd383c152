-- Create media storage bucket for voice message attachments
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'media',
  'media',
  true,
  10485760, -- 10MB limit
  ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'video/webm', 'video/quicktime']
)
ON CONFLICT (id) DO NOTHING;

-- Create storage policies for media uploads
CREATE POLICY "Anyone can view media files" 
ON storage.objects 
FOR SELECT 
USING (bucket_id = 'media');

CREATE POLICY "Authenticated users can upload media files" 
ON storage.objects 
FOR INSERT 
WITH CHECK (bucket_id = 'media' AND auth.uid() IS NOT NULL);

CREATE POLICY "Users can update their own media files" 
ON storage.objects 
FOR UPDATE 
USING (bucket_id = 'media' AND auth.uid() IS NOT NULL);

CREATE POLICY "Users can delete their own media files" 
ON storage.objects 
FOR DELETE 
USING (bucket_id = 'media' AND auth.uid() IS NOT NULL);

-- Ensure the voice_message_media table has proper structure
CREATE TABLE IF NOT EXISTS public.voice_message_media (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    voice_message_id TEXT NOT NULL,
    url TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('image', 'video')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on voice_message_media table
ALTER TABLE public.voice_message_media ENABLE ROW LEVEL SECURITY;

-- Create policies for voice_message_media
CREATE POLICY "Anyone can view voice message media"
ON public.voice_message_media
FOR SELECT
USING (true);

CREATE POLICY "Authenticated users can insert voice message media"
ON public.voice_message_media
FOR INSERT
WITH CHECK (true);

CREATE POLICY "Users can update voice message media"
ON public.voice_message_media
FOR UPDATE
USING (true);

CREATE POLICY "Users can delete voice message media"
ON public.voice_message_media
FOR DELETE
USING (true);