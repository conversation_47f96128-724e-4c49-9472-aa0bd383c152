
import { supabase } from '@/lib/supabase';
import { v4 as uuidv4 } from 'uuid';

// Interface for storage services
interface StorageService {
  uploadForTranscription: (audioBlob: Blob, userId: string) => Promise<string>;
  getTranscription: (audioUrl: string) => Promise<string | null>;
  saveTranscription: (audioUrl: string, transcript: string) => Promise<boolean>;
  uploadAudio?: (audioBlob: Blob, userId: string) => Promise<string>;
}

// Supabase implementation of storage service
class SupabaseStorageService implements StorageService {
  async uploadForTranscription(audioBlob: Blob, userId: string): Promise<string> {
    try {
      const fileName = `transcriptions/${userId}/${uuidv4()}.webm`;
      
      // Upload to Supabase storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('audio')
        .upload(fileName, audioBlob, {
          contentType: 'audio/webm',
          cacheControl: '3600'
        });
        
      if (uploadError) {
        console.error('Error uploading audio for transcription:', uploadError);
        throw uploadError;
      }
      
      // Get the public URL
      const { data: urlData } = supabase.storage
        .from('audio')
        .getPublicUrl(fileName);
        
      console.log('Audio uploaded for transcription', urlData.publicUrl);
      return urlData.publicUrl;
    } catch (error) {
      console.error('Error in uploadForTranscription:', error);
      throw error;
    }
  }

  async uploadAudio(audioBlob: Blob, userId: string): Promise<string> {
    return this.uploadForTranscription(audioBlob, userId);
  }
  
  async getTranscription(audioUrl: string): Promise<string | null> {
    try {
      // Extract the audio ID from the URL
      const urlParts = audioUrl.split('/');
      const fileName = urlParts[urlParts.length - 1];
      const audioId = fileName.split('.')[0];
      
      // Look up the transcription in the database
      const { data, error } = await supabase
        .from('transcriptions')
        .select('text')
        .eq('audio_id', audioId)
        .single();
        
      if (error) {
        console.error('Error getting transcription:', error);
        return null;
      }
      
      return data?.text || null;
    } catch (error) {
      console.error('Error in getTranscription:', error);
      return null;
    }
  }
  
  async saveTranscription(audioUrl: string, transcript: string): Promise<boolean> {
    try {
      // Extract the audio ID from the URL
      const urlParts = audioUrl.split('/');
      const fileName = urlParts[urlParts.length - 1];
      const audioId = fileName.split('.')[0];
      
      // Store the transcription in the database
      const { error } = await supabase
        .from('transcriptions')
        .upsert({
          audio_id: audioId,
          text: transcript,
          created_at: new Date().toISOString()
        });
        
      if (error) {
        console.error('Error saving transcription:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Error in saveTranscription:', error);
      return false;
    }
  }
}

// Mock implementation for local development or testing
class MockStorageService implements StorageService {
  async uploadForTranscription(audioBlob: Blob, userId: string): Promise<string> {
    // Create a blob URL for the audio
    const url = URL.createObjectURL(audioBlob);
    console.log('Mock uploaded audio:', url);
    return url;
  }
  
  async uploadAudio(audioBlob: Blob, userId: string): Promise<string> {
    return this.uploadForTranscription(audioBlob, userId);
  }
  
  async getTranscription(audioUrl: string): Promise<string | null> {
    return null; // No transcription available in mock
  }
  
  async saveTranscription(audioUrl: string, transcript: string): Promise<boolean> {
    console.log('Mock saved transcription:', { audioUrl, transcript });
    return true;
  }
}

// Variable to control storage provider selection
let forceBlockchainStorage = false;

// Function to set blockchain storage preference
export function setBlockchainStoragePreference(useBlockchain: boolean): void {
  forceBlockchainStorage = useBlockchain;
}

// Factory function to get the appropriate storage service
export function getStorageService(): StorageService {
  if (typeof window === 'undefined') {
    // Server-side rendering
    return new MockStorageService();
  }
  
  // Check if we have Supabase config
  if (supabase) {
    return new SupabaseStorageService();
  }
  
  // Fallback to mock
  return new MockStorageService();
}

// Export a default object with all our functions
const storageServiceFactory = {
  getStorageService,
  setBlockchainStoragePreference,
  forceBlockchainStorage: (value: boolean) => {
    forceBlockchainStorage = value;
  }
};

export default storageServiceFactory;
