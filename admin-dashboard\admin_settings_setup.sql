-- Create admin_settings table
CREATE TABLE IF NOT EXISTS admin_settings (
  id TEXT PRIMARY KEY,
  site_name TEXT,
  site_description TEXT,
  theme TEXT,
  nft_storage_api_key TEXT,
  helius_api_key TEXT,
  eleven_labs_api_key TEXT,
  require_email_verification BOOLEAN,
  max_login_attempts INTEGER,
  session_timeout INTEGER,
  email_notifications BO<PERSON>EAN,
  admin_alerts <PERSON><PERSON><PERSON><PERSON><PERSON>,
  alert_email TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies
ALTER TABLE admin_settings ENABLE ROW LEVEL SECURITY;

-- Only allow admins to read settings
CREATE POLICY "Admins can read settings" 
ON admin_settings FOR SELECT 
USING (auth.uid() IN (SELECT id FROM admin_profiles));

-- Only allow super_admins to update sensitive settings
CREATE POLICY "Super admins can update all settings" 
ON admin_settings FOR UPDATE 
USING (auth.uid() IN (SELECT id FROM admin_profiles WHERE role = 'super_admin'));

-- Allow regular admins to update non-sensitive settings
CREATE POLICY "Regular admins can update non-sensitive settings" 
ON admin_settings FOR UPDATE 
USING (
  auth.uid() IN (SELECT id FROM admin_profiles) AND
  (
    -- Only updating general or notification settings
    (
      (OLD.nft_storage_api_key IS NULL OR NEW.nft_storage_api_key = OLD.nft_storage_api_key) AND
      (OLD.helius_api_key IS NULL OR NEW.helius_api_key = OLD.helius_api_key) AND
      (OLD.eleven_labs_api_key IS NULL OR NEW.eleven_labs_api_key = OLD.eleven_labs_api_key) AND
      (OLD.require_email_verification IS NULL OR NEW.require_email_verification = OLD.require_email_verification) AND
      (OLD.max_login_attempts IS NULL OR NEW.max_login_attempts = OLD.max_login_attempts) AND
      (OLD.session_timeout IS NULL OR NEW.session_timeout = OLD.session_timeout)
    )
  )
);

-- Only allow super_admins to insert settings
CREATE POLICY "Super admins can insert settings" 
ON admin_settings FOR INSERT 
TO authenticated
WITH CHECK (auth.uid() IN (SELECT id FROM admin_profiles WHERE role = 'super_admin'));

-- Insert default settings
INSERT INTO admin_settings (
  id, 
  site_name, 
  site_description, 
  theme,
  require_email_verification,
  max_login_attempts,
  session_timeout,
  email_notifications,
  admin_alerts,
  alert_email
) VALUES (
  'global',
  'Audra',
  'Voice-based Web3 social platform',
  'system',
  true,
  5,
  24,
  true,
  true,
  '<EMAIL>'
) ON CONFLICT (id) DO NOTHING;

-- Instructions:
-- 1. Run this script in the Supabase SQL editor after creating the admin_profiles table
-- 2. This will set up the admin_settings table with default values
-- 3. Only super_admins will be able to update sensitive settings like API keys
