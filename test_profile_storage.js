#!/usr/bin/env node

/**
 * Test script to verify profile storage is working correctly
 */

import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://jcltjkaumevuycntdmds.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpjbHRqa2F1bWV2dXljbnRkbWRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTkwMDEsImV4cCI6MjA2MTg3NTAwMX0.LqQSk0NwB1mDB4Cznn0iQSrYvsAAWyUsaj8reo_etqM";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testProfileStorage() {
  console.log('🧪 Testing profile storage system...\n');

  const testWalletAddress = 'test_wallet_' + Date.now();

  try {
    // Test 1: Check if RPC functions exist
    console.log('1️⃣ Testing RPC functions...');
    
    try {
      const { data: createTest, error: createError } = await supabase
        .rpc('get_or_create_profile', { p_wallet_address: testWalletAddress });

      if (createError) {
        console.log('❌ get_or_create_profile function not available:', createError.message);
        console.log('Please run: node fix_profile_storage.js');
        return;
      } else if (createTest && createTest.length > 0) {
        console.log('✅ get_or_create_profile function is working');
        console.log('   Created profile:', createTest[0].username);
      }
    } catch (error) {
      console.log('❌ RPC function test failed:', error.message);
      return;
    }

    // Test 2: Test profile update
    console.log('\n2️⃣ Testing profile updates...');
    
    try {
      const { data: updateTest, error: updateError } = await supabase
        .rpc('update_profile_by_address', {
          p_wallet_address: testWalletAddress,
          p_username: 'updated_test_user',
          p_display_name: 'Updated Test User',
          p_bio: 'This is an updated test bio',
          p_avatar_url: 'https://example.com/avatar.jpg',
          p_social_links: { twitter: '@testuser', website: 'https://test.com' }
        });

      if (updateError) {
        console.log('❌ update_profile_by_address function failed:', updateError.message);
      } else if (updateTest && updateTest.length > 0) {
        console.log('✅ update_profile_by_address function is working');
        console.log('   Updated profile:', updateTest[0].display_name);
        console.log('   Bio:', updateTest[0].bio);
        console.log('   Social links:', updateTest[0].social_links);
      }
    } catch (error) {
      console.log('❌ Profile update test failed:', error.message);
    }

    // Test 3: Test profile retrieval
    console.log('\n3️⃣ Testing profile retrieval...');
    
    try {
      const { data: retrieveTest, error: retrieveError } = await supabase
        .from('profiles')
        .select('*')
        .eq('wallet_address', testWalletAddress)
        .single();

      if (retrieveError) {
        console.log('❌ Profile retrieval failed:', retrieveError.message);
      } else {
        console.log('✅ Profile retrieval is working');
        console.log('   Retrieved profile:', retrieveTest.display_name);
        console.log('   Username:', retrieveTest.username);
        console.log('   Bio:', retrieveTest.bio);
      }
    } catch (error) {
      console.log('❌ Profile retrieval test failed:', error.message);
    }

    // Test 4: Test username uniqueness
    console.log('\n4️⃣ Testing username uniqueness...');
    
    try {
      const duplicateWallet = 'test_wallet_duplicate_' + Date.now();
      const { data: duplicateTest, error: duplicateError } = await supabase
        .rpc('update_profile_by_address', {
          p_wallet_address: duplicateWallet,
          p_username: 'updated_test_user', // Same username as before
          p_display_name: 'Duplicate Test User'
        });

      if (duplicateError) {
        console.log('❌ Username uniqueness test failed:', duplicateError.message);
      } else if (duplicateTest && duplicateTest.length > 0) {
        console.log('✅ Username uniqueness is working');
        console.log('   Generated unique username:', duplicateTest[0].username);
        
        // Clean up duplicate test profile
        await supabase.from('profiles').delete().eq('wallet_address', duplicateWallet);
      }
    } catch (error) {
      console.log('❌ Username uniqueness test failed:', error.message);
    }

    // Clean up test profile
    console.log('\n🧹 Cleaning up test data...');
    const { error: cleanupError } = await supabase
      .from('profiles')
      .delete()
      .eq('wallet_address', testWalletAddress);

    if (cleanupError) {
      console.log('⚠️  Failed to clean up test profile:', cleanupError.message);
    } else {
      console.log('✅ Test data cleaned up');
    }

    console.log('\n🎉 Profile storage test complete!');
    console.log('\n📋 Test Results Summary:');
    console.log('  ✅ RPC functions are available');
    console.log('  ✅ Profile creation works');
    console.log('  ✅ Profile updates work');
    console.log('  ✅ Profile retrieval works');
    console.log('  ✅ Username uniqueness works');
    console.log('\n🔧 Your profile storage system is working correctly!');
    console.log('Profile changes should now persist after page refresh.');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure you ran: node fix_profile_storage.js');
    console.log('2. Check your Supabase connection');
    console.log('3. Verify the RPC functions exist in your database');
    console.log('4. Check the profiles table structure');
  }
}

// Run the test
testProfileStorage().catch(console.error);
