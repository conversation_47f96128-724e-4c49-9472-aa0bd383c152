import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, AlertCircle, Loader2 } from 'lucide-react';
import { supabase } from '@/services/supabase';
import { format, subDays, differenceInDays } from 'date-fns';

// Import Skeleton component if available, otherwise create a simple fallback
let Skeleton: React.FC<React.HTMLAttributes<HTMLDivElement>>;
try {
  Skeleton = require('@/components/ui/skeleton').Skeleton;
} catch (error) {
  // Fallback Skeleton component
  Skeleton = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
    <div
      className={`animate-pulse rounded-md bg-secondary ${className || ''}`}
      {...props}
    />
  );
}

// Dynamically import chart.js and react-chartjs-2
let ChartJS: any;
let Bar: any;
let Line: any;
let Pie: any;

// Flag to track if chart.js is available
const isChartJsAvailable = typeof window !== 'undefined';

// Try to import and register chart.js components
try {
  if (isChartJsAvailable) {
    import('chart.js').then((ChartModule) => {
      ChartJS = ChartModule.Chart;
      ChartJS.register(
        ChartModule.CategoryScale,
        ChartModule.LinearScale,
        ChartModule.BarElement,
        ChartModule.LineElement,
        ChartModule.PointElement,
        ChartModule.ArcElement,
        ChartModule.Title,
        ChartModule.Tooltip,
        ChartModule.Legend
      );
    });

    import('react-chartjs-2').then((ChartComponents) => {
      Bar = ChartComponents.Bar;
      Line = ChartComponents.Line;
      Pie = ChartComponents.Pie;
    });
  }
} catch (error) {
  console.error('Failed to load chart.js:', error);
}

interface VerificationAnalyticsProps {
  timeRange?: '7d' | '30d' | '90d' | 'all';
}

const VerificationAnalytics: React.FC<VerificationAnalyticsProps> = ({ timeRange = '30d' }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [analyticsData, setAnalyticsData] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchAnalyticsData();
  }, [timeRange]);

  const fetchAnalyticsData = async () => {
    try {
      setIsLoading(true);

      // Calculate date range
      const now = new Date();
      let startDate;

      switch (timeRange) {
        case '7d':
          startDate = subDays(now, 7);
          break;
        case '30d':
          startDate = subDays(now, 30);
          break;
        case '90d':
          startDate = subDays(now, 90);
          break;
        case 'all':
        default:
          startDate = new Date(2020, 0, 1); // Far in the past
          break;
      }

      // Fetch verification applications
      const { data: applications, error: applicationsError } = await supabase
        .from('verification_applications')
        .select('*')
        .gte('submitted_at', startDate.toISOString())
        .order('submitted_at', { ascending: true });

      if (applicationsError) {
        throw applicationsError;
      }

      // Process data for analytics
      const data = processAnalyticsData(applications, startDate, now);
      setAnalyticsData(data);
    } catch (error) {
      console.error('Error fetching verification analytics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const processAnalyticsData = (applications: any[], startDate: Date, endDate: Date) => {
    // Count applications by status
    const statusCounts = {
      pending: 0,
      approved: 0,
      rejected: 0
    };

    // Count applications by type
    const typeCounts: Record<string, number> = {};

    // Track applications over time
    const dayCount = differenceInDays(endDate, startDate) + 1;
    const dateLabels = Array.from({ length: dayCount }, (_, i) =>
      format(subDays(endDate, dayCount - i - 1), 'MMM d')
    );

    const submittedByDay = new Array(dayCount).fill(0);
    const approvedByDay = new Array(dayCount).fill(0);
    const rejectedByDay = new Array(dayCount).fill(0);

    // Calculate average processing time
    let totalProcessingTime = 0;
    let processedCount = 0;

    // Process each application
    applications.forEach(app => {
      // Count by status
      if (app.status in statusCounts) {
        statusCounts[app.status as keyof typeof statusCounts]++;
      }

      // Count by type
      const type = app.type || 'unknown';
      typeCounts[type] = (typeCounts[type] || 0) + 1;

      // Track over time
      const submittedDate = new Date(app.submitted_at);
      const dayIndex = differenceInDays(submittedDate, startDate);

      if (dayIndex >= 0 && dayIndex < dayCount) {
        submittedByDay[dayIndex]++;
      }

      if (app.status === 'approved' || app.status === 'rejected') {
        const reviewedDate = new Date(app.reviewed_at);
        const reviewedDayIndex = differenceInDays(reviewedDate, startDate);

        if (reviewedDayIndex >= 0 && reviewedDayIndex < dayCount) {
          if (app.status === 'approved') {
            approvedByDay[reviewedDayIndex]++;
          } else {
            rejectedByDay[reviewedDayIndex]++;
          }
        }

        // Calculate processing time
        if (app.reviewed_at) {
          const processingTime = differenceInDays(new Date(app.reviewed_at), submittedDate);
          totalProcessingTime += processingTime;
          processedCount++;
        }
      }
    });

    // Calculate approval rate
    const totalProcessed = statusCounts.approved + statusCounts.rejected;
    const approvalRate = totalProcessed > 0 ? (statusCounts.approved / totalProcessed) * 100 : 0;

    // Calculate average processing time
    const avgProcessingTime = processedCount > 0 ? totalProcessingTime / processedCount : 0;

    return {
      statusCounts,
      typeCounts,
      dateLabels,
      submittedByDay,
      approvedByDay,
      rejectedByDay,
      approvalRate,
      avgProcessingTime,
      totalApplications: applications.length
    };
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2 mb-2">
            <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
            <div className="font-semibold">Loading analytics...</div>
          </div>
          <Skeleton className="h-8 w-64 mb-2" />
          <Skeleton className="h-4 w-full" />
        </CardHeader>
        <CardContent className="space-y-8">
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    );
  }

  if (!analyticsData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Verification Analytics</CardTitle>
          <CardDescription>No data available for the selected time period</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-center text-muted-foreground py-8">No verification applications found</p>
        </CardContent>
      </Card>
    );
  }

  const statusChartData = {
    labels: ['Pending', 'Approved', 'Rejected'],
    datasets: [
      {
        label: 'Applications by Status',
        data: [
          analyticsData.statusCounts.pending,
          analyticsData.statusCounts.approved,
          analyticsData.statusCounts.rejected
        ],
        backgroundColor: [
          'rgba(255, 159, 64, 0.7)',
          'rgba(75, 192, 192, 0.7)',
          'rgba(255, 99, 132, 0.7)'
        ],
        borderColor: [
          'rgb(255, 159, 64)',
          'rgb(75, 192, 192)',
          'rgb(255, 99, 132)'
        ],
        borderWidth: 1
      }
    ]
  };

  const typeLabels = Object.keys(analyticsData.typeCounts);
  const typeChartData = {
    labels: typeLabels,
    datasets: [
      {
        label: 'Applications by Type',
        data: typeLabels.map(type => analyticsData.typeCounts[type]),
        backgroundColor: [
          'rgba(54, 162, 235, 0.7)',
          'rgba(153, 102, 255, 0.7)',
          'rgba(255, 206, 86, 0.7)',
          'rgba(75, 192, 192, 0.7)',
          'rgba(255, 99, 132, 0.7)',
          'rgba(255, 159, 64, 0.7)'
        ],
        borderWidth: 1
      }
    ]
  };

  const timelineChartData = {
    labels: analyticsData.dateLabels,
    datasets: [
      {
        label: 'Submitted',
        data: analyticsData.submittedByDay,
        borderColor: 'rgb(54, 162, 235)',
        backgroundColor: 'rgba(54, 162, 235, 0.5)',
        tension: 0.3
      },
      {
        label: 'Approved',
        data: analyticsData.approvedByDay,
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.5)',
        tension: 0.3
      },
      {
        label: 'Rejected',
        data: analyticsData.rejectedByDay,
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.5)',
        tension: 0.3
      }
    ]
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Verification Analytics</CardTitle>
        <CardDescription>
          Insights into verification applications {timeRange !== 'all' ? `over the past ${timeRange}` : 'for all time'}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <Card>
            <CardContent className="pt-6">
              <div className="text-2xl font-bold">{analyticsData.totalApplications}</div>
              <p className="text-xs text-muted-foreground">Total Applications</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="text-2xl font-bold">{analyticsData.approvalRate.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">Approval Rate</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="text-2xl font-bold">{analyticsData.avgProcessingTime.toFixed(1)} days</div>
              <p className="text-xs text-muted-foreground">Avg. Processing Time</p>
            </CardContent>
          </Card>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="overview" className="flex items-center gap-1">
              <PieChart size={16} />
              <span>Overview</span>
            </TabsTrigger>
            <TabsTrigger value="types" className="flex items-center gap-1">
              <BarChart size={16} />
              <span>Types</span>
            </TabsTrigger>
            <TabsTrigger value="timeline" className="flex items-center gap-1">
              <LineChart size={16} />
              <span>Timeline</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="h-[300px]">
            {Pie ? (
              <Pie data={statusChartData} options={{ responsive: true, maintainAspectRatio: false }} />
            ) : (
              <div className="flex flex-col items-center justify-center h-full">
                <AlertCircle className="h-10 w-10 text-muted-foreground mb-2" />
                <p className="text-muted-foreground">Chart library not available</p>
                <p className="text-xs text-muted-foreground mt-1">Please install chart.js and react-chartjs-2</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="types" className="h-[300px]">
            {Bar ? (
              <Bar data={typeChartData} options={{ responsive: true, maintainAspectRatio: false }} />
            ) : (
              <div className="flex flex-col items-center justify-center h-full">
                <AlertCircle className="h-10 w-10 text-muted-foreground mb-2" />
                <p className="text-muted-foreground">Chart library not available</p>
                <p className="text-xs text-muted-foreground mt-1">Please install chart.js and react-chartjs-2</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="timeline" className="h-[300px]">
            {Line ? (
              <Line data={timelineChartData} options={{ responsive: true, maintainAspectRatio: false }} />
            ) : (
              <div className="flex flex-col items-center justify-center h-full">
                <AlertCircle className="h-10 w-10 text-muted-foreground mb-2" />
                <p className="text-muted-foreground">Chart library not available</p>
                <p className="text-xs text-muted-foreground mt-1">Please install chart.js and react-chartjs-2</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default VerificationAnalytics;
