-- Create reposts table
CREATE TABLE IF NOT EXISTS public.reposts (
  id UUID PRIMARY KEY,
  user_id TEXT NOT NULL,
  post_id TEXT NOT NULL,
  post_type TEXT NOT NULL CHECK (post_type IN ('voice_message', 'journal')),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(user_id, post_id, post_type)
);

-- Enable RLS on reposts table
ALTER TABLE public.reposts ENABLE ROW LEVEL SECURITY;

-- Create policies for reposts table

-- 1. Allow users to view all reposts
CREATE POLICY "Users can view all reposts"
ON public.reposts
FOR SELECT
USING (true);

-- 2. Allow users to insert their own reposts
CREATE POLICY "Users can insert their own reposts"
ON public.reposts
FOR INSERT
WITH CHECK (user_id = auth.uid());

-- 3. Allow users to delete their own reposts
CREATE POLICY "Users can delete their own reposts"
ON public.reposts
FOR DELETE
USING (user_id = auth.uid());

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS reposts_user_id_idx ON public.reposts(user_id);
CREATE INDEX IF NOT EXISTS reposts_post_id_idx ON public.reposts(post_id);
CREATE INDEX IF NOT EXISTS reposts_post_type_idx ON public.reposts(post_type);

-- Create function to get reposts for a user
CREATE OR REPLACE FUNCTION get_user_reposts(user_id_param TEXT)
RETURNS TABLE (
  id UUID,
  user_id TEXT,
  post_id TEXT,
  post_type TEXT,
  created_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT r.id, r.user_id, r.post_id, r.post_type, r.created_at
  FROM public.reposts r
  WHERE r.user_id = user_id_param
  ORDER BY r.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Create function to get reposts for a post
CREATE OR REPLACE FUNCTION get_post_reposts(post_id_param TEXT, post_type_param TEXT)
RETURNS TABLE (
  id UUID,
  user_id TEXT,
  post_id TEXT,
  post_type TEXT,
  created_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT r.id, r.user_id, r.post_id, r.post_type, r.created_at
  FROM public.reposts r
  WHERE r.post_id = post_id_param
  AND r.post_type = post_type_param
  ORDER BY r.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Create function to check if a user has reposted a post
CREATE OR REPLACE FUNCTION has_user_reposted(user_id_param TEXT, post_id_param TEXT, post_type_param TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  repost_exists BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM public.reposts r
    WHERE r.user_id = user_id_param
    AND r.post_id = post_id_param
    AND r.post_type = post_type_param
  ) INTO repost_exists;
  
  RETURN repost_exists;
END;
$$ LANGUAGE plpgsql;
