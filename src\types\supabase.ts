
export type Profile = {
  id: string;
  display_name: string | null;
  username: string | null;
  avatar_url: string | null;
  bio: string | null;
  created_at: string;
  updated_at: string;
  wallet_address: string | null;
  cover_image_url: string | null;
  social_links: any;
  post_count: number | null;
  like_count: number | null;
  tip_count: number | null;
  verification_type: string | null;
};

export type Channel = {
  id: string;
  name: string;
  description: string | null;
  created_by: string;
  is_private: boolean;
  created_at: string;
  updated_at: string;
};

export type ChannelMember = {
  id: string;
  channel_id: string;
  profile_id: string;
  role: 'owner' | 'moderator' | 'member';
  joined_at: string;
};

export type VoiceMessage = {
  id: string;
  profile_id: string;
  channel_id: string | null;
  parent_id: string | null;
  transcript: string | null;
  audio_url: string;
  audio_duration: number;
  created_at: string;
  updated_at: string;
};

export type ChainVoicePost = {
  id: string;
  title: string;
  transcription: string | null;
  audio_url: string;
  audio_duration: number;
  source_chain: string;
  event_type: string;
  tags: string[];
  timestamp: string;
  metadata: Record<string, any>;
};

export type Like = {
  id: string;
  voice_message_id: string;
  profile_id: string;
  created_at: string;
};

export type Tip = {
  id: string;
  voice_message_id: string;
  sender_id: string;
  receiver_id: string;
  amount: number;
  currency: string;
  created_at: string;
};

export type Notification = {
  id: string;
  type: string;
  from_address: string | null;
  to_address: string;
  message_id: string | null;
  read: boolean;
  data: Record<string, any>;
  created_at: string;
};
