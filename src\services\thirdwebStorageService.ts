/**
 * Thirdweb Storage Service
 * This service handles uploading files to IPFS via Thirdweb Storage
 */

import { MediaFile, MediaItem } from '@/types/media';
import { VoicePostMetadata } from '@/services/disabledCeramicClient';
import { publishVoicePost, getVoicePost } from '@/services/disabledCeramicClient';
import * as thirdwebStorage from './thirdwebStorage';

/**
 * BlockchainStorageService using Thirdweb
 * This service handles uploading files to IPFS via Thirdweb Storage
 */
export class ThirdwebStorageService {
  /**
   * Upload audio to IPFS
   * @param audioBlob The audio blob to upload
   * @param userId The user ID (wallet address)
   * @param options Additional options
   * @returns The IPFS URI or gateway URL
   */
  public async uploadAudio(
    audioBlob: Blob,
    userId: string,
    options: {
      encrypt?: boolean;
      fileName?: string;
    } = {}
  ): Promise<string> {
    try {
      // Generate a unique file name if not provided
      const fileName = options.fileName || `audio_${userId.substring(0, 10)}_${Date.now()}.webm`;
      
      console.log(`Uploading audio to IPFS via Thirdweb: ${fileName}`);
      
      try {
        // Try to upload to IPFS via Thirdweb
        console.log('Attempting to upload audio to IPFS via Thirdweb...');
        const ipfsUri = await thirdwebStorage.uploadBlobToIPFS(audioBlob, fileName);
        
        // Store in global cache for immediate access
        if (typeof window !== 'undefined') {
          if (!(window as any).audioCache) {
            (window as any).audioCache = {};
          }
          
          // Use the resolved gateway URL as the key
          const gatewayUrl = thirdwebStorage.resolveIPFSUri(ipfsUri);
          (window as any).audioCache[gatewayUrl] = audioBlob;
          
          // Also store the original blob for backup
          (window as any).audioCache[`original_${gatewayUrl}`] = audioBlob;
        }
        
        console.log('Successfully uploaded audio to IPFS via Thirdweb:', ipfsUri);
        
        // Return the gateway URL for immediate playback
        return thirdwebStorage.resolveIPFSUri(ipfsUri);
      } catch (ipfsError) {
        console.error('Error uploading audio to IPFS via Thirdweb:', ipfsError);
        
        // Fall back to blob URL if IPFS upload fails
        console.warn('Falling back to blob URL for audio storage');
        const blobUrl = URL.createObjectURL(audioBlob);
        console.log('Created blob URL:', blobUrl);
        
        // Store in global cache
        if (typeof window !== 'undefined') {
          if (!(window as any).audioCache) {
            (window as any).audioCache = {};
          }
          
          (window as any).audioCache[blobUrl] = audioBlob;
          
          // Also store in localStorage for persistence across page refreshes
          try {
            // Store a reference to the blob URL
            const audioUrls = JSON.parse(localStorage.getItem('audioUrls') || '{}');
            audioUrls[blobUrl] = {
              timestamp: Date.now(),
              userId: userId,
              fileName: fileName
            };
            localStorage.setItem('audioUrls', JSON.stringify(audioUrls));
            
            console.log('Stored blob URL reference in localStorage');
          } catch (localStorageError) {
            console.warn('Failed to store blob URL reference in localStorage:', localStorageError);
          }
        }
        
        return blobUrl;
      }
    } catch (error) {
      console.error('Error in uploadAudio:', error);
      
      // Final fallback to blob URL
      console.warn('Final fallback to blob URL for audio storage');
      const blobUrl = URL.createObjectURL(audioBlob);
      
      // Store in global cache
      if (typeof window !== 'undefined') {
        if (!(window as any).audioCache) {
          (window as any).audioCache = {};
        }
        
        (window as any).audioCache[blobUrl] = audioBlob;
      }
      
      return blobUrl;
    }
  }
  
  /**
   * Upload audio for transcription
   * @param audioBlob The audio blob to upload
   * @param userId The user ID (wallet address)
   * @returns The IPFS URI or gateway URL
   */
  public async uploadForTranscription(audioBlob: Blob, userId: string): Promise<string> {
    try {
      // Generate a unique file name
      const timestamp = Date.now();
      const fileName = `transcription_${userId.substring(0, 10)}_${timestamp}.webm`;
      
      // Upload to IPFS via Thirdweb
      const ipfsUri = await thirdwebStorage.uploadBlobToIPFS(audioBlob, fileName);
      
      console.log('Successfully uploaded audio for transcription to IPFS via Thirdweb');
      
      // Return the gateway URL for immediate access
      return thirdwebStorage.resolveIPFSUri(ipfsUri);
    } catch (error) {
      console.error('Error uploading audio for transcription to IPFS:', error);
      
      // Fall back to blob URL if IPFS upload fails
      console.warn('Falling back to blob URL for audio storage');
      const blobUrl = URL.createObjectURL(audioBlob);
      return blobUrl;
    }
  }
  
  /**
   * Save a voice message with metadata to Ceramic
   * @param userId The user ID (wallet address)
   * @param audioUrl The audio URL (IPFS URI or gateway URL)
   * @param transcript The transcript text
   * @param audioDuration The audio duration in seconds
   * @param media Optional media files
   * @param options Additional options
   * @returns The Ceramic document ID
   */
  public async saveVoiceMessage(
    userId: string,
    audioUrl: string,
    transcript: string,
    audioDuration: number,
    media: MediaFile[] = [],
    options: {
      parentId?: string;
      channelId?: string;
      isPinned?: boolean;
      encrypted?: boolean;
    } = {}
  ): Promise<string> {
    try {
      // Process media files - upload to IPFS if they're not already there
      const processedMedia: MediaItem[] = await Promise.all(
        media.map(async (item) => {
          // If the URL is already an IPFS URI, use it
          if (item.url.startsWith('ipfs://')) {
            return {
              id: item.id,
              url: item.url,
              type: item.type
            };
          }
          
          // If it's a blob URL and we have the file, upload to IPFS
          if (item.file) {
            const ipfsUri = await thirdwebStorage.uploadBlobToIPFS(
              item.file,
              `${item.id}_${Date.now()}.${item.file.name.split('.').pop() || 'jpg'}`
            );
            return {
              id: item.id,
              url: ipfsUri,
              type: item.type
            };
          }
          
          // Otherwise, just use the existing URL
          return {
            id: item.id,
            url: item.url,
            type: item.type
          };
        })
      );
      
      // Create metadata for Ceramic
      const metadata: VoicePostMetadata = {
        audio: audioUrl,
        transcript,
        encrypted: options.encrypted || false,
        createdAt: new Date().toISOString(),
        author: userId,
        media: processedMedia,
        parentId: options.parentId,
        channelId: options.channelId,
        isPinned: options.isPinned || false,
        duration: audioDuration
      };
      
      // Publish to Ceramic
      const documentId = await publishVoicePost(metadata);
      
      return documentId;
    } catch (error) {
      console.error('Error saving voice message to Ceramic:', error);
      throw error;
    }
  }
  
  /**
   * Get a voice message from Ceramic
   * @param documentId The Ceramic document ID
   * @returns The voice post metadata
   */
  public async getVoiceMessage(documentId: string): Promise<VoicePostMetadata> {
    try {
      return await getVoicePost(documentId);
    } catch (error) {
      console.error('Error getting voice message from Ceramic:', error);
      throw error;
    }
  }
}

// Export a singleton instance
const thirdwebStorageService = new ThirdwebStorageService();
export default thirdwebStorageService;
