import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  MessageSquare, 
  BookOpen, 
  Clock, 
  Edit, 
  Trash2, 
  Play, 
  Plus,
  ArrowRight
} from 'lucide-react';
import { toast } from '@/components/ui/sonner';
import { format } from 'date-fns';
import { draftService, Draft, DraftType } from '@/services/draftService';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Skeleton } from '@/components/ui/skeleton';

const Drafts: React.FC = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [voiceMessageDrafts, setVoiceMessageDrafts] = useState<Draft[]>([]);
  const [journalDrafts, setJournalDrafts] = useState<Draft[]>([]);
  const [draftToDelete, setDraftToDelete] = useState<Draft | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Get current user
  const getCurrentUserAccount = () => {
    return localStorage.getItem('connectedAccount') || '';
  };

  // Load drafts
  useEffect(() => {
    const loadDrafts = async () => {
      setIsLoading(true);
      try {
        const userId = getCurrentUserAccount();
        if (!userId) {
          toast.error('Please connect your wallet to view drafts');
          return;
        }

        // Load voice message drafts
        const voiceDrafts = await draftService.getDrafts(userId, 'voice_message');
        setVoiceMessageDrafts(voiceDrafts);

        // Load journal drafts
        const journalDrafts = await draftService.getDrafts(userId, 'journal');
        setJournalDrafts(journalDrafts);
      } catch (error) {
        console.error('Error loading drafts:', error);
        toast.error('Failed to load drafts');
      } finally {
        setIsLoading(false);
      }
    };

    loadDrafts();
  }, []);

  // Format time for audio duration
  const formatTime = (seconds: number = 0) => {
    const mins = Math.floor(seconds / 60).toString().padStart(2, '0');
    const secs = Math.floor(seconds % 60).toString().padStart(2, '0');
    return `${mins}:${secs}`;
  };

  // Handle edit draft
  const handleEditDraft = (draft: Draft) => {
    if (draft.type === 'voice_message') {
      navigate(`/edit-voice-message/${draft.id}`);
    } else if (draft.type === 'journal') {
      navigate(`/edit-journal/${draft.id}`);
    }
  };

  // Handle delete draft
  const handleDeleteDraft = async () => {
    if (!draftToDelete) return;

    try {
      const userId = getCurrentUserAccount();
      const success = await draftService.deleteDraft(draftToDelete.id, userId);

      if (success) {
        toast.success('Draft deleted successfully');
        
        // Update state
        if (draftToDelete.type === 'voice_message') {
          setVoiceMessageDrafts(prev => prev.filter(d => d.id !== draftToDelete.id));
        } else if (draftToDelete.type === 'journal') {
          setJournalDrafts(prev => prev.filter(d => d.id !== draftToDelete.id));
        }
      } else {
        toast.error('Failed to delete draft');
      }
    } catch (error) {
      console.error('Error deleting draft:', error);
      toast.error('Failed to delete draft');
    } finally {
      setDraftToDelete(null);
      setIsDeleteDialogOpen(false);
    }
  };

  // Render draft card
  const renderDraftCard = (draft: Draft) => (
    <Card key={draft.id} className="mb-4">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">
          {draft.title || (draft.type === 'voice_message' ? 'Voice Message Draft' : 'Journal Draft')}
        </CardTitle>
        <CardDescription className="flex items-center text-xs">
          <Clock className="h-3 w-3 mr-1" />
          {format(draft.updatedAt, 'PPp')}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <p className="text-sm line-clamp-2">
          {draft.transcript || 'No transcript available'}
        </p>
        {draft.audioDuration && (
          <div className="flex items-center mt-2 text-xs text-muted-foreground">
            <Play className="h-3 w-3 mr-1" />
            <span>{formatTime(draft.audioDuration)}</span>
          </div>
        )}
      </CardContent>
      <CardFooter className="pt-0 flex justify-end gap-2">
        <Button
          variant="outline"
          size="sm"
          className="h-8"
          onClick={() => {
            setDraftToDelete(draft);
            setIsDeleteDialogOpen(true);
          }}
        >
          <Trash2 className="h-4 w-4 mr-1" />
          Delete
        </Button>
        <Button
          variant="default"
          size="sm"
          className="h-8"
          onClick={() => handleEditDraft(draft)}
        >
          <Edit className="h-4 w-4 mr-1" />
          Edit
        </Button>
      </CardFooter>
    </Card>
  );

  // Render empty state
  const renderEmptyState = (type: DraftType) => (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <div className="bg-muted rounded-full p-4 mb-4">
        {type === 'voice_message' ? (
          <MessageSquare className="h-8 w-8 text-muted-foreground" />
        ) : (
          <BookOpen className="h-8 w-8 text-muted-foreground" />
        )}
      </div>
      <h3 className="font-medium mb-2">No drafts yet</h3>
      <p className="text-sm text-muted-foreground mb-4">
        {type === 'voice_message'
          ? 'Your voice message drafts will appear here'
          : 'Your journal drafts will appear here'}
      </p>
      <Button
        variant="outline"
        onClick={() => navigate(type === 'voice_message' ? '/' : '/journals/create')}
      >
        <Plus className="h-4 w-4 mr-2" />
        {type === 'voice_message' ? 'Create Voice Message' : 'Create Journal'}
      </Button>
    </div>
  );

  // Render loading state
  const renderLoadingState = () => (
    <div className="space-y-4">
      {[1, 2, 3].map(i => (
        <Card key={i} className="mb-4">
          <CardHeader className="pb-2">
            <Skeleton className="h-5 w-3/4" />
            <Skeleton className="h-3 w-1/4 mt-2" />
          </CardHeader>
          <CardContent className="pb-2">
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-3/4" />
          </CardContent>
          <CardFooter className="pt-0 flex justify-end gap-2">
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-8 w-20" />
          </CardFooter>
        </Card>
      ))}
    </div>
  );

  return (
    <div className="container mx-auto px-4 py-6 max-w-3xl">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-bold">Drafts</h1>
        <Button variant="ghost" onClick={() => navigate('/')}>
          Home
          <ArrowRight className="h-4 w-4 ml-2" />
        </Button>
      </div>

      <Separator className="mb-6" />

      <Tabs defaultValue="voice-messages">
        <TabsList className="grid grid-cols-2 mb-6">
          <TabsTrigger value="voice-messages" className="flex items-center gap-1">
            <MessageSquare className="h-4 w-4" />
            <span>Voice Messages</span>
          </TabsTrigger>
          <TabsTrigger value="journals" className="flex items-center gap-1">
            <BookOpen className="h-4 w-4" />
            <span>Journals</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="voice-messages">
          {isLoading ? (
            renderLoadingState()
          ) : voiceMessageDrafts.length === 0 ? (
            renderEmptyState('voice_message')
          ) : (
            <div className="space-y-4">
              {voiceMessageDrafts.map(draft => renderDraftCard(draft))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="journals">
          {isLoading ? (
            renderLoadingState()
          ) : journalDrafts.length === 0 ? (
            renderEmptyState('journal')
          ) : (
            <div className="space-y-4">
              {journalDrafts.map(draft => renderDraftCard(draft))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Delete confirmation dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete your draft.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteDraft}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default Drafts;
