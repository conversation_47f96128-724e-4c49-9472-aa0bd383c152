
import React, { useState, useRef, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Mic, Square, Loader2, CheckCircle2, XCircle, Trash2 } from "lucide-react";
import { useUserSettings } from '@/hooks/use-supabase-storage';
import { formatDuration } from '@/utils/formatters';
import { toast } from '@/components/ui/sonner';
import { supabase } from '@/lib/supabase';
import { cn } from '@/lib/utils';
import { useWhisper } from '@/hooks/use-whisper'; // Make sure to import the hook
import { StopCircle, X } from "lucide-react";
import { v4 as uuidv4 } from 'uuid';

interface VoiceRecorderProps {
  onRecordingComplete?: (audioBlob: Blob, audioUrl: string, transcript: string, duration: number) => void;
  onCancel?: () => void;
  className?: string;
  maxDuration?: number; // Maximum recording duration in seconds
}

export function VoiceRecorder({
  onRecordingComplete,
  onCancel,
  className,
  maxDuration = 300 // 5 minutes default max
}: VoiceRecorderProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [persistentUrl, setPersistentUrl] = useState<string | null>(null);
  const recorderRef = useRef<MediaRecorder | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  
  // Use the whisper hook for transcription
  const { transcribing, transcript, startTranscription } = useWhisper({
    onTranscriptionProgress: (text) => {
      console.log('Transcription in progress:', text);
    },
    onTranscriptionComplete: (text) => {
      console.log('Transcription completed:', text);
    }
  });

  // Start recording
  const startRecording = async () => {
    try {
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        console.log('Starting voice recording...');
        
        // Get user media
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        streamRef.current = stream;
        
        // Create recorder
        const options = { mimeType: 'audio/webm' };
        const recorder = new MediaRecorder(stream, options);
        recorderRef.current = recorder;
        
        // Reset chunks
        chunksRef.current = [];
        
        // Set up event handlers
        recorder.ondataavailable = (e) => {
          console.log('Data available from recorder, size:', e.data.size);
          if (e.data.size > 0) {
            chunksRef.current.push(e.data);
          }
        };
        
        recorder.onstop = async () => {
          console.log('Recorder stopped, processing audio...');
          
          // Stop the timer
          if (timerRef.current) {
            clearInterval(timerRef.current);
            timerRef.current = null;
          }
          
          // Process the recording
          await processRecording();
        };
        
        // Start recording
        recorder.start(1000); // Capture data every second
        setIsRecording(true);
        
        // Start the timer
        let seconds = 0;
        timerRef.current = setInterval(() => {
          seconds++;
          setRecordingTime(seconds);
          
          // Stop recording if max duration reached
          if (seconds >= maxDuration) {
            stopRecording();
          }
        }, 1000);
        
        console.log('Recording started successfully');
      } else {
        toast.error("Your browser doesn't support audio recording");
      }
    } catch (error) {
      console.error('Error starting recording:', error);
      toast.error("Couldn't access microphone. Please check permissions.");
    }
  };

  // Stop recording
  const stopRecording = () => {
    if (recorderRef.current && recorderRef.current.state === 'recording') {
      console.log('Stopping recording...');
      recorderRef.current.stop();
      setIsRecording(false);
      
      // Stop and release the media stream
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
        streamRef.current = null;
      }
    }
  };

  // Process the recording
  const processRecording = async () => {
    try {
      if (chunksRef.current.length === 0) {
        console.warn('No audio data available');
        toast.error("No audio was recorded");
        return;
      }
      
      // Create a blob from the chunks
      const blob = new Blob(chunksRef.current, { type: 'audio/webm' });
      
      console.log('Creating audio from chunks, size:', blob.size);
      
      if (blob.size < 100) {
        console.warn('Audio data too small, likely no audio recorded');
        toast.error("Recording too short or no audio detected");
        return;
      }
      
      // Store the blob
      setAudioBlob(blob);
      
      // Create a URL for the blob
      const url = URL.createObjectURL(blob);
      setAudioUrl(url);
      
      // Start transcription of the audio
      console.log('Starting transcription...');
      const transcriptText = await startTranscription(blob);
      console.log('Transcription result:', transcriptText);
      
      // Upload to Supabase storage
      try {
        const userId = localStorage.getItem('connectedAccount') || 'anonymous';
        console.log('Uploading audio to Supabase storage...');
        
        const fileName = `recordings/${userId}/${uuidv4()}.webm`;
        
        // Upload to Supabase storage
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('audio')
          .upload(fileName, blob, {
            contentType: 'audio/webm',
            cacheControl: '3600'
          });
          
        if (uploadError) {
          console.error('Error uploading audio recording:', uploadError);
          throw uploadError;
        }
        
        // Get the public URL
        const { data: urlData } = supabase.storage
          .from('audio')
          .getPublicUrl(fileName);
          
        console.log('Audio stored permanently at:', urlData.publicUrl);
        setPersistentUrl(urlData.publicUrl);
        
        // Store the transcript in the database
        const { error: transcriptError } = await supabase
          .from('transcriptions')
          .insert({
            audio_id: fileName,
            text: transcriptText,
            created_at: new Date().toISOString()
          });
          
        if (transcriptError) {
          console.error('Error storing transcript:', transcriptError);
        }
        
        // Call onRecordingComplete with our data
        if (onRecordingComplete) {
          onRecordingComplete(blob, urlData.publicUrl, transcriptText, recordingTime);
        }
      } catch (uploadError) {
        console.error('Error uploading audio:', uploadError);
        
        // Fall back to the blob URL
        if (onRecordingComplete) {
          onRecordingComplete(blob, url, transcriptText, recordingTime);
        }
      }
    } catch (error) {
      console.error('Error processing recording:', error);
      toast.error("Error processing recording");
    }
  };

  // Cancel recording
  const cancelRecording = () => {
    if (isRecording) {
      // Stop recording
      stopRecording();
    }
    
    // Clear the audio state
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }
    
    setAudioBlob(null);
    setAudioUrl(null);
    setPersistentUrl(null);
    setRecordingTime(0);
    
    // Call onCancel callback
    if (onCancel) {
      onCancel();
    }
  };

  // Delete the current recording
  const deleteRecording = () => {
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }
    
    setAudioBlob(null);
    setAudioUrl(null);
    setPersistentUrl(null);
    setRecordingTime(0);
  };

  // Format time for display
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      
      if (recorderRef.current && recorderRef.current.state === 'recording') {
        recorderRef.current.stop();
      }
      
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
      
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  return (
    <div className={cn("flex flex-col items-center justify-center space-y-4 p-4 bg-background rounded-lg border", className)}>
      {/* Recording Controls */}
      <div className="flex items-center justify-center space-x-4 w-full">
        {!audioUrl && !isRecording && (
          <Button
            variant="default"
            size="lg"
            className="bg-red-500 hover:bg-red-600 text-white"
            onClick={startRecording}
          >
            <Mic className="mr-2 h-5 w-5" />
            Record Voice
          </Button>
        )}
        
        {isRecording && (
          <>
            <div className="flex-1 h-10 bg-muted rounded-full overflow-hidden relative">
              <div 
                className="absolute inset-0 bg-red-500 opacity-20 animate-pulse" 
                style={{ 
                  width: `${Math.min(100, (recordingTime / maxDuration) * 100)}%` 
                }}
              ></div>
              <div className="absolute inset-0 flex items-center justify-center text-sm font-medium">
                Recording: {formatTime(recordingTime)}
              </div>
            </div>
            
            <Button
              variant="destructive"
              size="icon"
              onClick={stopRecording}
            >
              <StopCircle className="h-5 w-5" />
            </Button>
          </>
        )}
        
        {audioUrl && !isRecording && (
          <>
            <audio 
              src={audioUrl} 
              controls 
              className="flex-1 h-10" 
            />
            
            <Button
              variant="destructive"
              size="icon"
              onClick={deleteRecording}
            >
              <Trash2 className="h-5 w-5" />
            </Button>
          </>
        )}
        
        {!isRecording && (
          <Button
            variant="outline"
            size="icon"
            onClick={cancelRecording}
          >
            <X className="h-5 w-5" />
          </Button>
        )}
      </div>
      
      {/* Transcription Status */}
      {transcribing && (
        <div className="w-full text-center text-sm text-muted-foreground">
          <Loader2 className="h-4 w-4 animate-spin inline-block mr-2" />
          Transcribing audio...
        </div>
      )}
      
      {transcript && !transcribing && (
        <div className="w-full text-sm">
          <div className="font-medium mb-1">Transcript:</div>
          <div className="bg-muted p-2 rounded text-muted-foreground">
            {transcript}
          </div>
        </div>
      )}
      
      {persistentUrl && (
        <div className="w-full text-xs text-muted-foreground">
          <span className="font-medium">Stored:</span> Audio saved to Supabase
        </div>
      )}
    </div>
  );
}

export default VoiceRecorder;
