import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { VoiceMessageProps } from '@/types/voice-message';

interface AnalyticsProps {
  messages: VoiceMessageProps[];
  userAddress: string;
}

const Analytics: React.FC<AnalyticsProps> = ({ messages, userAddress }) => {
  // Filter messages to only include those from the current user
  const userMessages = messages.filter(message => message.userAddress === userAddress);

  return (
    <div className="w-full md:container md:mx-auto md:max-w-3xl p-4">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Voice Analytics</h1>
        <p className="text-muted-foreground">
          Insights and metrics for your voice content
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Posts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userMessages.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Replies</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {userMessages.reduce((sum, message) => sum + (message.replies?.length || 0), 0)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Media</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {userMessages.reduce((sum, message) => sum + (message.media?.length || 0), 0)}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          {userMessages.length > 0 ? (
            <div className="space-y-4">
              {userMessages.slice(0, 5).map((message, index) => (
                <div key={index} className="border-b pb-3">
                  <p className="text-sm">{message.transcript.substring(0, 100)}...</p>
                  <div className="flex gap-4 mt-2 text-xs text-muted-foreground">
                    <span>{new Date(message.timestamp).toLocaleDateString()}</span>
                    <span>{message.replies?.length || 0} replies</span>
                    <span>{message.media?.length || 0} media</span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-muted-foreground">No voice messages yet.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Analytics;
