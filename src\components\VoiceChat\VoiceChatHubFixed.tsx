import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import {
  MessageCircle,
  Users,
  Plus,
  Search,
  Mic,
  Volume2,
  Heart,
  Zap,
  Sparkles,
  Waves,
  Brain,
  Palette,
  Globe,
  Lock,
  Crown,
  Star,
  Headphones,
  Send,
  Smile,
  Paperclip,
  DollarSign,
  Settings,
  Play,
  Pause,
  MicOff,
  X,
  MoreHorizontal,
  Trash2,
  ArrowLeft
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/components/ui/sonner';
import { voiceChatService, type VoiceChat, type VoiceChatMessage, type EmotionalWave } from '@/services/voiceChatService';
import { voiceRecordingService } from '@/services/voiceRecordingService';
import { supabase } from '@/integrations/supabase/client';
import { useWhisper } from '@/hooks/use-whisper-improved';
import audioStorageService from '@/services/audioStorageService';
import SpatialChatView from '@/components/VoiceChat/SpatialChatViewNew';
import AudioPlayer from '@/components/AudioPlayer';
import { useIsMobile } from '@/hooks/use-mobile';

const VoiceChatHub: React.FC = () => {
  const { user } = useAuth();
  const isMobile = useIsMobile();
  const [activeTab, setActiveTab] = useState('chats');
  const [selectedChat, setSelectedChat] = useState<string | null>(null);
  const [chats, setChats] = useState<VoiceChat[]>([]);
  const [messages, setMessages] = useState<VoiceChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSpaceChat, setIsSpaceChat] = useState(false);
  const [isWaveChat, setIsWaveChat] = useState(false);

  // Voice recording states
  const [showVoiceRecorder, setShowVoiceRecorder] = useState(false);
  const [isProcessingVoice, setIsProcessingVoice] = useState(false);

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [showAIModal, setShowAIModal] = useState(false);
  const [showSpaceModal, setShowSpaceModal] = useState(false);
  const [createChatType, setCreateChatType] = useState<'direct' | 'group' | 'wave' | 'space'>('direct');

  // Create chat form states
  const [chatName, setChatName] = useState('');
  const [chatDescription, setChatDescription] = useState('');
  const [isPrivate, setIsPrivate] = useState(false);

  // Image upload states
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  // Tip modal states
  const [showTipModal, setShowTipModal] = useState(false);
  const [tipRecipientId, setTipRecipientId] = useState<string>('');

  // Load chats on component mount
  useEffect(() => {
    if (user) {
      loadChats();
    }
  }, [user]);

  // Load messages when chat is selected
  useEffect(() => {
    if (selectedChat) {
      loadMessages(selectedChat);
    }
  }, [selectedChat]);

  const loadChats = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const userChats = await voiceChatService.getUserChats(user.id);
      setChats(userChats);
    } catch (error) {
      console.error('Error loading chats:', error);
      toast.error('Failed to load chats');
    } finally {
      setIsLoading(false);
    }
  };

  const loadMessages = async (chatId: string) => {
    if (!user) return;

    try {
      const chatMessages = await voiceChatService.getChatMessagesFiltered(chatId, user.id);
      setMessages(chatMessages);

      // Check if it's a space or wave chat
      const chat = chats.find(c => c.id === chatId);
      setIsSpaceChat(chat?.type === 'space' || chat?.type === 'wave');
      setIsWaveChat(chat?.type === 'wave');
    } catch (error) {
      console.error('Error loading messages:', error);
      toast.error('Failed to load messages');
    }
  };

  const sendTextMessage = async () => {
    if (!newMessage.trim() || !selectedChat || !user) return;

    try {
      const message = await voiceChatService.sendTextMessage(
        selectedChat,
        user.id,
        newMessage
      );

      if (message) {
        setMessages(prev => [...prev, message]);
        setNewMessage('');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    }
  };

  const startVoiceMessage = () => {
    setShowVoiceRecorder(true);
  };

  const handleVoiceRecordingComplete = async (audioBlob: Blob, duration: number, transcript?: string) => {
    if (!selectedChat || !user) return;

    try {
      setIsProcessingVoice(true);

      // Upload voice recording
      const voiceUrl = await voiceRecordingService.uploadVoiceRecording(audioBlob, user.id, selectedChat);

      if (voiceUrl) {
        // Send voice message
        const message = await voiceChatService.sendVoiceMessage(
          selectedChat,
          user.id,
          voiceUrl,
          duration,
          transcript
        );

        if (message) {
          setMessages(prev => [...prev, message]);
        }
      }
    } catch (error) {
      console.error('Error sending voice message:', error);
      toast.error('Failed to send voice message');
    } finally {
      setIsProcessingVoice(false);
      setShowVoiceRecorder(false);
    }
  };

  const createNewChat = (type: 'direct' | 'group' | 'wave' | 'space') => {
    setCreateChatType(type);
    setShowCreateModal(true);
    setChatName('');
    setChatDescription('');
    setIsPrivate(false);
  };

  const handleCreateChat = async () => {
    if (!chatName.trim() || !user) return;

    try {
      const newChat = await voiceChatService.createChat({
        name: chatName,
        description: chatDescription,
        type: createChatType,
        creator_id: user.id,
        is_private: isPrivate,
      });

      if (newChat) {
        setChats([newChat, ...chats]);
        setSelectedChat(newChat.id);
        setShowCreateModal(false);
        toast.success(`${createChatType} chat created successfully!`);
      }
    } catch (error) {
      console.error('Error creating chat:', error);
      toast.error('Failed to create chat');
    }
  };

  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const sendImageMessage = async () => {
    if (!selectedImage || !selectedChat || !user?.id) return;

    try {
      // Upload image to Supabase Storage
      const fileName = `chat_image_${Date.now()}_${selectedImage.name}`;
      const { data, error } = await supabase.storage
        .from('voice-recordings')
        .upload(`chat_images/${fileName}`, selectedImage);

      if (error) {
        toast.error('Failed to upload image');
        return;
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('voice-recordings')
        .getPublicUrl(data.path);

      // Send message with image URL
      const message = await voiceChatService.sendTextMessage(
        selectedChat,
        user.id,
        `📷 Image: ${urlData.publicUrl}`
      );

      if (message) {
        setMessages(prev => [...prev, message]);
        setSelectedImage(null);
        setImagePreview(null);
      }
    } catch (error) {
      console.error('Error sending image message:', error);
      toast.error('Failed to send image');
    }
  };

  const changeTheme = () => {
    // This would change the chat theme
    toast.success('Theme changed!');
  };

  const openTipModal = () => {
    const chat = chats.find(c => c.id === selectedChat);
    if (chat) {
      setTipRecipientId(chat.creator_id);
      setShowTipModal(true);
    }
  };

  const deleteMessage = async (messageId: string, deleteType: 'for-me' | 'for-everyone') => {
    if (!user) return;

    try {
      let success = false;

      if (deleteType === 'for-me') {
        success = await voiceChatService.deleteMessageForMe(messageId, user.id);
      } else {
        success = await voiceChatService.deleteMessageForEveryone(messageId, user.id);
      }

      if (success) {
        // Reload messages to reflect the deletion
        if (selectedChat) {
          loadMessages(selectedChat);
        }
        toast.success(`Message deleted ${deleteType}`);
      }
    } catch (error) {
      console.error('Error deleting message:', error);
      toast.error('Failed to delete message');
    }
  };

  const containerClasses = isMobile
    ? 'h-[calc(100vh-200px)] flex flex-col bg-background rounded-lg border'
    : 'h-screen flex flex-col bg-background';

  return (
    <div className={containerClasses}>
      {/* Header - Different for Mobile and Desktop */}
      {isMobile ? (
        <div className="border-b bg-card p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <MessageCircle className="h-6 w-6 text-primary" />
              <h1 className="text-lg font-semibold">Messages</h1>
            </div>

            <div className="flex items-center gap-1">
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => setShowSettingsModal(true)}>
                <Settings className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => setShowAIModal(true)}>
                <Brain className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <div className="border-b bg-card p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="relative">
                <MessageCircle className="h-8 w-8 text-primary" />
                <Sparkles className="h-4 w-4 text-yellow-500 absolute -top-1 -right-1" />
              </div>
              <div>
                <h1 className="text-xl font-bold">VoiceWave Chat</h1>
                <p className="text-sm text-muted-foreground">Revolutionary messaging</p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={() => setShowSettingsModal(true)}>
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
              <Button variant="outline" size="sm" onClick={() => setShowAIModal(true)}>
                <Brain className="h-4 w-4 mr-2" />
                AI Assistant
              </Button>
            </div>
          </div>
        </div>
      )}

      <div className={`flex-1 flex ${isMobile ? 'flex-col' : 'flex-row'} overflow-hidden`}>
        {/* Sidebar - Desktop: Fixed width, Mobile: Responsive */}
        <div className={`${isMobile ? (selectedChat ? 'hidden' : 'flex-1') : 'w-80'} ${!isMobile || !selectedChat ? 'border-r' : ''} bg-card flex flex-col`}>
          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <TabsList className="grid w-full grid-cols-3 m-4 mb-2">
              <TabsTrigger value="chats">Chats</TabsTrigger>
              <TabsTrigger value="waves">Waves</TabsTrigger>
              <TabsTrigger value="spaces">Spaces</TabsTrigger>
            </TabsList>

            <TabsContent value="chats" className="flex-1 px-4 pb-4">
              <div className="space-y-2">
                <Button className="w-full justify-start" variant="outline" onClick={() => createNewChat('direct')}>
                  <Plus className="h-4 w-4 mr-2" />
                  New Chat
                </Button>

                {/* Chat List */}
                <ScrollArea className="flex-1">
                  {chats.filter(chat => chat.type === 'direct' || chat.type === 'group').map((chat) => (
                    <Card
                      key={chat.id}
                      className={`cursor-pointer transition-all hover:shadow-md mb-2 ${
                        selectedChat === chat.id ? 'ring-2 ring-primary' : ''
                      }`}
                      onClick={() => setSelectedChat(chat.id)}
                    >
                      <CardContent className="p-3">
                        <div className="flex items-start gap-3">
                          <Avatar className="h-10 w-10">
                            <AvatarFallback>
                              {chat.name.slice(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <h3 className="font-medium truncate">{chat.name}</h3>
                            <p className="text-sm text-muted-foreground truncate">
                              {chat.description || 'No description'}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </ScrollArea>
              </div>
            </TabsContent>

            <TabsContent value="waves" className="flex-1 px-4 pb-4">
              <div className="space-y-4">
                <div className="flex gap-2">
                  <Button onClick={() => createNewChat('wave')} className="flex-1">
                    <Waves className="h-4 w-4 mr-2" />
                    Create Wave
                  </Button>
                </div>

                {/* Wave Chats */}
                <ScrollArea className="flex-1">
                  {chats.filter(chat => chat.type === 'wave').map((chat) => (
                    <Card
                      key={chat.id}
                      className={`cursor-pointer transition-all hover:shadow-md mb-2 ${
                        selectedChat === chat.id ? 'ring-2 ring-primary' : ''
                      } border-l-4 border-l-purple-500`}
                      onClick={() => setSelectedChat(chat.id)}
                    >
                      <CardContent className="p-3">
                        <div className="flex items-start gap-3">
                          <div className="relative">
                            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                              <Waves className="h-5 w-5 text-white" />
                            </div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="font-medium truncate">{chat.name}</h3>
                            <p className="text-sm text-muted-foreground truncate">
                              {chat.description || 'Wave energy chat'}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </ScrollArea>
              </div>
            </TabsContent>

            <TabsContent value="spaces" className="flex-1 px-4 pb-4">
              <div className="space-y-4">
                <div className="flex gap-2">
                  <Button onClick={() => createNewChat('space')} className="flex-1">
                    <Headphones className="h-4 w-4 mr-2" />
                    Create Space
                  </Button>
                </div>

                {/* Space Chats */}
                <ScrollArea className="flex-1">
                  {chats.filter(chat => chat.type === 'space').map((chat) => (
                    <Card
                      key={chat.id}
                      className={`cursor-pointer transition-all hover:shadow-md mb-2 ${
                        selectedChat === chat.id ? 'ring-2 ring-primary' : ''
                      } border-l-4 border-l-blue-500`}
                      onClick={() => setSelectedChat(chat.id)}
                    >
                      <CardContent className="p-3">
                        <div className="flex items-start gap-3">
                          <div className="relative">
                            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center">
                              <Globe className="h-5 w-5 text-white" />
                            </div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="font-medium truncate">{chat.name}</h3>
                            <p className="text-sm text-muted-foreground truncate">
                              {chat.description || 'Spatial audio space'}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </ScrollArea>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* Main Chat Area */}
        <div className={`${isMobile ? (selectedChat ? 'flex-1' : 'hidden') : 'flex-1'} flex flex-col ${isMobile && selectedChat ? 'absolute inset-0 top-[60px] bg-background z-10' : ''}`}>
          {selectedChat ? (
            <>
              {/* Chat Header */}
              <div className="border-b bg-card p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {isMobile && (
                      <Button variant="ghost" size="sm" onClick={() => setSelectedChat(null)}>
                        <ArrowLeft className="h-4 w-4" />
                      </Button>
                    )}
                    <Avatar className="h-8 w-8">
                      <AvatarFallback>
                        {chats.find(c => c.id === selectedChat)?.name.slice(0, 2).toUpperCase() || 'CH'}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-medium">{chats.find(c => c.id === selectedChat)?.name || 'Chat'}</h3>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        Active
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {!isMobile && (
                      <>
                        <Button variant="ghost" size="sm" onClick={() => setShowAIModal(true)}>
                          <Brain className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => changeTheme()}>
                          <Palette className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                    <Button variant="ghost" size="sm" onClick={() => openTipModal()}>
                      <DollarSign className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              {/* Messages Area */}
              {isSpaceChat ? (
                <SpatialChatView
                  messages={messages}
                  participants={[]}
                  onSendMessage={sendTextMessage}
                  onSendVoice={startVoiceMessage}
                  currentUserId={user?.id || ''}
                  onDeleteMessage={deleteMessage}
                />
              ) : (
                <ScrollArea className="flex-1 p-4">
                  <div className="space-y-4">
                    {messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex gap-3 group ${message.sender_id === user?.id ? 'justify-end' : ''}`}
                      >
                        {message.sender_id !== user?.id && (
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={message.sender_profile?.avatar_url} />
                            <AvatarFallback>
                              {message.sender_profile?.display_name?.slice(0, 2) || 'U'}
                            </AvatarFallback>
                          </Avatar>
                        )}

                        <div className={`max-w-md ${message.sender_id === user?.id ? 'order-first mr-2' : 'order-last'}`}>
                          {message.sender_id !== user?.id && (
                            <div className="flex items-center mb-1">
                              <span className="text-sm font-medium">
                                {message.sender_profile?.display_name || 'Unknown'}
                              </span>
                            </div>
                          )}

                          <div className={`relative rounded-lg p-3 ${
                            message.sender_id === user?.id
                              ? 'bg-primary text-primary-foreground'
                              : 'bg-muted'
                          }`}>
                            {message.message_type === 'text' && (
                              <p className="text-sm">{message.content}</p>
                            )}

                            {message.message_type === 'voice' && message.voice_url && (
                              <div className="space-y-2">
                                <AudioPlayer
                                  src={message.voice_url}
                                  className="w-full"
                                />

                                {message.voice_transcript && (
                                  <div className="bg-black/10 rounded-lg p-2 text-xs italic">
                                    "{message.voice_transcript}"
                                  </div>
                                )}
                              </div>
                            )}
                          </div>

                          <div className="flex items-center justify-end mt-1">
                            <span className="text-xs text-muted-foreground">
                              {new Date(message.created_at).toLocaleTimeString()}
                            </span>
                          </div>
                        </div>

                        {message.sender_id === user?.id && (
                          <Avatar className="h-8 w-8">
                            <AvatarFallback>ME</AvatarFallback>
                          </Avatar>
                        )}
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              )}

              {/* Input Area - Different for Mobile and Desktop */}
              {!isSpaceChat && (
                <div className="border-t bg-card p-3">
                  {/* Image Preview */}
                  {imagePreview && (
                    <div className="mb-3 p-2 bg-muted rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Image Preview:</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedImage(null);
                            setImagePreview(null);
                          }}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                      <img src={imagePreview} alt="Preview" className="max-w-32 h-auto rounded" />
                      <Button onClick={sendImageMessage} size="sm" className="mt-2 w-full">
                        Send Image 📷
                      </Button>
                    </div>
                  )}

                  {isMobile ? (
                    <div className="space-y-2">
                      <Textarea
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        placeholder="Type a message..."
                        className="w-full min-h-[60px]"
                        disabled={isProcessingVoice}
                      />

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={startVoiceMessage}
                            disabled={isProcessingVoice}
                            className="h-9 w-9 p-0 rounded-full"
                          >
                            {isProcessingVoice ? (
                              <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                            ) : (
                              <Mic className="h-4 w-4" />
                            )}
                          </Button>
                          <Button variant="ghost" size="sm" className="h-9 w-9 p-0 rounded-full">
                            <Smile className="h-4 w-4" />
                          </Button>
                          <label htmlFor="mobile-image-upload">
                            <Button variant="ghost" size="sm" asChild className="h-9 w-9 p-0 rounded-full">
                              <span>
                                <Paperclip className="h-4 w-4" />
                              </span>
                            </Button>
                          </label>
                          <input
                            id="mobile-image-upload"
                            type="file"
                            accept="image/*"
                            onChange={handleImageSelect}
                            className="hidden"
                          />
                        </div>
                        <Button
                          onClick={sendTextMessage}
                          disabled={!newMessage.trim() || isProcessingVoice}
                          className="rounded-full h-9 w-9 p-0"
                        >
                          <Send className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={startVoiceMessage}
                        disabled={isProcessingVoice}
                      >
                        {isProcessingVoice ? (
                          <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                        ) : (
                          <Mic className="h-4 w-4" />
                        )}
                      </Button>
                      <Input
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && newMessage.trim()) {
                            sendTextMessage();
                          }
                        }}
                        placeholder="Type a message or speak your mind..."
                        className="flex-1"
                        disabled={isProcessingVoice}
                      />
                      <Button variant="ghost" size="sm">
                        <Smile className="h-4 w-4" />
                      </Button>
                      <label htmlFor="desktop-image-upload">
                        <Button variant="ghost" size="sm" asChild>
                          <span>
                            <Paperclip className="h-4 w-4" />
                          </span>
                        </Button>
                      </label>
                      <input
                        id="desktop-image-upload"
                        type="file"
                        accept="image/*"
                        onChange={handleImageSelect}
                        className="hidden"
                      />
                      <Button
                        size="sm"
                        onClick={sendTextMessage}
                        disabled={!newMessage.trim() || isProcessingVoice}
                      >
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <MessageCircle className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Welcome to VoiceWave Chat</h3>
                <p className="text-muted-foreground mb-4">
                  Select a chat to start your revolutionary messaging experience
                </p>
                <div className="flex gap-2 justify-center">
                  <Button onClick={() => createNewChat('direct')}>
                    <MessageCircle className="h-4 w-4 mr-2" />
                    New Chat
                  </Button>
                  <Button variant="outline" onClick={() => createNewChat('group')}>
                    <Users className="h-4 w-4 mr-2" />
                    Create Group
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Create Chat Modal */}
      <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {createChatType === 'direct' && 'Create New Chat'}
              {createChatType === 'group' && 'Create Group Chat'}
              {createChatType === 'wave' && 'Create Wave Chat'}
              {createChatType === 'space' && 'Create Spatial Audio Space'}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={chatName}
                onChange={(e) => setChatName(e.target.value)}
                placeholder="Enter chat name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                value={chatDescription}
                onChange={(e) => setChatDescription(e.target.value)}
                placeholder="Enter a description"
                rows={3}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="isPrivate"
                checked={isPrivate}
                onCheckedChange={setIsPrivate}
              />
              <Label htmlFor="isPrivate">Private Chat</Label>
            </div>
          </div>

          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setShowCreateModal(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateChat} disabled={!chatName.trim()}>
              Create
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Voice Recorder Modal */}
      <Dialog open={showVoiceRecorder} onOpenChange={setShowVoiceRecorder}>
        <DialogContent className={`${isMobile ? 'max-w-[95vw] max-h-[80vh]' : 'sm:max-w-md'} overflow-y-auto`}>
          <DialogHeader>
            <DialogTitle>🎤 Record Voice Message</DialogTitle>
          </DialogHeader>

          <div className="py-4">
            <div className="p-4 bg-muted rounded-lg text-center">
              <Mic className="h-12 w-12 mx-auto mb-4 text-primary" />
              <p>Voice recording component would go here</p>
              <div className="flex justify-center gap-2 mt-4">
                <Button variant="outline" onClick={() => setShowVoiceRecorder(false)}>
                  Cancel
                </Button>
                <Button>
                  Send
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* AI Assistant Modal */}
      <Dialog open={showAIModal} onOpenChange={setShowAIModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>🧠 AI Assistant</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <div className="p-4 bg-muted rounded-lg text-center">
              <Brain className="h-12 w-12 mx-auto mb-4 text-primary" />
              <p className="mb-4">AI Assistant features coming soon!</p>
              <Button onClick={() => setShowAIModal(false)}>
                Close
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Settings Modal */}
      <Dialog open={showSettingsModal} onOpenChange={setShowSettingsModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>⚙️ Chat Settings</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span>Notifications</span>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <span>Sound Effects</span>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <span>Auto-transcribe Voice</span>
                <Switch defaultChecked />
              </div>
            </div>
            <div className="flex justify-end mt-6">
              <Button onClick={() => setShowSettingsModal(false)}>
                Close
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Tip Modal */}
      <Dialog open={showTipModal} onOpenChange={setShowTipModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>💰 Send Tip</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <div className="p-4 bg-muted rounded-lg text-center">
              <DollarSign className="h-12 w-12 mx-auto mb-4 text-primary" />
              <p className="mb-4">Tipping feature coming soon!</p>
              <Button onClick={() => setShowTipModal(false)}>
                Close
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default VoiceChatHub;
