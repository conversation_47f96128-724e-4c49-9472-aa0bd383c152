-- Fix the unique constraint to remove DEFER<PERSON>BLE so it can be used in ON CONFLICT
ALTER TABLE public.stream_participants 
DROP CONSTRAINT unique_active_participant;

-- Add a non-deferrable unique constraint that can be used in ON CONFLICT
ALTER TABLE public.stream_participants 
ADD CONSTRAINT unique_active_participant 
UNIQUE (stream_id, profile_id);

-- Update the join_live_stream function to automatically join hosts
CREATE OR REPLACE FUNCTION public.join_live_stream(
  p_stream_id UUID,
  p_profile_id UUID,
  p_role TEXT DEFAULT 'listener'
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_host_id UUID;
  v_actual_role TEXT;
BEGIN
  -- Check if stream exists and is live
  SELECT host_profile_id INTO v_host_id
  FROM public.live_streams 
  WHERE id = p_stream_id AND status = 'live';
  
  IF v_host_id IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- If user is the host, make them host regardless of requested role
  IF v_host_id = p_profile_id THEN
    v_actual_role := 'host';
  ELSE
    v_actual_role := p_role;
  END IF;
  
  -- Check if user is already in the stream
  IF EXISTS (
    SELECT 1 FROM public.stream_participants 
    WHERE stream_id = p_stream_id 
    AND profile_id = p_profile_id 
    AND left_at IS NULL
  ) THEN
    RETURN TRUE; -- Already joined
  END IF;
  
  -- Add participant with upsert logic
  INSERT INTO public.stream_participants (
    stream_id, profile_id, role, joined_at
  ) VALUES (
    p_stream_id, p_profile_id, v_actual_role, NOW()
  )
  ON CONFLICT (stream_id, profile_id) 
  DO UPDATE SET 
    left_at = NULL,
    joined_at = NOW(),
    role = v_actual_role;
  
  RETURN TRUE;
END;
$$;