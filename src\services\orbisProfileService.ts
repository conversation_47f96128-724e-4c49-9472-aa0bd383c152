import { UserProfile, UserProfileUpdate } from '@/types/user-profile';
import { toast } from '@/components/ui/sonner';
import { uploadBlobToIPFS } from './nftStorage';
import { connectOrbis, createOrUpdateProfile, getProfile, orbis } from './orbisClient';

// In-memory cache for profiles to avoid unnecessary network requests during a session
const profileCache: Record<string, { profile: UserProfile; timestamp: number }> = {};
const CACHE_EXPIRY = 5 * 60 * 1000; // 5 minutes

/**
 * Ensure the user is connected to Orbis
 * @returns True if connected, false otherwise
 */
export async function ensureConnected(): Promise<boolean> {
  try {
    // Check if already connected
    const { status, did } = await orbis.isConnected();
    
    if (status === 200 && did) {
      console.log('Already connected to Orbis with DID:', did);
      return true;
    }
    
    // Connect if not already connected
    console.log('Not connected to Orbis, connecting...');
    const res = await connectOrbis();
    return !!res;
  } catch (error) {
    console.error('Error ensuring Orbis connection:', error);
    return false;
  }
}

/**
 * Create a new profile in Orbis
 * @param profile The profile to create
 * @returns The DID of the created profile
 */
export async function createProfile(profile: UserProfile): Promise<string> {
  try {
    // Ensure we're connected to Orbis
    const isConnected = await ensureConnected();
    if (!isConnected) {
      throw new Error('Failed to connect to Orbis');
    }

    // Upload profile image to IPFS if it's a blob URL
    if (profile.profileImageUrl && profile.profileImageUrl.startsWith('blob:')) {
      try {
        const response = await fetch(profile.profileImageUrl);
        const blob = await response.blob();
        const fileName = `profile_${profile.address.substring(0, 10)}_${Date.now()}.${blob.type.split('/')[1] || 'jpg'}`;
        const ipfsUri = await uploadBlobToIPFS(blob, fileName);
        profile.profileImageUrl = ipfsUri;
      } catch (error) {
        console.error('Error uploading profile image to IPFS:', error);
      }
    }

    // Upload cover image to IPFS if it's a blob URL
    if (profile.coverImageUrl && profile.coverImageUrl.startsWith('blob:')) {
      try {
        const response = await fetch(profile.coverImageUrl);
        const blob = await response.blob();
        const fileName = `cover_${profile.address.substring(0, 10)}_${Date.now()}.${blob.type.split('/')[1] || 'jpg'}`;
        const ipfsUri = await uploadBlobToIPFS(blob, fileName);
        profile.coverImageUrl = ipfsUri;
      } catch (error) {
        console.error('Error uploading cover image to IPFS:', error);
      }
    }

    // Create the profile in Orbis
    const result = await createOrUpdateProfile(profile);
    
    if (!result) {
      throw new Error('Failed to create profile in Orbis');
    }
    
    // Get the DID from the result
    const did = result.did || (await orbis.isConnected()).did;
    
    if (!did) {
      throw new Error('Failed to get DID from Orbis');
    }
    
    // Add the DID to the profile
    const profileWithDid = {
      ...profile,
      orbisDid: did
    };
    
    // Add to in-memory cache
    profileCache[profile.address.toLowerCase()] = {
      profile: profileWithDid,
      timestamp: Date.now()
    };
    
    console.log('Successfully created profile in Orbis with DID:', did);
    return did;
  } catch (error) {
    console.error('Error creating profile in Orbis:', error);
    toast.error('Failed to create profile. Please try again.');
    throw error;
  }
}

/**
 * Update a profile in Orbis
 * @param did The DID of the profile to update
 * @param profile The current profile
 * @param update The profile updates
 * @returns The DID of the updated profile
 */
export async function updateProfile(
  did: string,
  profile: UserProfile,
  update: UserProfileUpdate
): Promise<string> {
  try {
    // Ensure we're connected to Orbis
    const isConnected = await ensureConnected();
    if (!isConnected) {
      throw new Error('Failed to connect to Orbis');
    }

    // Create updated profile
    const updatedProfile: UserProfile = {
      ...profile,
      ...update,
      socialLinks: {
        ...profile.socialLinks,
        ...update.socialLinks
      },
      stats: {
        ...profile.stats,
        ...update.stats
      },
      // Ensure orbisDid is preserved
      orbisDid: did
    };

    // Upload profile image to IPFS if it's a blob URL
    if (update.profileImageUrl && update.profileImageUrl.startsWith('blob:')) {
      try {
        const response = await fetch(update.profileImageUrl);
        const blob = await response.blob();
        const fileName = `profile_${profile.address.substring(0, 10)}_${Date.now()}.${blob.type.split('/')[1] || 'jpg'}`;
        const ipfsUri = await uploadBlobToIPFS(blob, fileName);
        updatedProfile.profileImageUrl = ipfsUri;
      } catch (error) {
        console.error('Error uploading profile image to IPFS:', error);
      }
    }

    // Upload cover image to IPFS if it's a blob URL
    if (update.coverImageUrl && update.coverImageUrl.startsWith('blob:')) {
      try {
        const response = await fetch(update.coverImageUrl);
        const blob = await response.blob();
        const fileName = `cover_${profile.address.substring(0, 10)}_${Date.now()}.${blob.type.split('/')[1] || 'jpg'}`;
        const ipfsUri = await uploadBlobToIPFS(blob, fileName);
        updatedProfile.coverImageUrl = ipfsUri;
      } catch (error) {
        console.error('Error uploading cover image to IPFS:', error);
      }
    }

    // Update the profile in Orbis
    const result = await createOrUpdateProfile(updatedProfile);
    
    if (!result) {
      throw new Error('Failed to update profile in Orbis');
    }
    
    // Update in-memory cache
    profileCache[profile.address.toLowerCase()] = {
      profile: updatedProfile,
      timestamp: Date.now()
    };
    
    console.log('Successfully updated profile in Orbis');
    return did;
  } catch (error) {
    console.error('Error updating profile in Orbis:', error);
    toast.error('Failed to update profile. Please try again.');
    throw error;
  }
}

/**
 * Get a profile by DID from Orbis
 * @param did The DID of the profile to get
 * @returns The profile or null if not found
 */
export async function getProfileByDid(did: string): Promise<UserProfile | null> {
  try {
    return await getProfile(did);
  } catch (error) {
    console.error('Error getting profile by DID from Orbis:', error);
    return null;
  }
}

/**
 * Get a profile by address from Orbis
 * @param address The wallet address
 * @returns The profile and DID or null if not found
 */
export async function getProfileByAddress(address: string): Promise<{ profile: UserProfile; did: string } | null> {
  try {
    if (!address) {
      throw new Error('Address is required');
    }

    const normalizedAddress = address.toLowerCase();

    // Check in-memory cache first (for performance during a session)
    if (
      profileCache[normalizedAddress] &&
      Date.now() - profileCache[normalizedAddress].timestamp < CACHE_EXPIRY
    ) {
      console.log(`Using in-memory cached profile for ${normalizedAddress}`);
      return {
        profile: profileCache[normalizedAddress].profile,
        did: profileCache[normalizedAddress].profile.orbisDid || ''
      };
    }

    // Get the profile from Orbis
    const profile = await getProfile(normalizedAddress);
    
    if (profile) {
      // Add to in-memory cache
      profileCache[normalizedAddress] = {
        profile,
        timestamp: Date.now()
      };
      
      return {
        profile,
        did: profile.orbisDid || ''
      };
    }

    // If we couldn't find the profile, return null
    console.log(`No profile found for address: ${normalizedAddress}`);
    return null;
  } catch (error) {
    console.error('Error querying profile from Orbis:', error);
    return null;
  }
}
