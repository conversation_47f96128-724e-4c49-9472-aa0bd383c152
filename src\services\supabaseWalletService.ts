import { supabase } from '@/integrations/supabase/client';
import { WalletData, Transaction, Token } from '@/types/wallet';

/**
 * Save wallet data to Supabase
 * @param walletData The wallet data to save
 * @param userId The user ID associated with this wallet
 */
export const saveWalletData = async (
  walletData: WalletData,
  userId: string
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('wallet_data')
      .upsert({
        user_id: userId,
        wallet_data: walletData,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id'
      });

    if (error) {
      console.error('Error saving wallet data to Supabase:', error);
      return false;
    }

    console.log(`Saved wallet data for user ${userId}`);
    return true;
  } catch (error) {
    console.error('Error saving wallet data:', error);
    return false;
  }
};

/**
 * Get wallet data from Supabase
 * @param userId The user ID associated with the wallet
 * @returns The wallet data or null if not found
 */
export const getWalletData = async (userId: string): Promise<WalletData | null> => {
  try {
    const { data, error } = await supabase
      .from('wallet_data')
      .select('wallet_data')
      .eq('user_id', userId)
      .maybeSingle();

    if (error) {
      console.error('Error getting wallet data from Supabase:', error);
      return null;
    }

    if (data) {
      return data.wallet_data as WalletData;
    }

    return null;
  } catch (error) {
    console.error('Error getting wallet data:', error);
    return null;
  }
};

/**
 * Save wallet security settings to Supabase
 * @param userId The user ID associated with the wallet
 * @param isLocked Whether the wallet is locked
 * @param hasBackup Whether the wallet has a backup
 */
export const saveWalletSecurity = async (
  userId: string,
  isLocked: boolean,
  hasBackup: boolean
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('wallet_security')
      .upsert({
        user_id: userId,
        is_locked: isLocked,
        has_backup: hasBackup,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id'
      });

    if (error) {
      console.error('Error saving wallet security to Supabase:', error);
      return false;
    }

    console.log(`Saved wallet security for user ${userId}`);
    return true;
  } catch (error) {
    console.error('Error saving wallet security:', error);
    return false;
  }
};

/**
 * Get wallet security settings from Supabase
 * @param userId The user ID associated with the wallet
 * @returns The wallet security settings or null if not found
 */
export const getWalletSecurity = async (
  userId: string
): Promise<{ isLocked: boolean; hasBackup: boolean } | null> => {
  try {
    const { data, error } = await supabase
      .from('wallet_security')
      .select('is_locked, has_backup')
      .eq('user_id', userId)
      .maybeSingle();

    if (error) {
      console.error('Error getting wallet security from Supabase:', error);
      return null;
    }

    if (data) {
      return {
        isLocked: data.is_locked,
        hasBackup: data.has_backup
      };
    }

    return null;
  } catch (error) {
    console.error('Error getting wallet security:', error);
    return null;
  }
};
