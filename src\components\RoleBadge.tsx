import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Crown, Shield, Users, Eye } from 'lucide-react';

export type Role = 'owner' | 'admin' | 'moderator' | 'member' | 'guest';

interface RoleBadgeProps {
  role: Role;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  variant?: 'default' | 'outline' | 'secondary';
  className?: string;
}

const RoleBadge: React.FC<RoleBadgeProps> = ({
  role,
  size = 'md',
  showIcon = true,
  variant = 'outline',
  className = ''
}) => {
  const roleIcons = {
    owner: Crown,
    admin: Shield,
    moderator: Shield,
    member: Users,
    guest: Eye
  };

  const roleConfig = {
    owner: {
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-500/10',
      borderColor: 'border-yellow-500/20',
    },
    admin: {
      color: 'text-red-500',
      bgColor: 'bg-red-500/10',
      borderColor: 'border-red-500/20',
    },
    moderator: {
      color: 'text-blue-500',
      bgColor: 'bg-blue-500/10',
      borderColor: 'border-blue-500/20',
    },
    member: {
      color: 'text-green-500',
      bgColor: 'bg-green-500/10',
      borderColor: 'border-green-500/20',
    },
    guest: {
      color: 'text-gray-500',
      bgColor: 'bg-gray-500/10',
      borderColor: 'border-gray-500/20',
    }
  };

  const Icon = roleIcons[role];
  const config = roleConfig[role];
  
  const sizeClasses = {
    sm: 'text-xs px-1.5 py-0.5',
    md: 'text-xs px-2 py-1',
    lg: 'text-sm px-3 py-1.5'
  };

  const iconSizes = {
    sm: 10,
    md: 12,
    lg: 14
  };

  const roleLabels = {
    owner: 'Owner',
    admin: 'Admin',
    moderator: 'Moderator',
    member: 'Member',
    guest: 'Guest'
  };

  return (
    <Badge
      variant={variant}
      className={`
        ${sizeClasses[size]}
        ${config.color}
        ${variant === 'default' ? config.bgColor : ''}
        ${variant === 'outline' ? config.borderColor : ''}
        inline-flex items-center gap-1
        ${className}
      `}
    >
      {showIcon && <Icon size={iconSizes[size]} />}
      {roleLabels[role]}
    </Badge>
  );
};

export default RoleBadge;
