-- Advanced Journal System Migration
-- This adds privacy levels, access control, and social features to journals

-- Add new columns to journals table
ALTER TABLE journals ADD COLUMN IF NOT EXISTS privacy_level TEXT DEFAULT 'public' 
  CHECK (privacy_level IN ('my_journal', 'public', 'locked_public', 'private', 'locked_private'));

ALTER TABLE journals ADD COLUMN IF NOT EXISTS summoned_users JSONB DEFAULT '[]'::JSONB;
ALTER TABLE journals ADD COLUMN IF NOT EXISTS tip_to_unlock_amount DECIMAL(10,2);
ALTER TABLE journals ADD COLUMN IF NOT EXISTS tip_to_unlock_currency TEXT DEFAULT 'SOL';
ALTER TABLE journals ADD COLUMN IF NOT EXISTS repost_count INTEGER DEFAULT 0;
ALTER TABLE journals ADD COLUMN IF NOT EXISTS reply_count INTEGER DEFAULT 0;
ALTER TABLE journals ADD COLUMN IF NOT EXISTS reaction_count INTEGER DEFAULT 0;

-- Create journal_access table for tracking who can access private journals
CREATE TABLE IF NOT EXISTS journal_access (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  journal_id UUID NOT NULL REFERENCES journals(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL,
  access_type TEXT NOT NULL CHECK (access_type IN ('summoned', 'tip_unlocked', 'owner')),
  granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  granted_by TEXT, -- User who granted access (for summons)
  tip_amount DECIMAL(10,2), -- Amount paid for tip unlock
  tip_currency TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(journal_id, user_id, access_type)
);

-- Create journal_reposts table
CREATE TABLE IF NOT EXISTS journal_reposts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  journal_id UUID NOT NULL REFERENCES journals(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(journal_id, user_id)
);

-- Create journal_replies table  
CREATE TABLE IF NOT EXISTS journal_replies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  journal_id UUID NOT NULL REFERENCES journals(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL,
  content TEXT,
  audio_url TEXT,
  audio_duration INTEGER,
  is_voice_reply BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create journal_reactions table
CREATE TABLE IF NOT EXISTS journal_reactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  journal_id UUID NOT NULL REFERENCES journals(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL,
  reaction_type TEXT NOT NULL CHECK (reaction_type IN ('like', 'love', 'laugh', 'wow', 'sad', 'angry')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(journal_id, user_id, reaction_type)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_journals_privacy_level ON journals(privacy_level);
CREATE INDEX IF NOT EXISTS idx_journals_summoned_users ON journals USING GIN(summoned_users);
CREATE INDEX IF NOT EXISTS idx_journal_access_journal_id ON journal_access(journal_id);
CREATE INDEX IF NOT EXISTS idx_journal_access_user_id ON journal_access(user_id);
CREATE INDEX IF NOT EXISTS idx_journal_reposts_journal_id ON journal_reposts(journal_id);
CREATE INDEX IF NOT EXISTS idx_journal_reposts_user_id ON journal_reposts(user_id);
CREATE INDEX IF NOT EXISTS idx_journal_replies_journal_id ON journal_replies(journal_id);
CREATE INDEX IF NOT EXISTS idx_journal_reactions_journal_id ON journal_reactions(journal_id);

-- Enable RLS on new tables
ALTER TABLE journal_access ENABLE ROW LEVEL SECURITY;
ALTER TABLE journal_reposts ENABLE ROW LEVEL SECURITY;
ALTER TABLE journal_replies ENABLE ROW LEVEL SECURITY;
ALTER TABLE journal_reactions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for journal_access
CREATE POLICY "Users can view their own journal access" ON journal_access
  FOR SELECT USING (user_id = auth.uid()::text OR user_id = (SELECT wallet_address FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can grant access to their own journals" ON journal_access
  FOR INSERT WITH CHECK (
    EXISTS (SELECT 1 FROM journals WHERE id = journal_id AND profile_id = auth.uid()::text)
    OR EXISTS (SELECT 1 FROM journals WHERE id = journal_id AND profile_id = (SELECT wallet_address FROM profiles WHERE id = auth.uid()))
  );

-- RLS Policies for journal_reposts
CREATE POLICY "Users can view all reposts" ON journal_reposts FOR SELECT USING (true);
CREATE POLICY "Users can create reposts" ON journal_reposts FOR INSERT WITH CHECK (
  user_id = auth.uid()::text OR user_id = (SELECT wallet_address FROM profiles WHERE id = auth.uid())
);
CREATE POLICY "Users can delete their own reposts" ON journal_reposts FOR DELETE USING (
  user_id = auth.uid()::text OR user_id = (SELECT wallet_address FROM profiles WHERE id = auth.uid())
);

-- RLS Policies for journal_replies
CREATE POLICY "Users can view all replies" ON journal_replies FOR SELECT USING (true);
CREATE POLICY "Users can create replies" ON journal_replies FOR INSERT WITH CHECK (
  user_id = auth.uid()::text OR user_id = (SELECT wallet_address FROM profiles WHERE id = auth.uid())
);
CREATE POLICY "Users can update their own replies" ON journal_replies FOR UPDATE USING (
  user_id = auth.uid()::text OR user_id = (SELECT wallet_address FROM profiles WHERE id = auth.uid())
);
CREATE POLICY "Users can delete their own replies" ON journal_replies FOR DELETE USING (
  user_id = auth.uid()::text OR user_id = (SELECT wallet_address FROM profiles WHERE id = auth.uid())
);

-- RLS Policies for journal_reactions
CREATE POLICY "Users can view all reactions" ON journal_reactions FOR SELECT USING (true);
CREATE POLICY "Users can create reactions" ON journal_reactions FOR INSERT WITH CHECK (
  user_id = auth.uid()::text OR user_id = (SELECT wallet_address FROM profiles WHERE id = auth.uid())
);
CREATE POLICY "Users can update their own reactions" ON journal_reactions FOR UPDATE USING (
  user_id = auth.uid()::text OR user_id = (SELECT wallet_address FROM profiles WHERE id = auth.uid())
);
CREATE POLICY "Users can delete their own reactions" ON journal_reactions FOR DELETE USING (
  user_id = auth.uid()::text OR user_id = (SELECT wallet_address FROM profiles WHERE id = auth.uid())
);

-- Function to check if user can access a journal
CREATE OR REPLACE FUNCTION can_user_access_journal(
  p_journal_id UUID,
  p_user_id TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  journal_record RECORD;
  has_access BOOLEAN := false;
BEGIN
  -- Get journal details
  SELECT * INTO journal_record FROM journals WHERE id = p_journal_id;
  
  IF NOT FOUND THEN
    RETURN false;
  END IF;
  
  -- Owner can always access their own journals
  IF journal_record.profile_id = p_user_id OR 
     LOWER(journal_record.profile_id) = LOWER(p_user_id) THEN
    RETURN true;
  END IF;
  
  -- Check based on privacy level
  CASE journal_record.privacy_level
    WHEN 'my_journal' THEN
      -- Only owner can access
      RETURN false;
      
    WHEN 'public' THEN
      -- Everyone can access if not locked
      RETURN NOT COALESCE(journal_record.is_locked, false);
      
    WHEN 'locked_public' THEN
      -- Everyone can see it exists, but need to check unlock conditions
      IF NOT COALESCE(journal_record.is_locked, false) THEN
        RETURN true;
      END IF;
      
      -- Check if unlock date has passed
      IF journal_record.scheduled_for IS NOT NULL AND 
         journal_record.scheduled_for <= NOW() THEN
        RETURN true;
      END IF;
      
      RETURN false;
      
    WHEN 'private' THEN
      -- Check if user is summoned
      RETURN journal_record.summoned_users ? p_user_id;
      
    WHEN 'locked_private' THEN
      -- Check if user has paid to unlock or is summoned
      SELECT EXISTS(
        SELECT 1 FROM journal_access 
        WHERE journal_id = p_journal_id 
        AND user_id = p_user_id 
        AND access_type IN ('tip_unlocked', 'summoned')
      ) INTO has_access;
      
      RETURN has_access OR (journal_record.summoned_users ? p_user_id);
      
    ELSE
      RETURN false;
  END CASE;
END;
$$;

-- Function to get accessible journals for a user
CREATE OR REPLACE FUNCTION get_accessible_journals(
  p_user_id TEXT,
  p_privacy_filter TEXT DEFAULT NULL
)
RETURNS TABLE(
  id UUID,
  title TEXT,
  transcript TEXT,
  audio_url TEXT,
  audio_duration INTEGER,
  profile_id TEXT,
  privacy_level TEXT,
  is_locked BOOLEAN,
  scheduled_for TIMESTAMP WITH TIME ZONE,
  summoned_users JSONB,
  tip_to_unlock_amount DECIMAL,
  tip_to_unlock_currency TEXT,
  repost_count INTEGER,
  reply_count INTEGER,
  reaction_count INTEGER,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  can_access BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    j.id,
    j.title,
    j.transcript,
    j.audio_url,
    j.audio_duration,
    j.profile_id,
    j.privacy_level,
    j.is_locked,
    j.scheduled_for,
    j.summoned_users,
    j.tip_to_unlock_amount,
    j.tip_to_unlock_currency,
    j.repost_count,
    j.reply_count,
    j.reaction_count,
    j.created_at,
    j.updated_at,
    can_user_access_journal(j.id, p_user_id) as can_access
  FROM journals j
  WHERE 
    (p_privacy_filter IS NULL OR j.privacy_level = p_privacy_filter)
    AND (
      -- User's own journals
      j.profile_id = p_user_id OR 
      LOWER(j.profile_id) = LOWER(p_user_id) OR
      
      -- Public journals
      j.privacy_level IN ('public', 'locked_public') OR
      
      -- Private journals user has access to
      (j.privacy_level = 'private' AND j.summoned_users ? p_user_id) OR
      
      -- Locked private journals (visible but may need unlock)
      j.privacy_level = 'locked_private'
    )
  ORDER BY j.created_at DESC;
END;
$$;

-- Function to increment journal counts
CREATE OR REPLACE FUNCTION increment_journal_count(
  journal_id UUID,
  count_type TEXT
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  CASE count_type
    WHEN 'repost' THEN
      UPDATE journals SET repost_count = COALESCE(repost_count, 0) + 1 WHERE id = journal_id;
    WHEN 'reply' THEN
      UPDATE journals SET reply_count = COALESCE(reply_count, 0) + 1 WHERE id = journal_id;
    WHEN 'reaction' THEN
      UPDATE journals SET reaction_count = COALESCE(reaction_count, 0) + 1 WHERE id = journal_id;
  END CASE;
END;
$$;

-- Function to append to array (for summoned users)
CREATE OR REPLACE FUNCTION array_append_unique(
  existing_array JSONB,
  new_elements TEXT[]
)
RETURNS JSONB
LANGUAGE plpgsql
IMMUTABLE
AS $$
DECLARE
  result JSONB;
  element TEXT;
BEGIN
  result := COALESCE(existing_array, '[]'::JSONB);

  FOREACH element IN ARRAY new_elements
  LOOP
    IF NOT (result ? element) THEN
      result := result || jsonb_build_array(element);
    END IF;
  END LOOP;

  RETURN result;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION can_user_access_journal TO authenticated;
GRANT EXECUTE ON FUNCTION can_user_access_journal TO anon;
GRANT EXECUTE ON FUNCTION get_accessible_journals TO authenticated;
GRANT EXECUTE ON FUNCTION get_accessible_journals TO anon;
GRANT EXECUTE ON FUNCTION increment_journal_count TO authenticated;
GRANT EXECUTE ON FUNCTION increment_journal_count TO anon;
GRANT EXECUTE ON FUNCTION array_append_unique TO authenticated;
GRANT EXECUTE ON FUNCTION array_append_unique TO anon;

-- Update existing journals to have privacy_level
UPDATE journals SET privacy_level = 'public' WHERE privacy_level IS NULL;

-- Comments for documentation
COMMENT ON TABLE journal_access IS 'Tracks who can access private journals and how they gained access';
COMMENT ON TABLE journal_reposts IS 'Journal reposts for social sharing';
COMMENT ON TABLE journal_replies IS 'Replies to journals (text or voice)';
COMMENT ON TABLE journal_reactions IS 'Emoji reactions to journals';
COMMENT ON FUNCTION can_user_access_journal IS 'Checks if a user can access a specific journal based on privacy rules';
COMMENT ON FUNCTION get_accessible_journals IS 'Returns all journals a user can see with access information';
