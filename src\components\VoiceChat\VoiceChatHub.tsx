import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import {
  MessageCircle,
  Users,
  Plus,
  Search,
  Mic,
  Volume2,
  Heart,
  Zap,
  Sparkles,
  Waves,
  Brain,
  Palette,
  Globe,
  Lock,
  Crown,
  Star,
  Headphones,
  Send,
  Paperclip,
  DollarSign,
  Settings,
  Play,
  Pause,
  MicOff,
  X,
  MoreHorizontal,
  Trash2,
  ArrowLeft,
  Info
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/components/ui/sonner';
import { voiceChatService, type VoiceChat, type VoiceChatMessage, type EmotionalWave } from '@/services/voiceChatService';
import { voiceRecordingService } from '@/services/voiceRecordingService';
import { supabase } from '@/integrations/supabase/client';
import MessageReactions from '@/components/MessageReactions';
import TipModalWithoutWallet from '@/components/TipModalWithoutWallet';
import AudioRecorderWithFilters from '@/components/AudioRecorderWithFilters';
import AudioPlayer from '@/components/AudioPlayer';
import { useWhisper } from '@/hooks/use-whisper-improved';
import audioStorageService from '@/services/audioStorageService';
import SpatialChatView from '@/components/VoiceChat/SpatialChatViewNew';
import GroupManagementModal from '@/components/VoiceChat/GroupManagementModal';
import { useIsMobile } from '@/hooks/use-mobile';

// Interfaces are now imported from the service

interface VoiceChatHubProps {
  onChatStateChange?: (isInChat: boolean) => void;
}

const VoiceChatHub: React.FC<VoiceChatHubProps> = ({ onChatStateChange }) => {
  const { user } = useAuth();
  const isMobile = useIsMobile();
  const [activeTab, setActiveTab] = useState('direct');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedChat, setSelectedChat] = useState<string | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [emotionalWaves, setEmotionalWaves] = useState<EmotionalWave[]>([]);

  // Real data states
  const [chats, setChats] = useState<VoiceChat[]>([]);
  const [messages, setMessages] = useState<VoiceChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  // Voice recording states
  const [showVoiceRecorder, setShowVoiceRecorder] = useState(false);
  const [isProcessingVoice, setIsProcessingVoice] = useState(false);

  // Tip modal states
  const [showTipModal, setShowTipModal] = useState(false);
  const [tipRecipientId, setTipRecipientId] = useState<string>('');

  // Group management modal states
  const [showGroupManagement, setShowGroupManagement] = useState(false);

  // Clear all chats function
  const clearAllChats = async () => {
    if (!user?.id) return;

    const confirmed = window.confirm('Are you sure you want to delete ALL chats? This cannot be undone.');
    if (!confirmed) return;

    try {
      const success = await voiceChatService.clearAllUserChats(user.id);
      if (success) {
        setChats([]);
        setSelectedChat(null);
        toast.success('All chats cleared successfully');
      } else {
        toast.error('Failed to clear chats');
      }
    } catch (error) {
      toast.error('Failed to clear chats');
    }
  };

  // Delete individual chat
  const deleteChat = async (chatId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent chat selection
    if (!user?.id) return;

    const chat = chats.find(c => c.id === chatId);
    const confirmed = window.confirm(`Are you sure you want to delete "${chat?.name}"? This cannot be undone.`);
    if (!confirmed) return;

    try {
      const success = await voiceChatService.deleteChat(chatId, user.id);
      if (success) {
        setChats(chats.filter(c => c.id !== chatId));
        if (selectedChat === chatId) {
          setSelectedChat(null);
        }
        toast.success('Chat deleted successfully');
      } else {
        toast.error('Failed to delete chat');
      }
    } catch (error) {
      toast.error('Failed to delete chat');
    }
  };

  // Image upload states
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [showAIModal, setShowAIModal] = useState(false);
  const [showSpaceModal, setShowSpaceModal] = useState(false);
  const [createChatType, setCreateChatType] = useState<'direct' | 'group' | 'wave'>('direct');

  // Theme state
  const [currentTheme, setCurrentTheme] = useState<string>('calm');

  // Create chat form states
  const [chatName, setChatName] = useState('');
  const [chatDescription, setChatDescription] = useState('');
  const [isPrivate, setIsPrivate] = useState(false);
  const [participantEmails, setParticipantEmails] = useState('');

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Load user's chats on component mount
  useEffect(() => {
    if (user?.id) {
      loadUserChats();
    }
  }, [user?.id]);

  // Notify parent about chat state changes
  useEffect(() => {
    if (onChatStateChange && isMobile) {
      onChatStateChange(!!selectedChat);
    }
  }, [selectedChat, onChatStateChange, isMobile]);

  // Load messages when chat is selected
  useEffect(() => {
    if (selectedChat) {
      loadChatMessages(selectedChat);
      loadEmotionalWaves(selectedChat);
    }
  }, [selectedChat]);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadUserChats = async () => {
    if (!user?.id) return;

    setIsLoading(true);
    try {
      const userChats = await voiceChatService.getUserChats(user.id);
      setChats(userChats);
    } catch (error) {
      console.error('Error loading chats:', error);
      toast.error('Failed to load chats');
    } finally {
      setIsLoading(false);
    }
  };

  const loadChatMessages = async (chatId: string) => {
    if (!user?.id) return;

    try {
      const chatMessages = await voiceChatService.getChatMessagesFiltered(chatId, user.id);
      setMessages(chatMessages);
    } catch (error) {
      console.error('Error loading messages:', error);
      toast.error('Failed to load messages');
    }
  };

  const deleteMessage = async (messageId: string, deleteType: 'for-me' | 'for-everyone') => {
    if (!user?.id) return;

    try {
      let success = false;

      if (deleteType === 'for-everyone') {
        success = await voiceChatService.deleteMessageForEveryone(messageId, user.id);
        if (success) {
          toast.success('Message deleted for everyone');
        } else {
          toast.error('Failed to delete message for everyone');
        }
      } else {
        success = await voiceChatService.deleteMessageForMe(messageId, user.id);
        if (success) {
          toast.success('Message deleted for you');
        } else {
          toast.error('Failed to delete message');
        }
      }

      if (success && selectedChat) {
        await loadChatMessages(selectedChat);
      }
    } catch (error) {
      console.error('Error deleting message:', error);
      toast.error('Failed to delete message');
    }
  };

  const loadEmotionalWaves = async (chatId: string) => {
    try {
      const waves = await voiceChatService.getEmotionalWaves(chatId);
      setEmotionalWaves(waves);
    } catch (error) {
      console.error('Error loading emotional waves:', error);
    }
  };

  const getEmotionalColor = (tone: string) => {
    const colors = {
      calm: 'bg-blue-500/20 border-blue-500/50',
      excited: 'bg-orange-500/20 border-orange-500/50',
      focused: 'bg-green-500/20 border-green-500/50',
      creative: 'bg-purple-500/20 border-purple-500/50',
      mysterious: 'bg-indigo-500/20 border-indigo-500/50'
    };
    return colors[tone as keyof typeof colors] || colors.calm;
  };

  const getThemeStyles = (theme: string) => {
    switch (theme) {
      case 'calm':
        return {
          headerBg: 'bg-gradient-to-r from-blue-500/10 to-cyan-500/10',
          accent: 'text-blue-500',
          glow: 'shadow-lg shadow-blue-500/20'
        };
      case 'excited':
        return {
          headerBg: 'bg-gradient-to-r from-red-500/10 to-orange-500/10',
          accent: 'text-red-500',
          glow: 'shadow-lg shadow-red-500/20'
        };
      case 'focused':
        return {
          headerBg: 'bg-gradient-to-r from-yellow-500/10 to-amber-500/10',
          accent: 'text-yellow-500',
          glow: 'shadow-lg shadow-yellow-500/20'
        };
      case 'creative':
        return {
          headerBg: 'bg-gradient-to-r from-purple-500/10 to-pink-500/10',
          accent: 'text-purple-500',
          glow: 'shadow-lg shadow-purple-500/20'
        };
      case 'mysterious':
        return {
          headerBg: 'bg-gradient-to-r from-indigo-500/10 to-purple-500/10',
          accent: 'text-indigo-500',
          glow: 'shadow-lg shadow-indigo-500/20'
        };
      default:
        return {
          headerBg: 'bg-card',
          accent: 'text-primary',
          glow: 'shadow-lg shadow-primary/20'
        };
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'direct': return <MessageCircle className="h-4 w-4" />;
      case 'group': return <Users className="h-4 w-4" />;
      case 'space': return <Globe className="h-4 w-4" />;
      case 'wave': return <Waves className="h-4 w-4" />;
      default: return <MessageCircle className="h-4 w-4" />;
    }
  };

  const createNewChat = (type: 'direct' | 'group' | 'wave') => {
    setCreateChatType(type);
    setShowCreateModal(true);
    setChatName('');
    setChatDescription('');
    setIsPrivate(false);
    setParticipantEmails('');
  };

  const handleCreateChat = async () => {
    if (!user?.id || !chatName.trim()) {
      toast.error('Please enter a chat name');
      return;
    }

    try {
      // Process participants - convert emails to user IDs if needed
      const participantIds = participantEmails
        .split('\n')
        .map(email => email.trim())
        .filter(email => email && email !== user.email);

      console.log('Creating chat:', {
        name: chatName.trim(),
        type: createChatType,
        creator_id: user.id,
        participants: participantIds
      });

      const chatData = await voiceChatService.createChat({
        name: chatName.trim(),
        description: chatDescription.trim() || undefined,
        type: createChatType,
        creator_id: user.id,
        is_private: isPrivate,
        participants: participantIds,
      });

      if (chatData) {
        toast.success(`${createChatType.charAt(0).toUpperCase() + createChatType.slice(1)} chat "${chatName}" created successfully!`);
        setShowCreateModal(false);
        // Reset form
        setChatName('');
        setChatDescription('');
        setIsPrivate(false);
        setParticipantEmails('');
        // Reload chats and select the new one
        await loadUserChats();
        setSelectedChat(chatData.id);
      } else {
        toast.error('Failed to create chat');
      }
    } catch (error) {
      console.error('Error creating chat:', error);
      toast.error('Failed to create chat');
    }
  };

  const startVoiceMessage = () => {
    if (!selectedChat || !user?.id) return;
    setShowVoiceRecorder(true);
  };

  const handleVoiceRecordingComplete = async (audioBlob: Blob, transcript: string, duration: number) => {
    if (!selectedChat || !user?.id) return;

    console.log('Voice recording completed:', {
      blobSize: audioBlob.size,
      transcript,
      duration
    });

    setIsProcessingVoice(true);
    setShowVoiceRecorder(false);

    try {
      // Upload audio using existing audio storage service
      const audioUrl = await audioStorageService.uploadAudio(audioBlob, user.id);

      console.log('Audio uploaded to:', audioUrl);

      if (audioUrl) {
        const message = await voiceChatService.sendVoiceMessage(
          selectedChat,
          user.id,
          audioUrl,
          duration,
          transcript || 'Voice message'
        );

        console.log('Voice message sent:', message);

        if (message) {
          setMessages(prev => [...prev, message]);
          toast.success('🎵 Voice message sent!');
          // Reload messages to ensure we get the latest
          await loadChatMessages(selectedChat);
        } else {
          toast.error('Failed to send voice message');
        }
      } else {
        toast.error('Failed to upload voice recording');
      }
    } catch (error) {
      console.error('Error processing voice message:', error);
      toast.error('Failed to send voice message');
    } finally {
      setIsProcessingVoice(false);
    }
  };

  const sendTextMessage = async () => {
    if (!selectedChat || !user?.id || !newMessage.trim()) return;

    try {
      const message = await voiceChatService.sendTextMessage(
        selectedChat,
        user.id,
        newMessage.trim()
      );

      if (message) {
        setMessages(prev => [...prev, message]);
        setNewMessage('');
      } else {
        toast.error('Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendTextMessage();
    }
  };

  const sendEmotionalWave = async (emotion: string) => {
    if (!selectedChat || !user?.id) return;

    try {
      const wave = await voiceChatService.sendEmotionalWave(
        selectedChat,
        user.id,
        emotion,
        Math.random() * 100
      );

      if (wave) {
        setEmotionalWaves(prev => [...prev, wave]);
        toast.success(`${emotion} wave sent! 🌊`);

        // Update chat emotional tone based on wave
        await updateChatEmotionalTone(selectedChat, emotion);

        // Reload messages to show the wave message
        await loadChatMessages(selectedChat);
        await loadUserChats(); // Reload to get updated emotional tone
      } else {
        toast.error('Failed to send emotional wave');
      }
    } catch (error) {
      console.error('Error sending emotional wave:', error);
      toast.error('Failed to send emotional wave');
    }
  };

  const updateChatEmotionalTone = async (chatId: string, emotion: string) => {
    // Map emotions to tones
    const emotionToTone: { [key: string]: string } = {
      '💙': 'calm',
      '🔥': 'excited',
      '⚡': 'focused',
      '🌟': 'creative',
      '🌊': 'mysterious',
    };

    const newTone = emotionToTone[emotion] || 'calm';

    try {
      await supabase
        .from('voice_chats')
        .update({
          emotional_tone: newTone,
          updated_at: new Date().toISOString()
        })
        .eq('id', chatId);
    } catch (error) {
      console.error('Error updating emotional tone:', error);
    }
  };

  const changeTheme = () => {
    if (!selectedChat) return;

    const themes = [
      { name: 'calm', emoji: '💙', color: 'bg-blue-500/20', description: 'Peaceful & Serene' },
      { name: 'excited', emoji: '🔥', color: 'bg-red-500/20', description: 'Energetic & Dynamic' },
      { name: 'focused', emoji: '⚡', color: 'bg-yellow-500/20', description: 'Sharp & Concentrated' },
      { name: 'creative', emoji: '🌟', color: 'bg-purple-500/20', description: 'Imaginative & Inspiring' },
      { name: 'mysterious', emoji: '🌊', color: 'bg-indigo-500/20', description: 'Deep & Enigmatic' }
    ];

    const currentChat = chats.find(c => c.id === selectedChat);
    const currentIndex = themes.findIndex(t => t.name === (currentChat?.emotional_tone || 'calm'));
    const nextIndex = (currentIndex + 1) % themes.length;
    const newTheme = themes[nextIndex];

    // Update local state immediately for visual feedback
    setCurrentTheme(newTheme.name);

    // Update the chat's emotional tone
    updateChatEmotionalTone(selectedChat, newTheme.emoji);

    // Show enhanced toast with theme info
    toast.success(
      <div className="flex items-center gap-2">
        <span className="text-lg">{newTheme.emoji}</span>
        <div>
          <div className="font-medium">Theme: {newTheme.name}</div>
          <div className="text-sm opacity-75">{newTheme.description}</div>
        </div>
      </div>,
      { duration: 3000 }
    );
  };

  const openTipModal = () => {
    if (!selectedChat) return;

    const currentChat = chats.find(c => c.id === selectedChat);
    if (currentChat) {
      setTipRecipientId(currentChat.creator_id);
      setShowTipModal(true);
    }
  };

  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const sendImageMessage = async () => {
    if (!selectedImage || !selectedChat || !user?.id) return;

    try {
      // Upload image to Supabase Storage
      const fileName = `chat_image_${Date.now()}_${selectedImage.name}`;
      const { data, error } = await supabase.storage
        .from('voice-recordings')
        .upload(`chat_images/${fileName}`, selectedImage);

      if (error) {
        toast.error('Failed to upload image');
        return;
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('voice-recordings')
        .getPublicUrl(data.path);

      // Send as text message with image URL
      const message = await voiceChatService.sendTextMessage(
        selectedChat,
        user.id,
        `📷 Image: ${urlData.publicUrl}`
      );

      if (message) {
        setMessages(prev => [...prev, message]);
        setSelectedImage(null);
        setImagePreview(null);
        toast.success('Image sent! 📷');
      }
    } catch (error) {
      console.error('Error sending image:', error);
      toast.error('Failed to send image');
    }
  };

  return (
    <div className={`${isMobile ? 'h-screen mobile-container' : 'h-screen'} flex flex-col bg-background overflow-hidden`} style={isMobile ? { height: '100vh', height: '100dvh' } : {}}>
      {/* Main Header - Only show on desktop or when no chat selected on mobile */}
      {(!isMobile || !selectedChat) && (
        <div className="border-b bg-card p-3 flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="relative">
                <MessageCircle className={`${isMobile ? 'h-5 w-5' : 'h-8 w-8'} text-primary`} />
                {!isMobile && <Sparkles className="h-4 w-4 text-yellow-500 absolute -top-1 -right-1" />}
              </div>
              {!isMobile && (
                <div>
                  <h1 className="text-xl font-bold">VoiceWave Chat</h1>
                  <p className="text-sm text-muted-foreground">Revolutionary messaging</p>
                </div>
              )}
            </div>

            {/* Show Settings and AI Assistant buttons only when no chat is selected */}
            {!selectedChat && (
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={clearAllChats} className="text-red-600 hover:text-red-700">
                  <Trash2 className="h-4 w-4" />
                  {!isMobile && <span className="ml-2">Clear All</span>}
                </Button>
                <Button variant="outline" size="sm" onClick={() => setShowSettingsModal(true)}>
                  <Settings className="h-4 w-4" />
                  {!isMobile && <span className="ml-2">Settings</span>}
                </Button>
                <Button variant="outline" size="sm" onClick={() => setShowAIModal(true)}>
                  <Brain className="h-4 w-4" />
                  {!isMobile && <span className="ml-2">AI Assistant</span>}
                </Button>
              </div>
            )}
          </div>
        </div>
      )}

      <div className={`flex-1 flex ${isMobile ? 'flex-col' : 'flex-row'} overflow-hidden`}>
        {/* Sidebar - Desktop: Fixed width, Mobile: Responsive */}
        <div className={`${isMobile ? (selectedChat ? 'hidden' : 'flex-1') : 'w-96'} ${!isMobile || !selectedChat ? 'border-r' : ''} bg-card flex flex-col overflow-hidden`}>
          {/* Search */}
          <div className="p-4 border-b">
            <div className="relative">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search chats or summon users..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>
          </div>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className={`flex-1 flex flex-col ${isMobile ? 'h-full min-h-0' : ''}`}>
            <TabsList className="grid w-full grid-cols-4 m-4 mb-2">
              <TabsTrigger value="direct" className="text-xs">
                <MessageCircle className="h-3 w-3 mr-1" />
                Direct
              </TabsTrigger>
              <TabsTrigger value="groups" className="text-xs">
                <Users className="h-3 w-3 mr-1" />
                Groups
              </TabsTrigger>
              <TabsTrigger value="waves" className="text-xs">
                <Waves className="h-3 w-3 mr-1" />
                Waves
              </TabsTrigger>
              <TabsTrigger value="spaces" className="text-xs">
                <Globe className="h-3 w-3 mr-1" />
                Spaces
              </TabsTrigger>
            </TabsList>

            {/* Direct Messages Tab */}
            <TabsContent value="direct" className={`flex-1 m-0 ${isMobile ? 'h-full overflow-hidden' : ''}`}>
              <ScrollArea className={`flex-1 ${isMobile ? 'h-full' : 'h-[calc(100vh-200px)]'}`}>
                <div className={`${isMobile ? 'p-3 pr-8 mobile-chat-content' : 'p-2'} space-y-1`}>
                  {/* Create New Direct Chat Button */}
                  <div className={`mb-4 ${isMobile ? 'pr-2' : ''}`}>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => createNewChat('direct')}
                      className={`w-full h-8 ${isMobile ? 'mr-2' : ''}`}
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      New Direct Message
                    </Button>
                  </div>

                  {/* Direct Chats List */}
                  {chats.filter(chat => chat.type === 'direct').length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <MessageCircle className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No direct messages yet</p>
                    </div>
                  ) : (
                    chats.filter(chat => chat.type === 'direct').map((chat) => (
                      <div
                        key={chat.id}
                        onClick={() => setSelectedChat(chat.id)}
                        className={`p-3 rounded-lg cursor-pointer transition-all duration-200 ${
                          selectedChat === chat.id
                            ? 'bg-primary/10 border-primary/20'
                            : 'hover:bg-muted/50 border-transparent'
                        } border group`}
                      >
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback>{chat.name[0]?.toUpperCase()}</AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-sm truncate">{chat.name}</div>
                            <div className="text-xs text-muted-foreground truncate">
                              {chat.last_message?.content || 'No messages yet'}
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => deleteChat(chat.id, e)}
                            className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0 text-red-500 hover:text-red-700"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Groups Tab */}
            <TabsContent value="groups" className={`flex-1 m-0 ${isMobile ? 'h-full overflow-hidden' : ''}`}>
              <ScrollArea className={`flex-1 ${isMobile ? 'h-full' : 'h-[calc(100vh-200px)]'}`}>
                <div className={`${isMobile ? 'p-3 pr-8 mobile-chat-content' : 'p-2'} space-y-1`}>
                  {/* Create New Group Button */}
                  <div className={`mb-4 ${isMobile ? 'pr-2' : ''}`}>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => createNewChat('group')}
                      className={`w-full h-8 ${isMobile ? 'mr-2' : ''}`}
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Create New Group
                    </Button>
                  </div>

                  {/* Groups List */}
                  {chats.filter(chat => chat.type === 'group').length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <Users className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No groups yet</p>
                      <p className="text-xs mt-1">Create your first group above</p>
                    </div>
                  ) : (
                    chats.filter(chat => chat.type === 'group').map((chat) => (
                      <div
                        key={chat.id}
                        onClick={() => setSelectedChat(chat.id)}
                        className={`p-3 rounded-lg cursor-pointer transition-all duration-200 ${
                          selectedChat === chat.id
                            ? 'bg-primary/10 border-primary/20'
                            : 'hover:bg-muted/50 border-transparent'
                        } border group`}
                      >
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback>
                              <Users className="h-4 w-4" />
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <span className="font-medium text-sm truncate">{chat.name}</span>
                              <Crown className="h-3 w-3 text-yellow-500" />
                            </div>
                            <div className="text-xs text-muted-foreground truncate">
                              {chat.participant_count || 0} members • {chat.last_message?.content || 'No messages yet'}
                            </div>
                          </div>
                          <div className="flex items-center gap-1">
                            {chat.unread_count && chat.unread_count > 0 && (
                              <Badge variant="destructive" className="text-xs">
                                {chat.unread_count}
                              </Badge>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => deleteChat(chat.id, e)}
                              className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0 text-red-500 hover:text-red-700"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="waves" className={`flex-1 m-0 ${isMobile ? 'h-full overflow-hidden' : ''}`}>
              <ScrollArea className={`flex-1 ${isMobile ? 'h-full' : 'h-[calc(100vh-200px)]'}`}>
                <div className={`${isMobile ? 'p-3 pr-8 mobile-chat-content' : 'p-2'} space-y-1`}>
                  {/* Create Wave Button */}
                  <div className="p-4 text-center border-b">
                    <Waves className="h-8 w-8 mx-auto text-primary mb-2" />
                    <h3 className="font-medium mb-2">Emotional Waves</h3>
                    <p className="text-xs text-muted-foreground mb-3">
                      Send and receive emotional energy across the network
                    </p>
                    <Button onClick={() => createNewChat('wave')} size="sm" className="w-full">
                      <Zap className="h-4 w-4 mr-2" />
                      Create Wave Chat
                    </Button>
                  </div>

                  {/* Wave Chats */}
                  {chats.filter(chat => chat.type === 'wave').map((chat) => (
                    <Card
                      key={chat.id}
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        selectedChat === chat.id ? 'ring-2 ring-primary' : ''
                      } ${getEmotionalColor(chat.emotional_tone)} border-l-4 border-l-purple-500`}
                      onClick={() => setSelectedChat(chat.id)}
                    >
                      <CardContent className={`${isMobile ? 'p-1.5' : 'p-3'}`}>
                        <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>
                          <div className="relative">
                            <div className={`${isMobile ? 'w-6 h-6' : 'w-10 h-10'} rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center`}>
                              <Waves className={`${isMobile ? 'h-3 w-3' : 'h-5 w-5'} text-white`} />
                            </div>
                            <div className={`absolute -bottom-0.5 -right-0.5 ${isMobile ? 'w-1.5 h-1.5' : 'w-3 h-3'} bg-purple-500 rounded-full border border-background animate-pulse`} />
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-1">
                                <Waves className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-purple-500`} />
                                <span className={`font-medium ${isMobile ? 'text-xs' : 'text-sm'} truncate`}>
                                  {chat.name}
                                </span>
                              </div>
                              {chat.unread_count && chat.unread_count > 0 && (
                                <Badge variant="destructive" className={`${isMobile ? 'h-3 text-xs px-1' : 'h-5 text-xs'}`}>
                                  {chat.unread_count}
                                </Badge>
                              )}
                            </div>

                            {chat.last_message && (
                              <div className="mt-1">
                                <p className="text-xs text-muted-foreground truncate">
                                  {chat.last_message.content}
                                </p>
                                <div className="flex items-center justify-between mt-1">
                                  <span className="text-xs text-muted-foreground">
                                    {new Date(chat.last_message.created_at).toLocaleTimeString()}
                                  </span>
                                  <div className="flex items-center gap-1">
                                    <Sparkles className="h-3 w-3 text-purple-500" />
                                    <span className="text-xs text-purple-500">Wave Energy</span>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}

                  {chats.filter(chat => chat.type === 'wave').length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      <Waves className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No wave chats yet</p>
                      <p className="text-xs">Create one to start sending emotional energy!</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="spaces" className={`flex-1 m-0 ${isMobile ? 'h-full overflow-hidden' : ''}`}>
              <ScrollArea className={`flex-1 ${isMobile ? 'h-full' : 'h-[calc(100vh-200px)]'}`}>
                <div className={`${isMobile ? 'p-3 pr-8 mobile-chat-content' : 'p-2'} space-y-1`}>
                  {/* Create/Join Space Buttons */}
                  <div className="p-4 text-center border-b">
                    <Globe className="h-8 w-8 mx-auto text-blue-500 mb-2" />
                    <h3 className="font-medium mb-2">Voice Spaces</h3>
                    <p className="text-xs text-muted-foreground mb-3">
                      Join spatial audio conversations with 3D positioning
                    </p>
                    <div className="flex gap-2">
                      <Button onClick={() => setShowSpaceModal(true)} size="sm" className="flex-1">
                        <Headphones className="h-4 w-4 mr-2" />
                        Join Space
                      </Button>
                      <Button onClick={() => createNewChat('space')} variant="outline" size="sm" className="flex-1">
                        <Plus className="h-4 w-4 mr-2" />
                        Create Space
                      </Button>
                    </div>
                  </div>

                  {/* Space Chats */}
                  {chats.filter(chat => chat.type === 'space').map((chat) => (
                    <Card
                      key={chat.id}
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        selectedChat === chat.id ? 'ring-2 ring-primary' : ''
                      } ${getEmotionalColor(chat.emotional_tone)} border-l-4 border-l-blue-500`}
                      onClick={() => setSelectedChat(chat.id)}
                    >
                      <CardContent className={`${isMobile ? 'p-1.5' : 'p-3'}`}>
                        <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>
                          <div className="relative">
                            <div className={`${isMobile ? 'w-6 h-6' : 'w-10 h-10'} rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center`}>
                              <Globe className={`${isMobile ? 'h-3 w-3' : 'h-5 w-5'} text-white`} />
                            </div>
                            <div className={`absolute -bottom-0.5 -right-0.5 ${isMobile ? 'w-1.5 h-1.5' : 'w-3 h-3'} bg-green-500 rounded-full border border-background`} />
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-1">
                                <Globe className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-blue-500`} />
                                <span className={`font-medium ${isMobile ? 'text-xs' : 'text-sm'} truncate`}>
                                  {chat.name}
                                </span>
                                {!isMobile && (
                                  <Badge variant="secondary" className="text-xs">
                                    3D Audio
                                  </Badge>
                                )}
                              </div>
                              {chat.unread_count && chat.unread_count > 0 && (
                                <Badge variant="destructive" className={`${isMobile ? 'h-3 text-xs px-1' : 'h-5 text-xs'}`}>
                                  {chat.unread_count}
                                </Badge>
                              )}
                            </div>

                            {/* Hide last message on mobile for compact design */}
                            {!isMobile && (
                              <div className="mt-1">
                                <p className="text-xs text-muted-foreground truncate">
                                  Space message preview...
                                </p>
                                <div className="flex items-center justify-between mt-1">
                                  <span className="text-xs text-muted-foreground">
                                    {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                  </span>
                                  <div className="flex items-center gap-1">
                                    <Headphones className="h-3 w-3 text-blue-500" />
                                    <span className="text-xs text-blue-500">Spatial Audio</span>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}

                  {chats.filter(chat => chat.type === 'space').length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      <Globe className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No voice spaces yet</p>
                      <p className="text-xs">Create or join a space for 3D audio experience!</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </div>

        {/* Main Chat Area - Desktop: Always visible, Mobile: Only when chat selected */}
        <div className={`${isMobile ? (selectedChat ? 'flex-1' : 'hidden') : 'flex-1'} flex flex-col overflow-hidden`}>
          {selectedChat ? (
            <div className="h-full flex flex-col overflow-hidden">
              {/* Chat Header - Fixed */}
              <div className={`border-b ${isMobile ? 'p-3 safe-area-top' : 'p-4'} transition-all duration-500 ${getThemeStyles(currentTheme).headerBg} ${getThemeStyles(currentTheme).glow} bg-background/95 backdrop-blur-sm flex-shrink-0`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {isMobile && (
                      <Button variant="ghost" size="sm" onClick={() => setSelectedChat(null)}>
                        <ArrowLeft className="h-4 w-4" />
                      </Button>
                    )}
                    {(() => {
                      const currentChat = chats.find(c => c.id === selectedChat);
                      const isGroupChat = currentChat?.type === 'group';

                      return (
                        <>
                          <Avatar className={`${isMobile ? 'h-6 w-6' : 'h-8 w-8'}`}>
                            <AvatarFallback className={`${isMobile ? 'text-xs' : 'text-sm'}`}>
                              {isGroupChat ? (
                                <Users className="h-3 w-3" />
                              ) : (
                                currentChat?.name?.[0]?.toUpperCase() || 'C'
                              )}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <h3
                                className={`${isMobile ? 'text-sm' : 'text-base'} font-medium ${isGroupChat ? 'cursor-pointer hover:text-primary' : ''}`}
                                onClick={isGroupChat ? () => setShowGroupManagement(true) : undefined}
                              >
                                {currentChat?.name || 'Chat'}
                              </h3>
                              {isGroupChat && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => setShowGroupManagement(true)}
                                  className="h-6 w-6 p-0"
                                >
                                  <Info className="h-3 w-3" />
                                </Button>
                              )}
                            </div>
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              <div className={`w-1.5 h-1.5 rounded-full transition-all duration-300 ${
                                currentTheme === 'calm' ? 'bg-blue-500' :
                                currentTheme === 'excited' ? 'bg-red-500' :
                                currentTheme === 'focused' ? 'bg-yellow-500' :
                                currentTheme === 'creative' ? 'bg-purple-500' :
                                currentTheme === 'mysterious' ? 'bg-indigo-500' : 'bg-green-500'
                              }`}></div>
                              {isGroupChat ? (
                                `${currentChat?.participant_count || 0} members • ${currentChat?.type}`
                              ) : (
                                `Online • ${currentTheme.charAt(0).toUpperCase() + currentTheme.slice(1)} mood`
                              )}
                            </div>
                          </div>
                        </>
                      );
                    })()}
                  </div>

                  <div className="flex items-center gap-1">
                    <Button variant="ghost" size="sm" onClick={() => setShowAIModal(true)} className={isMobile ? 'h-8 w-8 p-0' : ''}>
                      <Brain className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => changeTheme()}
                      className={`transition-all duration-300 ${getThemeStyles(currentTheme).accent} hover:${getThemeStyles(currentTheme).glow} ${isMobile ? 'h-8 w-8 p-0' : ''}`}
                      title={`Current theme: ${currentTheme}`}
                    >
                      <Palette className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => openTipModal()} className={isMobile ? 'h-8 w-8 p-0' : ''}>
                      <DollarSign className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              {/* Messages Area - Scrollable */}
              {(() => {
                const currentChat = chats.find(c => c.id === selectedChat);
                const isSpaceChat = currentChat?.type === 'space';

                if (isSpaceChat) {
                  return (
                    <div className="flex-1 overflow-hidden">
                      <div className="h-full overflow-y-auto">
                        <SpatialChatView
                          messages={messages}
                          participants={[]}
                          onSendMessage={sendTextMessage}
                          onSendVoice={startVoiceMessage}
                          currentUserId={user?.id || ''}
                          onDeleteMessage={deleteMessage}
                        />
                      </div>
                    </div>
                  );
                }

                return (
                  <div className="flex-1 overflow-hidden">
                    <div className="h-full overflow-y-auto">
                      <div className={`space-y-4 ${isMobile ? 'p-3' : 'p-4'}`}>
                      {messages.length === 0 ? (
                        <div className="text-center text-sm text-muted-foreground">
                          No messages yet. Start the conversation! 🎤
                        </div>
                      ) : (
                        messages.map((message) => {
                          const isWaveChat = currentChat?.type === 'wave';

                      return (
                        <div
                          key={message.id}
                          className={`group flex gap-3 hover:bg-muted/50 rounded-lg p-2 transition-all duration-200 ${
                            isSpaceChat ? 'relative border-l-4 border-l-blue-500 bg-blue-50/30 dark:bg-blue-950/30' :
                            isWaveChat ? 'relative border-l-4 border-l-purple-500 bg-purple-50/30 dark:bg-purple-950/30' :
                            ''
                          }`}
                        >
                          {/* Subtle Chat Type Indicators */}
                          {isSpaceChat && (
                            <div className="absolute top-2 right-2 opacity-60">
                              <Globe className="h-3 w-3 text-blue-500" />
                            </div>
                          )}

                          {isWaveChat && (
                            <div className="absolute top-2 right-2 opacity-60">
                              <Waves className="h-3 w-3 text-purple-500" />
                            </div>
                          )}

                          <Avatar className={`h-8 w-8 flex-shrink-0 ${
                            isSpaceChat ? 'ring-2 ring-blue-300' :
                            isWaveChat ? 'ring-2 ring-purple-300' : ''
                          }`}>
                            <AvatarImage src={message.sender_profile?.avatar_url} />
                            <AvatarFallback>
                              {message.sender_profile?.display_name?.slice(0, 2).toUpperCase() || 'U'}
                            </AvatarFallback>
                          </Avatar>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-1">
                              <div className="flex items-center gap-2">
                                <span className={`text-sm font-medium ${
                                  isSpaceChat ? 'text-blue-700 dark:text-blue-300' :
                                  isWaveChat ? 'text-purple-700 dark:text-purple-300' : ''
                                }`}>
                                  {message.sender_profile?.display_name || 'Unknown User'}
                                </span>
                                <span className="text-xs text-muted-foreground">
                                  {new Date(message.created_at).toLocaleTimeString()}
                                </span>
                                {message.message_type === 'voice' && (
                                  <div className="flex items-center gap-1">
                                    <Mic className={`h-3 w-3 ${
                                      isSpaceChat ? 'text-blue-500' :
                                      isWaveChat ? 'text-purple-500' : 'text-blue-500'
                                    }`} />
                                    {isSpaceChat && <span className="text-xs text-blue-500">Spatial</span>}
                                  </div>
                                )}
                                {message.message_type === 'emotion' && (
                                  <Heart className="h-3 w-3 text-red-500" />
                                )}
                              </div>

                              {/* Message Actions */}
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <MoreHorizontal className="h-3 w-3" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem
                                    onClick={() => deleteMessage(message.id, 'for-me')}
                                    className="text-orange-600 hover:text-orange-700"
                                  >
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Delete for me
                                  </DropdownMenuItem>
                                  {(message.sender_id === user?.id) && (
                                    <DropdownMenuItem
                                      onClick={() => deleteMessage(message.id, 'for-everyone')}
                                      className="text-red-600 hover:text-red-700"
                                    >
                                      <Trash2 className="h-4 w-4 mr-2" />
                                      Delete for everyone
                                    </DropdownMenuItem>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>

                          {message.message_type === 'text' && (
                            <>
                              {message.content?.startsWith('📷 Image:') ? (
                                <div className="max-w-xs">
                                  <img
                                    src={message.content.replace('📷 Image: ', '')}
                                    alt="Shared image"
                                    className="rounded-lg max-w-full h-auto cursor-pointer"
                                    onClick={() => window.open(message.content?.replace('📷 Image: ', ''), '_blank')}
                                  />
                                </div>
                              ) : (
                                <p className="text-sm">{message.content}</p>
                              )}
                            </>
                          )}

                          {message.message_type === 'voice' && (
                            <div className={`p-3 rounded-lg max-w-sm ${
                              isSpaceChat ? 'bg-blue-50 dark:bg-blue-900/20' :
                              isWaveChat ? 'bg-purple-50 dark:bg-purple-900/20' :
                              'bg-muted'
                            }`}>
                              {/* Simple Audio Type Indicator */}
                              {(isSpaceChat || isWaveChat) && (
                                <div className="mb-2 flex items-center gap-2 text-xs">
                                  {isSpaceChat && (
                                    <>
                                      <Headphones className="h-3 w-3 text-blue-500" />
                                      <span className="text-blue-600 dark:text-blue-400">Spatial Audio</span>
                                    </>
                                  )}
                                  {isWaveChat && (
                                    <>
                                      <Waves className="h-3 w-3 text-purple-500" />
                                      <span className="text-purple-600 dark:text-purple-400">Wave Energy</span>
                                    </>
                                  )}
                                </div>
                              )}

                              {message.voice_url ? (
                                <AudioPlayer
                                  src={message.voice_url}
                                  className="w-full"
                                />
                              ) : (
                                <div className="flex items-center gap-2 text-muted-foreground">
                                  <Mic className="h-4 w-4" />
                                  <span className="text-sm">Voice message unavailable</span>
                                </div>
                              )}

                              <div className="mt-2 flex items-center justify-between text-xs text-muted-foreground">
                                <span>
                                  Duration: {message.voice_duration ? `${Math.round(message.voice_duration)}s` : 'Unknown'}
                                </span>
                                {message.voice_transcript && (
                                  <span className="text-blue-500">Transcribed</span>
                                )}
                              </div>

                              {message.voice_transcript && (
                                <div className={`mt-2 p-2 bg-background rounded border-l-2 ${
                                  isSpaceChat ? 'border-blue-500' :
                                  isWaveChat ? 'border-purple-500' : 'border-blue-500'
                                }`}>
                                  <p className="text-xs text-muted-foreground italic">
                                    "{message.voice_transcript}"
                                  </p>
                                </div>
                              )}
                            </div>
                          )}

                          {/* Message Reactions */}
                          <div className="mt-2">
                            <MessageReactions
                              messageId={message.id}
                              userId={user?.id || ''}
                            />
                          </div>
                        </div>
                      </div>
                      );
                    })
                  )}
                        <div ref={messagesEndRef} />
                      </div>
                    </div>
                  </div>
                );
              })()}

              {/* Input Area - Fixed at bottom */}
              <div className={`border-t bg-background/95 backdrop-blur-sm flex-shrink-0 ${isMobile ? 'p-3 safe-area-bottom' : 'p-4'}`}>
                {/* Image Preview */}
                {imagePreview && (
                  <div className="mb-3 p-2 bg-muted rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Image Preview:</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedImage(null);
                          setImagePreview(null);
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    <img src={imagePreview} alt="Preview" className="max-w-32 h-auto rounded" />
                    <Button onClick={sendImageMessage} size="sm" className="mt-2 w-full">
                      Send Image 📷
                    </Button>
                  </div>
                )}

                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={startVoiceMessage}
                    disabled={isProcessingVoice}
                    className={isMobile ? 'h-9 w-9 p-0' : ''}
                  >
                    {isProcessingVoice ? (
                      <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                    ) : (
                      <Mic className="h-4 w-4" />
                    )}
                  </Button>

                  {/* Standard textarea input */}
                  <Textarea
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendTextMessage();
                      }
                    }}
                    placeholder="Type a message..."
                    className={`flex-1 resize-none ${isMobile ? 'min-h-[36px] max-h-[100px] text-base' : 'min-h-[40px] max-h-[80px]'}`}
                    disabled={isProcessingVoice}
                    rows={1}
                    style={isMobile ? { fontSize: '16px' } : {}}
                  />

                  <label htmlFor="image-upload">
                    <Button variant="ghost" size="sm" asChild className={isMobile ? 'h-9 w-9 p-0' : ''}>
                      <span>
                        <Paperclip className="h-4 w-4" />
                      </span>
                    </Button>
                  </label>
                  <input
                    id="image-upload"
                    type="file"
                    accept="image/*"
                    onChange={handleImageSelect}
                    className="hidden"
                  />

                  <Button
                    size="sm"
                    onClick={sendTextMessage}
                    disabled={!newMessage.trim() || isProcessingVoice}
                    className={isMobile ? 'h-9 w-9 p-0' : ''}
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>

                {/* Emotional Wave Buttons */}
                <div className="flex items-center gap-2 mt-2">
                  <span className="text-xs text-muted-foreground">Send wave:</span>
                  {['💙', '🔥', '⚡', '🌟', '🌊'].map((emoji, i) => (
                    <Button
                      key={i}
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={() => sendEmotionalWave(emoji)}
                    >
                      {emoji}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <MessageCircle className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Welcome to VoiceWave Chat</h3>
                <p className="text-muted-foreground mb-4">
                  Select a chat to start your revolutionary messaging experience
                </p>
                <div className="flex gap-2 justify-center">
                  <Button onClick={() => createNewChat('direct')}>
                    <Plus className="h-4 w-4 mr-2" />
                    Start Direct Chat
                  </Button>
                  <Button variant="outline" onClick={() => createNewChat('group')}>
                    <Users className="h-4 w-4 mr-2" />
                    Create Group
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Create Chat Modal */}
      <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              Create {createChatType === 'direct' ? 'Direct Chat' :
                     createChatType === 'group' ? 'Group Chat' :
                     'Wave Chat'}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="chatName">Chat Name</Label>
              <Input
                id="chatName"
                value={chatName}
                onChange={(e) => setChatName(e.target.value)}
                placeholder={`Enter ${createChatType} chat name...`}
              />
            </div>

            {createChatType !== 'direct' && (
              <div>
                <Label htmlFor="chatDescription">Description (Optional)</Label>
                <Textarea
                  id="chatDescription"
                  value={chatDescription}
                  onChange={(e) => setChatDescription(e.target.value)}
                  placeholder="Describe your chat..."
                  rows={3}
                />
              </div>
            )}

            {createChatType === 'group' && (
              <>
                {/* Group Icon Upload */}
                <div>
                  <Label htmlFor="groupIcon">Group Icon (Optional)</Label>
                  <div className="mt-2 flex items-center gap-4">
                    <div className="w-16 h-16 rounded-full bg-muted border-2 border-dashed border-muted-foreground/25 flex items-center justify-center overflow-hidden">
                      {imagePreview ? (
                        <img src={imagePreview} alt="Group icon" className="w-full h-full object-cover" />
                      ) : (
                        <Users className="h-6 w-6 text-muted-foreground" />
                      )}
                    </div>
                    <div className="flex flex-col gap-2">
                      <Input
                        id="groupIcon"
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            setSelectedImage(file);
                            const reader = new FileReader();
                            reader.onload = (e) => setImagePreview(e.target?.result as string);
                            reader.readAsDataURL(file);
                          }
                        }}
                        className="hidden"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => document.getElementById('groupIcon')?.click()}
                      >
                        Choose Image
                      </Button>
                      {imagePreview && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedImage(null);
                            setImagePreview(null);
                          }}
                        >
                          Remove
                        </Button>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="isPrivate"
                    checked={isPrivate}
                    onCheckedChange={setIsPrivate}
                  />
                  <Label htmlFor="isPrivate">Private Group</Label>
                </div>

                <div>
                  <Label htmlFor="participants">Invite Participants</Label>
                  <Textarea
                    id="participants"
                    value={participantEmails}
                    onChange={(e) => setParticipantEmails(e.target.value)}
                    placeholder="Enter email addresses or usernames, one per line..."
                    rows={4}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Add participants by email or username
                  </p>
                </div>
              </>
            )}

            {createChatType === 'wave' && (
              <div className="bg-muted p-4 rounded-lg">
                <h4 className="font-medium mb-2">🌊 Wave Chat Features:</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Send emotional energy waves</li>
                  <li>• Visual mood representation</li>
                  <li>• Dynamic theme changes</li>
                  <li>• Collective emotional experience</li>
                </ul>
              </div>
            )}

            <div className="flex gap-2 pt-4">
              <Button
                onClick={handleCreateChat}
                className="flex-1"
                disabled={!chatName.trim()}
              >
                Create {createChatType === 'direct' ? 'Chat' :
                       createChatType === 'group' ? 'Group' :
                       'Wave'}
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowCreateModal(false)}
              >
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Settings Modal */}
      <Dialog open={showSettingsModal} onOpenChange={setShowSettingsModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>VoiceWave Chat Settings</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium">Notification Settings</h4>
              <div className="flex items-center justify-between">
                <Label>Sound notifications</Label>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <Label>Desktop notifications</Label>
                <Switch defaultChecked />
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Voice Settings</h4>
              <div className="flex items-center justify-between">
                <Label>Auto-transcription</Label>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <Label>Voice enhancement</Label>
                <Switch defaultChecked />
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Privacy Settings</h4>
              <div className="flex items-center justify-between">
                <Label>Show online status</Label>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <Label>Allow emotional waves</Label>
                <Switch defaultChecked />
              </div>
            </div>

            <Button onClick={() => setShowSettingsModal(false)} className="w-full">
              Save Settings
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* AI Assistant Modal */}
      <Dialog open={showAIModal} onOpenChange={setShowAIModal}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>🧠 AI Assistant</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div className="bg-muted p-4 rounded-lg">
              <h4 className="font-medium mb-2">AI Features Available:</h4>
              <ul className="text-sm space-y-1">
                <li>• Smart message suggestions</li>
                <li>• Voice transcription & translation</li>
                <li>• Emotional tone analysis</li>
                <li>• Conversation summaries</li>
                <li>• User summoning predictions</li>
              </ul>
            </div>

            <div className="space-y-2">
              <Label>Ask AI Assistant</Label>
              <Textarea
                placeholder="How can I help you with your conversations?"
                rows={3}
                className="resize-none"
              />
            </div>

            <div className="flex gap-2">
              <Button className="flex-1">
                <Brain className="h-4 w-4 mr-2" />
                Ask AI
              </Button>
              <Button variant="outline" onClick={() => setShowAIModal(false)}>
                Close
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Join Space Modal */}
      <Dialog open={showSpaceModal} onOpenChange={setShowSpaceModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Join Voice Space</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Space ID or Invite Link</Label>
              <Textarea
                placeholder="Enter space ID or paste invite link..."
                rows={2}
                className="resize-none"
              />
            </div>

            <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
              <h4 className="font-medium mb-2">🌍 Spatial Audio Features:</h4>
              <ul className="text-sm space-y-1">
                <li>• 3D positioned voices</li>
                <li>• Immersive audio experience</li>
                <li>• Move around virtual space</li>
                <li>• Distance-based volume</li>
              </ul>
            </div>

            <div className="flex gap-2">
              <Button className="flex-1">
                <Headphones className="h-4 w-4 mr-2" />
                Join Space
              </Button>
              <Button variant="outline" onClick={() => setShowSpaceModal(false)}>
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Voice Recorder Modal */}
      <Dialog open={showVoiceRecorder} onOpenChange={setShowVoiceRecorder}>
        <DialogContent className={`${isMobile ? 'max-w-[95vw] max-h-[80vh]' : 'sm:max-w-md'} overflow-y-auto`}>
          <DialogHeader>
            <DialogTitle>🎤 Record Voice Message</DialogTitle>
          </DialogHeader>

          <div className="py-4">
            <AudioRecorderWithFilters
              onRecordingComplete={handleVoiceRecordingComplete}
              onCancel={() => setShowVoiceRecorder(false)}
              placeholder="Recording your voice message..."
              maxMediaFiles={0}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* Tip Modal */}
      {showTipModal && (
        <TipModalWithoutWallet
          isOpen={showTipModal}
          onClose={() => setShowTipModal(false)}
          recipientAddress={tipRecipientId}
        />
      )}

      {/* Group Management Modal */}
      {selectedChat && showGroupManagement && (() => {
        const currentChat = chats.find(c => c.id === selectedChat);
        return currentChat?.type === 'group' ? (
          <GroupManagementModal
            isOpen={showGroupManagement}
            onClose={() => setShowGroupManagement(false)}
            chat={currentChat}
            onChatUpdate={(updatedChat) => {
              setChats(chats.map(c => c.id === updatedChat.id ? updatedChat : c));
            }}
          />
        ) : null;
      })()}
    </div>
  );
};

export default VoiceChatHub;
