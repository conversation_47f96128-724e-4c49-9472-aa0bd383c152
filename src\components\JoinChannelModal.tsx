
import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Drawer, DrawerContent } from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { X, LogIn } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { toast } from '@/components/ui/sonner';
import { useChannels } from '@/contexts/ChannelContext';

interface JoinChannelModalProps {
  isOpen: boolean;
  onClose: () => void;
  inviteCode?: string;
}

const JoinChannelModal: React.FC<JoinChannelModalProps> = ({
  isOpen,
  onClose,
  inviteCode: initialInviteCode
}) => {
  const isMobile = useIsMobile();
  const { useInvite, publicChannels, joinChannel } = useChannels();
  
  const [inviteCode, setInviteCode] = useState(initialInviteCode || '');
  const [isJoining, setIsJoining] = useState(false);
  
  // Reset invite code when modal is opened with a new initial code
  useEffect(() => {
    if (isOpen) {
      setInviteCode(initialInviteCode || '');
    }
  }, [isOpen, initialInviteCode]);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInviteCode(e.target.value);
  };
  
  const handleJoinWithInvite = async () => {
    if (!inviteCode) {
      toast('Please enter an invite code');
      return;
    }
    
    setIsJoining(true);
    
    try {
      // Only trim the code when processing, not during typing
      const trimmedCode = inviteCode.trim().toUpperCase();
      const success = await useInvite(trimmedCode);
      
      if (success) {
        toast('Successfully joined the channel!');
        onClose();
      } else {
        toast('Invalid or expired invite code');
      }
    } catch (error) {
      console.error('Error joining channel with invite:', error);
      toast('An error occurred while joining the channel.');
    } finally {
      setIsJoining(false);
    }
  };
  
  const handleJoinPublicChannel = async (channelId: string) => {
    setIsJoining(true);
    
    try {
      joinChannel(channelId);
      toast('Successfully joined the channel!');
      onClose();
    } catch (error) {
      console.error('Error joining public channel:', error);
      toast('An error occurred while joining the channel.');
    } finally {
      setIsJoining(false);
    }
  };
  
  const ModalContent = () => (
    <div className="flex flex-col py-6 px-4 space-y-6 max-h-[80vh] overflow-y-auto">
      <div className="flex justify-between items-center w-full">
        <DialogTitle className="text-xl font-semibold">Join a Channel</DialogTitle>
        <Button variant="ghost" size="icon" onClick={onClose}>
          <X size={20} />
        </Button>
      </div>
      
      <DialogDescription className="text-sm text-muted-foreground">
        Join channels using an invite code or browse public channels
      </DialogDescription>
      
      <div className="space-y-6 dark:text-white">
        {/* Join with Invite */}
        <div>
          <h3 className="text-sm font-medium mb-2">Join with an Invite</h3>
          <div className="flex gap-2">
            <Input 
              value={inviteCode} 
              onChange={handleInputChange}
              placeholder="Enter invite code"
              className="font-mono uppercase dark:bg-gray-800 dark:text-white dark:border-gray-700"
            />
            <Button 
              onClick={handleJoinWithInvite}
              disabled={!inviteCode || isJoining}
              className="bg-voicechain-purple hover:bg-voicechain-accent"
            >
              {isJoining ? 'Joining...' : (
                <>
                  <LogIn size={16} className="mr-2" />
                  Join
                </>
              )}
            </Button>
          </div>
        </div>
        
        {/* Public Channels */}
        {publicChannels.length > 0 && (
          <div>
            <h3 className="text-sm font-medium mb-2">Public Channels</h3>
            <div className="space-y-2">
              {publicChannels.map(channel => (
                <div 
                  key={channel.id} 
                  className="flex items-center justify-between p-3 dark:bg-gray-800 bg-secondary rounded-lg"
                >
                  <div>
                    <p className="font-medium">#{channel.name}</p>
                    <p className="text-xs text-muted-foreground">{channel.members.length} members</p>
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleJoinPublicChannel(channel.id)}
                    disabled={isJoining}
                    className="dark:border-gray-600 dark:text-gray-200"
                  >
                    {isJoining ? 'Joining...' : 'Join'}
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
  
  if (isMobile) {
    return (
      <Drawer open={isOpen} onOpenChange={open => !open && onClose()}>
        <DrawerContent className="max-h-[90vh] dark:bg-gray-900">
          <ModalContent />
        </DrawerContent>
      </Drawer>
    );
  }
  
  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-md dark:bg-gray-900">
        <ModalContent />
      </DialogContent>
    </Dialog>
  );
};

export default JoinChannelModal;
