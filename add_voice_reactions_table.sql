-- Create voice_reactions table
CREATE TABLE IF NOT EXISTS public.voice_reactions (
  id UUID PRIMARY KEY,
  message_id TEXT NOT NULL,
  user_id TEXT NOT NULL,
  reaction_type TEXT NOT NULL,
  audio_url TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(message_id, user_id, reaction_type)
);

-- Enable RLS on voice_reactions table
ALTER TABLE public.voice_reactions ENABLE ROW LEVEL SECURITY;

-- Create policies for voice_reactions table

-- 1. Allow users to view all voice reactions
CREATE POLICY "Users can view all voice reactions"
ON public.voice_reactions
FOR SELECT
USING (true);

-- 2. Allow users to insert their own voice reactions
CREATE POLICY "Users can insert their own voice reactions"
ON public.voice_reactions
FOR INSERT
WITH CHECK (user_id = auth.uid());

-- 3. Allow users to delete their own voice reactions
CREATE POLICY "Users can delete their own voice reactions"
ON public.voice_reactions
FOR DELETE
USING (user_id = auth.uid());

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS voice_reactions_message_id_idx ON public.voice_reactions(message_id);
CREATE INDEX IF NOT EXISTS voice_reactions_user_id_idx ON public.voice_reactions(user_id);
CREATE INDEX IF NOT EXISTS voice_reactions_reaction_type_idx ON public.voice_reactions(reaction_type);

-- Create function to get voice reactions for a message
CREATE OR REPLACE FUNCTION get_message_voice_reactions(message_id_param TEXT)
RETURNS TABLE (
  id UUID,
  message_id TEXT,
  user_id TEXT,
  reaction_type TEXT,
  audio_url TEXT,
  created_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT vr.id, vr.message_id, vr.user_id, vr.reaction_type, vr.audio_url, vr.created_at
  FROM public.voice_reactions vr
  WHERE vr.message_id = message_id_param
  ORDER BY vr.created_at ASC;
END;
$$ LANGUAGE plpgsql;
