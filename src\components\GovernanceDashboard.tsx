import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { supabase } from '@/lib/supabase';
import { toast } from '@/components/ui/sonner';
import ProposalCreation from './ProposalCreation';
import { 
  Vote, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Users, 
  DollarSign,
  TrendingUp,
  Play,
  Calendar,
  Award
} from 'lucide-react';

interface Proposal {
  id: string;
  title: string;
  description: string;
  proposal_type: string;
  audio_proposal_url?: string;
  voting_end: string;
  status: string;
  min_approval_percentage: number;
  proposer: {
    wallet_address: string;
    display_name?: string;
  };
  vote_counts: {
    yes: number;
    no: number;
    abstain: number;
    total: number;
  };
  user_vote?: string;
}

interface GovernanceDashboardProps {
  channelId: string;
  userAddress: string;
}

const GovernanceDashboard: React.FC<GovernanceDashboardProps> = ({
  channelId,
  userAddress
}) => {
  const [proposals, setProposals] = useState<Proposal[]>([]);
  const [loading, setLoading] = useState(true);
  const [votingLoading, setVotingLoading] = useState<string | null>(null);
  const [treasuryBalance, setTreasuryBalance] = useState(0);

  useEffect(() => {
    loadProposals();
    loadTreasuryBalance();
  }, [channelId]);

  const loadProposals = async () => {
    try {
      setLoading(true);

      // Get user profile for vote checking
      const { data: profileData } = await supabase
        .from('profiles')
        .select('id')
        .eq('wallet_address', userAddress)
        .single();

      // Get proposals with vote counts
      const { data: proposalsData, error } = await supabase
        .from('proposals')
        .select(`
          *,
          proposer:profiles!proposer_profile_id(wallet_address, display_name),
          votes(vote_choice, voter_profile_id)
        `)
        .eq('channel_id', channelId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading proposals:', error);
        return;
      }

      // Process proposals with vote counts
      const processedProposals = proposalsData?.map(proposal => {
        const votes = proposal.votes || [];
        const voteCounts = {
          yes: votes.filter((v: any) => v.vote_choice === 'yes').length,
          no: votes.filter((v: any) => v.vote_choice === 'no').length,
          abstain: votes.filter((v: any) => v.vote_choice === 'abstain').length,
          total: votes.length
        };

        const userVote = profileData ? 
          votes.find((v: any) => v.voter_profile_id === profileData.id)?.vote_choice : 
          undefined;

        return {
          ...proposal,
          vote_counts: voteCounts,
          user_vote: userVote
        };
      }) || [];

      setProposals(processedProposals);

    } catch (error) {
      console.error('Error loading proposals:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadTreasuryBalance = async () => {
    try {
      const { data, error } = await supabase
        .from('channel_treasury')
        .select(`
          balance,
          token:tokens(symbol, price_usd)
        `)
        .eq('channel_id', channelId);

      if (error) {
        console.error('Error loading treasury:', error);
        return;
      }

      const totalUSD = data?.reduce((sum, treasury) => {
        return sum + (treasury.balance * (treasury.token?.price_usd || 0));
      }, 0) || 0;

      setTreasuryBalance(totalUSD);
    } catch (error) {
      console.error('Error loading treasury:', error);
    }
  };

  const handleVote = async (proposalId: string, voteChoice: 'yes' | 'no' | 'abstain') => {
    try {
      setVotingLoading(proposalId);

      // Get user profile
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('wallet_address', userAddress)
        .single();

      if (profileError || !profileData) {
        toast('Please connect your wallet first');
        return;
      }

      // Submit vote
      const { error } = await supabase
        .from('votes')
        .upsert({
          proposal_id: proposalId,
          voter_profile_id: profileData.id,
          vote_choice: voteChoice,
          vote_weight: 1.0
        });

      if (error) {
        console.error('Error submitting vote:', error);
        toast('Failed to submit vote');
        return;
      }

      toast(`Vote submitted: ${voteChoice.toUpperCase()} ✅`);
      loadProposals(); // Refresh to show updated counts

    } catch (error) {
      console.error('Error submitting vote:', error);
      toast('Failed to submit vote');
    } finally {
      setVotingLoading(null);
    }
  };

  const getProposalStatus = (proposal: Proposal) => {
    const now = new Date();
    const endDate = new Date(proposal.voting_end);
    
    if (proposal.status === 'executed') return { label: 'Executed', color: 'bg-green-500' };
    if (proposal.status === 'rejected') return { label: 'Rejected', color: 'bg-red-500' };
    if (endDate < now) return { label: 'Ended', color: 'bg-gray-500' };
    return { label: 'Active', color: 'bg-blue-500' };
  };

  const getApprovalPercentage = (proposal: Proposal) => {
    const { yes, no } = proposal.vote_counts;
    const totalDecisive = yes + no;
    return totalDecisive > 0 ? (yes / totalDecisive) * 100 : 0;
  };

  const formatTimeRemaining = (endDate: string) => {
    const now = new Date();
    const end = new Date(endDate);
    const diffMs = end.getTime() - now.getTime();
    
    if (diffMs <= 0) return 'Ended';
    
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    
    if (diffDays > 0) return `${diffDays}d ${diffHours}h remaining`;
    return `${diffHours}h remaining`;
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-24 bg-secondary rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-500/10 rounded-lg flex items-center justify-center">
                <Vote size={20} className="text-blue-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Active Proposals</p>
                <p className="text-xl font-bold">
                  {proposals.filter(p => p.status === 'active').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-green-500/10 rounded-lg flex items-center justify-center">
                <DollarSign size={20} className="text-green-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Treasury Balance</p>
                <p className="text-xl font-bold">${treasuryBalance.toFixed(2)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-purple-500/10 rounded-lg flex items-center justify-center">
                <TrendingUp size={20} className="text-purple-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Participation Rate</p>
                <p className="text-xl font-bold">
                  {proposals.length > 0 ? 
                    Math.round(proposals.reduce((sum, p) => sum + p.vote_counts.total, 0) / proposals.length) : 0
                  }%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Governance</h2>
        <ProposalCreation
          channelId={channelId}
          userAddress={userAddress}
          onProposalCreated={loadProposals}
        />
      </div>

      {/* Proposals */}
      <Tabs defaultValue="active" className="space-y-4">
        <TabsList>
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="completed">Completed</TabsTrigger>
          <TabsTrigger value="all">All</TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="space-y-4">
          {proposals.filter(p => p.status === 'active').map(proposal => {
            const status = getProposalStatus(proposal);
            const approvalPercentage = getApprovalPercentage(proposal);
            const hasVoted = !!proposal.user_vote;

            return (
              <Card key={proposal.id} className="border-l-4 border-l-blue-500">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <CardTitle className="text-lg">{proposal.title}</CardTitle>
                        <Badge className={`${status.color} text-white`}>
                          {status.label}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {proposal.proposal_type.replace('_', ' ')}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        By {proposal.proposer.display_name || 
                            `${proposal.proposer.wallet_address.slice(0, 6)}...${proposal.proposer.wallet_address.slice(-4)}`}
                      </p>
                      <p className="text-sm">{proposal.description}</p>
                    </div>
                    
                    {proposal.audio_proposal_url && (
                      <Button variant="outline" size="sm">
                        <Play size={14} className="mr-1" />
                        Audio
                      </Button>
                    )}
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Voting Progress */}
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Approval: {approvalPercentage.toFixed(1)}%</span>
                      <span>{formatTimeRemaining(proposal.voting_end)}</span>
                    </div>
                    <Progress value={approvalPercentage} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground mt-1">
                      <span>Yes: {proposal.vote_counts.yes}</span>
                      <span>No: {proposal.vote_counts.no}</span>
                      <span>Total: {proposal.vote_counts.total}</span>
                    </div>
                  </div>

                  {/* Voting Buttons */}
                  {!hasVoted && proposal.status === 'active' && (
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={() => handleVote(proposal.id, 'yes')}
                        disabled={votingLoading === proposal.id}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <CheckCircle size={14} className="mr-1" />
                        Yes
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleVote(proposal.id, 'no')}
                        disabled={votingLoading === proposal.id}
                      >
                        <XCircle size={14} className="mr-1" />
                        No
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleVote(proposal.id, 'abstain')}
                        disabled={votingLoading === proposal.id}
                      >
                        <Clock size={14} className="mr-1" />
                        Abstain
                      </Button>
                    </div>
                  )}

                  {hasVoted && (
                    <Badge variant="secondary" className="flex items-center gap-1 w-fit">
                      <CheckCircle size={12} />
                      You voted: {proposal.user_vote?.toUpperCase()}
                    </Badge>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </TabsContent>

        <TabsContent value="completed" className="space-y-4">
          {proposals.filter(p => p.status !== 'active').map(proposal => (
            <Card key={proposal.id} className="opacity-75">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{proposal.title}</CardTitle>
                  <Badge className={getProposalStatus(proposal).color + ' text-white'}>
                    {getProposalStatus(proposal).label}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Final result: {getApprovalPercentage(proposal).toFixed(1)}% approval
                </p>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="all" className="space-y-4">
          {proposals.map(proposal => (
            <Card key={proposal.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{proposal.title}</CardTitle>
                  <Badge className={getProposalStatus(proposal).color + ' text-white'}>
                    {getProposalStatus(proposal).label}
                  </Badge>
                </div>
              </CardHeader>
            </Card>
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default GovernanceDashboard;
