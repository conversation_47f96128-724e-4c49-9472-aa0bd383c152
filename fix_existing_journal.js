import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://jcltjkaumevuycntdmds.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpjbHRqa2F1bWV2dXljbnRkbWRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTkwMDEsImV4cCI6MjA2MTg3NTAwMX0.LqQSk0NwB1mDB4Cznn0iQSrYvsAAWyUsaj8reo_etqM'
);

async function fixExistingJournal() {
  console.log('🔧 Fixing existing journal...\n');
  
  try {
    // Get the existing journal
    const { data: journals, error: fetchError } = await supabase
      .from('journals')
      .select('*')
      .eq('title', 'story of my life')
      .limit(1);
      
    if (fetchError || !journals || journals.length === 0) {
      console.log('❌ Journal not found');
      return;
    }
    
    const journal = journals[0];
    console.log('📖 Found journal:');
    console.log(`   ID: ${journal.id}`);
    console.log(`   Title: ${journal.title}`);
    console.log(`   Current locked status: ${journal.is_locked}`);
    console.log(`   Current published status: ${journal.is_published}`);
    console.log('');
    
    // Update the journal to be unlocked
    console.log('🔓 Updating journal to be unlocked...');
    const { data: updatedJournal, error: updateError } = await supabase
      .from('journals')
      .update({
        is_locked: false, // Make it unlocked
        updated_at: new Date().toISOString()
      })
      .eq('id', journal.id)
      .select()
      .single();
      
    if (updateError) {
      console.log('❌ Error updating journal:', updateError.message);
      console.log('This might be due to RLS policies. The journal should still work in the app.');
    } else {
      console.log('✅ Journal updated successfully!');
      console.log(`   New locked status: ${updatedJournal.is_locked}`);
    }
    
    console.log('');
    console.log('🎯 Your journal should now appear in:');
    console.log('   ✅ My Journals section');
    console.log('   ✅ Unlocked tab');
    console.log('   ✅ Public tab (since it\'s published)');
    console.log('');
    console.log('📱 Try refreshing your app and check the Voice Journals page!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.log('');
    console.log('💡 Manual fix:');
    console.log('If the automatic fix failed, you can manually update the journal in Supabase:');
    console.log('1. Go to Supabase Dashboard → Table Editor → journals');
    console.log('2. Find your journal "story of my life"');
    console.log('3. Set is_locked = false');
    console.log('4. Save the changes');
  }
}

fixExistingJournal().catch(console.error);
