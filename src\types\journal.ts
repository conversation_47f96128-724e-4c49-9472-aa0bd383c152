export type JournalPrivacyLevel = 'my_journal' | 'public' | 'locked_public' | 'private' | 'locked_private';

export interface JournalEntry {
  id: string;
  title: string;
  description?: string;
  audioUrl: string;
  transcript: string;
  createdAt: Date;
  userAddress: string;

  // Privacy & Access Control
  privacyLevel: JournalPrivacyLevel;
  isLocked: boolean;
  isUnlocked?: boolean;

  // Legacy fields for backward compatibility
  isPrivate?: boolean;
  isPublished?: boolean;

  // Unlock conditions
  scheduledFor?: Date;
  unlockCondition?: {
    type: 'time' | 'token' | 'event' | 'date' | 'password' | 'tip';
    value: string;
    unlockDate?: Date;
    tokenAddress?: string;
    eventId?: string;
    tipAmount?: number; // For tip-to-unlock
    tipCurrency?: string;
  };

  // Access control for private journals
  summonedUsers?: string[]; // User IDs who can access private journals
  tipToUnlockAmount?: number; // Amount required to unlock locked_private
  tipToUnlockCurrency?: string; // Currency for tip unlock

  // Social features
  repostCount?: number;
  replyCount?: number;
  reactionCount?: number;

  duration: number;
  media?: Array<{
    id: string;
    url: string;
    type: 'image' | 'video';
  }>;
}

export interface JournalMedia {
  id: string;
  journalId: string;
  url: string;
  type: 'image' | 'video';
  createdAt: Date;
}

export interface JournalComment {
  id: string;
  journalId: string;
  userAddress: string;
  content: string;
  createdAt: Date;
}

export interface JournalLike {
  id: string;
  journalId: string;
  userAddress: string;
  createdAt: Date;
}

export interface JournalUnlockRequest {
  id: string;
  journalId: string;
  userAddress: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: Date;
  updatedAt?: Date;
}

export interface JournalUnlockCondition {
  type: 'time' | 'token' | 'event' | 'date' | 'password';
  value: string;
  unlockDate?: Date;
  tokenAddress?: string;
  eventId?: string;
}

export interface CreateJournalInput {
  title: string;
  description?: string;
  audioUrl: string;
  transcript: string;
  userAddress: string;
  isPrivate?: boolean;
  unlockCondition?: JournalUnlockCondition;
  duration: number;
  media?: Array<{
    url: string;
    type: 'image' | 'video';
  }>;
}

export interface UpdateJournalInput {
  title?: string;
  description?: string;
  isPrivate?: boolean;
  unlockCondition?: JournalUnlockCondition;
}
