
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { UserCheck, UserPlus } from 'lucide-react';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/components/ui/sonner';
import { NotificationType } from '@/types/notification';
import { supabase } from '@/integrations/supabase/client';

interface UserProfileActionsProps {
  userAddress: string;
  compact?: boolean;
}

const UserProfileActions: React.FC<UserProfileActionsProps> = ({ userAddress, compact = false }) => {
  const { currentProfile, isFollowing, followUser, unfollowUser, incrementFollowerCount, decrementFollowerCount, incrementFollowingCount, decrementFollowingCount } = useProfiles();
  const { isAuthenticated } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  const alreadyFollowing = isFollowing(userAddress);

  const handleFollowAction = async () => {
    if (!isAuthenticated) {
      toast.error('You must be logged in to follow users');
      return;
    }

    if (!currentProfile) {
      toast.error('Your profile could not be loaded');
      return;
    }

    setIsLoading(true);

    try {
      if (alreadyFollowing) {
        await unfollowUser(userAddress);
      } else {
        await followUser(userAddress);
        
        // Send notification - handle manually since we fixed the service flow
        try {
          // Create a notification for the follow action
          const notificationData = {
            type: 'follow' as NotificationType,
            from_address: currentProfile.address,
            to_address: userAddress,
            data: {
              follower_profile: {
                username: currentProfile.username,
                display_name: currentProfile.displayName,
                avatar_url: currentProfile.profileImageUrl
              }
            },
            created_at: new Date().toISOString()
          };
          
          await supabase.from('notifications').insert(notificationData);
        } catch (notifError) {
          console.error('Error creating notification:', notifError);
        }
      }
    } catch (error) {
      console.error('Error following/unfollowing user:', error);
      toast.error(alreadyFollowing ? 'Failed to unfollow user' : 'Failed to follow user');
    } finally {
      setIsLoading(false);
    }
  };

  if (compact) {
    return (
      <Button
        variant={alreadyFollowing ? 'outline' : 'default'}
        size="sm"
        onClick={handleFollowAction}
        disabled={isLoading || currentProfile?.address === userAddress}
      >
        {alreadyFollowing ? <UserCheck className="h-4 w-4" /> : <UserPlus className="h-4 w-4" />}
      </Button>
    );
  }

  return (
    <Button
      variant={alreadyFollowing ? 'outline' : 'default'}
      size="default"
      onClick={handleFollowAction}
      disabled={isLoading || currentProfile?.address === userAddress}
      className="w-full sm:w-auto"
    >
      {alreadyFollowing ? (
        <>
          <UserCheck className="mr-2 h-4 w-4" />
          Following
        </>
      ) : (
        <>
          <UserPlus className="mr-2 h-4 w-4" />
          Follow
        </>
      )}
    </Button>
  );
};

export default UserProfileActions;
