
import React, { useState } from 'react';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { Drawer, DrawerContent } from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';
import AudioRecorderWithFilters from './AudioRecorderWithFilters';
import { MediaFile } from './MediaUploader';
import { useIsMobile } from '@/hooks/use-mobile';
import FirstVoiceModal from './FirstVoiceModal';
import { useFirstVoice } from '@/hooks/use-first-voice';

interface RecordingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onRecordingComplete: (audioBlob: Blob, transcript: string, duration?: number, media?: MediaFile[]) => void;
  userAddress: string;
}

const RecordingModal: React.FC<RecordingModalProps> = ({ isOpen, onClose, onRecordingComplete, userAddress }) => {
  const isMobile = useIsMobile();
  const [showFirstVoiceModal, setShowFirstVoiceModal] = useState(false);
  const { isFirstVoice, markFirstVoiceRecorded } = useFirstVoice(userAddress);

  const handleRecordingComplete = (audioBlob: Blob, transcript: string, duration?: number, media?: MediaFile[]) => {
    // Check if this is the user's first voice recording
    if (isFirstVoice) {
      // Show the first voice modal
      setShowFirstVoiceModal(true);
      // Mark that the user has recorded their first voice
      markFirstVoiceRecorded();
    } else {
      // Just complete the recording normally
      onRecordingComplete(audioBlob, transcript, duration, media);
      onClose();
    }
  };

  const handleFirstVoiceModalClose = () => {
    setShowFirstVoiceModal(false);
    // Now complete the recording
    onRecordingComplete(audioBlob.current, transcript.current, duration.current, media.current);
    onClose();
  };

  // Store the recording data to use after the first voice modal is closed
  const audioBlob = React.useRef<Blob | null>(null);
  const transcript = React.useRef<string>('');
  const duration = React.useRef<number | undefined>(undefined);
  const media = React.useRef<MediaFile[] | undefined>(undefined);

  // Update the handleRecordingComplete function to store the data
  const handleRecordingCompleteInternal = (blob: Blob, text: string, dur?: number, med?: MediaFile[]) => {
    audioBlob.current = blob;
    transcript.current = text;
    duration.current = dur;
    media.current = med;

    handleRecordingComplete(blob, text, dur, med);
  };

  const ModalContent = () => (
    <div className="flex flex-col items-center justify-center py-6 px-4 space-y-6 relative">
      <div className="flex justify-between items-center w-full sticky top-0 bg-background z-10 pb-2">
        {isMobile ? (
          <h2 className="text-xl font-semibold">Record a voice message</h2>
        ) : (
          <DialogTitle className="text-xl font-semibold">Record a voice message</DialogTitle>
        )}
        <Button variant="ghost" size="icon" onClick={onClose}>
          <X size={20} />
        </Button>
      </div>

      <div className="w-full max-w-md">
        <AudioRecorderWithFilters
          onRecordingComplete={handleRecordingCompleteInternal}
          maxMediaFiles={4}
        />
      </div>
    </div>
  );

  if (isMobile) {
    return (
      <>
        <Drawer open={isOpen} onOpenChange={open => !open && onClose()}>
          <DrawerContent className="max-h-[85vh] overflow-y-auto">
            <ModalContent />
          </DrawerContent>
        </Drawer>

        {/* First Voice Modal */}
        <FirstVoiceModal
          isOpen={showFirstVoiceModal}
          onClose={handleFirstVoiceModalClose}
        />
      </>
    );
  }

  return (
    <>
      <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
        <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
          <DialogTitle className="sr-only">Record a voice message</DialogTitle>
          <ModalContent />
        </DialogContent>
      </Dialog>

      {/* First Voice Modal */}
      <FirstVoiceModal
        isOpen={showFirstVoiceModal}
        onClose={handleFirstVoiceModalClose}
      />
    </>
  );
};

export default RecordingModal;
