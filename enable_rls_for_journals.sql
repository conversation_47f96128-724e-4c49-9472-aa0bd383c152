-- Enable RLS on journals table
ALTER TABLE public.journals ENABLE ROW LEVEL SECURITY;

-- Enable RLS on journal_media table
ALTER TABLE public.journal_media ENABLE ROW LEVEL SECURITY;

-- Create policies for journals table

-- 1. Allow users to view their own journals
CREATE POLICY "Users can view their own journals"
ON public.journals
FOR SELECT
USING (profile_id = auth.uid());

-- 2. Allow users to view published journals that are not locked
CREATE POLICY "Users can view published journals that are not locked"
ON public.journals
FOR SELECT
USING (is_published = true AND is_locked = false);

-- 3. Allow users to view published journals that are locked (but they can't access content)
CREATE POLICY "Users can view published locked journals"
ON public.journals
FOR SELECT
USING (is_published = true AND is_locked = true);

-- 4. Allow users to insert their own journals
CREATE POLICY "Users can insert their own journals"
ON public.journals
FOR INSERT
WITH CHECK (profile_id = auth.uid());

-- 5. Allow users to update their own journals
CREATE POLICY "Users can update their own journals"
ON public.journals
FOR UPDATE
USING (profile_id = auth.uid())
WITH CHECK (profile_id = auth.uid());

-- 6. Allow users to delete their own journals
CREATE POLICY "Users can delete their own journals"
ON public.journals
FOR DELETE
USING (profile_id = auth.uid());

-- Create policies for journal_media table

-- 1. Allow users to view media for journals they can view
CREATE POLICY "Users can view media for journals they can view"
ON public.journal_media
FOR SELECT
USING (
  journal_id IN (
    SELECT id FROM public.journals 
    WHERE profile_id = auth.uid() 
    OR (is_published = true AND is_locked = false)
    OR (is_published = true AND is_locked = true)
  )
);

-- 2. Allow users to insert media for their own journals
CREATE POLICY "Users can insert media for their own journals"
ON public.journal_media
FOR INSERT
WITH CHECK (
  journal_id IN (
    SELECT id FROM public.journals 
    WHERE profile_id = auth.uid()
  )
);

-- 3. Allow users to update media for their own journals
CREATE POLICY "Users can update media for their own journals"
ON public.journal_media
FOR UPDATE
USING (
  journal_id IN (
    SELECT id FROM public.journals 
    WHERE profile_id = auth.uid()
  )
)
WITH CHECK (
  journal_id IN (
    SELECT id FROM public.journals 
    WHERE profile_id = auth.uid()
  )
);

-- 4. Allow users to delete media for their own journals
CREATE POLICY "Users can delete media for their own journals"
ON public.journal_media
FOR DELETE
USING (
  journal_id IN (
    SELECT id FROM public.journals 
    WHERE profile_id = auth.uid()
  )
);

-- Also check if post_reactions and follows tables need RLS
DO $$
BEGIN
  -- Check if post_reactions table exists and needs RLS
  IF EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'post_reactions'
  ) THEN
    -- Enable RLS if it's not already enabled
    IF NOT EXISTS (
      SELECT FROM pg_tables 
      WHERE schemaname = 'public' 
      AND tablename = 'post_reactions' 
      AND rowsecurity = true
    ) THEN
      ALTER TABLE public.post_reactions ENABLE ROW LEVEL SECURITY;
      
      -- Create policies for post_reactions
      CREATE POLICY "Users can view all reactions"
      ON public.post_reactions
      FOR SELECT
      TO authenticated
      USING (true);
      
      CREATE POLICY "Users can insert their own reactions"
      ON public.post_reactions
      FOR INSERT
      TO authenticated
      WITH CHECK (user_id = auth.uid());
      
      CREATE POLICY "Users can update their own reactions"
      ON public.post_reactions
      FOR UPDATE
      TO authenticated
      USING (user_id = auth.uid())
      WITH CHECK (user_id = auth.uid());
      
      CREATE POLICY "Users can delete their own reactions"
      ON public.post_reactions
      FOR DELETE
      TO authenticated
      USING (user_id = auth.uid());
    END IF;
  END IF;
  
  -- Check if follows table exists and needs RLS
  IF EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'follows'
  ) THEN
    -- Enable RLS if it's not already enabled
    IF NOT EXISTS (
      SELECT FROM pg_tables 
      WHERE schemaname = 'public' 
      AND tablename = 'follows' 
      AND rowsecurity = true
    ) THEN
      ALTER TABLE public.follows ENABLE ROW LEVEL SECURITY;
      
      -- Create policies for follows
      CREATE POLICY "Users can view all follows"
      ON public.follows
      FOR SELECT
      TO authenticated
      USING (true);
      
      CREATE POLICY "Users can follow others"
      ON public.follows
      FOR INSERT
      TO authenticated
      WITH CHECK (follower_id = auth.uid());
      
      CREATE POLICY "Users can unfollow"
      ON public.follows
      FOR DELETE
      TO authenticated
      USING (follower_id = auth.uid());
    END IF;
  END IF;
END $$;
