import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  <PERSON><PERSON>List,
  TabsTrigger
} from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON><PERSON>3,
  <PERSON><PERSON><PERSON> as Line<PERSON>hartIcon,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Calendar,
  Download,
  Share2,
  RefreshCw
} from 'lucide-react';
import { VoiceMessageProps } from './VoiceMessage';

// Sample data for analytics
const generateSampleData = (messages: VoiceMessageProps[]) => {
  // Engagement by day
  const engagementByDay = [
    { date: '2023-06-01', listens: 24, replies: 5, reactions: 12 },
    { date: '2023-06-02', listens: 18, replies: 3, reactions: 8 },
    { date: '2023-06-03', listens: 32, replies: 7, reactions: 15 },
    { date: '2023-06-04', listens: 45, replies: 12, reactions: 22 },
    { date: '2023-06-05', listens: 38, replies: 8, reactions: 19 },
    { date: '2023-06-06', listens: 29, replies: 6, reactions: 14 },
    { date: '2023-06-07', listens: 36, replies: 9, reactions: 18 },
  ];

  // Engagement by content type
  const engagementByType = [
    { type: 'Voice Only', engagement: 42 },
    { type: 'Voice + Image', engagement: 68 },
    { type: 'Voice + Video', engagement: 85 },
    { type: 'Voice + Multiple Media', engagement: 92 },
  ];

  // Listener retention
  const listenerRetention = [
    { segment: '0-25%', percentage: 100 },
    { segment: '25-50%', percentage: 82 },
    { segment: '50-75%', percentage: 65 },
    { segment: '75-100%', percentage: 48 },
  ];

  // Reaction types
  const reactionTypes = [
    { type: 'Laugh', count: 45 },
    { type: 'Applause', count: 32 },
    { type: 'Agree', count: 28 },
    { type: 'Disagree', count: 15 },
    { type: 'Love', count: 38 },
    { type: 'Fire', count: 22 },
    { type: 'Wow', count: 19 },
  ];

  // Top performing content
  const topContent = messages.slice(0, 5).map((message, index) => ({
    id: message.id,
    transcript: message.transcript.substring(0, 50) + (message.transcript.length > 50 ? '...' : ''),
    engagement: 95 - (index * 15),
    listens: 120 - (index * 20),
    replies: 25 - (index * 5),
    reactions: 40 - (index * 8),
  }));

  return {
    engagementByDay,
    engagementByType,
    listenerRetention,
    reactionTypes,
    topContent,
  };
};

interface VoiceAnalyticsDashboardProps {
  messages: VoiceMessageProps[];
  userAddress: string;
}

const VoiceAnalyticsDashboard: React.FC<VoiceAnalyticsDashboardProps> = ({
  messages,
  userAddress
}) => {
  const [timeRange, setTimeRange] = useState('week');
  const [isLoading, setIsLoading] = useState(false);
  const [analyticsData, setAnalyticsData] = useState<any>(null);

  useEffect(() => {
    // Generate sample data on component mount
    loadAnalyticsData();
  }, [messages, timeRange]);

  const loadAnalyticsData = () => {
    setIsLoading(true);

    // Simulate API call delay
    setTimeout(() => {
      const data = generateSampleData(messages);
      setAnalyticsData(data);
      setIsLoading(false);
    }, 1000);
  };

  const handleRefresh = () => {
    loadAnalyticsData();
  };

  const handleDownload = () => {
    // In a real app, this would generate a CSV or PDF report
    alert('Analytics report download started');
  };

  const handleShare = () => {
    // In a real app, this would open a share dialog
    alert('Share analytics dashboard');
  };

  if (!analyticsData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin h-8 w-8 border-4 border-voicechain-purple border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold">Voice Analytics</h2>
        <div className="flex items-center space-x-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Time Range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">Today</SelectItem>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
              <SelectItem value="year">This Year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon" onClick={handleRefresh}>
            <RefreshCw size={16} />
          </Button>
          <Button variant="outline" size="icon" onClick={handleDownload}>
            <Download size={16} />
          </Button>
          <Button variant="outline" size="icon" onClick={handleShare}>
            <Share2 size={16} />
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Listens</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.engagementByDay.reduce((sum: number, day: any) => sum + day.listens, 0)}</div>
            <p className="text-xs text-muted-foreground">+12% from last period</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Replies</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.engagementByDay.reduce((sum: number, day: any) => sum + day.replies, 0)}</div>
            <p className="text-xs text-muted-foreground">+8% from last period</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Reactions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.engagementByDay.reduce((sum: number, day: any) => sum + day.reactions, 0)}</div>
            <p className="text-xs text-muted-foreground">+15% from last period</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="engagement">
        <TabsList>
          <TabsTrigger value="engagement" className="flex items-center gap-1">
            <LineChartIcon size={14} />
            <span>Engagement</span>
          </TabsTrigger>
          <TabsTrigger value="content" className="flex items-center gap-1">
            <BarChart3 size={14} />
            <span>Content</span>
          </TabsTrigger>
          <TabsTrigger value="audience" className="flex items-center gap-1">
            <PieChartIcon size={14} />
            <span>Audience</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="engagement" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Engagement Over Time</CardTitle>
              <CardDescription>Listens, replies, and reactions over the past week</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-72 flex flex-col justify-center items-center bg-secondary/30 rounded-lg p-4">
                <div className="text-center mb-4">
                  <p className="text-sm text-muted-foreground">Engagement data visualization</p>
                </div>
                <div className="w-full">
                  {analyticsData.engagementByDay.map((day: any, index: number) => (
                    <div key={index} className="flex items-center mb-2">
                      <div className="w-24 text-xs">{day.date}</div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <div className="h-2 bg-blue-500 rounded" style={{ width: `${day.listens * 2}px` }}></div>
                          <span className="text-xs">{day.listens} listens</span>
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                          <div className="h-2 bg-green-500 rounded" style={{ width: `${day.replies * 4}px` }}></div>
                          <span className="text-xs">{day.replies} replies</span>
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                          <div className="h-2 bg-purple-500 rounded" style={{ width: `${day.reactions * 3}px` }}></div>
                          <span className="text-xs">{day.reactions} reactions</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Listener Retention</CardTitle>
              <CardDescription>Percentage of listeners who stay through each segment</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-72 flex flex-col justify-center items-center bg-secondary/30 rounded-lg p-4">
                <div className="text-center mb-4">
                  <p className="text-sm text-muted-foreground">Listener retention visualization</p>
                </div>
                <div className="w-full">
                  {analyticsData.listenerRetention.map((segment: any, index: number) => (
                    <div key={index} className="flex items-center mb-4">
                      <div className="w-24 text-xs">{segment.segment}</div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <div className="h-6 bg-purple-500 rounded" style={{ width: `${segment.percentage * 2}px` }}></div>
                          <span className="text-xs font-medium">{segment.percentage}%</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Engagement by Content Type</CardTitle>
              <CardDescription>How different types of content perform</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-72 flex flex-col justify-center items-center bg-secondary/30 rounded-lg p-4">
                <div className="text-center mb-4">
                  <p className="text-sm text-muted-foreground">Content type engagement visualization</p>
                </div>
                <div className="w-full">
                  {analyticsData.engagementByType.map((item: any, index: number) => (
                    <div key={index} className="flex items-center mb-4">
                      <div className="w-40 text-xs">{item.type}</div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <div className="h-6 bg-purple-500 rounded" style={{ width: `${item.engagement * 2}px` }}></div>
                          <span className="text-xs font-medium">{item.engagement} points</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Reaction Types</CardTitle>
              <CardDescription>Distribution of reaction types on your content</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-72 flex flex-col justify-center items-center bg-secondary/30 rounded-lg p-4">
                <div className="text-center mb-4">
                  <p className="text-sm text-muted-foreground">Reaction types visualization</p>
                </div>
                <div className="w-full grid grid-cols-2 gap-4">
                  {analyticsData.reactionTypes.map((reaction: any, index: number) => {
                    const colors = ["bg-blue-500", "bg-cyan-500", "bg-indigo-500", "bg-violet-500", "bg-purple-500", "bg-fuchsia-500", "bg-pink-500"];
                    const color = colors[index % colors.length];
                    return (
                      <div key={index} className="flex flex-col items-center">
                        <div className={`w-16 h-16 rounded-full ${color} flex items-center justify-center mb-2`}>
                          <span className="text-white font-bold">{reaction.count}</span>
                        </div>
                        <span className="text-xs">{reaction.type}</span>
                      </div>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="audience" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Top Performing Content</CardTitle>
              <CardDescription>Your most engaging voice posts</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.topContent.map((content: any, index: number) => (
                  <div key={index} className="flex items-center justify-between border-b pb-3">
                    <div className="flex-1">
                      <p className="text-sm font-medium">{content.transcript}</p>
                      <div className="flex items-center gap-4 mt-1">
                        <span className="text-xs text-muted-foreground">{content.listens} listens</span>
                        <span className="text-xs text-muted-foreground">{content.replies} replies</span>
                        <span className="text-xs text-muted-foreground">{content.reactions} reactions</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-bold">{content.engagement}%</div>
                      <div className="text-xs text-muted-foreground">engagement</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default VoiceAnalyticsDashboard;
