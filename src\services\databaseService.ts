
import { supabase } from '@/integrations/supabase/client';
import { VoiceMessageProps } from '@/types/voice-message';
import { JournalEntry } from '@/pages/JournalDetail';
import { loadRepliesForPost, replyToVoiceMessage } from './replyService';
import { toast } from '@/components/ui/sonner';
import { v4 as uuidv4 } from 'uuid';

/**
 * Get all voice messages from the database
 * @returns Array of voice messages
 */
export async function getVoiceMessages(): Promise<VoiceMessageProps[]> {
  try {
    console.log('Fetching voice messages and reposts from database');

    // Get original voice messages
    const { data: messagesData, error: messagesError } = await supabase
      .from('voice_messages')
      .select(`
        *,
        voice_message_media(*),
        profiles(wallet_address)
      `)
      .is('deleted_at', null)
      .order('created_at', { ascending: false });

    if (messagesError) {
      throw messagesError;
    }

    // Get reposts with original message data using RPC function to handle type casting
    const { data: repostsData, error: repostsError } = await supabase
      .rpc('get_reposts_with_messages');

    if (repostsError) {
      console.warn('Error fetching reposts:', repostsError);
    }

    // Load replies for all messages using the new reply service
    const messageIds = (messagesData || []).map(msg => msg.id);
    const repliesByParent = new Map<string, VoiceMessageProps[]>();

    // Load replies for each message
    for (const messageId of messageIds) {
      try {
        const replies = await loadRepliesForPost(messageId);
        if (replies.length > 0) {
          repliesByParent.set(messageId, replies.map(replyToVoiceMessage));
        }
      } catch (error) {
        console.warn(`Error loading replies for message ${messageId}:`, error);
      }
    }

    // Transform original messages with replies
    const originalMessages: VoiceMessageProps[] = (messagesData || [])
      .filter(message => !message.parent_id) // Only get top-level messages
      .map(message => ({
        id: message.id,
        userAddress: message.profiles?.wallet_address || message.profile_id,
        audioUrl: message.audio_url,
        transcript: message.transcript || '',
        timestamp: new Date(message.created_at),
        duration: Number(message.audio_duration) || 0,
        isPinned: Boolean(message.is_pinned),
        media: message.voice_message_media?.map(media => ({
          id: media.id,
          url: media.url,
          type: media.type
        })) || [],
        replies: repliesByParent.get(message.id) || [],
        isRepost: false
      }));

    // Transform reposts to look like messages but with repost attribution
    const repostMessages: VoiceMessageProps[] = (repostsData || []).map(repost => ({
      id: `repost-${repost.repost_id}`, // Unique ID for the repost
      originalId: repost.vm_id, // Keep reference to original
      userAddress: repost.reposter_wallet_address || repost.repost_user_id, // Reposter's address
      audioUrl: repost.vm_audio_url,
      transcript: repost.vm_transcript || '',
      timestamp: new Date(repost.repost_created_at), // Use repost timestamp for feed ordering
      duration: Number(repost.vm_audio_duration) || 0,
      isPinned: Boolean(repost.vm_is_pinned),
      media: [], // TODO: Add media support for reposts
      replies: [],
      isRepost: true,
      originalUserAddress: repost.original_wallet_address || repost.vm_profile_id,
      repostTimestamp: new Date(repost.repost_created_at)
    }));

    // Combine and sort by timestamp (most recent first)
    const allMessages = [...originalMessages, ...repostMessages].sort((a, b) =>
      b.timestamp.getTime() - a.timestamp.getTime()
    );

    console.log(`Fetched ${originalMessages.length} original messages and ${repostMessages.length} reposts`);
    return allMessages;
  } catch (error) {
    console.error('Error fetching voice messages:', error);
    return [];
  }
}

/**
 * Get voice messages by a specific user
 * @param walletAddress The user's wallet address
 * @returns Array of voice messages
 */
export async function getPostsByWalletAddress(walletAddress: string): Promise<VoiceMessageProps[]> {
  try {
    console.log(`Fetching voice messages for user ${walletAddress}`);

    // First, get the profile ID from the wallet address (case-insensitive)
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('id, wallet_address')
      .ilike('wallet_address', walletAddress)
      .single();

    if (profileError) {
      console.error('Error getting profile ID for wallet address:', profileError);
      return [];
    }

    if (!profileData) {
      console.log('No profile found for wallet address:', walletAddress);
      return [];
    }

    const profileId = profileData.id;
    console.log(`Found profile ID ${profileId} for wallet address ${walletAddress}`);

    const { data, error } = await supabase
      .rpc('get_user_voice_messages_text', { user_id_param: profileId });

    if (error) {
      console.error('Error calling get_user_voice_messages_text function:', error);

      // Fallback to direct query using the profile ID
      const { data: fallbackData, error: fallbackError } = await supabase
        .from('voice_messages')
        .select('*, voice_message_media(*)')
        .eq('profile_id', profileId)
        .is('deleted_at', null)
        .order('created_at', { ascending: false });

      if (fallbackError) {
        throw fallbackError;
      }

      console.log(`Fallback query found ${fallbackData?.length || 0} messages for profile ID ${profileId}`);

      // Transform database records to VoiceMessageProps
      return (fallbackData || []).map(message => ({
        id: message.id,
        userAddress: walletAddress, // Use the wallet address for consistency
        audioUrl: message.audio_url,
        transcript: message.transcript || '',
        timestamp: new Date(message.created_at),
        duration: Number(message.audio_duration) || 0,
        isPinned: Boolean(message.is_pinned),
        media: message.voice_message_media?.map(media => ({
          url: media.url,
          type: media.type
        })) || [],
        replies: [] // Initialize with empty replies
      }));
    }

    console.log(`RPC query found ${data?.length || 0} messages for profile ID ${profileId}`);

    // Transform RPC data to VoiceMessageProps
    const messages: VoiceMessageProps[] = await Promise.all((data || []).map(async message => {
      // Fetch media for each message directly from the table
      let mediaItems = [];
      try {
        const { data: mediaData } = await supabase
          .from('voice_message_media')
          .select('url, type')
          .eq('voice_message_id', message.id);

        mediaItems = (mediaData || []).map((media: any) => ({
          url: media.url,
          type: media.type
        }));
      } catch (mediaError) {
        console.warn(`Error fetching media for message ${message.id}:`, mediaError);
      }

      return {
        id: message.id,
        userAddress: walletAddress, // Use the wallet address for consistency
        audioUrl: message.audio_url,
        transcript: message.transcript || '',
        timestamp: new Date(message.created_at),
        duration: Number(message.audio_duration) || 0,
        isPinned: Boolean(message.is_pinned),
        media: mediaItems,
        replies: [] // Initialize with empty replies
      };
    }));

    return messages;
  } catch (error) {
    console.error(`Error fetching voice messages for user ${walletAddress}:`, error);
    return [];
  }
}

/**
 * Get all journals from the database
 * @returns Array of journal entries
 */
export async function getJournals(): Promise<JournalEntry[]> {
  try {
    console.log('Fetching journals from database');

    const { data, error } = await supabase
      .from('journals')
      .select('*, journal_media(*)')
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    // Transform database records to JournalEntry
    const journals: JournalEntry[] = (data || []).map(journal => ({
      id: journal.id,
      userAddress: journal.profile_id,
      title: journal.title,
      transcript: journal.transcript || '',
      audioUrl: journal.audio_url,
      duration: journal.audio_duration || 0,
      createdAt: new Date(journal.created_at),
      scheduledFor: journal.scheduled_for ? new Date(journal.scheduled_for) : undefined,
      isPublished: journal.is_published !== false, // Default to true
      isLocked: Boolean(journal.is_locked),
      unlockCondition: journal.unlock_condition || { type: 'date', value: '' },
      media: journal.journal_media?.map((media: any) => ({
        url: media.url,
        type: media.type
      })) || []
    }));

    return journals;
  } catch (error) {
    console.error('Error fetching journals:', error);
    return [];
  }
}

/**
 * Get journals by a specific user
 * @param walletAddress The user's wallet address
 * @returns Array of journal entries
 */
export async function getJournalsByWalletAddress(walletAddress: string): Promise<JournalEntry[]> {
  try {
    console.log(`Fetching journals for user ${walletAddress}`);

    const { data, error } = await supabase
      .rpc('get_user_journals_text', { user_id_param: walletAddress.toLowerCase() });

    if (error) {
      console.error('Error calling get_user_journals_text function:', error);

      // Fallback to direct query
      const { data: fallbackData, error: fallbackError } = await supabase
        .from('journals')
        .select('*, journal_media(*)')
        .eq('profile_id', walletAddress.toLowerCase())
        .order('created_at', { ascending: false });

      if (fallbackError) {
        throw fallbackError;
      }

      // Transform database records to JournalEntry
      return (fallbackData || []).map(journal => ({
        id: journal.id,
        userAddress: journal.profile_id,
        title: journal.title,
        transcript: journal.transcript || '',
        audioUrl: journal.audio_url,
        duration: journal.audio_duration || 0,
        createdAt: new Date(journal.created_at),
        scheduledFor: journal.scheduled_for ? new Date(journal.scheduled_for) : undefined,
        isPublished: journal.is_published !== false, // Default to true
        isLocked: Boolean(journal.is_locked),
        unlockCondition: journal.unlock_condition || { type: 'date', value: '' },
        media: journal.journal_media?.map((media: any) => ({
          url: media.url,
          type: media.type
        })) || []
      }));
    }

    // Transform RPC data to JournalEntry
    const journals: JournalEntry[] = await Promise.all((data || []).map(async journal => {
      // Process unlock condition to ensure it's in the right format
      let unlockCondition = { type: 'date', value: '' };
      if (journal.unlock_condition) {
        try {
          if (typeof journal.unlock_condition === 'string') {
            const parsed = JSON.parse(journal.unlock_condition);
            unlockCondition = {
              type: parsed.type || 'date',
              value: parsed.value || ''
            };
          } else if (typeof journal.unlock_condition === 'object') {
            unlockCondition = {
              type: (journal.unlock_condition.type as string) || 'date',
              value: (journal.unlock_condition.value as string) || ''
            };
          }
        } catch (e) {
          console.warn('Error parsing unlock condition:', e);
        }
      }

      // Get journal media
      let mediaItems = [];
      try {
        const { data: mediaData } = await supabase
          .from('journal_media')
          .select('*')
          .eq('journal_id', journal.id);

        mediaItems = (mediaData || []).map(media => ({
          url: media.url,
          type: media.type
        }));
      } catch (mediaError) {
        console.warn(`Error fetching media for journal ${journal.id}:`, mediaError);
      }

      return {
        id: journal.id,
        userAddress: journal.profile_id,
        title: journal.title,
        transcript: journal.transcript || '',
        audioUrl: journal.audio_url,
        duration: Number(journal.audio_duration) || 0,
        createdAt: new Date(journal.created_at),
        scheduledFor: journal.scheduled_for ? new Date(journal.scheduled_for) : undefined,
        isPublished: journal.is_published !== false, // Default to true
        isLocked: Boolean(journal.is_locked),
        unlockCondition: unlockCondition,
        media: mediaItems
      };
    }));

    return journals;
  } catch (error) {
    console.error(`Error fetching journals for user ${walletAddress}:`, error);
    return [];
  }
}

/**
 * Initialize database storage buckets for media files
 */
export async function initializeStorageBuckets(): Promise<void> {
  try {
    // Get list of buckets
    const { data: buckets, error } = await supabase.storage.listBuckets();

    if (error) {
      console.error('Error listing buckets:', error);
      return;
    }

    // Define required buckets
    const requiredBuckets = [
      {
        name: 'audio',
        public: true,
        fileSizeLimit: 50000000, // 50MB
        allowedMimeTypes: ['audio/webm', 'audio/mp3', 'audio/mp4', 'audio/mpeg', 'audio/wav', 'audio/ogg']
      },
      {
        name: 'profiles',
        public: true,
        fileSizeLimit: 5000000, // 5MB
        allowedMimeTypes: ['image/png', 'image/jpeg', 'image/gif', 'image/webp']
      }
    ];

    // Check if buckets exist first
    for (const bucket of requiredBuckets) {
      // First check if the bucket already exists
      const { data: existingBucket, error: checkError } = await supabase.storage.getBucket(bucket.name);

      if (checkError && checkError.message !== 'The resource was not found') {
        console.warn(`Error checking if ${bucket.name} bucket exists:`, checkError);
      }

      // Only create the bucket if it doesn't exist
      if (!existingBucket) {
        try {
          console.log(`Attempting to create ${bucket.name} bucket...`);

          // Use direct SQL query to create bucket to bypass RLS
          const { error: sqlError } = await supabase.rpc('create_storage_bucket', {
            bucket_name: bucket.name,
            is_public: bucket.public
          });

          if (sqlError) {
            console.warn(`Could not create ${bucket.name} bucket using RPC:`, sqlError);

            // Fallback to regular method
            try {
              const { error: createError } = await supabase.storage.createBucket(bucket.name, {
                public: bucket.public,
                fileSizeLimit: bucket.fileSizeLimit,
                allowedMimeTypes: bucket.allowedMimeTypes
              });

              if (createError) {
                console.warn(`Could not create ${bucket.name} bucket:`, createError);
              } else {
                console.log(`Created ${bucket.name} bucket successfully`);
              }
            } catch (createErr) {
              console.warn(`Error creating ${bucket.name} bucket:`, createErr);
            }
          } else {
            console.log(`Created ${bucket.name} bucket successfully using RPC`);
          }
        } catch (err) {
          console.warn(`Error creating ${bucket.name} bucket:`, err);
        }
      } else {
        console.log(`${bucket.name} bucket already exists`);
      }
    }
  } catch (error) {
    console.error('Error initializing storage buckets:', error);
  }
}

// Export all functions
const databaseService = {
  getVoiceMessages,
  getPostsByWalletAddress,
  getJournals,
  getJournalsByWalletAddress,
  initializeStorageBuckets
};

export default databaseService;
