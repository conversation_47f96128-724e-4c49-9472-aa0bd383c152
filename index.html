
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
  <title>Audra - Voice Web3 Social Platform</title>
  <meta name="description" content="A decentralized voice-first social platform built on Web3 technology. Share voice posts, create journals, and connect with your community." />
  <meta name="author" content="Audra Team" />
  <meta name="keywords" content="voice, web3, social, blockchain, decentralized, audio, posts, journals" />

  <!-- PWA Meta Tags -->
  <meta name="application-name" content="Audra" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
  <meta name="apple-mobile-web-app-title" content="Audra" />
  <meta name="mobile-web-app-capable" content="yes" />
  <meta name="msapplication-TileColor" content="#0a0a0a" />
  <meta name="msapplication-tap-highlight" content="no" />
  <meta name="theme-color" content="#0a0a0a" />

  <!-- Manifest -->
  <link rel="manifest" href="/manifest.json" />

  <!-- Icons -->
  <link rel="icon" href="/favicon.ico" />
  <!-- Emoji favicon for modern browsers -->
  <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎤</text></svg>" />
  <!-- Apple touch icons using emoji fallback -->
  <link rel="apple-touch-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 180 180'><rect width='180' height='180' fill='%23000'/><text x='90' y='130' font-size='120' text-anchor='middle' fill='white'>🎤</text></svg>" />

  <!-- Apple Splash Screens - Removed missing files -->

  <!-- Open Graph -->
  <meta property="og:title" content="Audra - Voice Web3 Social Platform" />
  <meta property="og:description" content="A decentralized voice-first social platform built on Web3 technology. Share voice posts, create journals, and connect with your community." />
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://audra.app" />
  <meta property="og:site_name" content="Audra" />

  <!-- Twitter -->
  <meta name="twitter:card" content="summary" />
  <meta name="twitter:site" content="@audra_app" />
  <meta name="twitter:title" content="Audra - Voice Web3 Social Platform" />
  <meta name="twitter:description" content="A decentralized voice-first social platform built on Web3 technology." />

  <!-- Preload critical resources -->
  <link rel="preload" href="/fonts/inter.woff2" as="font" type="font/woff2" crossorigin />

  <!-- DNS prefetch for external resources -->
  <link rel="dns-prefetch" href="//supabase.co" />
  <link rel="dns-prefetch" href="//fonts.googleapis.com" />
</head>

<body>
  <div id="root"></div>
  <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
  <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>

  <!-- Node.js polyfills for browser environment -->
  <script>
    // Define global
    window.global = window;

    // Define process
    window.process = {
      env: {
        NODE_ENV: 'production',
        BROWSER: true
      },
      browser: true,
      nextTick: function (fn) { setTimeout(fn, 0); }
    };

    // Prepare for Buffer
    window.Buffer = window.Buffer || {};
  </script>

  <script type="module" src="/src/main.tsx"></script>
</body>

</html>
