/**
 * Disabled Orbis Client
 * This is a stub implementation that doesn't actually connect to Orbis or Ceramic
 * It's used to prevent errors when Orbis/Ceramic is not available
 */

import { toast } from '@/components/ui/sonner';
import { UserProfile } from '@/types/user-profile';
import * as simpleProfileService from './simpleProfileService';

// Mock Orbis class
export class Orbis {
  _apiUrl: string;
  did: any;

  constructor() {
    this._apiUrl = 'disabled';
    console.log('Created disabled Orbis client (stub implementation)');
  }

  // Add methods that might be called
  async connect() {
    console.warn('Orbis is disabled - connect called but will return a mock response');
    return {
      status: 200,
      did: 'disabled-orbis-did',
      details: {
        did: 'disabled-orbis-did',
        profile: null
      }
    };
  }

  async isConnected() {
    console.warn('Orbis is disabled - isConnected called but will return a mock response');
    return {
      status: 200,
      did: 'disabled-orbis-did'
    };
  }

  async updateProfile(profile: any) {
    console.warn('Orbis is disabled - updateProfile called but will return a mock response');
    
    // Try to update the profile in our simple profile service
    try {
      const address = profile.address || profile.walletAddress || localStorage.getItem('connectedAccount');
      if (address) {
        const normalizedAddress = address.toLowerCase();
        
        // Create a profile update object
        const profileUpdate = {
          username: profile.username,
          displayName: profile.displayName,
          bio: profile.description,
          profileImageUrl: profile.pfp,
          coverImageUrl: profile.cover,
          socialLinks: profile.data?.socialLinks,
          stats: profile.data?.stats
        };
        
        // Update the profile
        await simpleProfileService.updateProfile(normalizedAddress, profileUpdate);
        
        console.log('Profile updated in simple profile service');
      }
    } catch (error) {
      console.error('Error updating profile in simple profile service:', error);
    }
    
    return {
      status: 200,
      doc: {
        did: 'disabled-orbis-did',
        profile: profile
      }
    };
  }

  async getProfile(didOrAddress: string) {
    console.warn('Orbis is disabled - getProfile called but will use simple profile service');
    
    try {
      // Try to get the profile from our simple profile service
      const profile = await simpleProfileService.getProfileByAddress(didOrAddress);
      
      if (profile) {
        console.log('Found profile in simple profile service:', profile);
        
        // Convert to Orbis format
        const orbisProfile = {
          did: 'disabled-orbis-did',
          address: profile.address,
          username: profile.username,
          displayName: profile.displayName,
          description: profile.bio,
          pfp: profile.profileImageUrl,
          cover: profile.coverImageUrl,
          data: {
            socialLinks: profile.socialLinks,
            stats: profile.stats,
            joinedDate: profile.joinedDate instanceof Date ? profile.joinedDate.toISOString() : profile.joinedDate,
            verification: profile.verification
          }
        };
        
        return {
          data: orbisProfile,
          error: null
        };
      }
      
      console.log('No profile found in simple profile service for:', didOrAddress);
      return {
        data: null,
        error: null
      };
    } catch (error) {
      console.error('Error getting profile from simple profile service:', error);
      return {
        data: null,
        error: 'Failed to get profile'
      };
    }
  }

  async createPost(content: any) {
    console.warn('Orbis is disabled - createPost called but will return a mock response');
    return {
      status: 200,
      doc: {
        content: content,
        stream_id: `disabled-post-${Date.now()}`,
        timestamp: Date.now()
      }
    };
  }
}

// Create a disabled Orbis instance
export const orbis = new Orbis();

/**
 * Connect to Orbis with the user's wallet
 * This is a stub implementation that doesn't actually connect to Orbis
 * @param forceConnect Force connection even if auto-connect is disabled
 * @returns A mock DID
 */
export async function connectOrbis(forceConnect: boolean = false): Promise<string | null> {
  console.warn('Orbis is disabled - connectOrbis called but will return a mock DID');
  return 'disabled-orbis-did';
}

/**
 * Check if the user is connected to Orbis
 * This is a stub implementation that always returns true
 * @returns Always true
 */
export async function isConnectedToOrbis(): Promise<boolean> {
  console.warn('Orbis is disabled - isConnectedToOrbis called but will return true');
  return true;
}

/**
 * Get the current user's DID
 * This is a stub implementation that returns a mock DID
 * @returns A mock DID
 */
export async function getCurrentUserDid(): Promise<string | null> {
  console.warn('Orbis is disabled - getCurrentUserDid called but will return a mock DID');
  return 'disabled-orbis-did';
}

/**
 * Create or update a user profile in Orbis
 * This is a stub implementation that uses our simple profile service
 * @param profile The user profile data
 * @returns A mock response
 */
export async function createOrUpdateProfile(profile: UserProfile): Promise<any | null> {
  console.warn('Orbis is disabled - createOrUpdateProfile called but will use simple profile service');
  
  try {
    // Try to update the profile in our simple profile service
    const updatedProfile = await simpleProfileService.updateProfile(profile.address, profile);
    
    return {
      did: 'disabled-orbis-did',
      profile: updatedProfile
    };
  } catch (error) {
    console.error('Error updating profile in simple profile service:', error);
    toast.error('Error updating profile. Please try again.');
    return null;
  }
}

/**
 * Get a user profile from Orbis
 * This is a stub implementation that uses our simple profile service
 * @param did The user's DID or address
 * @returns The profile or null if not found
 */
export async function getProfile(did: string): Promise<UserProfile | null> {
  console.warn('Orbis is disabled - getProfile called but will use simple profile service');
  
  try {
    // Try to get the profile from our simple profile service
    return await simpleProfileService.getProfileByAddress(did);
  } catch (error) {
    console.error('Error getting profile from simple profile service:', error);
    return null;
  }
}

/**
 * Create a post in Orbis
 * This is a stub implementation that returns a mock response
 * @param content The post content
 * @param context The context (channel) to post in
 * @returns A mock response
 */
export async function createPost(content: any, context?: string): Promise<any | null> {
  console.warn('Orbis is disabled - createPost called but will return a mock response');
  
  return {
    stream_id: `disabled-post-${Date.now()}`,
    content: content
  };
}
