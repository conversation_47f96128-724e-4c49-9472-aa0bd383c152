import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { toast } from '@/components/ui/sonner';
import storageServiceFactory from '@/services/storageServiceFactory';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';

interface StorageSettingsProps {
  onClose?: () => void;
}

export function StorageSettings({ onClose }: StorageSettingsProps) {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [useBlockchain, setUseBlockchain] = useState<boolean>(true);
  const [storageProvider, setStorageProvider] = useState<'thirdweb' | 'nftstorage'>('thirdweb');

  // Load settings from Supabase on mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        setIsLoading(true);

        // Get blockchain storage setting
        const { data: blockchainData, error: blockchainError } = await supabase
          .from('user_settings')
          .select('value')
          .eq('key', 'useBlockchainStorage')
          .eq('user_id', user?.id || 'global')
          .maybeSingle();

        if (blockchainError) {
          console.error('Error loading blockchain storage setting:', blockchainError);
        } else if (blockchainData) {
          setUseBlockchain(blockchainData.value !== false);
        }

        // Get storage provider setting
        const { data: providerData, error: providerError } = await supabase
          .from('user_settings')
          .select('value')
          .eq('key', 'storageProvider')
          .eq('user_id', user?.id || 'global')
          .maybeSingle();

        if (providerError) {
          console.error('Error loading storage provider setting:', providerError);
        } else if (providerData) {
          setStorageProvider(providerData.value as 'thirdweb' | 'nftstorage');
        }
      } catch (error) {
        console.error('Error loading storage settings:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, [user]);

  // Handle save button click
  const handleSave = async () => {
    try {
      // Save blockchain storage setting
      const { error: blockchainError } = await supabase
        .from('user_settings')
        .upsert({
          key: 'useBlockchainStorage',
          user_id: user?.id || 'global',
          value: useBlockchain,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'key,user_id'
        });

      if (blockchainError) {
        console.error('Error saving blockchain storage setting:', blockchainError);
        toast.error('Failed to save blockchain storage setting');
        return;
      }

      // Save storage provider setting
      const { error: providerError } = await supabase
        .from('user_settings')
        .upsert({
          key: 'storageProvider',
          user_id: user?.id || 'global',
          value: storageProvider,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'key,user_id'
        });

      if (providerError) {
        console.error('Error saving storage provider setting:', providerError);
        toast.error('Failed to save storage provider setting');
        return;
      }

      // Force blockchain storage if enabled
      if (useBlockchain) {
        storageServiceFactory.forceBlockchainStorage(true);
      } else {
        storageServiceFactory.forceBlockchainStorage(false);
      }

      toast.success('Storage settings saved successfully');

      if (onClose) {
        onClose();
      }
    } catch (error) {
      console.error('Error saving storage settings:', error);
      toast.error('Failed to save storage settings');
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Storage Settings</CardTitle>
        <CardDescription>
          Configure how your voice recordings and media are stored
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Blockchain Storage Toggle */}
        <div className="flex items-center justify-between space-x-2">
          <Label htmlFor="use-blockchain" className="flex flex-col space-y-1">
            <span>Use Decentralized Storage</span>
            <span className="text-sm text-muted-foreground">
              Store your content on decentralized networks instead of centralized servers
            </span>
          </Label>
          <Switch
            id="use-blockchain"
            checked={useBlockchain}
            onCheckedChange={setUseBlockchain}
          />
        </div>

        {/* Storage Provider Selection */}
        {useBlockchain && (
          <div className="space-y-3">
            <Label>Storage Provider</Label>
            <RadioGroup
              value={storageProvider}
              onValueChange={(value) => setStorageProvider(value as 'thirdweb' | 'nftstorage')}
              className="space-y-2"
            >
              <div className="flex items-start space-x-2">
                <RadioGroupItem value="thirdweb" id="thirdweb" />
                <div className="grid gap-1">
                  <Label htmlFor="thirdweb" className="font-medium">
                    Thirdweb Storage
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Reliable decentralized storage with good performance and reliability
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-2">
                <RadioGroupItem value="nftstorage" id="nftstorage" />
                <div className="grid gap-1">
                  <Label htmlFor="nftstorage" className="font-medium">
                    NFT.Storage
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Free decentralized storage backed by Protocol Labs and Filecoin
                  </p>
                </div>
              </div>
            </RadioGroup>
          </div>
        )}

        {/* Storage Information */}
        <div className="rounded-md bg-muted p-4 text-sm">
          <h4 className="font-medium mb-2">About Decentralized Storage</h4>
          <p className="text-muted-foreground mb-2">
            Decentralized storage ensures your content is permanently preserved on IPFS
            and accessible from anywhere, even if our servers go down.
          </p>
          <p className="text-muted-foreground">
            <strong>Thirdweb Storage</strong> is recommended for most users due to its reliability and performance.
            No wallet connection is required.
          </p>
        </div>
      </CardContent>

      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button onClick={handleSave}>
          Save Changes
        </Button>
      </CardFooter>
    </Card>
  );
}

export default StorageSettings;
