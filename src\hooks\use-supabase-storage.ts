
import { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/sonner';
import type { J<PERSON> } from '@/integrations/supabase/types';

/**
 * Hook for storing user settings in Supabase
 */
export function useUserSettings() {
  const [isLoading, setIsLoading] = useState(false);

  const getSetting = useCallback(async <T>(key: string, userId: string): Promise<T | null> => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('user_settings')
        .select('value')
        .eq('user_id', userId)
        .eq('key', key)
        .maybeSingle();

      if (error) {
        console.error('Error getting setting:', error);
        return null;
      }

      return data?.value as unknown as T || null;
    } catch (error) {
      console.error('Error getting setting:', error);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const saveSetting = useCallback(async <T>(key: string, value: T, userId: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      // Check if setting exists
      const { data: existingData, error: existingError } = await supabase
        .from('user_settings')
        .select('id')
        .eq('user_id', userId)
        .eq('key', key)
        .maybeSingle();

      if (existingError) {
        console.error('Error checking existing setting:', existingError);
        return false;
      }

      // Convert value to a JSON-compatible type
      const jsonValue = value as unknown as Json;

      if (existingData) {
        // Update existing setting
        const { error: updateError } = await supabase
          .from('user_settings')
          .update({ value: jsonValue })
          .eq('id', existingData.id);

        if (updateError) {
          console.error('Error updating setting:', updateError);
          return false;
        }
      } else {
        // Create new setting
        const { error: insertError } = await supabase
          .from('user_settings')
          .insert({
            user_id: userId,
            key,
            value: jsonValue
          });

        if (insertError) {
          console.error('Error creating setting:', insertError);
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Error saving setting:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const deleteSetting = useCallback(async (key: string, userId: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      const { error } = await supabase
        .from('user_settings')
        .delete()
        .eq('user_id', userId)
        .eq('key', key);

      if (error) {
        console.error('Error deleting setting:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error deleting setting:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    isLoading,
    getSetting,
    saveSetting,
    deleteSetting,
  };
}

/**
 * Hook for handling file uploads to Supabase Storage
 */
export function useSupabaseStorage() {
  const [isUploading, setIsUploading] = useState(false);
  const [progress, setProgress] = useState(0);

  const uploadFile = useCallback(async (file: File, bucket: string, path: string): Promise<string | null> => {
    try {
      setIsUploading(true);
      setProgress(0);
      
      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(path, file, {
          cacheControl: '3600',
          upsert: true,
          onUploadProgress: (event) => {
            const percent = (event.loaded / event.total) * 100;
            setProgress(percent);
          },
        });

      if (error) {
        toast.error(`Upload failed: ${error.message}`);
        return null;
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from(bucket)
        .getPublicUrl(data.path);

      return urlData.publicUrl;
    } catch (error) {
      console.error('Error uploading file:', error);
      toast.error(`Upload failed: ${(error as Error).message}`);
      return null;
    } finally {
      setIsUploading(false);
      setProgress(0);
    }
  }, []);

  const deleteFile = useCallback(async (bucket: string, path: string): Promise<boolean> => {
    try {
      const { error } = await supabase.storage
        .from(bucket)
        .remove([path]);

      if (error) {
        toast.error(`Delete failed: ${error.message}`);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error deleting file:', error);
      toast.error(`Delete failed: ${(error as Error).message}`);
      return false;
    }
  }, []);

  return {
    isUploading,
    progress,
    uploadFile,
    deleteFile,
  };
}
