
import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import App from './App'
import './index.css'

// Import utility functions but avoid calling them immediately to prevent errors
import { ensureStorageBuckets } from './services/supabaseStorageService'
import { createUpdateProfileByAddressFunction } from './services/profileSyncService'

// Initialize Supabase features when the app is ready
window.addEventListener('load', async () => {
  try {
    // Initialize storage buckets
    await ensureStorageBuckets();
    
    // Setup profiles function
    await createUpdateProfileByAddressFunction();
  } catch (error) {
    console.error('Error during app initialization:', error);
  }
});

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
