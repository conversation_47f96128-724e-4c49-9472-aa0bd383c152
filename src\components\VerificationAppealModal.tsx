import React, { useState } from 'react';
import { Dialog, DialogContent, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from '@/components/ui/sonner';
import { Shield, AlertCircle, Check } from 'lucide-react';
import { submitVerificationAppeal } from '@/services/verificationService';
import { VerificationType } from './VerificationBadge';
import { format, addDays } from 'date-fns';

interface VerificationAppealModalProps {
  isOpen: boolean;
  onClose: () => void;
  userAddress: string;
  rejectedApplication?: {
    id: string;
    type: VerificationType;
    rejectedAt: string;
    reason?: string;
  };
}

const VerificationAppealModal: React.FC<VerificationAppealModalProps> = ({
  isOpen,
  onClose,
  userAddress,
  rejectedApplication
}) => {
  const [appealReason, setAppealReason] = useState('');
  const [additionalInfo, setAdditionalInfo] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Calculate when the user can appeal (7 days after rejection)
  const rejectedDate = rejectedApplication?.rejectedAt ? new Date(rejectedApplication.rejectedAt) : new Date();
  const appealAvailableDate = addDays(rejectedDate, 7);
  const canAppealNow = new Date() >= appealAvailableDate;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!appealReason.trim()) {
      toast.error('Please provide a reason for your appeal');
      return;
    }

    setIsSubmitting(true);

    try {
      await submitVerificationAppeal(
        userAddress,
        rejectedApplication?.id || '',
        rejectedApplication?.type || 'creator',
        appealReason,
        additionalInfo
      );

      toast.success('Verification appeal submitted successfully');
      onClose();

      // Reset form
      setAppealReason('');
      setAdditionalInfo('');
    } catch (error) {
      console.error('Error submitting verification appeal:', error);
      
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error('Failed to submit verification appeal');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5 text-primary" />
          Appeal Verification Rejection
        </DialogTitle>
        <DialogDescription>
          Explain why you believe your verification application should be reconsidered.
          Appeals are reviewed by our team with additional scrutiny.
        </DialogDescription>

        {!canAppealNow ? (
          <div className="bg-amber-50 p-4 rounded-md my-4">
            <div className="flex items-start">
              <AlertCircle className="h-5 w-5 text-amber-500 shrink-0 mt-0.5 mr-2" />
              <div>
                <h4 className="font-medium text-amber-800">Appeal Not Available Yet</h4>
                <p className="text-sm mt-1 text-amber-700">
                  You can appeal this rejection on {format(appealAvailableDate, 'MMMM d, yyyy')}.
                  We require a 7-day waiting period between rejection and appeal.
                </p>
              </div>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="bg-secondary/30 p-3 rounded-md mb-4">
              <h4 className="text-sm font-medium mb-1 flex items-center">
                <Shield className="h-4 w-4 mr-1 text-voicechain-accent" />
                Appeal Process
              </h4>
              <ol className="text-xs text-muted-foreground space-y-1 ml-5 list-decimal">
                <li>Submit your appeal with a clear explanation</li>
                <li>Provide any additional information or evidence</li>
                <li>Our team will review your appeal (typically within 3-5 days)</li>
                <li>You'll receive a notification with the final decision</li>
              </ol>
            </div>

            {rejectedApplication?.reason && (
              <div className="space-y-2">
                <Label>Original Rejection Reason</Label>
                <div className="p-3 bg-red-50 rounded-md text-sm text-red-800">
                  {rejectedApplication.reason}
                </div>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="appealReason">Reason for Appeal <span className="text-red-500">*</span></Label>
              <Textarea
                id="appealReason"
                placeholder="Explain why you believe your verification application should be reconsidered..."
                value={appealReason}
                onChange={(e) => setAppealReason(e.target.value)}
                className="min-h-[100px]"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="additionalInfo">Additional Information</Label>
              <Textarea
                id="additionalInfo"
                placeholder="Provide any additional information, evidence, or links that support your appeal..."
                value={additionalInfo}
                onChange={(e) => setAdditionalInfo(e.target.value)}
                className="min-h-[80px]"
              />
              <p className="text-xs text-muted-foreground">
                Include links to social media profiles, websites, or other evidence that supports your appeal.
              </p>
            </div>

            <DialogFooter className="sticky bottom-0 bg-background pt-2 pb-1 border-t mt-4">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting} className="bg-voicechain-purple hover:bg-voicechain-accent">
                {isSubmitting ? (
                  <span className="flex items-center gap-1">
                    <span className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                    Submitting...
                  </span>
                ) : (
                  <span className="flex items-center gap-1">
                    <Check className="h-4 w-4" />
                    Submit Appeal
                  </span>
                )}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default VerificationAppealModal;
