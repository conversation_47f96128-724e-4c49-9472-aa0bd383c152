
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, MessageCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import VoiceMessageWithoutChannel from '@/components/VoiceMessageWithoutChannel';
import { VoiceMessageProps } from '@/types/voice-message';
import ReplyModal from '@/components/ReplyModal';
import { Skeleton } from '@/components/ui/skeleton';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import { filterDeletedPosts } from '@/services/voiceMessageService.js';
import { supabase } from '@/integrations/supabase/client';

interface PostDetailProps {
  messages: VoiceMessageProps[];
  onReply: (parentId: string) => void;
  connectedAccount: string;
}

const PostDetail: React.FC<PostDetailProps> = ({ messages, onReply, connectedAccount }) => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [post, setPost] = useState<VoiceMessageProps | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isReplyModalOpen, setIsReplyModalOpen] = useState(false);
  const { getProfileByAddress } = useProfiles();

  // Find the post by ID - improved to avoid empty screen issue
  useEffect(() => {
    if (!id) {
      navigate('/');
      return;
    }

    // Set loading immediately to avoid flash
    setIsLoading(true);

    // Check if we have post data in the navigation state
    const state = window.history.state?.usr;
    if (state && state.postData && state.postData.id === id) {
      console.log('Using post data from navigation state:', state.postData);
      // Use the post data from navigation state
      fetchPostReactions(state.postData);
      setIsLoading(false);
      return;
    }

    // Filter out deleted posts and find the post in the messages array
    const filteredMessages = filterDeletedPosts(messages);
    const foundPost = findPostById(id, filteredMessages);

    if (foundPost) {
      // Fetch reactions from Supabase
      fetchPostReactions(foundPost);
      setIsLoading(false);
    } else {
      // If post not found, try to fetch it directly from Supabase
      fetchPostFromSupabase(id);
    }
  }, [id, messages, navigate]);

  // Fetch post directly from Supabase
  const fetchPostFromSupabase = async (postId: string) => {
    try {
      console.log('Fetching post directly from Supabase:', postId);
      const { data, error } = await supabase
        .from('voice_messages')
        .select('*')
        .eq('id', postId)
        .is('deleted_at', null)
        .single();

      if (error) {
        console.error('Error fetching post from Supabase:', error);
        // If post not found, redirect to home after a short delay
        const timer = setTimeout(() => {
          navigate('/');
        }, 500);
        return () => clearTimeout(timer);
      }

      if (data) {
        console.log('Post found in Supabase:', data);
        // Convert the database post to the VoiceMessageProps format
        const postData: VoiceMessageProps = {
          id: data.id,
          audioUrl: data.audio_url,
          transcript: data.transcript || '',
          userAddress: data.profile_id,
          timestamp: new Date(data.created_at),
          duration: data.duration || 0,
          isPinned: data.is_pinned || false,
          replies: [],
          media: data.media || []
        };

        // Fetch reactions for this post
        fetchPostReactions(postData);
        setIsLoading(false);
      } else {
        // If post not found, redirect to home after a short delay
        const timer = setTimeout(() => {
          navigate('/');
        }, 500);
        return () => clearTimeout(timer);
      }
    } catch (error) {
      console.error('Error fetching post from Supabase:', error);
      // If error, redirect to home after a short delay
      const timer = setTimeout(() => {
        navigate('/');
      }, 500);
      return () => clearTimeout(timer);
    }
  };

  // Fetch reactions for the post
  const fetchPostReactions = async (postData: VoiceMessageProps) => {
    try {
      const { data, error } = await supabase
        .from('voice_reactions')
        .select('*')
        .eq('voice_message_id', postData.id);

      if (error) {
        console.error('Error fetching reactions:', error);
        return;
      }

      if (data) {
        // Map the reactions to the format expected by the component
        const reactions = data.map(reaction => ({
          id: reaction.id,
          type: reaction.emoji,
          userAddress: reaction.profile_id,
          timestamp: new Date(reaction.created_at)
        }));

        // Update the post with reactions
        setPost({
          ...postData,
          reactions: reactions
        });
      } else {
        setPost(postData);
      }
    } catch (error) {
      console.error('Error fetching post reactions:', error);
      setPost(postData);
    }
  };

  // Recursive function to find a post by ID (including in replies)
  const findPostById = (postId: string, messageList: VoiceMessageProps[]): VoiceMessageProps | null => {
    for (const message of messageList) {
      if (message.id === postId) {
        return message;
      }

      // Check in replies if any
      if (message.replies && message.replies.length > 0) {
        const foundInReplies = findPostById(postId, message.replies);
        if (foundInReplies) {
          return foundInReplies;
        }
      }
    }

    return null;
  };

  // Handle reaction updates
  useEffect(() => {
    if (post && post.id) {
      // Set up a subscription for real-time updates
      const channel = supabase
        .channel('public:voice_reactions')
        .on('postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'voice_reactions',
            filter: `voice_message_id=eq.${post.id}`
          },
          () => {
            // Refetch reactions when there's a change
            if (post) {
              fetchPostReactions(post);
            }
          }
        )
        .subscribe();

      return () => {
        supabase.removeChannel(channel);
      };
    }
  }, [post]);

  // Handle reply button click
  const handleReplyClick = () => {
    if (post) {
      setIsReplyModalOpen(true);
    }
  };

  // Handle reply completion
  const handleReplyComplete = (parentId: string, audioBlob: Blob, transcript: string, duration?: number, media?: any[]) => {
    onReply(parentId);
    setIsReplyModalOpen(false);
  };

  if (isLoading) {
    return (
      <div className="container max-w-3xl mx-auto p-4">
        <div className="flex items-center mb-4">
          <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-xl font-bold ml-2">Post</h1>
        </div>

        <div className="space-y-4">
          <Skeleton className="h-[200px] w-full rounded-xl" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        </div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="container max-w-3xl mx-auto p-4">
        <div className="flex items-center mb-4">
          <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-xl font-bold ml-2">Post Not Found</h1>
        </div>

        <div className="text-center py-12">
          <p className="text-muted-foreground mb-4">The post you're looking for doesn't exist or has been removed.</p>
          <Button onClick={() => navigate('/')}>Return to Home</Button>
        </div>
      </div>
    );
  }

  // Get user profile
  const userProfile = getProfileByAddress(post.userAddress);

  return (
    <div className="w-full md:container md:mx-auto md:max-w-3xl flex flex-col min-h-0">
      {/* Header */}
      <div className="sticky top-0 bg-background/95 backdrop-blur-md z-10 px-3 py-3 sm:p-4 border-b border-border">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => {
              e.stopPropagation();
              navigate('/');
            }}
            className="hover:bg-secondary/80 active:bg-secondary"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-lg sm:text-xl font-bold ml-2">Post</h1>
        </div>
      </div>

      {/* Post Content */}
      <div className="flex-1 overflow-y-auto px-2 py-3 sm:p-4">
        {/* Main Post */}
        <div className="mb-4">
          <VoiceMessageWithoutChannel
            {...post}
            onReply={handleReplyClick}
            onDelete={(deletedId) => {
              // If the post is deleted, navigate back to home
              if (deletedId === id) {
                navigate('/');
              }
            }}
            isDetail={true}
          />
        </div>

        {/* Engagement Stats */}
        <div className="bg-secondary/30 rounded-lg p-3 mb-4">
          <div className="flex items-center justify-around text-sm text-muted-foreground">
            <div className="flex items-center">
              <MessageCircle size={16} className="mr-1" />
              <span>{post.replies?.length || 0} Replies</span>
            </div>
          </div>
        </div>

        {/* Reply Button */}
        <div className="mb-4">
          <Button
            onClick={handleReplyClick}
            className="w-full bg-voicechain-purple hover:bg-voicechain-accent"
          >
            <MessageCircle size={16} className="mr-2" />
            Reply to this post
          </Button>
        </div>

        {/* Replies Section */}
        {post.replies && post.replies.length > 0 ? (
          <div className="space-y-3">
            <h2 className="text-lg font-semibold px-1">Replies</h2>
            {post.replies.map((reply) => (
              <VoiceMessageWithoutChannel
                key={reply.id}
                {...reply}
                onReply={onReply}
                isReply={true}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No replies yet. Be the first to reply!</p>
          </div>
        )}
      </div>

      {/* Reply Modal */}
      {post && (
        <ReplyModal
          isOpen={isReplyModalOpen}
          onClose={() => setIsReplyModalOpen(false)}
          onReplyComplete={handleReplyComplete}
          parentId={post.id}
        />
      )}
    </div>
  );
};

export default PostDetail;
