-- Critical fix: Ensure current user has proper profile with correct data
-- Delete any conflicting profiles and create fresh one

-- First check what profiles exist for the current user
DO $$
DECLARE
    current_user_id TEXT := '0f59ebfe-9b88-48ca-a0e4-66a3f533d843';
    profile_count INT;
BEGIN
    -- Count existing profiles
    SELECT COUNT(*) INTO profile_count
    FROM profiles 
    WHERE id = current_user_id OR wallet_address = current_user_id;
    
    RAISE NOTICE 'Found % profiles for user %', profile_count, current_user_id;
    
    -- Delete any existing profiles for this user to start fresh
    DELETE FROM profiles 
    WHERE id = current_user_id OR wallet_address = current_user_id;
    
    -- Create a clean profile with the user's auth ID
    INSERT INTO profiles (
        id,
        wallet_address, 
        username,
        display_name,
        bio,
        avatar_url,
        cover_image_url,
        social_links,
        created_at,
        updated_at
    ) VALUES (
        current_user_id,
        current_user_id,
        'user_' || substr(current_user_id, 1, 8),
        'User',
        'Welcome to my profile!',
        '', -- This will be updated when user uploads image
        '',
        '{}',
        NOW(),
        NOW()
    );
    
    RAISE NOTICE 'Created fresh profile for user %', current_user_id;
END $$;