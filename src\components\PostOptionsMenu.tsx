import React from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import {
  MoreVertical,
  Pin,
  VolumeX,
  Copy,
  Flag,
  Share,
  Edit,
  Trash2
} from 'lucide-react';
import { toast } from '@/components/ui/sonner';

interface PostOptionsMenuProps {
  postId: string;
  isOwner: boolean;
  isPinned?: boolean;
  isMuted?: boolean;
  onDelete?: (postId: string) => void;
  onPin?: (postId: string, isPinned: boolean) => void;
  onMute?: (postId: string, isMuted: boolean) => void;
  onEdit?: (postId: string) => void;
}

const PostOptionsMenu: React.FC<PostOptionsMenuProps> = ({
  postId,
  isOwner,
  isPinned = false,
  isMuted = false,
  onDelete,
  onPin,
  onMute,
  onEdit
}) => {
  // Delete dialog removed since we're using the direct delete button

  const handleCopyLink = () => {
    // Create a shareable link to the post
    const postUrl = `${window.location.origin}/post/${postId}`;
    navigator.clipboard.writeText(postUrl);
    toast.success('Link copied to clipboard');
  };

  const handleShare = () => {
    // Use Web Share API if available
    if (navigator.share) {
      navigator.share({
        title: 'Check out this post',
        url: `${window.location.origin}/post/${postId}`
      }).catch(err => {
        console.error('Error sharing:', err);
      });
    } else {
      // Fallback to copy link
      handleCopyLink();
    }
  };

  const handleReport = () => {
    toast.info('Report functionality coming soon');
  };


  return (
    <div className="flex items-center" onClick={(e) => e.stopPropagation()} data-no-navigate>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 rounded-full"
            onClick={(e) => e.stopPropagation()}
            data-no-navigate
          >
            <MoreVertical className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          onClick={(e) => e.stopPropagation()}
          data-no-navigate
        >
          {isOwner && (
            <>
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(postId)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
              )}

              {onPin && (
                <DropdownMenuItem onClick={() => onPin(postId, !isPinned)}>
                  <Pin className="h-4 w-4 mr-2" />
                  {isPinned ? 'Unpin from profile' : 'Pin to profile'}
                </DropdownMenuItem>
              )}

              {onDelete && (
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    if (window.confirm('Are you sure you want to delete this post? This action cannot be undone.')) {
                      onDelete(postId);
                    }
                  }}
                  className="text-destructive"
                  data-no-navigate
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              )}

              <DropdownMenuSeparator />
            </>
          )}

          {/* Only show mute option for posts by other users */}
          {!isOwner && onMute && (
            <DropdownMenuItem onClick={() => onMute(postId, !isMuted)}>
              <VolumeX className="h-4 w-4 mr-2" />
              {isMuted ? 'Unmute' : 'Mute'}
            </DropdownMenuItem>
          )}

          <DropdownMenuItem onClick={handleCopyLink}>
            <Copy className="h-4 w-4 mr-2" />
            Copy link
          </DropdownMenuItem>

          <DropdownMenuItem onClick={handleShare}>
            <Share className="h-4 w-4 mr-2" />
            Share
          </DropdownMenuItem>

          {!isOwner && (
            <DropdownMenuItem onClick={handleReport}>
              <Flag className="h-4 w-4 mr-2" />
              Report
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default PostOptionsMenu;
