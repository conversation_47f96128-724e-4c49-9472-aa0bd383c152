import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { CheckCheck, X, AlertTriangle, Play, Pause, User, MessageSquare } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { toast } from '@/components/ui/toast';
import { supabase } from '@/services/supabase';

interface ReportedContent {
  id: string;
  content_id: string;
  content_type: 'voice_message' | 'comment' | 'profile';
  reason: string;
  details: string;
  status: 'pending' | 'dismissed' | 'removed';
  reported_at: string;
  resolved_at?: string;
  reporter_id: string;
  reported_user_id: string;
  reporter: {
    username: string;
    display_name: string;
    avatar_url?: string;
  };
  reported_user: {
    username: string;
    display_name: string;
    avatar_url?: string;
  };
  content_data?: {
    text?: string;
    audio_url?: string;
    duration?: number;
  };
}

const ContentModeration: React.FC = () => {
  const [reports, setReports] = useState<ReportedContent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('pending');
  const [processingId, setProcessingId] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState<Record<string, boolean>>({});
  const [selectedReports, setSelectedReports] = useState<string[]>([]);
  const [isBulkProcessing, setIsBulkProcessing] = useState(false);

  useEffect(() => {
    fetchReports(activeTab);
  }, [activeTab]);

  const fetchReports = async (status: string) => {
    try {
      setIsLoading(true);

      // Fetch reports from Supabase
      let query = supabase
        .from('content_reports')
        .select(`
          *,
          reporter:reporter_id(id, username, display_name, avatar_url),
          reported_user:reported_user_id(id, username, display_name, avatar_url)
        `)
        .order('reported_at', { ascending: false });

      // Filter by status if not 'all'
      if (status !== 'all') {
        query = query.eq('status', status);
      }

      const { data: reportData, error } = await query;

      if (error) {
        throw error;
      }

      // Fetch content data for each report
      const reportsWithContent = await Promise.all(reportData.map(async (report) => {
        let contentData = {};

        if (report.content_type === 'voice_message') {
          // Fetch voice message data
          const { data: voiceData, error: voiceError } = await supabase
            .from('voice_messages')
            .select('transcript, audio_url, duration')
            .eq('id', report.content_id)
            .single();

          if (!voiceError && voiceData) {
            contentData = {
              text: voiceData.transcript,
              audio_url: voiceData.audio_url,
              duration: voiceData.duration
            };
          }
        } else if (report.content_type === 'comment') {
          // Fetch comment data
          const { data: commentData, error: commentError } = await supabase
            .from('comments')
            .select('text')
            .eq('id', report.content_id)
            .single();

          if (!commentError && commentData) {
            contentData = {
              text: commentData.text
            };
          }
        }

        // Format the report data to match our interface
        return {
          id: report.id,
          content_id: report.content_id,
          content_type: report.content_type,
          reason: report.reason,
          details: report.details || '',
          status: report.status,
          reported_at: report.reported_at,
          resolved_at: report.resolved_at,
          reporter_id: report.reporter_id,
          reported_user_id: report.reported_user_id,
          reporter: {
            username: report.reporter?.username || 'unknown',
            display_name: report.reporter?.display_name || 'Unknown User',
            avatar_url: report.reporter?.avatar_url || `https://api.dicebear.com/7.x/avataaars/svg?seed=${report.reporter_id}`
          },
          reported_user: {
            username: report.reported_user?.username || 'unknown',
            display_name: report.reported_user?.display_name || 'Unknown User',
            avatar_url: report.reported_user?.avatar_url || `https://api.dicebear.com/7.x/avataaars/svg?seed=${report.reported_user_id}`
          },
          content_data: contentData
        };
      }));

      setReports(reportsWithContent);
    } catch (error) {
      console.error('Error fetching reports:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch reported content',
        duration: 3000
      });

      // Fallback to mock data if there's an error
      const mockReports: ReportedContent[] = [
        {
          id: '1',
          content_id: 'vm-123',
          content_type: 'voice_message',
          reason: 'Inappropriate content',
          details: 'This voice message contains offensive language',
          status: 'pending',
          reported_at: new Date(Date.now() - 3600000).toISOString(),
          reporter_id: 'user1',
          reported_user_id: 'user2',
          reporter: {
            username: 'reporter1',
            display_name: 'Concerned User',
            avatar_url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=reporter1'
          },
          reported_user: {
            username: 'user2',
            display_name: 'Reported User',
            avatar_url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user2'
          },
          content_data: {
            text: 'This is a transcription of the reported voice message',
            audio_url: 'https://example.com/audio.mp3',
            duration: 45
          }
        }
      ];

      // Filter by status if not 'all'
      const filteredReports = status === 'all'
        ? mockReports
        : mockReports.filter(report => report.status === status);

      setReports(filteredReports);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDismiss = async (id: string) => {
    try {
      setProcessingId(id);

      // Get the report to be dismissed
      const reportToUpdate = reports.find(report => report.id === id);
      if (!reportToUpdate) {
        throw new Error('Report not found');
      }

      // Update the report in Supabase
      const { error } = await supabase
        .from('content_reports')
        .update({
          status: 'dismissed',
          resolved_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) {
        throw error;
      }

      // Update local state
      setReports(reports.map(report =>
        report.id === id ? {
          ...report,
          status: 'dismissed',
          resolved_at: new Date().toISOString()
        } : report
      ));

      toast({
        title: 'Report Dismissed',
        description: 'The report has been dismissed',
        duration: 3000
      });
    } catch (error) {
      console.error('Error dismissing report:', error);
      toast({
        title: 'Error',
        description: 'Failed to dismiss report',
        duration: 3000
      });
    } finally {
      setProcessingId(null);
    }
  };

  const handleRemove = async (id: string) => {
    try {
      setProcessingId(id);

      // Get the report to be removed
      const reportToUpdate = reports.find(report => report.id === id);
      if (!reportToUpdate) {
        throw new Error('Report not found');
      }

      // Update the report in Supabase
      const { error: reportError } = await supabase
        .from('content_reports')
        .update({
          status: 'removed',
          resolved_at: new Date().toISOString()
        })
        .eq('id', id);

      if (reportError) {
        throw reportError;
      }

      // Remove or hide the content based on content type
      if (reportToUpdate.content_type === 'voice_message') {
        // Mark voice message as removed
        const { error: contentError } = await supabase
          .from('voice_messages')
          .update({
            is_removed: true,
            removed_at: new Date().toISOString(),
            removed_reason: reportToUpdate.reason
          })
          .eq('id', reportToUpdate.content_id);

        if (contentError) {
          console.error('Error removing voice message:', contentError);
        }
      } else if (reportToUpdate.content_type === 'comment') {
        // Mark comment as removed
        const { error: contentError } = await supabase
          .from('comments')
          .update({
            is_removed: true,
            removed_at: new Date().toISOString(),
            removed_reason: reportToUpdate.reason
          })
          .eq('id', reportToUpdate.content_id);

        if (contentError) {
          console.error('Error removing comment:', contentError);
        }
      } else if (reportToUpdate.content_type === 'profile') {
        // Flag user profile for review
        const { error: contentError } = await supabase
          .from('profiles')
          .update({
            is_flagged: true,
            flagged_at: new Date().toISOString(),
            flag_reason: reportToUpdate.reason
          })
          .eq('id', reportToUpdate.reported_user_id);

        if (contentError) {
          console.error('Error flagging profile:', contentError);
        }
      }

      // Update local state
      setReports(reports.map(report =>
        report.id === id ? {
          ...report,
          status: 'removed',
          resolved_at: new Date().toISOString()
        } : report
      ));

      toast({
        title: 'Content Removed',
        description: 'The reported content has been removed',
        duration: 3000
      });
    } catch (error) {
      console.error('Error removing content:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove content',
        duration: 3000
      });
    } finally {
      setProcessingId(null);
    }
  };

  const handleBulkDismiss = async () => {
    if (selectedReports.length === 0) {
      toast({
        title: 'No Reports Selected',
        description: 'Please select reports to dismiss',
        duration: 3000
      });
      return;
    }

    try {
      setIsBulkProcessing(true);

      // Update reports in Supabase
      const { error } = await supabase
        .from('content_reports')
        .update({
          status: 'dismissed',
          resolved_at: new Date().toISOString()
        })
        .in('id', selectedReports);

      if (error) {
        throw error;
      }

      // Update local state
      setReports(reports.map(report =>
        selectedReports.includes(report.id) ? {
          ...report,
          status: 'dismissed',
          resolved_at: new Date().toISOString()
        } : report
      ));

      toast({
        title: 'Reports Dismissed',
        description: `${selectedReports.length} reports have been dismissed`,
        duration: 3000
      });

      // Clear selection
      setSelectedReports([]);
    } catch (error) {
      console.error('Error bulk dismissing reports:', error);
      toast({
        title: 'Error',
        description: 'Failed to dismiss some reports',
        duration: 3000
      });
    } finally {
      setIsBulkProcessing(false);
    }
  };

  const handleBulkRemove = async () => {
    if (selectedReports.length === 0) {
      toast({
        title: 'No Reports Selected',
        description: 'Please select reports to process',
        duration: 3000
      });
      return;
    }

    try {
      setIsBulkProcessing(true);

      // Get the selected reports
      const reportsToUpdate = reports.filter(report => selectedReports.includes(report.id));

      // Update reports in Supabase
      const { error: reportError } = await supabase
        .from('content_reports')
        .update({
          status: 'removed',
          resolved_at: new Date().toISOString()
        })
        .in('id', selectedReports);

      if (reportError) {
        throw reportError;
      }

      // Process each content type
      const voiceMessageIds = reportsToUpdate
        .filter(report => report.content_type === 'voice_message')
        .map(report => report.content_id);

      const commentIds = reportsToUpdate
        .filter(report => report.content_type === 'comment')
        .map(report => report.content_id);

      const profileIds = reportsToUpdate
        .filter(report => report.content_type === 'profile')
        .map(report => report.reported_user_id);

      // Update voice messages
      if (voiceMessageIds.length > 0) {
        const { error: voiceError } = await supabase
          .from('voice_messages')
          .update({
            is_removed: true,
            removed_at: new Date().toISOString(),
            removed_reason: 'Removed by admin (bulk action)'
          })
          .in('id', voiceMessageIds);

        if (voiceError) {
          console.error('Error removing voice messages:', voiceError);
        }
      }

      // Update comments
      if (commentIds.length > 0) {
        const { error: commentError } = await supabase
          .from('comments')
          .update({
            is_removed: true,
            removed_at: new Date().toISOString(),
            removed_reason: 'Removed by admin (bulk action)'
          })
          .in('id', commentIds);

        if (commentError) {
          console.error('Error removing comments:', commentError);
        }
      }

      // Update profiles
      if (profileIds.length > 0) {
        const { error: profileError } = await supabase
          .from('profiles')
          .update({
            is_flagged: true,
            flagged_at: new Date().toISOString(),
            flag_reason: 'Flagged by admin (bulk action)'
          })
          .in('id', profileIds);

        if (profileError) {
          console.error('Error flagging profiles:', profileError);
        }
      }

      // Update local state
      setReports(reports.map(report =>
        selectedReports.includes(report.id) ? {
          ...report,
          status: 'removed',
          resolved_at: new Date().toISOString()
        } : report
      ));

      toast({
        title: 'Content Removed',
        description: `${selectedReports.length} items have been removed`,
        duration: 3000
      });

      // Clear selection
      setSelectedReports([]);
    } catch (error) {
      console.error('Error bulk removing content:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove some content',
        duration: 3000
      });
    } finally {
      setIsBulkProcessing(false);
    }
  };

  const toggleSelectReport = (id: string) => {
    setSelectedReports(prev =>
      prev.includes(id)
        ? prev.filter(reportId => reportId !== id)
        : [...prev, id]
    );
  };

  const toggleSelectAll = () => {
    if (selectedReports.length === reports.length) {
      // Deselect all
      setSelectedReports([]);
    } else {
      // Select all
      setSelectedReports(reports.map(report => report.id));
    }
  };

  const handlePlayPause = (id: string) => {
    setIsPlaying(prev => ({
      ...prev,
      [id]: !prev[id]
    }));

    // In a real implementation, this would play/pause the audio
    // For now, we'll just toggle the state
    toast({
      title: isPlaying[id] ? 'Paused' : 'Playing',
      description: 'Audio playback toggled',
      duration: 1000
    });
  };

  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'voice_message':
        return <MessageSquare className="h-4 w-4" />;
      case 'comment':
        return <MessageSquare className="h-4 w-4" />;
      case 'profile':
        return <User className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getContentTypeLabel = (type: string) => {
    switch (type) {
      case 'voice_message':
        return 'Voice Message';
      case 'comment':
        return 'Comment';
      case 'profile':
        return 'Profile';
      default:
        return type;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Content Moderation</CardTitle>
          <CardDescription>
            Review and moderate reported content
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="pending" onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-3 mb-6">
              <TabsTrigger value="pending">Pending</TabsTrigger>
              <TabsTrigger value="dismissed">Dismissed</TabsTrigger>
              <TabsTrigger value="removed">Removed</TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="space-y-4">
              {isLoading ? (
                <div className="text-center py-8">
                  <p>Loading reported content...</p>
                </div>
              ) : reports.length === 0 ? (
                <div className="text-center py-8">
                  <AlertTriangle className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No {activeTab} reports</h3>
                  <p className="text-muted-foreground">
                    {activeTab === 'pending'
                      ? 'All reports have been processed.'
                      : `No ${activeTab} reports found.`}
                  </p>
                </div>
              ) : (
                <>
                  {/* Bulk Actions */}
                  {activeTab === 'pending' && selectedReports.length > 0 && (
                    <div className="bg-muted p-4 rounded-md mb-6">
                      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                        <div>
                          <h3 className="font-medium">{selectedReports.length} reports selected</h3>
                          <p className="text-sm text-muted-foreground">
                            Select an action to perform on all selected reports
                          </p>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={handleBulkDismiss}
                            disabled={isBulkProcessing}
                          >
                            <CheckCheck className="mr-1 h-4 w-4" />
                            Dismiss All
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={handleBulkRemove}
                            disabled={isBulkProcessing}
                          >
                            <X className="mr-1 h-4 w-4" />
                            Remove All
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setSelectedReports([])}
                            disabled={isBulkProcessing}
                          >
                            Clear Selection
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Select All Button */}
                  {reports.length > 0 && activeTab === 'pending' && (
                    <div className="flex items-center mb-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={toggleSelectAll}
                        disabled={isBulkProcessing}
                      >
                        {selectedReports.length === reports.length ? 'Deselect All' : 'Select All'}
                      </Button>
                    </div>
                  )}

                  <div className="space-y-4">
                    {reports.map((report) => (
                      <Card key={report.id} className="overflow-hidden">
                        <div className="p-6">
                          <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4">
                            <div className="flex items-center mb-2 sm:mb-0">
                              {activeTab === 'pending' && (
                                <div className="flex items-center mr-3">
                                  <input
                                    type="checkbox"
                                    checked={selectedReports.includes(report.id)}
                                    onChange={() => toggleSelectReport(report.id)}
                                    disabled={isBulkProcessing}
                                    className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                                  />
                                </div>
                              )}
                              <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden mr-3">
                                {report.reported_user.avatar_url ? (
                                  <img
                                    src={report.reported_user.avatar_url}
                                    alt={report.reported_user.display_name}
                                    className="w-full h-full object-cover"
                                  />
                                ) : (
                                  <span className="text-gray-500 font-medium">
                                    {report.reported_user.display_name.charAt(0)}
                                  </span>
                                )}
                              </div>
                              <div>
                                <h3 className="font-medium">{report.reported_user.display_name}</h3>
                                <p className="text-sm text-muted-foreground">@{report.reported_user.username}</p>
                              </div>
                            </div>

                            <div className="flex flex-wrap gap-2">
                              <Badge className="bg-orange-500 text-white">
                                {getContentTypeIcon(report.content_type)}
                                <span className="ml-1">{getContentTypeLabel(report.content_type)}</span>
                              </Badge>

                              <Badge variant={
                                report.status === 'removed' ? 'destructive' :
                                  report.status === 'dismissed' ? 'default' : 'outline'
                              }>
                                {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                              </Badge>
                            </div>
                          </div>

                          <div className="space-y-3">
                            <div>
                              <h4 className="text-sm font-medium">Reason for Report</h4>
                              <p className="text-sm mt-1">{report.reason}</p>
                            </div>

                            <div>
                              <h4 className="text-sm font-medium">Details</h4>
                              <p className="text-sm mt-1">{report.details}</p>
                            </div>

                            {report.content_data?.text && (
                              <div>
                                <h4 className="text-sm font-medium">Content</h4>
                                <p className="text-sm mt-1 p-2 bg-muted rounded">{report.content_data.text}</p>
                              </div>
                            )}

                            {report.content_data?.audio_url && (
                              <div>
                                <h4 className="text-sm font-medium">Audio</h4>
                                <div className="flex items-center mt-1">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handlePlayPause(report.id)}
                                    className="mr-2"
                                  >
                                    {isPlaying[report.id] ? (
                                      <Pause className="h-4 w-4" />
                                    ) : (
                                      <Play className="h-4 w-4" />
                                    )}
                                  </Button>
                                  <span className="text-sm text-muted-foreground">
                                    {report.content_data.duration ? `${Math.floor(report.content_data.duration / 60)}:${(report.content_data.duration % 60).toString().padStart(2, '0')}` : 'Unknown duration'}
                                  </span>
                                </div>
                              </div>
                            )}

                            <div>
                              <h4 className="text-sm font-medium">Reported By</h4>
                              <div className="flex items-center mt-1">
                                <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden mr-2">
                                  {report.reporter.avatar_url ? (
                                    <img
                                      src={report.reporter.avatar_url}
                                      alt={report.reporter.display_name}
                                      className="w-full h-full object-cover"
                                    />
                                  ) : (
                                    <span className="text-gray-500 font-medium text-xs">
                                      {report.reporter.display_name.charAt(0)}
                                    </span>
                                  )}
                                </div>
                                <span className="text-sm">{report.reporter.display_name} (@{report.reporter.username})</span>
                              </div>
                            </div>

                            <div>
                              <h4 className="text-sm font-medium">Reported</h4>
                              <p className="text-sm mt-1">
                                {formatDistanceToNow(new Date(report.reported_at), { addSuffix: true })}
                              </p>
                            </div>

                            {report.resolved_at && (
                              <div>
                                <h4 className="text-sm font-medium">Resolved</h4>
                                <p className="text-sm mt-1">
                                  {formatDistanceToNow(new Date(report.resolved_at), { addSuffix: true })}
                                </p>
                              </div>
                            )}
                          </div>

                          {report.status === 'pending' && (
                            <div className="flex gap-2 justify-end mt-6">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDismiss(report.id)}
                                disabled={!!processingId}
                              >
                                <CheckCheck className="mr-1 h-4 w-4" />
                                Dismiss
                              </Button>
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => handleRemove(report.id)}
                                disabled={!!processingId}
                              >
                                <X className="mr-1 h-4 w-4" />
                                Remove Content
                              </Button>
                            </div>
                          )}
                        </div>
                      </Card>
                    ))}
                  </div>
                </>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default ContentModeration;
