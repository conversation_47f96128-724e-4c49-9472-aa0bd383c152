import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
  Globe,
  Headphones,
  Volume2,
  VolumeX,
  Mic,
  MicOff,
  Users,
  Settings,
  Maximize,
  RotateCcw,
  Zap,
  Move3D,
  RotateCw,
  ZoomIn,
  ZoomOut
} from 'lucide-react';
import { VoiceChatMessage } from '@/services/voiceChatService';
import AudioPlayer from '@/components/AudioPlayer';

interface SpatialChatViewProps {
  messages: VoiceChatMessage[];
  participants: any[];
  onSendMessage: (message: string) => void;
  onSendVoice: () => void;
  currentUserId: string;
}

interface Vector3D {
  x: number;
  y: number;
  z: number;
}

interface SpatialPosition extends Vector3D {
  rotationX: number;
  rotationY: number;
  rotationZ: number;
  scale: number;
}

interface SpatialMessage {
  id: string;
  message: VoiceChatMessage;
  position: SpatialPosition;
  isActive: boolean;
  distance: number;
  volume: number;
}

interface Camera {
  position: Vector3D;
  rotation: Vector3D;
  fov: number;
  zoom: number;
}

const SpatialChatView: React.FC<SpatialChatViewProps> = ({
  messages,
  participants,
  onSendMessage,
  onSendVoice,
  currentUserId
}) => {





  return (
    <div className="relative w-full h-full bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 overflow-hidden">
      {/* 3D Background Effects */}
      <div className="absolute inset-0">
        {/* Animated Particles */}
        {Array.from({ length: 30 }).map((_, i) => (
          <div
            key={`particle-${i}`}
            className="absolute w-1 h-1 bg-cyan-400 rounded-full opacity-60 animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 3}s`
            }}
          />
        ))}

        {/* 3D Grid Lines */}
        <div className="absolute inset-0 opacity-10">
          {Array.from({ length: 10 }).map((_, i) => (
            <div
              key={`grid-${i}`}
              className="absolute border-t border-cyan-400"
              style={{
                top: `${i * 10}%`,
                width: '100%',
                transform: `perspective(1000px) rotateX(${i * 2}deg)`
              }}
            />
          ))}
        </div>
      </div>

      {/* Normal Chat Container with 3D Effects */}
      <div className="relative h-full flex flex-col">
        {/* Messages Area - Normal Chat Flow */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-white/70">
                <div className="text-4xl mb-2">🌍</div>
                <p className="text-lg font-medium">Spatial Chat</p>
                <p className="text-sm">Send a message to start the 3D experience!</p>
              </div>
            </div>
          ) : (
            messages.map((message, index) => {
              const isVoiceMessage = message.message_type === 'voice';
              const isOwnMessage = message.sender_id === currentUserId;

              return (
                <div
                  key={message.id}
                  className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} mb-4`}
                  style={{
                    transform: `perspective(1000px) translateZ(${index * 2}px)`,
                    animation: `slideIn 0.5s ease-out ${index * 0.1}s both`
                  }}
                >
        {/* Simple Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-cyan-900/20"></div>

        {/* Simple Instructions */}
        {spatialMessages.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center">
            <Card className="backdrop-blur-xl bg-white/10 border border-white/20 shadow-2xl">
              <CardContent className="p-6 text-center">
                <div className="text-white space-y-3">
                  <h3 className="text-lg font-semibold">🌍 Spatial Chat</h3>
                  <p className="text-white/70">Messages appear floating in 3D space</p>
                  <p className="text-sm text-cyan-400">Send a message to get started!</p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}



        {/* 3D Floating Messages */}
        {spatialMessages.map((spatialMsg) => {
          const isVoiceMessage = spatialMsg.message.message_type === 'voice';
          const transform = calculateTransform(spatialMsg.position);

          return (
            <div
              key={spatialMsg.id}
              className={`absolute transition-all duration-500 ease-out cursor-pointer ${
                spatialMsg.isActive ? 'z-30' : 'z-10'
              }`}
              style={{
                left: '50%',
                top: '50%',
                transform: `translate(-50%, -50%) ${transform}`,
                filter: `brightness(${0.6 + spatialMsg.volume * 0.4})`,
                opacity: spatialMsg.volume * 0.8 + 0.2,
                transformStyle: 'preserve-3d'
              }}
              onClick={() => activateMessage(spatialMsg.id)}
            >
              <Card className="w-80 backdrop-blur-xl bg-white/10 border border-white/20 shadow-2xl transition-all duration-300 hover:bg-white/15 hover:shadow-cyan-500/20">
                <CardContent className="p-3">
                  {/* Message Header */}
                  <div className="flex items-center gap-2 mb-2">
                    <Avatar className="w-6 h-6">
                      <AvatarImage src={spatialMsg.message.sender_profile?.avatar_url} />
                      <AvatarFallback className="text-xs">
                        {spatialMsg.message.sender_profile?.display_name?.slice(0, 2) || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-white text-sm font-medium truncate">
                      {spatialMsg.message.sender_profile?.display_name || 'Unknown'}
                    </span>
                    {isVoiceMessage && (
                      <div className="flex items-center gap-1">
                        <Volume2 className="w-3 h-3 text-blue-400" />
                        <span className="text-xs text-blue-400">3D</span>
                      </div>
                    )}
                  </div>

                  {/* Message Content */}
                  {spatialMsg.message.message_type === 'text' && (
                    <p className="text-white/90 text-sm">
                      {spatialMsg.message.content}
                    </p>
                  )}

                  {isVoiceMessage && spatialMsg.message.voice_url && (
                    <div className="space-y-2">
                      {/* 3D Audio Visualizer */}
                      <div className="flex items-center justify-center gap-1 py-2">
                        {Array.from({ length: 8 }).map((_, i) => (
                          <div
                            key={i}
                            className="w-1 bg-blue-400 rounded animate-pulse"
                            style={{
                              height: `${10 + Math.random() * 20}px`,
                              animationDelay: `${i * 0.1}s`,
                              opacity: spatialMsg.volume
                            }}
                          />
                        ))}
                      </div>
                      
                      {/* Audio Player */}
                      <AudioPlayer 
                        src={spatialMsg.message.voice_url}
                        className="w-full"
                      />
                      
                    </div>
                  )}

                  {/* Message Reactions */}
                  <div className="flex items-center justify-between mt-3 pt-2 border-t border-white/10">
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 px-2 text-white/70 hover:text-white hover:bg-white/10"
                        onClick={(e) => {
                          e.stopPropagation();
                          // Add like functionality
                        }}
                      >
                        ❤️ <span className="ml-1 text-xs">0</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 px-2 text-white/70 hover:text-white hover:bg-white/10"
                        onClick={(e) => {
                          e.stopPropagation();
                          // Add reply functionality
                        }}
                      >
                        💬 Reply
                      </Button>
                    </div>
                    <div className="text-xs text-white/50">
                      {new Date(spatialMsg.message.created_at).toLocaleTimeString()}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Floating Connection Lines */}
              {spatialMsg.isActive && (
                <div className="absolute inset-0 pointer-events-none">
                  <div className="absolute top-1/2 left-1/2 w-32 h-32 border border-blue-400/50 rounded-full animate-ping transform -translate-x-1/2 -translate-y-1/2"></div>
                  <div className="absolute top-1/2 left-1/2 w-16 h-16 border-2 border-blue-400 rounded-full animate-pulse transform -translate-x-1/2 -translate-y-1/2"></div>
                </div>
              )}
            </div>
          );
        })}




      </div>

      {/* Custom CSS for animations */}
      <style>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
        }
        .animate-float {
          animation: float 6s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
};

export default SpatialChatView;
