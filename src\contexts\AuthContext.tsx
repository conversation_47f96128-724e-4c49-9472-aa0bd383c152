import React, { createContext, useContext, useState, useEffect } from 'react';
import { toast } from '@/components/ui/sonner';
import authService, {
  User,
  RegistrationData,
  LoginData,
  PasswordResetData,
  NewPasswordData
} from '@/services/authService';
import { WalletData } from '@/services/walletService';

interface AuthContextType {
  user: User | null;
  wallet: WalletData | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  register: (data: RegistrationData) => Promise<void>;
  login: (data: LoginData) => Promise<void>;
  logout: () => void;
  requestPasswordReset: (data: PasswordResetData) => Promise<void>;
  resetPassword: (data: NewPasswordData) => Promise<void>;
  updateProfile: (updates: Partial<User>) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [wallet, setWallet] = useState<WalletData | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Check if user is authenticated on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const isAuth = await authService.isAuthenticated();
        setIsAuthenticated(isAuth);

        if (isAuth) {
          const currentUser = await authService.getCurrentUser();
          const currentWallet = await authService.getCurrentUserWallet();

          setUser(currentUser);
          setWallet(currentWallet);

          // Set the userContext in the window object for global access
          if (currentUser && currentUser.walletAddress) {
            window.userContext = {
              walletAddress: currentUser.walletAddress,
              userId: currentUser.id,
              email: currentUser.email
            };
          }
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Register a new user
  const register = async (data: RegistrationData) => {
    setIsLoading(true);

    try {
      const response = await authService.register(data);

      setUser(response.user);
      setWallet(response.wallet);
      setIsAuthenticated(true);

      toast.success('Registration successful!');
    } catch (error: any) {
      console.error('Error registering:', error);
      toast.error(error.message || 'Registration failed');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Login user
  const login = async (data: LoginData) => {
    setIsLoading(true);

    try {
      const response = await authService.login(data);

      setUser(response.user);
      setWallet(response.wallet);
      setIsAuthenticated(true);

      // Set the userContext in the window object for global access
      if (response.user && response.user.walletAddress) {
        window.userContext = {
          walletAddress: response.user.walletAddress,
          userId: response.user.id,
          email: response.user.email
        };
      }

      toast.success('Login successful!');
    } catch (error: any) {
      console.error('Error logging in:', error);
      toast.error(error.message || 'Login failed');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout user
  const logout = () => {
    authService.logout();

    setUser(null);
    setWallet(null);
    setIsAuthenticated(false);

    // Clear the userContext from the window object
    window.userContext = undefined;

    toast.success('Logged out successfully');
  };

  // Request password reset
  const requestPasswordReset = async (data: PasswordResetData) => {
    setIsLoading(true);

    try {
      await authService.requestPasswordReset(data);

      toast.success('Password reset instructions sent to your email');
    } catch (error: any) {
      console.error('Error requesting password reset:', error);
      toast.error(error.message || 'Failed to request password reset');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Reset password
  const resetPassword = async (data: NewPasswordData) => {
    setIsLoading(true);

    try {
      await authService.resetPassword(data);

      toast.success('Password reset successful');
    } catch (error: any) {
      console.error('Error resetting password:', error);
      toast.error(error.message || 'Failed to reset password');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Update user profile
  const updateProfile = async (updates: Partial<User>) => {
    if (!user) {
      toast.error('You must be logged in to update your profile');
      return;
    }

    setIsLoading(true);

    try {
      const updatedUser = await authService.updateProfile(user.id, updates);

      setUser(updatedUser);

      toast.success('Profile updated successfully');
    } catch (error: any) {
      console.error('Error updating profile:', error);
      toast.error(error.message || 'Failed to update profile');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const value = {
    user,
    wallet,
    isAuthenticated,
    isLoading,
    register,
    login,
    logout,
    requestPasswordReset,
    resetPassword,
    updateProfile
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
