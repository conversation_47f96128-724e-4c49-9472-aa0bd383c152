
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { toast } from '@/components/ui/sonner';
import { Loader2, UserPlus } from 'lucide-react';
import { UserProfile } from '@/types/user-profile';
import * as authService from '@/services/authService';
import { supabase } from '@/integrations/supabase/client';

interface RegisterFormProps {
  onRegisterComplete?: (address: string) => void;
}

const RegisterForm: React.FC<RegisterFormProps> = ({ onRegisterComplete }) => {
  const navigate = useNavigate();
  const { register, isLoading } = useAuth();
  const { updateProfile } = useProfiles();

  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    displayName: '',
    bio: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when user types
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.username.trim()) {
      newErrors.username = 'Username is required';
    } else if (!/^[a-zA-Z0-9_]{3,20}$/.test(formData.username)) {
      newErrors.username = 'Username must be 3-20 characters and only contain letters, numbers, and underscores';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Invalid email address';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    if (!formData.displayName.trim()) {
      newErrors.displayName = 'Display name is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      // Register the user with all required fields
      const registerResponse = await register({
        email: formData.email,
        password: formData.password,
        username: formData.username,
        displayName: formData.displayName,
        bio: formData.bio || ''
      });

      // Get the current user after registration
      const user = await authService.getCurrentUser();
      if (user) {
        const userId = user.id;

        // Create user profile in your app's database
        if (userId) {
          console.log('Creating profile for new user:', userId);
          
          try {
            // First check if profile already exists to avoid duplicate key error
            const { data: existingProfile, error: checkError } = await supabase
              .from('profiles')
              .select('id')
              .eq('id', userId)
              .maybeSingle();
              
            if (checkError) {
              console.error('Error checking for existing profile:', checkError);
            }
            
            // If profile exists, update it instead of inserting
            if (existingProfile) {
              console.log('Profile already exists, updating instead of inserting');
              
              const { data: profileData, error: profileError } = await supabase
                .from('profiles')
                .update({
                  wallet_address: user.walletAddress,
                  solana_address: user.solana_address || '', // Updated to use correct property name
                  username: formData.username,
                  display_name: formData.displayName,
                  bio: formData.bio || '',
                  updated_at: new Date().toISOString()
                })
                .eq('id', userId)
                .select();
                
              if (profileError) {
                console.error('Error updating profile in Supabase:', profileError);
              } else {
                console.log('Successfully updated profile in Supabase:', profileData);
              }
            } else {
              // Create a new profile
              const { data: profileData, error: profileError } = await supabase
                .from('profiles')
                .insert({
                  id: userId,
                  wallet_address: user.walletAddress,
                  solana_address: user.solana_address || '', // Updated to use correct property name
                  username: formData.username,
                  display_name: formData.displayName,
                  bio: formData.bio || '',
                  avatar_url: '',
                  cover_image_url: '',
                  social_links: { twitter: '', github: '', website: '' },
                  is_verified: false,
                  post_count: 0,
                  like_count: 0,
                  tip_count: 0,
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString()
                })
                .select();
                
              if (profileError) {
                console.error('Error creating profile in Supabase:', profileError);
              } else {
                console.log('Successfully created profile in Supabase:', profileData);
              }
            }
          } catch (dbError) {
            console.error('Error interacting with database:', dbError);
          }
          
          // Create a user profile with the registration data
          const newProfile: UserProfile = {
            address: userId,
            walletAddress: user.walletAddress,
            solanaAddress: user.solana_address || '', // Updated to use correct property name
            username: formData.username,
            displayName: formData.displayName,
            bio: formData.bio || '',
            profileImageUrl: '',
            coverImageUrl: '',
            socialLinks: { twitter: '', github: '', website: '' },
            stats: { 
              posts: 0,
              likes: 0,
              tips: 0,
              followers: 0,
              following: 0
            },
            joinedDate: new Date(),
            verification: { 
              isVerified: false
            }
          };
          
          // Update the profile in context
          await updateProfile(userId, newProfile);

          // Store the connected account in localStorage
          localStorage.setItem('connectedAccount', userId);

          // Call onRegisterComplete callback with the user's address if provided
          if (onRegisterComplete) {
            onRegisterComplete(userId);
          }

          toast.success('Registration successful! Welcome to Audra.');

          // Navigate to home page after successful registration
          navigate('/');
        }
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      toast.error(error.message || 'Registration failed. Please try again.');
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Create Your Account</CardTitle>
        <CardDescription>
          Sign up to join the Web3 voice social platform
        </CardDescription>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="username">Username</Label>
            <Input
              id="username"
              name="username"
              placeholder="Enter a username"
              value={formData.username}
              onChange={handleChange}
              className={errors.username ? 'border-red-500' : ''}
            />
            {errors.username && (
              <p className="text-xs text-red-500">{errors.username}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="displayName">Display Name</Label>
            <Input
              id="displayName"
              name="displayName"
              placeholder="Enter your display name"
              value={formData.displayName}
              onChange={handleChange}
              className={errors.displayName ? 'border-red-500' : ''}
            />
            {errors.displayName && (
              <p className="text-xs text-red-500">{errors.displayName}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              name="email"
              type="email"
              placeholder="Enter your email"
              value={formData.email}
              onChange={handleChange}
              className={errors.email ? 'border-red-500' : ''}
            />
            {errors.email && (
              <p className="text-xs text-red-500">{errors.email}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              name="password"
              type="password"
              placeholder="Create a password"
              value={formData.password}
              onChange={handleChange}
              className={errors.password ? 'border-red-500' : ''}
            />
            {errors.password && (
              <p className="text-xs text-red-500">{errors.password}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Confirm Password</Label>
            <Input
              id="confirmPassword"
              name="confirmPassword"
              type="password"
              placeholder="Confirm your password"
              value={formData.confirmPassword}
              onChange={handleChange}
              className={errors.confirmPassword ? 'border-red-500' : ''}
            />
            {errors.confirmPassword && (
              <p className="text-xs text-red-500">{errors.confirmPassword}</p>
            )}
          </div>

          <Button
            type="submit"
            className="w-full bg-voicechain-purple hover:bg-voicechain-accent"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating Account...
              </>
            ) : (
              <>
                <UserPlus className="mr-2 h-4 w-4" />
                Sign Up
              </>
            )}
          </Button>
        </form>
      </CardContent>

      <CardFooter>
        <p className="text-xs text-muted-foreground text-center w-full">
          Already have an account? <a href="/login" className="text-voicechain-purple hover:underline">Log in</a>
        </p>
      </CardFooter>
    </Card>
  );
};

export default RegisterForm;
