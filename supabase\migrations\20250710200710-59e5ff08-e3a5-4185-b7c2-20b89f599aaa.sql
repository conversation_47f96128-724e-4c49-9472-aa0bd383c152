-- Create media storage bucket for voice message attachments
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'media',
  'media',
  true,
  10485760, -- 10MB limit
  ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'video/webm', 'video/quicktime']
)
ON CONFLICT (id) DO NOTHING;

-- Create storage policies for media uploads (only if they don't exist)
DO $$ 
BEGIN
  -- Check and create "Anyone can view media files" policy
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'storage' 
    AND tablename = 'objects' 
    AND policyname = 'Anyone can view media files'
  ) THEN
    CREATE POLICY "Anyone can view media files" 
    ON storage.objects 
    FOR SELECT 
    USING (bucket_id = 'media');
  END IF;

  -- Check and create "Authenticated users can upload media files" policy
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'storage' 
    AND tablename = 'objects' 
    AND policyname = 'Authenticated users can upload media files'
  ) THEN
    CREATE POLICY "Authenticated users can upload media files" 
    ON storage.objects 
    FOR INSERT 
    WITH CHECK (bucket_id = 'media' AND auth.uid() IS NOT NULL);
  END IF;

  -- Check and create "Users can update their own media files" policy
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'storage' 
    AND tablename = 'objects' 
    AND policyname = 'Users can update their own media files'
  ) THEN
    CREATE POLICY "Users can update their own media files" 
    ON storage.objects 
    FOR UPDATE 
    USING (bucket_id = 'media' AND auth.uid() IS NOT NULL);
  END IF;

  -- Check and create "Users can delete their own media files" policy
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'storage' 
    AND tablename = 'objects' 
    AND policyname = 'Users can delete their own media files'
  ) THEN
    CREATE POLICY "Users can delete their own media files" 
    ON storage.objects 
    FOR DELETE 
    USING (bucket_id = 'media' AND auth.uid() IS NOT NULL);
  END IF;
END $$;