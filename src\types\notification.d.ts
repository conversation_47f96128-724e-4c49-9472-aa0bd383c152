
export type NotificationType = 
  | 'like' 
  | 'reply' 
  | 'tip' 
  | 'summon' 
  | 'mention' 
  | 'system' 
  | 'follow' 
  | 'reaction';

export interface Notification {
  id: string;
  type: NotificationType;
  fromAddress: string;
  toAddress: string;
  messageId?: string;
  timestamp: Date;
  read: boolean;
  data?: Record<string, any>;
}

export interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  addNotification: (
    type: NotificationType,
    fromAddress: string,
    toAddress: string,
    messageId: string,
    data?: Record<string, any>
  ) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
}
