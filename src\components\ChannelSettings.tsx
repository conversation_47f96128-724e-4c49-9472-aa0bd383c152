import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Drawer, DrawerContent } from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { X, Upload, Hash, Plus, Tag, Trash2, UserPlus, UserMinus, Shield } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { toast } from '@/components/ui/sonner';
import { useChannels } from '@/contexts/ChannelContext';
import { Badge } from '@/components/ui/badge';
import { Channel } from '@/types/channel';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { formatDistanceToNow } from 'date-fns';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface ChannelSettingsProps {
  isOpen: boolean;
  onClose: () => void;
  channel: Channel;
  userAddress: string;
}

const ChannelSettings: React.FC<ChannelSettingsProps> = ({
  isOpen,
  onClose,
  channel,
  userAddress
}) => {
  const isMobile = useIsMobile();
  const { updateChannel, deleteChannel, updateMemberRole, removeMember, isUserOwner } = useChannels();
  const { getProfileByAddress } = useProfiles();

  const [formData, setFormData] = useState({
    name: channel?.name || '',
    description: channel?.description || '',
    isPrivate: channel?.isPrivate || false,
    coverImageUrl: channel?.coverImageUrl || '',
    tags: channel?.tags ? [...channel.tags] : [],
    currentTag: '',
    rules: channel?.rules || ''
  });

  const [coverImageFile, setCoverImageFile] = useState<File | null>(null);
  const [coverImagePreview, setCoverImagePreview] = useState<string>(channel?.coverImageUrl || '');
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('general');

  const isOwner = channel ? isUserOwner(channel.id, userAddress) : false;

  // Update form data when channel changes
  useEffect(() => {
    if (channel) {
      setFormData({
        name: channel.name || '',
        description: channel.description || '',
        isPrivate: channel.isPrivate || false,
        coverImageUrl: channel.coverImageUrl || '',
        tags: channel.tags ? [...channel.tags] : [],
        currentTag: '',
        rules: channel.rules || ''
      });
      setCoverImagePreview(channel.coverImageUrl || '');
    }
  }, [channel]);

  // Early return if no channel
  if (!channel) {
    return null;
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSwitchChange = (checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      isPrivate: checked
    }));
  };

  const handleCoverImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setCoverImageFile(file);

      // Create a preview
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setCoverImagePreview(event.target.result as string);
          setFormData(prev => ({
            ...prev,
            coverImageUrl: event.target.result as string
          }));
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const removeCoverImage = () => {
    setCoverImagePreview('');
    setCoverImageFile(null);
    setFormData(prev => ({
      ...prev,
      coverImageUrl: ''
    }));
  };

  const handleAddTag = () => {
    if (!formData.currentTag.trim()) return;

    // Check if tag already exists
    if (formData.tags.includes(formData.currentTag.trim())) {
      toast('Tag already exists');
      return;
    }

    setFormData(prev => ({
      ...prev,
      tags: [...prev.tags, prev.currentTag.trim()],
      currentTag: ''
    }));
  };

  const handleRemoveTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }));
  };

  const handleSaveChanges = () => {
    // Validate form
    if (!formData.name.trim()) {
      toast('Please enter a channel name');
      return;
    }

    if (!formData.description.trim()) {
      toast('Please enter a channel description');
      return;
    }

    // Update the channel
    updateChannel(channel.id, {
      name: formData.name.trim(),
      description: formData.description.trim(),
      isPrivate: formData.isPrivate,
      coverImageUrl: formData.coverImageUrl,
      tags: formData.tags,
      rules: formData.rules.trim()
    });

    toast('Channel settings updated successfully!');
    onClose();
  };

  const handleDeleteChannel = () => {
    if (!confirmDelete) {
      setConfirmDelete(true);
      return;
    }

    deleteChannel(channel.id);
    toast('Channel deleted successfully!');
    onClose();
  };

  const handleUpdateMemberRole = (memberAddress: string, role: 'owner' | 'moderator' | 'member') => {
    updateMemberRole(channel.id, memberAddress, role);
    toast(`Member role updated to ${role}`);
  };

  const handleRemoveMember = (memberAddress: string) => {
    removeMember(channel.id, memberAddress);
    toast('Member removed from channel');
  };

  const ModalContent = () => (
    <div className="flex flex-col py-6 px-4 space-y-6 max-h-[80vh] overflow-y-auto">
      <div className="flex justify-between items-center w-full">
        <h2 className="text-xl font-semibold">Channel Settings</h2>
        <Button variant="ghost" size="icon" onClick={onClose}>
          <X size={20} />
        </Button>
      </div>

      <Tabs defaultValue="general">
        <TabsList className="grid grid-cols-3 mb-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="rules">Rules</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          {/* Cover Image */}
          <div className="relative">
            <div
              className="w-full h-32 bg-secondary rounded-lg overflow-hidden flex items-center justify-center"
              style={{
                backgroundImage: coverImagePreview ? `url(${coverImagePreview})` : undefined,
                backgroundSize: 'cover',
                backgroundPosition: 'center'
              }}
            >
              {!coverImagePreview && <span className="text-muted-foreground">Add Cover Image (Optional)</span>}
            </div>
            <div className="absolute bottom-2 right-2 flex space-x-2">
              {coverImagePreview && (
                <Button
                  variant="destructive"
                  size="icon"
                  className="h-8 w-8 rounded-full"
                  onClick={removeCoverImage}
                >
                  <X size={14} />
                </Button>
              )}
              <label
                htmlFor="cover-upload"
                className="bg-background/80 p-2 rounded-full cursor-pointer hover:bg-background flex items-center justify-center"
              >
                <Upload size={18} />
                <input
                  id="cover-upload"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleCoverImageChange}
                />
              </label>
            </div>
          </div>

          {/* Channel Name */}
          <div>
            <Label htmlFor="name" className="flex items-center gap-2">
              <Hash size={16} />
              Channel Name
            </Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="e.g., announcements"
              maxLength={50}
              disabled={!isOwner}
            />
            <p className="text-xs text-muted-foreground mt-1">
              {formData.name.length}/50 characters
            </p>
          </div>

          {/* Channel Description */}
          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="What is this channel about?"
              className="resize-none h-24"
              maxLength={200}
              disabled={!isOwner}
            />
            <p className="text-xs text-muted-foreground mt-1">
              {formData.description.length}/200 characters
            </p>
          </div>

          {/* Channel Tags */}
          <div>
            <Label className="mb-2 block">Tags</Label>
            <div className="flex flex-wrap gap-2 mb-2">
              {formData.tags.map(tag => (
                <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                  {tag}
                  {isOwner && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4 rounded-full p-0"
                      onClick={() => handleRemoveTag(tag)}
                    >
                      <X size={10} />
                    </Button>
                  )}
                </Badge>
              ))}
            </div>
            {isOwner && (
              <>
                <div className="flex gap-2">
                  <Input
                    name="currentTag"
                    value={formData.currentTag}
                    onChange={handleInputChange}
                    placeholder="Add a tag"
                    onKeyDown={(e) => e.key === 'Enter' && handleAddTag()}
                  />
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={handleAddTag}
                    disabled={!formData.currentTag.trim()}
                  >
                    <Plus size={16} />
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Press Enter or click + to add a tag
                </p>
              </>
            )}
          </div>

          {/* Private Channel Switch */}
          {isOwner && (
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="isPrivate" className="text-base">Private Channel</Label>
                <p className="text-xs text-muted-foreground">
                  Private channels are only visible to members
                </p>
              </div>
              <Switch
                id="isPrivate"
                checked={formData.isPrivate}
                onCheckedChange={handleSwitchChange}
              />
            </div>
          )}

          {/* Save Changes Button */}
          {isOwner && (
            <Button
              onClick={handleSaveChanges}
              className="w-full bg-voicechain-purple hover:bg-voicechain-accent"
            >
              Save Changes
            </Button>
          )}

          {/* Delete Channel Button */}
          {isOwner && (
            <Button
              variant="destructive"
              className="w-full"
              onClick={handleDeleteChannel}
            >
              {confirmDelete ? 'Click again to confirm deletion' : 'Delete Channel'}
            </Button>
          )}
        </TabsContent>

        <TabsContent value="members" className="space-y-4">
          <h3 className="text-sm font-medium mb-2">Members ({channel.members.length})</h3>

          <div className="space-y-2">
            {channel.members.map(member => {
              const profile = getProfileByAddress(member.address);
              const isCurrentUser = member.address === userAddress;
              const canModify = isOwner && !isCurrentUser;

              return (
                <div
                  key={member.address}
                  className="flex items-center justify-between p-3 bg-secondary rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={profile.profileImageUrl} alt={profile.displayName} />
                      <AvatarFallback className="bg-voicechain-accent/20 text-voicechain-accent text-xs">
                        {profile.displayName.charAt(0)}
                      </AvatarFallback>
                    </Avatar>

                    <div>
                      <div className="flex items-center gap-2">
                        <p className="font-medium">{profile.displayName}</p>
                        {member.role === 'owner' && (
                          <Badge variant="purple" className="text-[10px] py-0 px-1">Owner</Badge>
                        )}
                        {member.role === 'moderator' && (
                          <Badge variant="accent" className="text-[10px] py-0 px-1">Mod</Badge>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Joined {formatDistanceToNow(member.joinedAt, { addSuffix: true })}
                      </p>
                    </div>
                  </div>

                  {canModify && (
                    <div className="flex items-center gap-2">
                      <Select
                        value={member.role}
                        onValueChange={(value: 'owner' | 'moderator' | 'member') =>
                          handleUpdateMemberRole(member.address, value)
                        }
                      >
                        <SelectTrigger className="w-24 h-8">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="owner">Owner</SelectItem>
                          <SelectItem value="moderator">Moderator</SelectItem>
                          <SelectItem value="member">Member</SelectItem>
                        </SelectContent>
                      </Select>

                      <Button
                        variant="destructive"
                        size="icon"
                        className="h-8 w-8"
                        onClick={() => handleRemoveMember(member.address)}
                      >
                        <UserMinus size={14} />
                      </Button>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="rules" className="space-y-4">
          <Label htmlFor="rules">Channel Rules</Label>
          <Textarea
            id="rules"
            name="rules"
            value={formData.rules}
            onChange={handleInputChange}
            placeholder="Set rules for your channel..."
            className="resize-none h-48"
            disabled={!isOwner}
          />

          {isOwner && (
            <Button
              onClick={handleSaveChanges}
              className="w-full bg-voicechain-purple hover:bg-voicechain-accent"
            >
              Save Rules
            </Button>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );

  if (isMobile) {
    return (
      <Drawer open={isOpen} onOpenChange={open => !open && onClose()}>
        <DrawerContent className="max-h-[90vh]">
          <ModalContent />
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <ModalContent />
      </DialogContent>
    </Dialog>
  );
};

export default ChannelSettings;
