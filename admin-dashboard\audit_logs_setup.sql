-- Create audit_logs table
CREATE TABLE IF NOT EXISTS audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  admin_id UUID REFERENCES admin_profiles(id),
  action TEXT NOT NULL,
  entity_type TEXT NOT NULL,
  entity_id TEXT,
  details JSONB,
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Only allow admins to read audit logs
CREATE POLICY "Admins can read audit logs" 
ON audit_logs FOR SELECT 
USING (auth.uid() IN (SELECT id FROM admin_profiles));

-- Only allow super_admins to delete audit logs (for data retention policies)
CREATE POLICY "Super admins can delete audit logs" 
ON audit_logs FOR DELETE 
USING (auth.uid() IN (SELECT id FROM admin_profiles WHERE role = 'super_admin'));

-- <PERSON>reate function to log admin actions
CREATE OR REPLACE FUNCTION log_admin_action(
  p_action TEXT,
  p_entity_type TEXT,
  p_entity_id TEXT,
  p_details JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  v_log_id UUID;
  v_ip_address TEXT;
  v_user_agent TEXT;
BEGIN
  -- Get client info from request headers
  v_ip_address := current_setting('request.headers', true)::json->'x-forwarded-for';
  v_user_agent := current_setting('request.headers', true)::json->'user-agent';
  
  -- Insert audit log
  INSERT INTO audit_logs (
    admin_id,
    action,
    entity_type,
    entity_id,
    details,
    ip_address,
    user_agent
  ) VALUES (
    auth.uid(),
    p_action,
    p_entity_type,
    p_entity_id,
    p_details,
    v_ip_address,
    v_user_agent
  ) RETURNING id INTO v_log_id;
  
  RETURN v_log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get audit logs with admin details
CREATE OR REPLACE FUNCTION get_audit_logs(
  p_limit INTEGER DEFAULT 100,
  p_offset INTEGER DEFAULT 0,
  p_action TEXT DEFAULT NULL,
  p_entity_type TEXT DEFAULT NULL,
  p_admin_id UUID DEFAULT NULL,
  p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  admin_id UUID,
  admin_email TEXT,
  admin_role TEXT,
  action TEXT,
  entity_type TEXT,
  entity_id TEXT,
  details JSONB,
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    al.id,
    al.admin_id,
    ap.email AS admin_email,
    ap.role AS admin_role,
    al.action,
    al.entity_type,
    al.entity_id,
    al.details,
    al.ip_address,
    al.user_agent,
    al.created_at
  FROM
    audit_logs al
  JOIN
    admin_profiles ap ON al.admin_id = ap.id
  WHERE
    (p_action IS NULL OR al.action = p_action) AND
    (p_entity_type IS NULL OR al.entity_type = p_entity_type) AND
    (p_admin_id IS NULL OR al.admin_id = p_admin_id) AND
    (p_start_date IS NULL OR al.created_at >= p_start_date) AND
    (p_end_date IS NULL OR al.created_at <= p_end_date)
  ORDER BY
    al.created_at DESC
  LIMIT
    p_limit
  OFFSET
    p_offset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Instructions:
-- 1. Run this script in the Supabase SQL editor after creating the admin_profiles table
-- 2. This will set up the audit_logs table and functions for logging admin actions
-- 3. Use the log_admin_action function to log actions in your database functions
-- 4. Use the get_audit_logs function to retrieve audit logs with admin details
