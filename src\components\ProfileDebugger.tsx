import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/contexts/AuthContext';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import { supabase } from '@/integrations/supabase/client';

export const ProfileDebugger: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { currentProfile, updateProfile } = useProfiles();
  const [testName, setTestName] = useState('');
  const [testBio, setTestBio] = useState('');
  const [debugInfo, setDebugInfo] = useState<string[]>([]);

  const addDebugInfo = (info: string) => {
    setDebugInfo(prev => [...prev, `${new Date().toLocaleTimeString()}: ${info}`]);
  };

  const testRPCFunction = async () => {
    addDebugInfo('🧪 Testing RPC function...');
    
    try {
      const { data, error } = await supabase.rpc('get_or_create_profile', {
        p_wallet_address: user?.id || 'test_user'
      });
      
      if (error) {
        addDebugInfo(`❌ RPC Error: ${error.message}`);
      } else {
        addDebugInfo(`✅ RPC Success: ${JSON.stringify(data)}`);
      }
    } catch (error) {
      addDebugInfo(`❌ RPC Exception: ${(error as Error).message}`);
    }
  };

  const testProfileUpdate = async () => {
    if (!user?.id) {
      addDebugInfo('❌ No user ID available');
      return;
    }

    addDebugInfo('🔄 Testing profile update...');
    addDebugInfo(`User ID: ${user.id}`);
    addDebugInfo(`Current Profile: ${JSON.stringify(currentProfile)}`);

    try {
      const result = await updateProfile(user.id, {
        displayName: testName || 'Test Name',
        bio: testBio || 'Test Bio'
      });

      if (result) {
        addDebugInfo(`✅ Update Success: ${JSON.stringify(result)}`);
      } else {
        addDebugInfo('❌ Update returned null');
      }
    } catch (error) {
      addDebugInfo(`❌ Update Error: ${(error as Error).message}`);
    }
  };

  const checkDatabase = async () => {
    if (!user?.id) {
      addDebugInfo('❌ No user ID available');
      return;
    }

    addDebugInfo('🔍 Checking database...');

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('wallet_address', user.id)
        .maybeSingle();

      if (error) {
        addDebugInfo(`❌ DB Error: ${error.message}`);
      } else if (data) {
        addDebugInfo(`✅ DB Profile Found: ${JSON.stringify(data)}`);
      } else {
        addDebugInfo('⚠️ No profile found in database');
      }
    } catch (error) {
      addDebugInfo(`❌ DB Exception: ${(error as Error).message}`);
    }
  };

  const clearDebugInfo = () => {
    setDebugInfo([]);
  };

  if (!isAuthenticated) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Profile Debugger</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Please log in to use the profile debugger.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Profile Debugger</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="testName">Test Display Name</Label>
            <Input
              id="testName"
              value={testName}
              onChange={(e) => setTestName(e.target.value)}
              placeholder="Enter test name"
            />
          </div>
          <div>
            <Label htmlFor="testBio">Test Bio</Label>
            <Input
              id="testBio"
              value={testBio}
              onChange={(e) => setTestBio(e.target.value)}
              placeholder="Enter test bio"
            />
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button onClick={testRPCFunction} variant="outline">
            Test RPC Function
          </Button>
          <Button onClick={testProfileUpdate} variant="outline">
            Test Profile Update
          </Button>
          <Button onClick={checkDatabase} variant="outline">
            Check Database
          </Button>
          <Button onClick={clearDebugInfo} variant="destructive">
            Clear Debug
          </Button>
        </div>

        <div className="space-y-2">
          <h3 className="font-semibold">Current State:</h3>
          <div className="text-sm space-y-1">
            <p><strong>User ID:</strong> {user?.id || 'None'}</p>
            <p><strong>Current Profile:</strong> {currentProfile ? 'Loaded' : 'None'}</p>
            {currentProfile && (
              <div className="ml-4 text-xs">
                <p>Name: {currentProfile.displayName}</p>
                <p>Bio: {currentProfile.bio}</p>
                <p>Username: {currentProfile.username}</p>
              </div>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="font-semibold">Debug Log:</h3>
          <div className="bg-gray-100 p-3 rounded max-h-64 overflow-y-auto text-sm font-mono">
            {debugInfo.length === 0 ? (
              <p className="text-gray-500">No debug info yet. Click a test button above.</p>
            ) : (
              debugInfo.map((info, index) => (
                <div key={index} className="mb-1">
                  {info}
                </div>
              ))
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfileDebugger;
