
import { supabase } from '@/lib/supabase';

/**
 * Service to handle voice transcription for both mobile and desktop
 */
export interface TranscriptionResult {
  text: string;
  success: boolean;
  error?: string;
}

/**
 * Transcribe audio using available methods
 * Uses multiple approaches to ensure transcription works across devices
 * 
 * @param audioBlob The audio blob to transcribe
 * @param userId The user ID for tracking
 * @returns The transcription result
 */
export async function transcribeAudio(audioBlob: Blob, userId: string): Promise<TranscriptionResult> {
  try {
    console.log('Starting transcription for audio', { size: audioBlob.size, type: audioBlob.type });
    
    // First try device-based transcription (for desktop browsers)
    const deviceResult = await tryDeviceTranscription(audioBlob);
    if (deviceResult.success && deviceResult.text && deviceResult.text !== "Click here to add your message text") {
      console.log('Device transcription succeeded:', deviceResult.text);
      
      // Store the result for sharing between tabs/sessions
      try {
        localStorage.setItem('last_transcription', deviceResult.text);
        localStorage.setItem('last_transcription_timestamp', Date.now().toString());
      } catch (e) {
        console.warn('Error storing transcription in localStorage:', e);
      }
      
      return deviceResult;
    }
    
    console.log('Device transcription failed or returned a placeholder, trying server transcription');
    
    // Try server-side transcription (works better for mobile)
    const serverResult = await tryServerTranscription(audioBlob, userId);
    if (serverResult.success) {
      console.log('Server transcription succeeded:', serverResult.text);
      
      // Store the result for future use
      try {
        localStorage.setItem('last_transcription', serverResult.text);
        localStorage.setItem('last_transcription_timestamp', Date.now().toString());
        localStorage.setItem('mobile_transcription_content', serverResult.text);
      } catch (e) {
        console.warn('Error storing transcription in localStorage:', e);
      }
      
      return serverResult;
    }
    
    // If both approaches fail, generate a fallback with timestamp
    console.warn('All transcription methods failed');
    const fallbackText = `Voice message recorded at ${new Date().toLocaleTimeString()}`;
    return {
      text: fallbackText,
      success: true
    };
  } catch (error) {
    console.error('Error in transcribeAudio:', error);
    return {
      text: `Voice message recorded at ${new Date().toLocaleTimeString()}`,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Try to transcribe audio using the device's speech recognition
 * This works best on desktop browsers
 * 
 * @param audioBlob The audio blob to transcribe
 * @returns The transcription result
 */
async function tryDeviceTranscription(audioBlob: Blob): Promise<TranscriptionResult> {
  return new Promise((resolve) => {
    // Check if browser supports SpeechRecognition
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      console.warn('Speech recognition not supported in this browser');
      resolve({
        text: `Voice message recorded at ${new Date().toLocaleTimeString()}`,
        success: false,
        error: 'Speech recognition not supported'
      });
      return;
    }
    
    try {
      // Create a direct URL for the audio
      const audioURL = URL.createObjectURL(audioBlob);
      
      // Create an audio element to analyze the audio
      const audioElement = new Audio(audioURL);
      
      // Wait for the audio to load metadata
      audioElement.addEventListener('loadedmetadata', () => {
        // If the audio is too short, return a message
        if (audioElement.duration < 0.5) {
          resolve({
            text: "Your recording was too short. Please try again with a longer recording.",
            success: false,
            error: 'Recording too short'
          });
          return;
        }
        
        // Try to use the browser's built-in speech recognition
        // @ts-ignore - TypeScript doesn't know about SpeechRecognition
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        const recognition = new SpeechRecognition();
        
        // Configure recognition
        recognition.lang = 'en-US';
        recognition.continuous = true;
        recognition.interimResults = true;
        
        let finalTranscript = '';
        
        // Handle recognition results
        recognition.onresult = (event: any) => {
          for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;
            if (event.results[i].isFinal) {
              finalTranscript += transcript + ' ';
            }
          }
          
          console.log('Current transcript:', finalTranscript);
        };
        
        // Handle recognition end
        recognition.onend = () => {
          if (finalTranscript.trim()) {
            resolve({
              text: finalTranscript.trim(),
              success: true
            });
          } else {
            resolve({
              text: `Voice message recorded at ${new Date().toLocaleTimeString()}`,
              success: false,
              error: 'No speech detected'
            });
          }
        };
        
        // Handle recognition errors
        recognition.onerror = (event: any) => {
          console.error('Speech recognition error:', event.error);
          resolve({
            text: `Voice message recorded at ${new Date().toLocaleTimeString()}`,
            success: false,
            error: `Recognition error: ${event.error}`
          });
        };
        
        // Start recognition
        try {
          recognition.start();
          
          // Play the audio at low volume to avoid feedback
          audioElement.volume = 0.1;
          audioElement.play().catch(err => {
            console.error('Error playing audio:', err);
          });
          
          // Stop recognition when audio ends
          audioElement.onended = () => {
            recognition.stop();
          };
          
          // Set a timeout to stop recognition after a reasonable time
          setTimeout(() => {
            try {
              if (recognition) {
                recognition.stop();
              }
            } catch (e) {
              console.warn('Error stopping recognition:', e);
            }
          }, Math.min((audioElement.duration * 1000) + 2000, 30000)); // Audio duration + 2s buffer, max 30s
        } catch (e) {
          console.error('Error starting recognition:', e);
          resolve({
            text: `Voice message recorded at ${new Date().toLocaleTimeString()}`,
            success: false,
            error: `Recognition start error: ${e instanceof Error ? e.message : e}`
          });
        }
      });
      
      // Handle audio load error
      audioElement.onerror = (e) => {
        console.error('Error loading audio for transcription:', e);
        resolve({
          text: `Voice message recorded at ${new Date().toLocaleTimeString()}`,
          success: false,
          error: 'Error loading audio'
        });
      };
    } catch (error) {
      console.error('Error in device transcription:', error);
      resolve({
        text: `Voice message recorded at ${new Date().toLocaleTimeString()}`,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });
}

/**
 * Try to transcribe audio using the server
 * This works well for mobile devices
 * 
 * @param audioBlob The audio blob to transcribe
 * @param userId The user ID for tracking
 * @returns The transcription result
 */
async function tryServerTranscription(audioBlob: Blob, userId: string): Promise<TranscriptionResult> {
  try {
    // Simulate a simple transcription for now
    // In a real app, you would send this to a server or use an edge function
    
    // Check if we have a cached transcript from a mobile device
    const cachedTranscript = localStorage.getItem('mobile_transcription_content');
    if (cachedTranscript) {
      console.log('Using cached transcript:', cachedTranscript);
      // Clear the cache after use
      localStorage.removeItem('mobile_transcription_content');
      return {
        text: cachedTranscript,
        success: true
      };
    }
    
    // Use a more meaningful fallback
    const timestamp = new Date().toLocaleTimeString();
    
    // Get audio duration if possible
    let durationText = "";
    try {
      const audioUrl = URL.createObjectURL(audioBlob);
      const audio = new Audio(audioUrl);
      await new Promise<void>((resolve) => {
        audio.onloadedmetadata = () => {
          durationText = ` (${Math.round(audio.duration)}s)`;
          resolve();
        };
        
        // Set a timeout in case metadata loading fails
        setTimeout(resolve, 1000);
      });
      URL.revokeObjectURL(audioUrl);
    } catch (e) {
      console.warn('Could not get audio duration:', e);
    }
    
    return {
      text: `Voice message${durationText} recorded at ${timestamp}`,
      success: true
    };
  } catch (error) {
    console.error('Error in server transcription:', error);
    return {
      text: `Voice message recorded at ${new Date().toLocaleTimeString()}`,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Convert a blob to a base64 string
 * @param blob The blob to convert
 * @returns A promise that resolves to a base64 string
 */
function blobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      resolve(reader.result as string);
    };
    reader.onerror = (e) => {
      reject(e);
    };
    reader.readAsDataURL(blob);
  });
}

/**
 * Check if an edge function exists
 * @param functionName The name of the function to check
 * @returns A promise that resolves to true if the function exists
 */
async function checkEdgeFunction(functionName: string): Promise<boolean> {
  try {
    // This is a simplified check
    return false;
  } catch (e) {
    // Function likely doesn't exist or isn't accessible
    console.warn(`Edge function ${functionName} not available:`, e);
    return false;
  }
}
