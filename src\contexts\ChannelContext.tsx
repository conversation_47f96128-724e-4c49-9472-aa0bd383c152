import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Channel, ChannelMember, ChannelMessage, ChannelInvite, createDefaultChannel, createChannelInvite } from '@/types/channel';
import { VoiceMessageProps } from '@/components/VoiceMessage';
import { MediaFile } from '@/components/MediaUploader';
import { useNotifications } from './NotificationContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/sonner';
import { v4 as uuidv4 } from 'uuid';

interface ChannelContextType {
  channels: Channel[];
  userChannels: Channel[];
  publicChannels: Channel[];
  activeChannel: Channel | null;
  channelInvites: ChannelInvite[];
  loading: boolean;

  // Channel management
  createChannel: (name: string, description: string, isPrivate: boolean, tags: string[]) => Channel;
  updateChannel: (channelId: string, updates: Partial<Omit<Channel, 'id' | 'createdAt' | 'createdBy' | 'members' | 'messages'>>) => void;
  deleteChannel: (channelId: string) => void;
  joinChannel: (channelId: string) => void;
  leaveChannel: (channelId: string) => void;
  setActiveChannel: (channelId: string | null) => void;

  // Member management
  addMember: (channelId: string, memberAddress: string, role?: ChannelMember['role']) => void;
  removeMember: (channelId: string, memberAddress: string) => void;
  updateMemberRole: (channelId: string, memberAddress: string, role: ChannelMember['role']) => void;

  // Message management
  addMessageToChannel: (channelId: string, message: Omit<VoiceMessageProps, 'parentId' | 'isReply'>) => void;

  // Invite management
  createInvite: (channelId: string, maxUses?: number, expiresInDays?: number) => Promise<ChannelInvite | null>;
  useInvite: (inviteCode: string) => Promise<boolean>;

  // Utility functions
  getChannelById: (channelId: string) => Channel | undefined;
  isUserMember: (channelId: string, userAddress: string) => boolean;
  isUserOwner: (channelId: string, userAddress: string) => boolean;
  isUserModerator: (channelId: string, userAddress: string) => boolean;
}

const ChannelContext = createContext<ChannelContextType | undefined>(undefined);

export const useChannels = () => {
  const context = useContext(ChannelContext);
  if (!context) {
    throw new Error('useChannels must be used within a ChannelProvider');
  }
  return context;
};

interface ChannelProviderProps {
  children: ReactNode;
  userAddress: string;
}

export const ChannelProvider: React.FC<ChannelProviderProps> = ({
  children,
  userAddress
}) => {
  const [channels, setChannels] = useState<Channel[]>([]);
  const [activeChannel, setActiveChannelState] = useState<Channel | null>(null);
  const [channelInvites, setChannelInvites] = useState<ChannelInvite[]>([]);
  const [loading, setLoading] = useState(true);
  const { addNotification } = useNotifications();

  // Filter channels for the current user
  const userChannels = channels.filter(channel =>
    channel.members.some(member => member.address === userAddress)
  );

  // Get public channels
  const publicChannels = channels.filter(channel => !channel.isPrivate);

  // Load channels from Supabase on mount and whenever userAddress changes
  useEffect(() => {
    if (!userAddress) {
      setLoading(false);
      return;
    }

    const fetchChannels = async () => {
      setLoading(true);
      try {
        // We need to use a profile UUID for Supabase rather than an Ethereum address
        const profileUuid = await getProfileUUID(userAddress);
        console.log('Found profile UUID:', profileUuid);
        
        if (!profileUuid) {
          // Create a profile if none exists
          const newProfileId = await createUserProfile(userAddress);
          if (newProfileId) {
            console.log('Created new profile with UUID:', newProfileId);
          } else {
            console.error('Failed to create user profile');
          }
          setLoading(false);
          return;
        }

        // First get all channels where the user is a member
        const { data: memberData, error: memberError } = await supabase
          .from('channel_members')
          .select('channel_id, role, joined_at')
          .eq('profile_id', profileUuid);

        if (memberError) {
          console.error('Error fetching channel membership:', memberError);
          throw memberError;
        }
        
        console.log('Found channel memberships:', memberData?.length || 0);

        // Get the channel IDs where the user is a member
        const userChannelIds = memberData?.map(member => member.channel_id) || [];

        // Get all public channels first (always show these)
        const { data: publicChannelData, error: publicChannelError } = await supabase
          .from('channels')
          .select('*')
          .eq('is_private', false)
          .order('created_at', { ascending: false });

        if (publicChannelError) {
          console.error('Error fetching public channels:', publicChannelError);
          throw publicChannelError;
        }

        // Get private channels where user is a member (if any)
        let privateChannelData = [];
        if (userChannelIds.length > 0) {
          const { data: privateData, error: privateError } = await supabase
            .from('channels')
            .select('*')
            .eq('is_private', true)
            .in('id', userChannelIds);

          if (privateError) {
            console.error('Error fetching private channels:', privateError);
          } else {
            privateChannelData = privateData || [];
          }
        }

        // Combine public and private channels
        const channelData = [...(publicChannelData || []), ...privateChannelData];

        console.log('Combined channel data:', channelData.length, 'channels');
        
        if (!channelData || channelData.length === 0) {
          console.log('No channels found');
          setChannels([]);
          setLoading(false);
          return;
        }
        
        console.log('Found channels:', channelData.length);

        // Get all members for these channels
        const { data: allMembersData, error: membersError } = await supabase
          .from('channel_members')
          .select('*')
          .in('channel_id', channelData.map(channel => channel.id));

        if (membersError) {
          console.error('Error fetching channel members:', membersError);
          throw membersError;
        }
        
        console.log('Found total channel members:', allMembersData?.length || 0);

        // Transform the data to match our local Channel structure
        const transformedChannels: Channel[] = channelData.map(channel => {
          const channelMembers = allMembersData
            .filter(member => member.channel_id === channel.id)
            .map(member => ({
              address: member.profile_id,
              joinedAt: new Date(member.joined_at),
              role: member.role as ChannelMember['role']
            }));

          return {
            id: channel.id,
            name: channel.name,
            description: channel.description || '',
            coverImageUrl: '',
            createdAt: new Date(channel.created_at),
            createdBy: channel.created_by,
            isPrivate: channel.is_private,
            members: channelMembers,
            tags: channel.tags || [],
            rules: channel.rules || '',
            messages: []
          };
        });

        setChannels(transformedChannels);

        // Also fetch channel invites for channels the user can moderate
        const moderatedChannelIds = memberData
          .filter(member => ['owner', 'moderator'].includes(member.role))
          .map(member => member.channel_id);

        if (moderatedChannelIds.length > 0) {
          const { data: inviteData, error: inviteError } = await supabase
            .from('channel_invites')
            .select('*')
            .in('channel_id', moderatedChannelIds);

          if (!inviteError && inviteData) {
            const transformedInvites: ChannelInvite[] = inviteData.map(invite => ({
              id: invite.id,
              channelId: invite.channel_id,
              createdBy: invite.created_by,
              createdAt: new Date(invite.created_at),
              expiresAt: invite.expires_at ? new Date(invite.expires_at) : undefined,
              maxUses: invite.max_uses,
              uses: invite.uses,
              code: invite.code
            }));
            setChannelInvites(transformedInvites);
            console.log('Found channel invites:', transformedInvites.length);
          }
        }
      } catch (error) {
        console.error('Error fetching channels from Supabase:', error);
        console.error('Error details:', error);
        toast('Error loading channels. Please refresh the page.');
        // Set empty arrays to prevent undefined errors
        setChannels([]);
        setChannelInvites([]);
      } finally {
        setLoading(false);
      }
    };

    fetchChannels();
  }, [userAddress]);

  // Helper function to get or create a profile UUID from an Ethereum address
  const getProfileUUID = async (ethAddress: string): Promise<string | null> => {
    try {
      // First check if a profile already exists with this address
      const { data, error } = await supabase
        .from('profiles')
        .select('id')
        .eq('wallet_address', ethAddress)
        .maybeSingle();
      
      if (error) {
        console.error('Error checking profile:', error);
        return null;
      }
      
      if (data) {
        return data.id;
      }
      
      return null;
    } catch (error) {
      console.error('Error in getProfileUUID:', error);
      return null;
    }
  };

  // Get or create profile for user - FIXED VERSION
  const createUserProfile = async (ethAddress: string): Promise<string | null> => {
    try {
      console.log("🔍 Looking for profile for wallet:", ethAddress);

      // Check if profile already exists
      const { data: existingProfile, error: profileError } = await supabase
        .from('profiles')
        .select('id, wallet_address')
        .eq('wallet_address', ethAddress)
        .single();

      if (existingProfile && !profileError) {
        console.log("✅ Found existing profile:", existingProfile.id, "for", ethAddress);
        return existingProfile.id;
      }

      console.log("❌ No profile found for", ethAddress);
      console.log("Profile error:", profileError);

      // For now, return null to indicate profile not found
      // In a real app, you'd create a new profile here
      return null;

    } catch (error) {
      console.error('❌ Error in createUserProfile:', error);
      return null;
    }
  };

  // Create a new channel
  const createChannel = (name: string, description: string, isPrivate: boolean, tags: string[]) => {
    // Generate a valid UUID for the channel ID
    const channelId = uuidv4();
    
    const createChannelInSupabase = async () => {
      try {
        // Get the profile UUID for the current user
        const profileId = await getProfileUUID(userAddress);
        
        if (!profileId) {
          // Create a profile if it doesn't exist
          const newProfileId = await createUserProfile(userAddress);
          if (!newProfileId) {
            throw new Error("Could not create user profile");
          }
        }
        
        console.log("🔍 Getting profile for user:", userAddress);

        // First try to get existing profile
        let finalProfileId = await getProfileUUID(userAddress);

        if (!finalProfileId) {
          console.warn("❌ No profile found for", userAddress);
          throw new Error(`No profile found for wallet ${userAddress}. Please create a profile first.`);
        }

        console.log("✅ Using profile ID:", finalProfileId, "for wallet:", userAddress);

        console.log("Creating channel with profile ID:", finalProfileId);

        // Insert channel
        const { data: channelData, error: channelError } = await supabase
          .from('channels')
          .insert({
            id: channelId,
            name: name,
            description: description,
            created_by: finalProfileId,
            is_private: isPrivate,
            tags: tags,
          })
          .select()
          .single();

        if (channelError) {
          console.error('Error creating channel:', channelError);
          throw channelError;
        }

        // Insert channel member (creator as owner)
        const { error: memberError } = await supabase
          .from('channel_members')
          .insert({
            channel_id: channelId,
            profile_id: finalProfileId,
            role: 'owner',
          });

        if (memberError) {
          console.error('Error adding channel member:', memberError);
          throw memberError;
        }

        console.log('Channel created successfully in Supabase');
        return true;
      } catch (error) {
        console.error('Error creating channel in Supabase:', error);
        toast('Failed to create channel. Please try again.');
        return false;
      }
    };

    const newChannel = createDefaultChannel(channelId, name, description, userAddress);
    newChannel.isPrivate = isPrivate;
    newChannel.tags = tags;

    // Create the channel in Supabase
    createChannelInSupabase().then(success => {
      if (success) {
        setChannels(prev => [...prev, newChannel]);
        setActiveChannelState(newChannel);
        toast(`Channel ${name} created successfully!`);
      }
    });

    return newChannel;
  };

  // Update a channel
  const updateChannel = (channelId: string, updates: Partial<Omit<Channel, 'id' | 'createdAt' | 'createdBy' | 'members' | 'messages'>>) => {
    // Check if user can modify the channel
    if (!isUserModerator(channelId, userAddress)) {
      toast('You do not have permission to update this channel');
      return;
    }

    setChannels(prev =>
      prev.map(channel =>
        channel.id === channelId
          ? { ...channel, ...updates }
          : channel
      )
    );

    // Update in Supabase
    (async () => {
      try {
        const { error } = await supabase
          .from('channels')
          .update({
            name: updates.name,
            description: updates.description,
            is_private: updates.isPrivate,
            tags: updates.tags,
            rules: updates.rules,
          })
          .eq('id', channelId);

        if (error) throw error;
      } catch (error) {
        console.error('Error updating channel in Supabase:', error);
        toast('Failed to update channel. Please try again.');
      }
    })();
  };

  // Delete a channel
  const deleteChannel = (channelId: string) => {
    // Check if user is the owner
    if (!isUserOwner(channelId, userAddress)) {
      toast('Only channel owners can delete channels');
      return;
    }

    setChannels(prev => prev.filter(channel => channel.id !== channelId));

    // Delete in Supabase
    (async () => {
      try {
        const { error } = await supabase
          .from('channels')
          .delete()
          .eq('id', channelId);

        if (error) throw error;
      } catch (error) {
        console.error('Error deleting channel in Supabase:', error);
      }
    })();

    // Also delete any invites for this channel
    setChannelInvites(prev => prev.filter(invite => invite.channelId !== channelId));

    // If the active channel is being deleted, set active channel to null
    if (activeChannel?.id === channelId) {
      setActiveChannelState(null);
    }
  };

  // Join a channel - SIMPLIFIED VERSION
  const joinChannel = (channelId: string) => {
    console.log('🚀 SIMPLE JOIN - Channel:', channelId);

    const channel = channels.find(c => c.id === channelId);
    if (!channel) {
      console.error('❌ Channel not found:', channelId);
      toast('Channel not found');
      return;
    }

    console.log('✅ ENTERING CHANNEL:', channel.name);

    // SIMPLE: Just enter the channel, skip complex membership logic
    setActiveChannelState(channel);
    toast(`Entered ${channel.name}`);
  };



  // Leave a channel
  const leaveChannel = (channelId: string) => {
    const channel = channels.find(c => c.id === channelId);
    if (!channel) return;

    // Check if user is a member
    if (!channel.members.some(member => member.address === userAddress)) return;

    // Check if user is the owner
    const isOwner = channel.members.some(member =>
      member.address === userAddress && member.role === 'owner'
    );

    if (isOwner) {
      // If user is the owner, check if there are other members
      const otherMembers = channel.members.filter(member => member.address !== userAddress);

      if (otherMembers.length === 0) {
        // If no other members, delete the channel
        deleteChannel(channelId);
        return;
      }

      // Find another member to promote to owner
      const moderators = otherMembers.filter(member => member.role === 'moderator');
      const newOwner = moderators.length > 0 ? moderators[0] : otherMembers[0];

      // Update local state
      setChannels(prev =>
        prev.map(channel => {
          if (channel.id === channelId) {
            return {
              ...channel,
              members: channel.members
                .filter(member => member.address !== userAddress)
                .map(member =>
                  member.address === newOwner.address
                    ? { ...member, role: 'owner' }
                    : member
                )
            };
          }
          return channel;
        })
      );

      // Update in Supabase
      (async () => {
        try {
          const profileId = await getProfileUUID(userAddress);
          if (!profileId) return;
          
          const newOwnerProfileId = await getProfileUUID(newOwner.address);
          if (!newOwnerProfileId) return;
          
          // Delete the current owner
          const { error: deleteError } = await supabase
            .from('channel_members')
            .delete()
            .eq('channel_id', channelId)
            .eq('profile_id', profileId);

          if (deleteError) throw deleteError;

          // Update the new owner
          const { error: updateError } = await supabase
            .from('channel_members')
            .update({ role: 'owner' })
            .eq('channel_id', channelId)
            .eq('profile_id', newOwnerProfileId);

          if (updateError) throw updateError;

          // Notify the new owner
          try {
            await addNotification(
              'message',  // Using 'message' as it's a valid type
              profileId,
              newOwnerProfileId,
              channelId,
              { text: `made you the owner of channel ${channel.name}` }
            );
          } catch (notifyError) {
            console.error('Error sending notification:', notifyError);
          }
        } catch (error) {
          console.error('Error updating channel ownership in Supabase:', error);
          toast('Failed to leave channel. Please try again.');
        }
      })();
    } else {
      // If user is not the owner, just remove them from the members
      setChannels(prev =>
        prev.map(channel => {
          if (channel.id === channelId) {
            return {
              ...channel,
              members: channel.members.filter(member => member.address !== userAddress)
            };
          }
          return channel;
        })
      );

      // Delete from Supabase
      (async () => {
        try {
          const profileId = await getProfileUUID(userAddress);
          if (!profileId) return;
          
          const { error } = await supabase
            .from('channel_members')
            .delete()
            .eq('channel_id', channelId)
            .eq('profile_id', profileId);

          if (error) throw error;
        } catch (error) {
          console.error('Error leaving channel in Supabase:', error);
          toast('Failed to leave channel. Please try again.');
        }
      })();
    }

    // If the active channel is being left, set active channel to null
    if (activeChannel?.id === channelId) {
      setActiveChannelState(null);
    }
  };

  // Set the active channel - SIMPLIFIED WITH LOGGING
  const setActiveChannel = (channelId: string | null) => {
    console.log('🎯 SETTING ACTIVE CHANNEL:', channelId);

    if (channelId === null) {
      console.log('✅ CLEARING ACTIVE CHANNEL');
      setActiveChannelState(null);
      return;
    }

    console.log('🔍 setActiveChannel - Looking for:', channelId);
    console.log('🔍 setActiveChannel - Available channels:', channels.map(c => ({ id: c.id, name: c.name })));
    const channel = channels.find(c => c.id === channelId);
    if (channel) {
      console.log('✅ FOUND CHANNEL, SETTING ACTIVE:', channel.name);
      setActiveChannelState(channel);
    } else {
      console.error('❌ CHANNEL NOT FOUND IN LIST:', channelId);
      console.log('Available channels:', channels.map(c => ({ id: c.id, name: c.name })));
    }
  };

  // Add a member to a channel
  const addMember = (channelId: string, memberAddress: string, role: ChannelMember['role'] = 'member') => {
    // Check if user can modify the channel
    if (!isUserModerator(channelId, userAddress)) {
      toast('You do not have permission to add members to this channel');
      return;
    }

    setChannels(prev =>
      prev.map(channel => {
        if (channel.id === channelId) {
          // Check if member already exists
          if (channel.members.some(member => member.address === memberAddress)) {
            return channel;
          }

          return {
            ...channel,
            members: [
              ...channel.members,
              {
                address: memberAddress,
                joinedAt: new Date(),
                role
              }
            ]
          };
        }
        return channel;
      })
    );

    // Insert in Supabase
    (async () => {
      try {
        const memberProfileId = await getProfileUUID(memberAddress);
        if (!memberProfileId) {
          // Create a profile for the member if it doesn't exist
          await createUserProfile(memberAddress);
        }
        
        const { error } = await supabase
          .from('channel_members')
          .insert({
            channel_id: channelId,
            profile_id: await getProfileUUID(memberAddress),
            role,
          });

        if (error) throw error;
      } catch (error) {
        console.error('Error adding member to channel in Supabase:', error);
        toast('Failed to add member. Please try again.');
      }
    })();
  };

  // Remove a member from a channel
  const removeMember = (channelId: string, memberAddress: string) => {
    // Check if user can modify the channel
    if (!isUserModerator(channelId, userAddress)) {
      toast('You do not have permission to remove members from this channel');
      return;
    }

    // Check if trying to remove the owner
    const channel = channels.find(c => c.id === channelId);
    const isTargetOwner = channel?.members.some(
      member => member.address === memberAddress && member.role === 'owner'
    );

    if (isTargetOwner) {
      toast('Cannot remove the channel owner');
      return;
    }

    setChannels(prev =>
      prev.map(channel => {
        if (channel.id === channelId) {
          return {
            ...channel,
            members: channel.members.filter(member => member.address !== memberAddress)
          };
        }
        return channel;
      })
    );

    // Delete from Supabase
    (async () => {
      try {
        const memberProfileId = await getProfileUUID(memberAddress);
        if (!memberProfileId) return;
        
        const { error } = await supabase
          .from('channel_members')
          .delete()
          .eq('channel_id', channelId)
          .eq('profile_id', memberProfileId);

        if (error) throw error;
      } catch (error) {
        console.error('Error removing member from channel in Supabase:', error);
        toast('Failed to remove member. Please try again.');
      }
    })();
  };

  // Update a member's role
  const updateMemberRole = (channelId: string, memberAddress: string, role: ChannelMember['role']) => {
    // Check if user can modify the channel
    if (!isUserOwner(channelId, userAddress)) {
      toast('Only channel owners can change member roles');
      return;
    }

    // Check if trying to change the owner's role
    const channel = channels.find(c => c.id === channelId);
    const isChangingSelf = memberAddress === userAddress;

    if (isChangingSelf && role !== 'owner') {
      toast('You cannot demote yourself as owner');
      return;
    }

    setChannels(prev =>
      prev.map(channel => {
        if (channel.id === channelId) {
          return {
            ...channel,
            members: channel.members.map(member =>
              member.address === memberAddress
                ? { ...member, role }
                : member
            )
          };
        }
        return channel;
      })
    );

    // Update in Supabase
    (async () => {
      try {
        const memberProfileId = await getProfileUUID(memberAddress);
        if (!memberProfileId) return;
        
        const { error } = await supabase
          .from('channel_members')
          .update({ role })
          .eq('channel_id', channelId)
          .eq('profile_id', memberProfileId);

        if (error) throw error;
      } catch (error) {
        console.error('Error updating member role in Supabase:', error);
        toast('Failed to update member role. Please try again.');
      }
    })();
  };

  // Add a message to a channel
  const addMessageToChannel = (channelId: string, message: Omit<VoiceMessageProps, 'parentId' | 'isReply'>) => {
    setChannels(prev =>
      prev.map(channel => {
        if (channel.id === channelId) {
          const channelMessage = {
            ...message,
            channelId,
            replies: []
          } as ChannelMessage;

          return {
            ...channel,
            messages: [channelMessage, ...channel.messages]
          };
        }
        return channel;
      })
    );

    // TODO: Implement storing messages in Supabase
    // This would require a voice_messages table in Supabase
  };

  // Create an invite for a channel
  const createInvite = async (channelId: string, maxUses?: number, expiresInDays?: number): Promise<ChannelInvite | null> => {
    // Check if user can create invites
    if (!isUserModerator(channelId, userAddress)) {
      toast('You do not have permission to create invites for this channel');
      return null;
    }

    try {
      const profileId = await getProfileUUID(userAddress);
      if (!profileId) {
        toast('Error retrieving user profile');
        return null;
      }
      
      const newInvite = createChannelInvite(channelId, profileId, maxUses, expiresInDays);
      
      // Calculate the expiry date for Supabase
      let expiresAt = null;
      if (expiresInDays && expiresInDays > 0) {
        const date = new Date();
        date.setDate(date.getDate() + expiresInDays);
        expiresAt = date.toISOString();
      }

      console.log("Creating invite with profile ID:", profileId);

      // Create in Supabase
      const { data, error } = await supabase
        .from('channel_invites')
        .insert({
          id: newInvite.id, // Include the UUID
          channel_id: newInvite.channelId,
          code: newInvite.code,
          created_by: profileId,
          expires_at: expiresAt,
          max_uses: maxUses,
          uses: 0
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating invite in Supabase:', error);
        throw error;
      }

      // Add the new invite to state
      setChannelInvites(prev => [...prev, newInvite]);

      return newInvite;
    } catch (error) {
      console.error('Error creating invite:', error);
      toast('Failed to create invite');
      return null;
    }
  };

  // Use an invite to join a channel
  const useInvite = async (inviteCode: string): Promise<boolean> => {
    try {
      console.log("Looking up invite code:", inviteCode);
      
      // Look up the invite in Supabase
      const { data, error } = await supabase
        .from('channel_invites')
        .select('*, channels(*)')
        .eq('code', inviteCode)
        .single();

      if (error || !data) {
        console.error('Invalid invite code:', error);
        toast('Invalid invite code');
        return false;
      }
      
      console.log("Found invite for channel:", data.channels.name);

      // Check if invite is expired
      if (data.expires_at && new Date(data.expires_at) < new Date()) {
        toast('This invite has expired');
        return false;
      }

      // Check if invite has reached max uses
      if (data.max_uses && data.uses >= data.max_uses) {
        toast('This invite has reached its maximum uses');
        return false;
      }

      // Get or create profile UUID
      let profileId = await getProfileUUID(userAddress);
      if (!profileId) {
        profileId = await createUserProfile(userAddress);
        if (!profileId) {
          toast('Error creating user profile');
          return false;
        }
      }
      
      console.log("Using invite with profile ID:", profileId);

      // Check if user is already a member of the channel
      const { data: memberCheck, error: memberCheckError } = await supabase
        .from('channel_members')
        .select('*')
        .eq('channel_id', data.channel_id)
        .eq('profile_id', profileId)
        .maybeSingle();

      if (memberCheckError) {
        console.error('Error checking membership:', memberCheckError);
      }

      if (memberCheck) {
        toast('You are already a member of this channel');
        return false;
      }

      // Join the channel in Supabase
      const { error: joinError } = await supabase
        .from('channel_members')
        .insert({
          channel_id: data.channel_id,
          profile_id: profileId,
          role: 'member',
        });
        
      if (joinError) {
        console.error('Error joining channel:', joinError);
        toast('Error joining channel');
        return false;
      }

      // Increment the uses count
      await supabase
        .from('channel_invites')
        .update({ uses: data.uses + 1 })
        .eq('id', data.id);
      
      // Update local state to reflect the new channel membership
      const channel = channels.find(c => c.id === data.channel_id);
      if (channel) {
        // Update the channel's members list
        setChannels(prev =>
          prev.map(c => {
            if (c.id === data.channel_id) {
              return {
                ...c,
                members: [
                  ...c.members,
                  {
                    address: profileId!,
                    joinedAt: new Date(),
                    role: 'member'
                  }
                ]
              };
            }
            return c;
          })
        );
        
        // Set as active channel
        setActiveChannelState(channel);
      } else {
        // Need to fetch the channel if it's not in local state
        const { data: channelData, error: channelError } = await supabase
          .from('channels')
          .select('*')
          .eq('id', data.channel_id)
          .single();
          
        if (channelError || !channelData) {
          console.error('Error fetching channel:', channelError);
          toast('Error fetching channel details');
          return true; // Return true since we did successfully join
        }
        
        // Get channel members
        const { data: membersData, error: membersError } = await supabase
          .from('channel_members')
          .select('*')
          .eq('channel_id', data.channel_id);
          
        if (membersError) {
          console.error('Error fetching channel members:', membersError);
        }
        
        const newChannel: Channel = {
          id: channelData.id,
          name: channelData.name,
          description: channelData.description || '',
          coverImageUrl: '',
          createdAt: new Date(channelData.created_at),
          createdBy: channelData.created_by,
          isPrivate: channelData.is_private,
          members: membersData?.map(m => ({
            address: m.profile_id,
            joinedAt: new Date(m.joined_at),
            role: m.role as ChannelMember['role']
          })) || [{
            address: profileId!,
            joinedAt: new Date(),
            role: 'member'
          }],
          tags: channelData.tags || [],
          rules: channelData.rules || '',
          messages: []
        };
        
        // Add to channels and set active
        setChannels(prev => [...prev, newChannel]);
        setActiveChannelState(newChannel);
      }

      toast('Successfully joined the channel!');
      return true;
    } catch (error) {
      console.error('Error using invite:', error);
      toast('Error joining channel with invite code');
      return false;
    }
  };

  // Utility function to get a channel by ID
  const getChannelById = (channelId: string) => {
    console.log('🔍 getChannelById called with:', channelId);
    console.log('🔍 Available channels:', channels.map(c => ({ id: c.id, name: c.name })));
    const found = channels.find(channel => channel.id === channelId);
    console.log('🔍 getChannelById result:', found ? found.name : 'NOT FOUND');
    return found;
  };

  // Check if a user is a member of a channel
  const isUserMember = (channelId: string, address: string) => {
    const channel = getChannelById(channelId);
    if (!channel) return false;
    return channel.members.some(member => member.address === address);
  };

  // Check if a user is the owner of a channel
  const isUserOwner = (channelId: string, address: string) => {
    const channel = getChannelById(channelId);
    if (!channel) return false;
    return channel.members.some(member =>
      member.address === address && member.role === 'owner'
    );
  };

  // Check if a user is a moderator of a channel
  const isUserModerator = (channelId: string, address: string) => {
    const channel = getChannelById(channelId);
    if (!channel) return false;
    return channel.members.some(member =>
      member.address === address && (member.role === 'moderator' || member.role === 'owner')
    );
  };

  return (
    <ChannelContext.Provider
      value={{
        channels,
        userChannels,
        publicChannels,
        activeChannel,
        channelInvites,
        loading,
        createChannel,
        updateChannel,
        deleteChannel,
        joinChannel,
        leaveChannel,
        setActiveChannel,
        addMember,
        removeMember,
        updateMemberRole,
        addMessageToChannel,
        createInvite,
        useInvite,
        getChannelById,
        isUserMember,
        isUserOwner,
        isUserModerator,
        refreshChannels: () => {
          console.log('Manual refresh triggered');
          setLoading(true);
          // Trigger useEffect by updating a dependency
          window.location.reload();
        }
      }}
    >
      {children}
    </ChannelContext.Provider>
  );
};
