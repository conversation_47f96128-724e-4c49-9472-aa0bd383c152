
import { VerificationType } from '@/components/VerificationBadge';

// Owner address (your address)
export const OWNER_ADDRESS = '0xAE06D814cD73F79C2B4AE483A9bB6Ce476941A6E';

// Addresses for different verification types
export const VERIFIED_ADDRESSES: Record<string, VerificationType> = {
  // Owner - your address
  '0xae06d814cd73f79c2b4ae483a9bb6ce476941a6e': 'owner',

  // Example addresses for different verification types
  '0x1234567890abcdef1234567890abcdef12345678': 'creator',
  '0xabcdef1234567890abcdef1234567890abcdef12': 'developer',
  '0x7890abcdef1234567890abcdef1234567890abcd': 'community',
  '0xdef1234567890abcdef1234567890abcdef12345': 'partner',
  '0x567890abcdef1234567890abcdef1234567890ab': 'investor',
  '0x90abcdef1234567890abcdef1234567890abcdef': 'early',
};

// Verification badge types and their descriptions
export const VERIFICATION_TYPES = {
  owner: {
    label: '',
    description: 'Verified',
    color: 'bg-purple-900'
  },
  creator: {
    label: 'Content Creator',
    description: 'Verified content creator with a significant following',
    color: 'bg-blue-500'
  },
  developer: {
    label: 'Developer',
    description: 'Verified developer who has contributed to blockchain projects',
    color: 'bg-green-500'
  },
  community: {
    label: 'Community Leader',
    description: 'Verified community leader or moderator',
    color: 'bg-yellow-500'
  },
  partner: {
    label: 'Official Partner',
    description: 'Verified partner of Audra',
    color: 'bg-orange-500'
  },
  investor: {
    label: 'Investor',
    description: 'Verified investor in Audra or related projects',
    color: 'bg-purple-500'
  },
  early: {
    label: 'Early Adopter',
    description: 'Verified early adopter of the platform',
    color: 'bg-teal-500'
  }
};

// Helper function to get verification description
export const getVerificationDescription = (type: string): string => {
  return (VERIFICATION_TYPES as any)[type]?.description || 'Verified user';
};

// Helper function to get verification label
export const getVerificationLabel = (type: string): string => {
  return (VERIFICATION_TYPES as any)[type]?.label || 'Verified';
};

// Helper function to get verification color
export const getVerificationColor = (type: string): string => {
  return (VERIFICATION_TYPES as any)[type]?.color || 'bg-blue-500';
};
