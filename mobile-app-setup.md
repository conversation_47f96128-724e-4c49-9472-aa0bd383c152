# Setting Up React Native for Audra Mobile App

## Initial Setup

1. Install React Native CLI:
```bash
npm install -g react-native-cli
```

2. Create a new React Native project:
```bash
npx react-native init AudraMobile --template react-native-template-typescript
```

3. Navigate to the project:
```bash
cd AudraMobile
```

4. Install necessary dependencies:
```bash
npm install @react-navigation/native @react-navigation/stack @supabase/supabase-js react-native-gesture-handler react-native-reanimated react-native-safe-area-context react-native-screens react-native-vector-icons @react-native-async-storage/async-storage ethers@5.7.2 react-native-audio-recorder-player react-native-fs react-native-permissions
```

5. Install iOS-specific dependencies (if on macOS):
```bash
cd ios && pod install && cd ..
```

6. For Android, update the `android/app/build.gradle` file to add permissions:
```gradle
android {
    // ...existing config

    defaultConfig {
        // ...existing config

        missingDimensionStrategy 'react-native-camera', 'general'
    }
}
```

## Project Structure

Create the following folder structure:
```
src/
├── api/
│   ├── supabase.ts
│   └── walletService.ts
├── components/
│   ├── AudioRecorder.tsx
│   ├── Post.tsx
│   └── ...
├── contexts/
│   ├── AuthContext.tsx
│   ├── WalletContext.tsx
│   └── ...
├── navigation/
│   ├── AppNavigator.tsx
│   ├── AuthNavigator.tsx
│   └── index.tsx
├── screens/
│   ├── auth/
│   │   ├── LoginScreen.tsx
│   │   └── RegisterScreen.tsx
│   ├── main/
│   │   ├── HomeScreen.tsx
│   │   ├── ProfileScreen.tsx
│   │   └── ...
│   └── ...
├── services/
│   ├── authService.ts
│   └── ...
├── utils/
│   ├── constants.ts
│   └── helpers.ts
└── App.tsx
```

## Supabase Integration

Create `src/api/supabase.ts`:
```typescript
import AsyncStorage from '@react-native-async-storage/async-storage';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://jcltjkaumevuycntdmds.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpjbHRqa2F1bWV2dXljbnRkbWRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTkwMDEsImV4cCI6MjA2MTg3NTAwMX0.LqQSk0NwB1mDB4Cznn0iQSrYvsAAWyUsaj8reo_etqM';

export const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});
```

## Navigation Setup

Create `src/navigation/index.tsx`:
```typescript
import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { useAuth } from '../contexts/AuthContext';
import AppNavigator from './AppNavigator';
import AuthNavigator from './AuthNavigator';

const Stack = createStackNavigator();

export default function Navigation() {
  const { user } = useAuth();

  return (
    <NavigationContainer>
      {user ? <AppNavigator /> : <AuthNavigator />}
    </NavigationContainer>
  );
}
```

## Auth Context

Create `src/contexts/AuthContext.tsx`:
```typescript
import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../api/supabase';
import walletManager from '../services/walletManager';

const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check for existing session
    checkUser();

    // Listen for auth changes
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN') {
          const userData = session.user;
          setUser(userData);
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
        }
      }
    );

    return () => {
      authListener.subscription.unsubscribe();
    };
  }, []);

  async function checkUser() {
    try {
      setLoading(true);
      const { data: { session } } = await supabase.auth.getSession();

      if (session?.user) {
        setUser(session.user);

        // Get wallet for user
        const wallet = walletManager.getWallet(session.user.id);
        console.log('User wallet:', wallet.address);
      }
    } catch (error) {
      console.error('Error checking auth:', error);
    } finally {
      setLoading(false);
    }
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
```
