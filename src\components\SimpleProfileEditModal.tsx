import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>T<PERSON>le, <PERSON><PERSON>Header, DialogFooter } from '@/components/ui/dialog';
import { Drawer, DrawerContent, DrawerHeader, DrawerFooter } from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { X, Upload, Globe } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { toast } from '@/components/ui/sonner';
import { UserProfile, UserProfileUpdate } from '@/types/user-profile';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import useProfileUpdate from '@/hooks/useProfileUpdate';

interface SimpleProfileEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  profile: UserProfile;
  onSave: (update: UserProfileUpdate) => void;
}

const SimpleProfileEditModal: React.FC<SimpleProfileEditModalProps> = ({
  isOpen,
  onClose,
  profile,
  onSave
}) => {
  const isMobile = useIsMobile();
  const { updateProfile, isUpdating } = useProfileUpdate();
  
  const [formData, setFormData] = useState<UserProfileUpdate & {
    profileImageFile?: File;
    coverImageFile?: File;
  }>({
    username: profile?.username || '',
    displayName: profile?.displayName || '',
    bio: profile?.bio || '',
    profileImageUrl: profile?.profileImageUrl || '',
    coverImageUrl: profile?.coverImageUrl || '',
    socialLinks: {
      twitter: profile?.socialLinks?.twitter || '',
      github: profile?.socialLinks?.github || '',
      website: profile?.socialLinks?.website || ''
    }
  });

  // Update form data when profile prop changes OR when modal opens
  useEffect(() => {
    console.log('Profile changed, updating form data:', {
      profile,
      isOpen,
      currentFormData: formData
    });
    
    if (profile && isOpen) {
      setFormData({
        username: profile.username || '',
        displayName: profile.displayName || '',
        bio: profile.bio || '',
        profileImageUrl: profile.profileImageUrl || '',
        coverImageUrl: profile.coverImageUrl || '',
        socialLinks: {
          twitter: profile.socialLinks?.twitter || '',
          github: profile.socialLinks?.github || '',
          website: profile.socialLinks?.website || ''
        }
      });
    }
  }, [profile, isOpen]);

  // Refs for file inputs
  const profileImageInputRef = React.useRef<HTMLInputElement>(null);
  const coverImageInputRef = React.useRef<HTMLInputElement>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    if (name.startsWith('social.')) {
      const socialType = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        socialLinks: {
          ...prev.socialLinks || {},
          [socialType]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleProfileImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // Create a preview
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setFormData(prev => ({
            ...prev,
            profileImageUrl: event.target?.result as string,
            profileImageFile: file
          }));
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleCoverImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // Create a preview
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setFormData(prev => ({
            ...prev,
            coverImageUrl: event.target?.result as string,
            coverImageFile: file
          }));
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async () => {
    try {
      // Validate the form data
      if (!formData.displayName || formData.displayName.trim() === '') {
        toast.error('Display name is required');
        return;
      }

      if (!formData.username || formData.username.trim() === '') {
        toast.error('Username is required');
        return;
      }

      console.log("🔄 SimpleProfileEditModal - Starting profile update");

      // Create the update data
      const update: UserProfileUpdate = {
        displayName: formData.displayName.trim(),
        username: formData.username.trim(),
        bio: formData.bio?.trim() || '',
        socialLinks: { ...formData.socialLinks }
      };

      // Use the unified profile update hook
      const success = await updateProfile(
        profile.address,
        update,
        formData.profileImageFile,
        formData.coverImageFile
      );

      if (success) {
        console.log("✅ Profile updated successfully");
        toast.success('Profile updated successfully!');
        
        // Call the onSave callback 
        onSave(update);
        
        // Close the modal
        onClose();
      } else {
        console.error("❌ Profile update failed");
        toast.error("Failed to update profile. Please try again.");
      }
    } catch (error) {
      console.error("❌ Error in profile update:", error);
      toast.error("An unexpected error occurred");
    }
  };

  // Function to handle removing profile image
  const removeProfileImage = () => {
    setFormData(prev => ({
      ...prev,
      profileImageUrl: '',
      profileImageFile: undefined
    }));
  };

  // Function to handle removing cover image
  const removeCoverImage = () => {
    setFormData(prev => ({
      ...prev,
      coverImageUrl: '',
      coverImageFile: undefined
    }));
  };

  const ModalContent = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center w-full mb-4">
        <h2 className="text-xl font-semibold">Edit Profile</h2>
        <Button 
          variant="ghost" 
          size="icon" 
          onClick={onClose}
          type="button"
        >
          <X size={20} />
        </Button>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="displayName">Display Name</Label>
          <Input
            id="displayName"
            name="displayName"
            value={formData.displayName}
            onChange={handleInputChange}
            placeholder="Your display name"
          />
        </div>

        <div>
          <Label htmlFor="username">Username</Label>
          <Input
            id="username"
            name="username"
            value={formData.username}
            onChange={handleInputChange}
            placeholder="username"
          />
        </div>

        <div>
          <Label htmlFor="bio">Bio</Label>
          <Textarea
            id="bio"
            name="bio"
            value={formData.bio}
            onChange={handleInputChange}
            placeholder="Tell us about yourself"
            className="resize-none h-24"
          />
        </div>

        <div>
          <Label htmlFor="social.twitter">Twitter</Label>
          <Input
            id="social.twitter"
            name="social.twitter"
            value={formData.socialLinks?.twitter || ''}
            onChange={handleInputChange}
            placeholder="@username"
          />
        </div>

        <div>
          <Label htmlFor="social.github">GitHub</Label>
          <Input
            id="social.github"
            name="social.github"
            value={formData.socialLinks?.github || ''}
            onChange={handleInputChange}
            placeholder="username"
          />
        </div>

        <div>
          <Label htmlFor="social.website">Website</Label>
          <Input
            id="social.website"
            name="social.website"
            value={formData.socialLinks?.website || ''}
            onChange={handleInputChange}
            placeholder="https://yourwebsite.com"
          />
        </div>

        <div className="flex items-center justify-between">
          <div>
            <Label htmlFor="profileImage">Profile Image</Label>
            <Input
              id="profileImage"
              name="profileImage"
              type="file"
              ref={profileImageInputRef}
              onChange={handleProfileImageChange}
              accept="image/*"
            />
          </div>
          {formData.profileImageUrl && (
            <div className="flex items-center">
              <Avatar>
                <AvatarImage src={formData.profileImageUrl} />
                <AvatarFallback>
                  <Upload size={20} />
                </AvatarFallback>
              </Avatar>
              <Button
                onClick={removeProfileImage}
                className="ml-2"
                variant="outline"
                size="sm"
                type="button"
              >
                Remove
              </Button>
            </div>
          )}
        </div>

        <div className="flex items-center justify-between">
          <div>
            <Label htmlFor="coverImage">Cover Image</Label>
            <Input
              id="coverImage"
              name="coverImage"
              type="file"
              ref={coverImageInputRef}
              onChange={handleCoverImageChange}
              accept="image/*"
            />
          </div>
          {formData.coverImageUrl && (
            <div className="flex items-center">
              <Avatar>
                <AvatarImage src={formData.coverImageUrl} />
                <AvatarFallback>
                  <Globe size={20} />
                </AvatarFallback>
              </Avatar>
              <Button
                onClick={removeCoverImage}
                className="ml-2"
                variant="outline"
                size="sm"
                type="button"
              >
                Remove
              </Button>
            </div>
          )}
        </div>
      </div>

      <div className="mt-6">
        <Button
          onClick={handleSubmit}
          className="w-full"
          disabled={isUpdating}
          type="button"
        >
          {isUpdating ? 'Saving...' : 'Save Profile'}
        </Button>
      </div>
    </div>
  );

  if (isMobile) {
    return (
      <Drawer open={isOpen} onOpenChange={open => !open && onClose()}>
        <DrawerContent className="px-4 py-6">
          <ModalContent />
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-md p-6">
        <ModalContent />
      </DialogContent>
    </Dialog>
  );
};

export default SimpleProfileEditModal;