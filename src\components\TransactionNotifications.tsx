import React, { useState, useEffect } from 'react';
import { useWallet } from '@/contexts/WalletContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/sonner';
import { Bell, BellOff, ArrowUpRight, ArrowDownLeft, ExternalLink } from 'lucide-react';
import { TransactionData } from '@/services/walletService';

// Mock function to simulate checking for new transactions
const checkForNewTransactions = async (
  address: string,
  lastCheckedTime: number
): Promise<TransactionData[]> => {
  // In a real implementation, this would call an API to check for new transactions
  // For now, we'll just return an empty array
  return [];
};

const TransactionNotifications: React.FC = () => {
  const { wallet, transactions } = useWallet();
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [lastCheckedTime, setLastCheckedTime] = useState(Date.now());
  const [newTransactions, setNewTransactions] = useState<TransactionData[]>([]);
  
  // Check for new transactions periodically
  useEffect(() => {
    if (!wallet || !notificationsEnabled) return;
    
    const checkInterval = setInterval(async () => {
      try {
        const newTxs = await checkForNewTransactions(wallet.address, lastCheckedTime);
        
        if (newTxs.length > 0) {
          setNewTransactions(prev => [...newTxs, ...prev]);
          setLastCheckedTime(Date.now());
          
          // Show notification for each new transaction
          newTxs.forEach(tx => {
            const isIncoming = tx.to.toLowerCase() === wallet.address.toLowerCase();
            
            toast(
              isIncoming ? 'Incoming Transaction' : 'Outgoing Transaction',
              {
                description: `${isIncoming ? 'Received' : 'Sent'} ${tx.amount} ${tx.token?.symbol || 'ETH'}`,
                action: {
                  label: 'View',
                  onClick: () => {
                    // Open transaction in explorer
                    if (tx.txHash) {
                      window.open(`https://basescan.org/tx/${tx.txHash}`, '_blank');
                    }
                  }
                }
              }
            );
          });
        }
      } catch (error) {
        console.error('Error checking for new transactions:', error);
      }
    }, 30000); // Check every 30 seconds
    
    return () => clearInterval(checkInterval);
  }, [wallet, notificationsEnabled, lastCheckedTime]);
  
  // Clear notifications
  const clearNotifications = () => {
    setNewTransactions([]);
  };
  
  if (!wallet) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Transaction Notifications</CardTitle>
          <CardDescription>You don't have a wallet yet</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            A wallet will be created for you automatically during registration.
          </p>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              <span>Transaction Notifications</span>
            </CardTitle>
            <CardDescription>Get notified about wallet activity</CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="notifications"
              checked={notificationsEnabled}
              onCheckedChange={setNotificationsEnabled}
            />
            <Label htmlFor="notifications" className="text-sm">
              {notificationsEnabled ? 'Enabled' : 'Disabled'}
            </Label>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <p className="text-sm font-medium">Notification Settings</p>
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <ArrowDownLeft className="h-4 w-4 text-green-500" />
              <Label htmlFor="incoming" className="text-sm">Incoming transactions</Label>
            </div>
            <Switch id="incoming" checked={notificationsEnabled} onCheckedChange={setNotificationsEnabled} />
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <ArrowUpRight className="h-4 w-4 text-red-500" />
              <Label htmlFor="outgoing" className="text-sm">Outgoing transactions</Label>
            </div>
            <Switch id="outgoing" checked={notificationsEnabled} onCheckedChange={setNotificationsEnabled} />
          </div>
        </div>
        
        <div className="pt-4 border-t">
          <p className="text-sm font-medium mb-3">Recent Notifications</p>
          
          {newTransactions.length === 0 ? (
            <div className="text-center py-8">
              <BellOff className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">No new notifications</p>
            </div>
          ) : (
            <div className="space-y-3">
              {newTransactions.map((tx) => {
                const isIncoming = tx.to.toLowerCase() === wallet.address.toLowerCase();
                
                return (
                  <div 
                    key={tx.id} 
                    className="flex items-center justify-between p-3 bg-secondary/50 rounded-md"
                  >
                    <div className="flex items-center gap-3">
                      {isIncoming ? (
                        <ArrowDownLeft className="h-5 w-5 text-green-500" />
                      ) : (
                        <ArrowUpRight className="h-5 w-5 text-red-500" />
                      )}
                      <div>
                        <p className="text-sm font-medium">
                          {isIncoming ? 'Received' : 'Sent'} {tx.amount} {tx.token?.symbol || 'ETH'}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(tx.timestamp).toLocaleString()}
                        </p>
                      </div>
                    </div>
                    {tx.txHash && (
                      <Button 
                        variant="ghost" 
                        size="icon"
                        onClick={() => window.open(`https://basescan.org/tx/${tx.txHash}`, '_blank')}
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                );
              })}
              
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full"
                onClick={clearNotifications}
              >
                Clear All
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default TransactionNotifications;
