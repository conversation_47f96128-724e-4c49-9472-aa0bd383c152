import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/lib/supabase';
import { toast } from '@/components/ui/sonner';
import { 
  Vote, 
  Mic, 
  Calendar, 
  Users, 
  DollarSign,
  Settings,
  MessageSquare,
  Plus,
  Play,
  Square
} from 'lucide-react';

interface ProposalCreationProps {
  channelId: string;
  userAddress: string;
  onProposalCreated?: () => void;
}

const ProposalCreation: React.FC<ProposalCreationProps> = ({
  channelId,
  userAddress,
  onProposalCreated
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    proposalType: 'topic_approval',
    votingDuration: '7', // days
    minParticipation: '1',
    minApprovalPercentage: '50',
    votingType: 'simple'
  });

  const proposalTypes = {
    topic_approval: {
      label: 'Topic Approval',
      description: 'Vote on new discussion topics',
      icon: MessageSquare
    },
    speaker_nomination: {
      label: 'Speaker Nomination',
      description: 'Nominate speakers for events',
      icon: Users
    },
    feature_request: {
      label: 'Feature Request',
      description: 'Propose new channel features',
      icon: Settings
    },
    treasury_allocation: {
      label: 'Treasury Allocation',
      description: 'Allocate treasury funds',
      icon: DollarSign
    },
    channel_update: {
      label: 'Channel Update',
      description: 'Propose channel changes',
      icon: Settings
    },
    custom: {
      label: 'Custom Proposal',
      description: 'Other governance matters',
      icon: Vote
    }
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const recorder = new MediaRecorder(stream);
      const chunks: BlobPart[] = [];

      recorder.ondataavailable = (e) => chunks.push(e.data);
      recorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'audio/webm' });
        setAudioBlob(blob);
        stream.getTracks().forEach(track => track.stop());
      };

      recorder.start();
      setMediaRecorder(recorder);
      setIsRecording(true);
    } catch (error) {
      console.error('Error starting recording:', error);
      toast('Failed to start recording');
    }
  };

  const stopRecording = () => {
    if (mediaRecorder && isRecording) {
      mediaRecorder.stop();
      setIsRecording(false);
      setMediaRecorder(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.title.trim() || !formData.description.trim()) {
      toast('Please fill in all required fields');
      return;
    }

    try {
      setLoading(true);

      // Get user profile
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('wallet_address', userAddress)
        .single();

      if (profileError || !profileData) {
        toast('Please connect your wallet first');
        return;
      }

      // Calculate voting end time
      const votingEnd = new Date();
      votingEnd.setDate(votingEnd.getDate() + parseInt(formData.votingDuration));

      // Upload audio if recorded
      let audioUrl = null;
      let audioDuration = null;

      if (audioBlob) {
        // Here you would upload to your storage service
        // For now, we'll simulate the upload
        audioUrl = `audio_proposals/${Date.now()}.webm`;
        audioDuration = 60; // Placeholder duration
      }

      // Create proposal
      const proposalData = {
        channel_id: channelId,
        proposer_profile_id: profileData.id,
        title: formData.title.trim(),
        description: formData.description.trim(),
        proposal_type: formData.proposalType,
        audio_proposal_url: audioUrl,
        audio_duration: audioDuration,
        voting_type: formData.votingType,
        voting_end: votingEnd.toISOString(),
        min_participation: parseInt(formData.minParticipation),
        min_approval_percentage: parseFloat(formData.minApprovalPercentage),
        status: 'active'
      };

      const { error } = await supabase
        .from('proposals')
        .insert(proposalData);

      if (error) {
        console.error('Error creating proposal:', error);
        toast('Failed to create proposal');
        return;
      }

      toast('Proposal created successfully! 🗳️');
      onProposalCreated?.();
      setIsOpen(false);
      
      // Reset form
      setFormData({
        title: '',
        description: '',
        proposalType: 'topic_approval',
        votingDuration: '7',
        minParticipation: '1',
        minApprovalPercentage: '50',
        votingType: 'simple'
      });
      setAudioBlob(null);

    } catch (error) {
      console.error('Error creating proposal:', error);
      toast('Failed to create proposal');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button className="bg-voicechain-purple hover:bg-voicechain-accent">
          <Plus size={16} className="mr-2" />
          Create Proposal
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Vote className="text-voicechain-purple" size={20} />
            Create Governance Proposal
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Proposal Type */}
          <div>
            <Label className="text-sm font-medium mb-3 block">Proposal Type</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {Object.entries(proposalTypes).map(([key, config]) => {
                const Icon = config.icon;
                return (
                  <Card
                    key={key}
                    className={`cursor-pointer transition-all ${
                      formData.proposalType === key 
                        ? 'border-voicechain-purple bg-voicechain-purple/5' 
                        : 'hover:border-border/80'
                    }`}
                    onClick={() => setFormData(prev => ({ ...prev, proposalType: key }))}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-start gap-3">
                        <div className="w-8 h-8 bg-voicechain-purple/10 rounded-lg flex items-center justify-center">
                          <Icon size={16} className="text-voicechain-purple" />
                        </div>
                        <div>
                          <h4 className="font-medium text-sm">{config.label}</h4>
                          <p className="text-xs text-muted-foreground">{config.description}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* Title */}
          <div>
            <Label htmlFor="title" className="text-sm font-medium">
              Proposal Title *
            </Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder="Enter a clear, descriptive title"
              required
            />
          </div>

          {/* Description */}
          <div>
            <Label htmlFor="description" className="text-sm font-medium">
              Description *
            </Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Provide detailed information about your proposal"
              rows={4}
              required
            />
          </div>

          {/* Audio Proposal */}
          <div>
            <Label className="text-sm font-medium mb-2 block">
              Audio Proposal (Optional)
            </Label>
            <div className="flex items-center gap-3">
              {!audioBlob ? (
                <Button
                  type="button"
                  variant="outline"
                  onClick={isRecording ? stopRecording : startRecording}
                  className={isRecording ? 'bg-red-50 border-red-200' : ''}
                >
                  {isRecording ? (
                    <>
                      <Square size={16} className="mr-2 text-red-500" />
                      Stop Recording
                    </>
                  ) : (
                    <>
                      <Mic size={16} className="mr-2" />
                      Record Audio
                    </>
                  )}
                </Button>
              ) : (
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <Play size={12} />
                    Audio Recorded
                  </Badge>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => setAudioBlob(null)}
                  >
                    Remove
                  </Button>
                </div>
              )}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Record a voice explanation of your proposal for better engagement
            </p>
          </div>

          {/* Voting Settings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="votingDuration" className="text-sm font-medium">
                Voting Duration
              </Label>
              <Select
                value={formData.votingDuration}
                onValueChange={(value) => setFormData(prev => ({ ...prev, votingDuration: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1 day</SelectItem>
                  <SelectItem value="3">3 days</SelectItem>
                  <SelectItem value="7">7 days</SelectItem>
                  <SelectItem value="14">14 days</SelectItem>
                  <SelectItem value="30">30 days</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="minApproval" className="text-sm font-medium">
                Min Approval %
              </Label>
              <Input
                id="minApproval"
                type="number"
                min="1"
                max="100"
                value={formData.minApprovalPercentage}
                onChange={(e) => setFormData(prev => ({ ...prev, minApprovalPercentage: e.target.value }))}
              />
            </div>
          </div>

          {/* Submit */}
          <div className="flex gap-3">
            <Button
              type="submit"
              disabled={loading}
              className="flex-1 bg-voicechain-purple hover:bg-voicechain-accent"
            >
              {loading ? 'Creating...' : 'Create Proposal'}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={loading}
            >
              Cancel
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default ProposalCreation;
