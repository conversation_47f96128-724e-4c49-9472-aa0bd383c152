import { supabase } from '@/integrations/supabase/client';

/**
 * Store audio URL mapping in Supabase
 * This helps track which posts are using which audio URLs
 * @param postId The ID of the post
 * @param audioUrl The audio URL
 */
export const storeAudioUrlMapping = async (postId: string, audioUrl: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('audio_mappings')
      .upsert({
        post_id: postId,
        audio_url: audioUrl,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'post_id'
      });

    if (error) {
      console.error('Error storing audio URL mapping in Supabase:', error);
      return false;
    }

    console.log(`Stored audio URL mapping for post ${postId}: ${audioUrl}`);
    return true;
  } catch (error) {
    console.error('Error storing audio URL mapping:', error);
    return false;
  }
};

/**
 * Get audio URL mapping from Supabase
 * @param postId The ID of the post
 * @returns The audio URL or null if not found
 */
export const getAudioUrlMapping = async (postId: string): Promise<string | null> => {
  try {
    const { data, error } = await supabase
      .from('audio_mappings')
      .select('audio_url')
      .eq('post_id', postId)
      .maybeSingle();

    if (error) {
      console.error('Error getting audio URL mapping from Supabase:', error);
      return null;
    }

    if (data) {
      return data.audio_url;
    }

    return null;
  } catch (error) {
    console.error('Error getting audio URL mapping:', error);
    return null;
  }
};

/**
 * Store transcript in Supabase
 * @param transcriptId The ID of the transcript
 * @param transcript The transcript text
 * @param userId The user ID associated with this transcript (optional)
 */
export const storeTranscript = async (
  transcriptId: string,
  transcript: string,
  userId?: string
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('transcripts')
      .upsert({
        id: transcriptId,
        text: transcript,
        user_id: userId || 'anonymous',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'id'
      });

    if (error) {
      console.error('Error storing transcript in Supabase:', error);
      return false;
    }

    console.log(`Stored transcript ${transcriptId}`);
    return true;
  } catch (error) {
    console.error('Error storing transcript:', error);
    return false;
  }
};

/**
 * Get transcript from Supabase
 * @param transcriptId The ID of the transcript
 * @returns The transcript text or null if not found
 */
export const getTranscript = async (transcriptId: string): Promise<string | null> => {
  try {
    const { data, error } = await supabase
      .from('transcripts')
      .select('text')
      .eq('id', transcriptId)
      .maybeSingle();

    if (error) {
      console.error('Error getting transcript from Supabase:', error);
      return null;
    }

    if (data) {
      return data.text;
    }

    return null;
  } catch (error) {
    console.error('Error getting transcript:', error);
    return null;
  }
};
